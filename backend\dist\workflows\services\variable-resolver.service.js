"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VariableResolverService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.VariableResolverService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let VariableResolverService = VariableResolverService_1 = class VariableResolverService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(VariableResolverService_1.name);
    }
    async resolveVariables(template, context) {
        try {
            const sessionVariables = await this.getSessionVariables(context.sessionId);
            const workflowVariables = await this.getWorkflowVariables(context.workflowId);
            const systemVariables = this.getSystemVariables();
            const allVariables = {
                ...systemVariables,
                ...workflowVariables,
                ...sessionVariables,
                ...context.variables,
            };
            return this.processTemplate(template, allVariables, context.nodeResults);
        }
        catch (error) {
            this.logger.error(`Failed to resolve variables: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getSessionVariables(sessionId) {
        try {
            const session = await this.prisma.session.findUnique({
                where: { id: sessionId },
            });
            if (!session) {
                return {};
            }
            const sessionContext = session.context;
            return sessionContext.variables || {};
        }
        catch (error) {
            this.logger.error(`Failed to get session variables: ${error.message}`, error.stack);
            return {};
        }
    }
    async getWorkflowVariables(workflowId) {
        try {
            const workflow = await this.prisma.workflow.findUnique({
                where: { id: workflowId },
            });
            if (!workflow) {
                return {};
            }
            const definition = workflow.definition;
            return definition.variables || {};
        }
        catch (error) {
            this.logger.error(`Failed to get workflow variables: ${error.message}`, error.stack);
            return {};
        }
    }
    getSystemVariables() {
        return {
            now: new Date().toISOString(),
            timestamp: Date.now(),
            random: Math.random(),
            uuid: this.generateUUID(),
        };
    }
    processTemplate(template, variables, nodeResults) {
        if (typeof template === 'string') {
            return template.replace(/\${(\w+)}/g, (match, varName) => {
                return variables[varName] !== undefined ?
                    JSON.stringify(variables[varName]) : match;
            });
        }
        else if (Array.isArray(template)) {
            return template.map(item => this.processTemplate(item, variables, nodeResults));
        }
        else if (template && typeof template === 'object') {
            const result = {};
            for (const key in template) {
                result[key] = this.processTemplate(template[key], variables, nodeResults);
            }
            return result;
        }
        else if (template === '$nodeResults' && nodeResults) {
            return Object.fromEntries(nodeResults);
        }
        else if (typeof template === 'string' && template.startsWith('$nodeResult.') && nodeResults) {
            const nodeId = template.split('.')[1];
            return nodeResults.get(nodeId) || null;
        }
        return template;
    }
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    async updateSessionVariables(sessionId, variables) {
        try {
            const session = await this.prisma.session.findUnique({
                where: { id: sessionId },
            });
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const sessionContext = session.context;
            const updatedContext = {
                ...sessionContext,
                variables: {
                    ...(sessionContext.variables || {}),
                    ...variables,
                },
            };
            await this.prisma.session.update({
                where: { id: sessionId },
                data: {
                    context: updatedContext,
                },
            });
        }
        catch (error) {
            this.logger.error(`Failed to update session variables: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.VariableResolverService = VariableResolverService;
exports.VariableResolverService = VariableResolverService = VariableResolverService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VariableResolverService);
//# sourceMappingURL=variable-resolver.service.js.map