# Production Environment Variables
NODE_ENV=production

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-here"
JWT_EXPIRES_IN="24h"

# API Configuration
API_PORT=3001
API_HOST="0.0.0.0"

# Frontend Configuration
NEXT_PUBLIC_API_URL="https://api.synapseai.com"
NEXT_PUBLIC_WS_URL="wss://api.synapseai.com"
NEXT_PUBLIC_APP_URL="https://app.synapseai.com"

# AI Provider Keys (Add your actual keys)
OPENAI_API_KEY="sk-your-openai-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"
GOOGLE_API_KEY="your-google-api-key"
MISTRAL_API_KEY="your-mistral-api-key"
GROQ_API_KEY="gsk_your-groq-key"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File Upload Configuration
MAX_FILE_SIZE="10MB"
UPLOAD_PATH="/uploads"

# Security Configuration
CORS_ORIGIN="https://app.synapseai.com"
RATE_LIMIT_WINDOW="15"
RATE_LIMIT_MAX="100"

# Monitoring & Analytics
SENTRY_DSN="your-sentry-dsn"
ANALYTICS_ENABLED="true"

# Feature Flags
ENABLE_WORKFLOWS="true"
ENABLE_AGENTS="true"
ENABLE_TOOLS="true"
ENABLE_ANALYTICS="true"
ENABLE_COLLABORATION="true"

# Backup Configuration
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS="30"