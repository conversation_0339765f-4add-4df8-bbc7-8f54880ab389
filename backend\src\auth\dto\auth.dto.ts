import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

// Authentication Schemas
export const LoginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  organizationSlug: z.string().optional(),
  mfaCode: z.string().length(6, 'MFA code must be 6 digits').optional(),
  rememberMe: z.boolean().default(false),
});

export const RegisterSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number and special character'),
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  organizationName: z.string().min(1, 'Organization name is required').max(100),
  organizationSlug: z.string()
    .min(3, 'Organization slug must be at least 3 characters')
    .max(50)
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
});

export const RefreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

export const ForgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
  organizationSlug: z.string().optional(),
});

export const ResetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const ChangePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const EnableMFASchema = z.object({
  secret: z.string().min(1, 'MFA secret is required'),
  code: z.string().length(6, 'MFA code must be 6 digits'),
});

export const VerifyMFASchema = z.object({
  code: z.string().length(6, 'MFA code must be 6 digits'),
});

export const UpdateProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50).optional(),
  lastName: z.string().min(1, 'Last name is required').max(50).optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.string().optional(),
    timezone: z.string().optional(),
    notifications: z.boolean().optional(),
    viewMode: z.enum(['grid', 'list']).optional(),
  }).optional(),
});

export const InviteUserSchema = z.object({
  email: z.string().email('Invalid email format'),
  role: z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']),
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  message: z.string().max(500).optional(),
});

export const AcceptInviteSchema = z.object({
  token: z.string().min(1, 'Invite token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
           'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const UpdateUserRoleSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  role: z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']),
});

export const SSOConfigSchema = z.object({
  provider: z.enum(['SAML', 'OAUTH', 'ACTIVE_DIRECTORY']),
  config: z.object({
    entityId: z.string().optional(),
    ssoUrl: z.string().url().optional(),
    certificate: z.string().optional(),
    clientId: z.string().optional(),
    clientSecret: z.string().optional(),
    domain: z.string().optional(),
    tenantId: z.string().optional(),
  }),
  isEnabled: z.boolean().default(true),
});

// Permission Schemas
export const PermissionCheckSchema = z.object({
  userId: z.string().cuid('Invalid user ID'),
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  scope: z.record(z.any()).optional(),
});

export const CreateAPIKeySchema = z.object({
  name: z.string().min(1, 'API key name is required').max(100),
  description: z.string().max(500).optional(),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  expiresAt: z.date().optional(),
});

// DTOs
export class LoginDto extends createZodDto(LoginSchema) {}
export class RegisterDto extends createZodDto(RegisterSchema) {}
export class RefreshTokenDto extends createZodDto(RefreshTokenSchema) {}
export class ForgotPasswordDto extends createZodDto(ForgotPasswordSchema) {}
export class ResetPasswordDto extends createZodDto(ResetPasswordSchema) {}
export class ChangePasswordDto extends createZodDto(ChangePasswordSchema) {}
export class EnableMFADto extends createZodDto(EnableMFASchema) {}
export class VerifyMFADto extends createZodDto(VerifyMFASchema) {}
export class UpdateProfileDto extends createZodDto(UpdateProfileSchema) {}
export class InviteUserDto extends createZodDto(InviteUserSchema) {}
export class AcceptInviteDto extends createZodDto(AcceptInviteSchema) {}
export class UpdateUserRoleDto extends createZodDto(UpdateUserRoleSchema) {}
export class SSOConfigDto extends createZodDto(SSOConfigSchema) {}
export class PermissionCheckDto extends createZodDto(PermissionCheckSchema) {}
export class CreateAPIKeyDto extends createZodDto(CreateAPIKeySchema) {}

// Response Types
export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    avatar?: string;
    organization: {
      id: string;
      name: string;
      slug: string;
      settings: any;
      branding: any;
    };
    permissions: string[];
    preferences: any;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresAt: number;
  };
  session: {
    id: string;
    expiresAt: string;
  };
}

export interface MFASetupResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface APIKeyResponse {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  createdAt: string;
  expiresAt?: string;
}