"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Bot,
  Plus,
  Search,
  Settings,
  TrendingUp,
  Users,
  MessageSquare,
  Activity,
  CheckCircle,
  Pause,
  AlertCircle,
} from "lucide-react";
import AgentManagementUI from "@/components/agents/AgentManagementUI";
import { useAgents } from "@/hooks/useAgents";

const AgentDashboardPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");

  const {
    agents,
    loading,
    error,
    total: agentsTotal,
  } = useAgents();

  const activeAgents = agents.filter(a => a.status === "ACTIVE").length;
  const totalSessions = agents.reduce((sum, agent) => sum + (agent._count?.sessions || 0), 0);
  const totalTasks = agents.reduce((sum, agent) => sum + (agent._count?.tasks || 0), 0);
  const collaborativeAgents = agents.filter(a => a.capabilities?.canCollaborate).length;

  const getAgentTypeDistribution = () => {
    const distribution: Record<string, number> = {};
    agents.forEach(agent => {
      distribution[agent.type] = (distribution[agent.type] || 0) + 1;
    });
    return distribution;
  };

  const typeDistribution = getAgentTypeDistribution();

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Agent Dashboard</h1>
          <p className="text-muted-foreground">
            Manage and monitor your AI agents
          </p>
        </div>
        <Button onClick={() => router.push("/dashboard/agents/new")}>
          <Plus className="mr-2 h-4 w-4" />
          Create Agent
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">
            <TrendingUp className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="agents">
            <Bot className="mr-2 h-4 w-4" />
            Agents
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Settings className="mr-2 h-4 w-4" />
            Templates
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
                <Bot className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{agentsTotal}</div>
                <p className="text-xs text-muted-foreground">
                  {activeAgents} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalSessions}</div>
                <p className="text-xs text-muted-foreground">
                  Across all agents
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalTasks}</div>
                <p className="text-xs text-muted-foreground">
                  Processed by agents
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Collaborative Agents</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{collaborativeAgents}</div>
                <p className="text-xs text-muted-foreground">
                  Can communicate with others
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Agent Type Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Agent Type Distribution</CardTitle>
              <CardDescription>
                Breakdown of agents by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(typeDistribution).map(([type, count]) => (
                  <div key={type} className="flex items-center">
                    <div className="w-36 font-medium">{type.replace('_', ' ')}</div>
                    <div className="flex-1">
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className={`h-full rounded-full ${type === "STANDALONE" ? "bg-blue-500" :
                              type === "TOOL_DRIVEN" ? "bg-green-500" :
                                type === "HYBRID" ? "bg-purple-500" :
                                  type === "MULTI_TASKING" ? "bg-orange-500" :
                                    "bg-pink-500"
                            }`}
                          style={{ width: `${(count / agentsTotal) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="w-12 text-right">{count}</div>
                    <div className="w-16 text-right text-muted-foreground">
                      {Math.round((count / agentsTotal) * 100)}%
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Agents */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Agents</CardTitle>
              <CardDescription>
                Your most recently created agents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {agents.slice(0, 5).map((agent) => (
                  <div key={agent.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="bg-primary/10 p-2 rounded-full">
                        <Bot className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {agent.type.replace('_', ' ')} • {agent.provider}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge className={
                        agent.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                          agent.status === "INACTIVE" ? "bg-yellow-100 text-yellow-800" :
                            "bg-red-100 text-red-800"
                      }>
                        {agent.status}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/dashboard/agents/${agent.id}`)}
                      >
                        View
                      </Button>
                    </div>
                  </div>
                ))}
                {agents.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">No agents created yet</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => router.push("/dashboard/agents/new")}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create Agent
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents">
          <AgentManagementUI />
        </TabsContent>

        <TabsContent value="templates">
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Agent Templates</CardTitle>
                <CardDescription>
                  Pre-configured templates for creating new agents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 mb-4">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input placeholder="Search templates..." className="flex-1" />
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Template
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Template cards would go here */}
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Customer Support Agent</CardTitle>
                        <Badge className="bg-blue-100 text-blue-800">STANDALONE</Badge>
                      </div>
                      <CardDescription>
                        Specialized agent for handling customer inquiries and support requests
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Category:</span>
                          <Badge variant="outline">Support</Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Provider:</span>
                          <span>OpenAI</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Used:</span>
                          <span>24 times</span>
                        </div>
                      </div>
                      <Button className="w-full mt-4">Use Template</Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Data Analysis Agent</CardTitle>
                        <Badge className="bg-green-100 text-green-800">TOOL_DRIVEN</Badge>
                      </div>
                      <CardDescription>
                        Expert agent for analyzing data and generating insights
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Category:</span>
                          <Badge variant="outline">Analytics</Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Provider:</span>
                          <span>Claude</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Used:</span>
                          <span>18 times</span>
                        </div>
                      </div>
                      <Button className="w-full mt-4">Use Template</Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Code Review Agent</CardTitle>
                        <Badge className="bg-purple-100 text-purple-800">HYBRID</Badge>
                      </div>
                      <CardDescription>
                        Specialized agent for reviewing code quality and security
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Category:</span>
                          <Badge variant="outline">Development</Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Provider:</span>
                          <span>OpenAI</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Used:</span>
                          <span>12 times</span>
                        </div>
                      </div>
                      <Button className="w-full mt-4">Use Template</Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AgentDashboardPage;