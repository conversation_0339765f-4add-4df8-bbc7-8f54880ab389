// Workflow Type Definitions for Production Use

import { AgentCommunication } from "./agent";

export interface WorkflowNode {
    id: string;
    type: string;
    data: {
        label?: string;
        [key: string]: any;
    };
    position?: {
        x: number;
        y: number;
    };
}

export interface WorkflowEdge {
    id: string;
    source: string;
    target: string;
    type?: string;
    data?: Record<string, any>;
}

export interface WorkflowDefinition {
    nodes: WorkflowNode[];
    edges: WorkflowEdge[];
    metadata?: Record<string, any>;
}

export interface WorkflowStepInput {
    stepId: string;
    name: string;
    type: string;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    input: Record<string, any>;
    executionId: string;
}

export interface WorkflowStep {
    id: string;
    stepId: string;
    name: string;
    type: string;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED';
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    error?: string;
    input?: Record<string, any>;
    output?: Record<string, any>;
    executionId: string;
    metadata?: Record<string, any>;
    communication?: AgentCommunication;
}

export interface WorkflowExecution {
    id: string;
    workflowId: string;
    sessionId: string;
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    error?: string;
    input?: Record<string, any>;
    output?: Record<string, any>;
    steps?: WorkflowStep[];
}

export interface WorkflowExecutionResponse {
    id: string;
    workflowId: string;
    workflowName: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    startedAt: Date;
    completedAt?: Date;
    duration?: number;
    error?: string;
    steps: Array<{
        id: string;
        name: string;
        status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
        duration?: number;
        error?: string;
        startedAt?: Date;
        completedAt?: Date;
    }>;
    metadata?: {
        input?: Record<string, any>;
        output?: Record<string, any>;
    };
}

export interface Workflow {
    id: string;
    name: string;
    description?: string;
    definition: WorkflowDefinition;
    tags: string[];
    isActive: boolean;
    creatorId: string;
    organizationId: string;
    createdAt: Date;
    updatedAt: Date;
    creator?: {
        firstName: string;
        lastName: string;
        avatar?: string;
    };
    executions?: WorkflowExecution[];
    _count?: {
        executions: number;
    };
}

export interface WorkflowStats {
    total: number;
    active: number;
    inactive: number;
    recentExecutions: number;
}

// Workflow Template Types
export interface WorkflowTemplate {
    id: string;
    name: string;
    description?: string;
    category: string;
    definition: WorkflowDefinition;
    tags: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedTime: number;
    usageCount: number;
    rating?: number;
    createdBy: string;
    isPublic: boolean;
    thumbnail?: string;
    documentation?: string;
    examples?: Array<{
        name: string;
        description: string;
        input: Record<string, any>;
        expectedOutput: Record<string, any>;
    }>;
}

// Workflow Execution Context
export interface WorkflowExecutionContext {
    executionId: string;
    workflowId: string;
    sessionId: string;
    userId: string;
    organizationId: string;
    variables: Record<string, any>;
    currentStep?: string;
    stepResults: Record<string, any>;
    errors: Array<{
        stepId: string;
        error: string;
        timestamp: Date;
    }>;
}

// API Request/Response Types
export interface CreateWorkflowRequest {
    name: string;
    description?: string;
    definition: WorkflowDefinition;
    tags?: string[];
    isActive?: boolean;
}

export interface UpdateWorkflowRequest extends Partial<CreateWorkflowRequest> {
    id: string;
}

export interface ExecuteWorkflowRequest {
    input?: Record<string, any>;
    options?: {
        waitForCompletion?: boolean;
        timeout?: number;
        retryPolicy?: {
            maxRetries: number;
            retryDelay: number;
        };
    };
}