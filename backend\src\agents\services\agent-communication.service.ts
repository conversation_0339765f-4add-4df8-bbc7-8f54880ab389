import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AgentStatus } from '@prisma/client';

export interface AgentMessage {
  id: string;
  fromAgentId: string;
  toAgentId: string;
  type: 'request' | 'response' | 'broadcast' | 'notification';
  content: any;
  metadata?: {
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    requiresResponse?: boolean;
    timeout?: number;
    correlationId?: string;
    sessionId?: string;
    workflowId?: string;
  };
  timestamp: Date;
  status: 'pending' | 'delivered' | 'acknowledged' | 'failed';
}

export interface AgentCommunicationChannel {
  id: string;
  name: string;
  participants: string[];
  type: 'direct' | 'group' | 'broadcast';
  organizationId: string;
  isActive: boolean;
  metadata?: any;
}

@Injectable()
export class AgentCommunicationService {
  private readonly logger = new Logger(AgentCommunicationService.name);
  private messageQueue = new Map<string, AgentMessage[]>();
  private activeChannels = new Map<string, AgentCommunicationChannel>();
  private agentSubscriptions = new Map<string, Set<string>>();

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private eventEmitter: EventEmitter2,
  ) {
    this.initializeService();
  }

  private async initializeService() {
    // Load active communication channels from database
    await this.loadActiveChannels();
    
    // Set up event listeners
    this.setupEventListeners();
  }

  private async loadActiveChannels() {
    try {
      const channels = await this.prisma.agentCommunicationChannel.findMany({
        where: { isActive: true },
        include: {
          participants: {
            include: {
              agent: {
                select: { id: true, name: true, status: true }
              }
            }
          }
        }
      });

      for (const channel of channels) {
        this.activeChannels.set(channel.id, {
          id: channel.id,
          name: channel.name,
          participants: channel.participants.map(p => p.agentId),
          type: channel.type as 'direct' | 'group' | 'broadcast',
          organizationId: channel.organizationId,
          isActive: channel.isActive,
          metadata: channel.metadata as any,
        });
      }

      this.logger.log(`Loaded ${channels.length} active communication channels`);
    } catch (error) {
      this.logger.error('Failed to load communication channels', error);
    }
  }

  private setupEventListeners() {
    // Listen for agent status changes
    this.eventEmitter.on('agent.status.changed', this.handleAgentStatusChange.bind(this));
    
    // Listen for workflow events that might trigger agent communication
    this.eventEmitter.on('workflow.agent.communication.required', this.handleWorkflowCommunication.bind(this));
  }

  async sendMessage(
    fromAgentId: string,
    toAgentId: string,
    content: any,
    options: {
      type?: 'request' | 'response' | 'notification';
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      requiresResponse?: boolean;
      timeout?: number;
      correlationId?: string;
      sessionId?: string;
      workflowId?: string;
    } = {}
  ): Promise<AgentMessage> {
    // Validate agents exist and are active
    const [fromAgent, toAgent] = await Promise.all([
      this.prisma.agentInstance.findUnique({
        where: { id: fromAgentId },
        select: { id: true, name: true, status: true, organizationId: true }
      }),
      this.prisma.agentInstance.findUnique({
        where: { id: toAgentId },
        select: { id: true, name: true, status: true, organizationId: true }
      })
    ]);

    if (!fromAgent || !toAgent) {
      throw new Error('One or both agents not found');
    }

    if (fromAgent.organizationId !== toAgent.organizationId) {
      throw new Error('Agents must be in the same organization');
    }

    if (toAgent.status !== AgentStatus.ACTIVE) {
      throw new Error('Target agent is not active');
    }

    // Create message
    const message: AgentMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromAgentId,
      toAgentId,
      type: options.type || 'notification',
      content,
      metadata: {
        priority: options.priority || 'normal',
        requiresResponse: options.requiresResponse || false,
        timeout: options.timeout || 30000,
        correlationId: options.correlationId,
        sessionId: options.sessionId,
        workflowId: options.workflowId,
      },
      timestamp: new Date(),
      status: 'pending',
    };

    // Store message in database
    await this.prisma.agentMessage.create({
      data: {
        id: message.id,
        fromAgentId: message.fromAgentId,
        toAgentId: message.toAgentId,
        type: message.type,
        content: message.content,
        metadata: message.metadata as any,
        status: message.status,
        organizationId: fromAgent.organizationId,
      }
    });

    // Add to message queue
    if (!this.messageQueue.has(toAgentId)) {
      this.messageQueue.set(toAgentId, []);
    }
    this.messageQueue.get(toAgentId)!.push(message);

    // Emit real-time event via APIX
    await this.apixGateway.emitAgentEvent(
      fromAgent.organizationId,
      toAgentId,
      'agent_message_received',
      {
        messageId: message.id,
        fromAgent: {
          id: fromAgent.id,
          name: fromAgent.name,
        },
        type: message.type,
        content: message.content,
        metadata: message.metadata,
        timestamp: message.timestamp,
      },
      { priority: message.metadata?.priority === 'urgent' ? 'high' : 'normal' }
    );

    // Also emit to sender for confirmation
    await this.apixGateway.emitAgentEvent(
      fromAgent.organizationId,
      fromAgentId,
      'agent_message_sent',
      {
        messageId: message.id,
        toAgent: {
          id: toAgent.id,
          name: toAgent.name,
        },
        type: message.type,
        status: 'delivered',
        timestamp: message.timestamp,
      }
    );

    // Update message status
    message.status = 'delivered';
    await this.prisma.agentMessage.update({
      where: { id: message.id },
      data: { status: 'delivered' }
    });

    // Set up timeout for response if required
    if (message.metadata?.requiresResponse && message.metadata?.timeout) {
      setTimeout(async () => {
        const currentMessage = await this.prisma.agentMessage.findUnique({
          where: { id: message.id }
        });
        
        if (currentMessage && currentMessage.status === 'delivered') {
          await this.handleMessageTimeout(message);
        }
      }, message.metadata.timeout);
    }

    this.logger.log(`Message sent from ${fromAgent.name} to ${toAgent.name}: ${message.id}`);

    return message;
  }

  async respondToMessage(
    messageId: string,
    respondingAgentId: string,
    response: any
  ): Promise<AgentMessage> {
    // Get original message
    const originalMessage = await this.prisma.agentMessage.findUnique({
      where: { id: messageId },
      include: {
        fromAgent: { select: { id: true, name: true, organizationId: true } },
        toAgent: { select: { id: true, name: true } }
      }
    });

    if (!originalMessage) {
      throw new Error('Original message not found');
    }

    if (originalMessage.toAgentId !== respondingAgentId) {
      throw new Error('Only the recipient can respond to this message');
    }

    // Create response message
    const responseMessage = await this.sendMessage(
      respondingAgentId,
      originalMessage.fromAgentId,
      response,
      {
        type: 'response',
        correlationId: originalMessage.id,
        sessionId: originalMessage.metadata?.sessionId,
        workflowId: originalMessage.metadata?.workflowId,
      }
    );

    // Mark original message as acknowledged
    await this.prisma.agentMessage.update({
      where: { id: messageId },
      data: { status: 'acknowledged' }
    });

    // Emit acknowledgment event
    await this.apixGateway.emitAgentEvent(
      originalMessage.fromAgent.organizationId,
      originalMessage.fromAgentId,
      'agent_message_acknowledged',
      {
        originalMessageId: messageId,
        responseMessageId: responseMessage.id,
        respondingAgent: {
          id: respondingAgentId,
          name: originalMessage.toAgent.name,
        },
        timestamp: new Date(),
      }
    );

    return responseMessage;
  }

  async broadcastMessage(
    fromAgentId: string,
    organizationId: string,
    content: any,
    options: {
      targetAgentIds?: string[];
      excludeAgentIds?: string[];
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      sessionId?: string;
      workflowId?: string;
    } = {}
  ): Promise<AgentMessage[]> {
    // Get sender agent
    const fromAgent = await this.prisma.agentInstance.findUnique({
      where: { id: fromAgentId },
      select: { id: true, name: true, organizationId: true }
    });

    if (!fromAgent || fromAgent.organizationId !== organizationId) {
      throw new Error('Invalid sender agent or organization mismatch');
    }

    // Get target agents
    let targetAgents;
    if (options.targetAgentIds) {
      targetAgents = await this.prisma.agentInstance.findMany({
        where: {
          id: { in: options.targetAgentIds },
          organizationId,
          status: AgentStatus.ACTIVE,
        },
        select: { id: true, name: true }
      });
    } else {
      targetAgents = await this.prisma.agentInstance.findMany({
        where: {
          organizationId,
          status: AgentStatus.ACTIVE,
          id: { 
            notIn: [fromAgentId, ...(options.excludeAgentIds || [])]
          }
        },
        select: { id: true, name: true }
      });
    }

    // Send message to each target agent
    const messages = await Promise.all(
      targetAgents.map(agent =>
        this.sendMessage(fromAgentId, agent.id, content, {
          type: 'broadcast',
          priority: options.priority,
          sessionId: options.sessionId,
          workflowId: options.workflowId,
        })
      )
    );

    this.logger.log(`Broadcast message sent from ${fromAgent.name} to ${targetAgents.length} agents`);

    return messages;
  }

  async createCommunicationChannel(
    name: string,
    participantIds: string[],
    organizationId: string,
    type: 'direct' | 'group' | 'broadcast' = 'group',
    metadata?: any
  ): Promise<AgentCommunicationChannel> {
    // Validate participants
    const participants = await this.prisma.agentInstance.findMany({
      where: {
        id: { in: participantIds },
        organizationId,
        status: AgentStatus.ACTIVE,
      },
      select: { id: true, name: true }
    });

    if (participants.length !== participantIds.length) {
      throw new Error('Some participant agents not found or inactive');
    }

    // Create channel in database
    const channel = await this.prisma.agentCommunicationChannel.create({
      data: {
        name,
        type,
        organizationId,
        isActive: true,
        metadata: metadata || {},
        participants: {
          create: participantIds.map(agentId => ({ agentId }))
        }
      },
      include: {
        participants: {
          include: {
            agent: { select: { id: true, name: true } }
          }
        }
      }
    });

    // Create channel object
    const channelObj: AgentCommunicationChannel = {
      id: channel.id,
      name: channel.name,
      participants: channel.participants.map(p => p.agentId),
      type: channel.type as 'direct' | 'group' | 'broadcast',
      organizationId: channel.organizationId,
      isActive: channel.isActive,
      metadata: channel.metadata as any,
    };

    // Store in memory
    this.activeChannels.set(channel.id, channelObj);

    // Notify participants
    for (const participant of participants) {
      await this.apixGateway.emitAgentEvent(
        organizationId,
        participant.id,
        'agent_channel_created',
        {
          channelId: channel.id,
          channelName: name,
          type,
          participants: participants.map(p => ({ id: p.id, name: p.name })),
          timestamp: new Date(),
        }
      );
    }

    this.logger.log(`Communication channel created: ${name} with ${participants.length} participants`);

    return channelObj;
  }

  async sendChannelMessage(
    channelId: string,
    fromAgentId: string,
    content: any,
    options: {
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      sessionId?: string;
      workflowId?: string;
    } = {}
  ): Promise<AgentMessage[]> {
    const channel = this.activeChannels.get(channelId);
    if (!channel) {
      throw new Error('Communication channel not found');
    }

    if (!channel.participants.includes(fromAgentId)) {
      throw new Error('Agent is not a participant in this channel');
    }

    // Get sender info
    const fromAgent = await this.prisma.agentInstance.findUnique({
      where: { id: fromAgentId },
      select: { id: true, name: true, organizationId: true }
    });

    if (!fromAgent) {
      throw new Error('Sender agent not found');
    }

    // Send to all other participants
    const targetAgentIds = channel.participants.filter(id => id !== fromAgentId);
    const messages = await Promise.all(
      targetAgentIds.map(agentId =>
        this.sendMessage(fromAgentId, agentId, content, {
          type: 'notification',
          priority: options.priority,
          sessionId: options.sessionId,
          workflowId: options.workflowId,
        })
      )
    );

    // Emit channel message event
    await this.apixGateway.emitToRoom(
      `channel:${channelId}`,
      'channel_message',
      {
        channelId,
        channelName: channel.name,
        fromAgent: {
          id: fromAgent.id,
          name: fromAgent.name,
        },
        content,
        timestamp: new Date(),
        messageIds: messages.map(m => m.id),
      },
      { priority: options.priority === 'urgent' ? 'high' : 'normal' }
    );

    return messages;
  }

  async getAgentMessages(
    agentId: string,
    options: {
      limit?: number;
      offset?: number;
      status?: 'pending' | 'delivered' | 'acknowledged' | 'failed';
      type?: 'request' | 'response' | 'broadcast' | 'notification';
      fromDate?: Date;
      toDate?: Date;
    } = {}
  ): Promise<{ messages: AgentMessage[]; total: number }> {
    const where: any = {
      OR: [
        { fromAgentId: agentId },
        { toAgentId: agentId }
      ]
    };

    if (options.status) {
      where.status = options.status;
    }

    if (options.type) {
      where.type = options.type;
    }

    if (options.fromDate || options.toDate) {
      where.createdAt = {};
      if (options.fromDate) {
        where.createdAt.gte = options.fromDate;
      }
      if (options.toDate) {
        where.createdAt.lte = options.toDate;
      }
    }

    const [messages, total] = await Promise.all([
      this.prisma.agentMessage.findMany({
        where,
        include: {
          fromAgent: { select: { id: true, name: true } },
          toAgent: { select: { id: true, name: true } }
        },
        orderBy: { createdAt: 'desc' },
        take: options.limit || 50,
        skip: options.offset || 0,
      }),
      this.prisma.agentMessage.count({ where })
    ]);

    return {
      messages: messages.map(msg => ({
        id: msg.id,
        fromAgentId: msg.fromAgentId,
        toAgentId: msg.toAgentId,
        type: msg.type as any,
        content: msg.content,
        metadata: msg.metadata as any,
        timestamp: msg.createdAt,
        status: msg.status as any,
      })),
      total
    };
  }

  async subscribeToAgent(agentId: string, subscriberAgentId: string): Promise<void> {
    if (!this.agentSubscriptions.has(agentId)) {
      this.agentSubscriptions.set(agentId, new Set());
    }
    
    this.agentSubscriptions.get(agentId)!.add(subscriberAgentId);

    // Emit subscription event
    const [agent, subscriber] = await Promise.all([
      this.prisma.agentInstance.findUnique({
        where: { id: agentId },
        select: { id: true, name: true, organizationId: true }
      }),
      this.prisma.agentInstance.findUnique({
        where: { id: subscriberAgentId },
        select: { id: true, name: true }
      })
    ]);

    if (agent && subscriber) {
      await this.apixGateway.emitAgentEvent(
        agent.organizationId,
        agentId,
        'agent_subscriber_added',
        {
          subscriber: {
            id: subscriber.id,
            name: subscriber.name,
          },
          timestamp: new Date(),
        }
      );
    }
  }

  async unsubscribeFromAgent(agentId: string, subscriberAgentId: string): Promise<void> {
    const subscribers = this.agentSubscriptions.get(agentId);
    if (subscribers) {
      subscribers.delete(subscriberAgentId);
      if (subscribers.size === 0) {
        this.agentSubscriptions.delete(agentId);
      }
    }
  }

  private async handleAgentStatusChange(payload: any) {
    const { agentId, status, organizationId } = payload;

    if (status === AgentStatus.INACTIVE || status === AgentStatus.ARCHIVED) {
      // Notify subscribers about agent going offline
      const subscribers = this.agentSubscriptions.get(agentId);
      if (subscribers) {
        for (const subscriberId of subscribers) {
          await this.apixGateway.emitAgentEvent(
            organizationId,
            subscriberId,
            'agent_status_changed',
            {
              agentId,
              status,
              timestamp: new Date(),
            }
          );
        }
      }

      // Clear message queue for inactive agent
      this.messageQueue.delete(agentId);
    }
  }

  private async handleWorkflowCommunication(payload: any) {
    const { fromAgentId, toAgentId, workflowId, sessionId, content, type } = payload;

    await this.sendMessage(fromAgentId, toAgentId, content, {
      type: type || 'notification',
      workflowId,
      sessionId,
      priority: 'normal',
    });
  }

  private async handleMessageTimeout(message: AgentMessage) {
    // Mark message as failed
    await this.prisma.agentMessage.update({
      where: { id: message.id },
      data: { status: 'failed' }
    });

    // Notify sender about timeout
    const fromAgent = await this.prisma.agentInstance.findUnique({
      where: { id: message.fromAgentId },
      select: { organizationId: true }
    });

    if (fromAgent) {
      await this.apixGateway.emitAgentEvent(
        fromAgent.organizationId,
        message.fromAgentId,
        'agent_message_timeout',
        {
          messageId: message.id,
          toAgentId: message.toAgentId,
          originalContent: message.content,
          timeout: message.metadata?.timeout,
          timestamp: new Date(),
        },
        { priority: 'high' }
      );
    }
  }

  // Cleanup method
  async cleanup() {
    this.messageQueue.clear();
    this.activeChannels.clear();
    this.agentSubscriptions.clear();
  }
}