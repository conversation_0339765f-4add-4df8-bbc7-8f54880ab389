import { Module } from '@nestjs/common';
import { SecurityService } from './security.service';
import { SecurityController } from './security.controller';
import { EncryptionService } from './encryption.service';
import { CorsService } from './cors.service';
import { SecurityAuditService } from './security-audit.service';

@Module({
    controllers: [SecurityController],
    providers: [
        SecurityService,
        EncryptionService,
        CorsService,
        SecurityAuditService,
    ],
    exports: [SecurityService, EncryptionService, CorsService],
})
export class SecurityModule { }