"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessageQueueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageQueueService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const apix_dto_1 = require("../dto/apix.dto");
const event_router_service_1 = require("./event-router.service");
const connection_manager_service_1 = require("./connection-manager.service");
let MessageQueueService = MessageQueueService_1 = class MessageQueueService {
    constructor(prisma, cacheManager, eventRouter, connectionManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.eventRouter = eventRouter;
        this.connectionManager = connectionManager;
        this.logger = new common_1.Logger(MessageQueueService_1.name);
        this.processingQueue = new Map();
        this.retryTimer = null;
        this.isProcessing = false;
        this.defaultRetryConfig = {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 300000,
            backoffMultiplier: 2,
            jitter: true,
        };
        this.startRetryProcessor();
    }
    async queueMessage(connectionId, eventId, priority = apix_dto_1.EventPriority.NORMAL, retryConfig) {
        const config = { ...this.defaultRetryConfig, ...retryConfig };
        const queuedMessage = await this.prisma.apiXMessageQueue.create({
            data: {
                connectionId,
                eventId,
                priority,
                maxAttempts: config.maxAttempts,
                status: "PENDING",
            },
        });
        await this.cacheManager.set(`queue:${queuedMessage.id}`, queuedMessage, 3600);
        this.logger.debug(`Message queued: ${queuedMessage.id} for connection ${connectionId}`);
        if (priority === apix_dto_1.EventPriority.HIGH ||
            priority === apix_dto_1.EventPriority.CRITICAL) {
            setImmediate(() => this.processQueue());
        }
        return queuedMessage.id;
    }
    async processQueue() {
        if (this.isProcessing)
            return;
        this.isProcessing = true;
        try {
            const pendingMessages = await this.prisma.apiXMessageQueue.findMany({
                where: {
                    status: "PENDING",
                    OR: [{ nextRetryAt: null }, { nextRetryAt: { lte: new Date() } }],
                },
                orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
                take: 100,
            });
            for (const message of pendingMessages) {
                await this.processMessage(message);
            }
            this.logger.debug(`Processed ${pendingMessages.length} queued messages`);
        }
        catch (error) {
            this.logger.error("Error processing message queue:", error);
        }
        finally {
            this.isProcessing = false;
        }
    }
    async processMessage(queuedMessage) {
        try {
            await this.prisma.apiXMessageQueue.update({
                where: { id: queuedMessage.id },
                data: {
                    status: "PROCESSING",
                    attempts: { increment: 1 },
                },
            });
            const event = await this.prisma.apiXEvent.findUnique({
                where: { id: queuedMessage.eventId },
            });
            if (!event) {
                await this.markMessageFailed(queuedMessage.id, "Event not found");
                return;
            }
            const connection = this.connectionManager.getConnection(queuedMessage.connectionId);
            if (!connection) {
                await this.markMessageFailed(queuedMessage.id, "Connection not found");
                return;
            }
            await this.eventRouter.routeEvent({
                id: event.id,
                type: event.eventType,
                channel: event.channel,
                payload: event.payload,
                sessionId: event.sessionId || undefined,
                userId: event.userId || undefined,
                organizationId: event.organizationId || undefined,
                priority: event.priority,
                compressed: event.compressed,
                correlationId: event.correlationId || undefined,
                metadata: event.metadata,
                timestamp: event.createdAt.getTime(),
            });
            await this.prisma.apiXMessageQueue.update({
                where: { id: queuedMessage.id },
                data: {
                    status: "COMPLETED",
                    processedAt: new Date(),
                },
            });
            await this.cacheManager.del(`queue:${queuedMessage.id}`);
            this.logger.debug(`Message processed successfully: ${queuedMessage.id}`);
        }
        catch (error) {
            await this.handleMessageError(queuedMessage, error);
        }
    }
    async retryMessage(messageId) {
        const message = await this.prisma.apiXMessageQueue.findUnique({
            where: { id: messageId },
        });
        if (!message || message.status !== "FAILED") {
            return false;
        }
        if (message.attempts >= message.maxAttempts) {
            this.logger.warn(`Message ${messageId} has exceeded max retry attempts`);
            return false;
        }
        const nextRetryAt = this.calculateNextRetryTime(message.attempts);
        await this.prisma.apiXMessageQueue.update({
            where: { id: messageId },
            data: {
                status: "PENDING",
                nextRetryAt,
                error: null,
            },
        });
        this.logger.log(`Message ${messageId} scheduled for retry at ${nextRetryAt}`);
        return true;
    }
    async cancelMessage(messageId) {
        try {
            await this.prisma.apiXMessageQueue.update({
                where: { id: messageId },
                data: {
                    status: "CANCELLED",
                    processedAt: new Date(),
                },
            });
            await this.cacheManager.del(`queue:${messageId}`);
            this.logger.log(`Message cancelled: ${messageId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to cancel message ${messageId}:`, error);
            return false;
        }
    }
    async getQueueStats() {
        const [statusCounts, priorityCounts, processingStats] = await Promise.all([
            this.prisma.apiXMessageQueue.groupBy({
                by: ["status"],
                _count: { status: true },
            }),
            this.prisma.apiXMessageQueue.groupBy({
                by: ["priority"],
                _count: { priority: true },
            }),
            this.prisma.apiXMessageQueue.aggregate({
                where: {
                    status: "COMPLETED",
                    processedAt: { not: null },
                },
                _avg: {
                    attempts: true,
                },
            }),
        ]);
        const oldestPending = await this.prisma.apiXMessageQueue.findFirst({
            where: { status: "PENDING" },
            orderBy: { createdAt: "asc" },
            select: { createdAt: true },
        });
        const oneHourAgo = new Date(Date.now() - 3600000);
        const recentlyProcessed = await this.prisma.apiXMessageQueue.count({
            where: {
                status: "COMPLETED",
                processedAt: { gte: oneHourAgo },
            },
        });
        const stats = {
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            cancelled: 0,
            totalByPriority: {},
            averageProcessingTime: processingStats._avg.attempts || 0,
            successRate: 0,
            throughputPerHour: recentlyProcessed,
            oldestPendingMessage: oldestPending?.createdAt || null,
        };
        Object.values(apix_dto_1.EventPriority).forEach((priority) => {
            stats.totalByPriority[priority] = 0;
        });
        for (const count of statusCounts) {
            const status = count.status.toLowerCase();
            if (status in stats) {
                stats[status] = count._count.status;
            }
        }
        for (const count of priorityCounts) {
            stats.totalByPriority[count.priority] =
                count._count.priority;
        }
        const totalProcessed = stats.completed + stats.failed;
        stats.successRate =
            totalProcessed > 0 ? (stats.completed / totalProcessed) * 100 : 0;
        return stats;
    }
    async getConnectionQueueStats(connectionId) {
        const [statusCounts, avgAttempts] = await Promise.all([
            this.prisma.apiXMessageQueue.groupBy({
                by: ["status"],
                where: { connectionId },
                _count: { status: true },
            }),
            this.prisma.apiXMessageQueue.aggregate({
                where: { connectionId },
                _avg: { attempts: true },
            }),
        ]);
        const stats = {
            pending: 0,
            processing: 0,
            failed: 0,
            avgRetryAttempts: avgAttempts._avg.attempts || 0,
        };
        for (const count of statusCounts) {
            const status = count.status.toLowerCase();
            if (status in stats) {
                stats[status] = count._count.status;
            }
        }
        return stats;
    }
    async cleanupOldMessages(olderThanDays = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        const result = await this.prisma.apiXMessageQueue.deleteMany({
            where: {
                createdAt: { lt: cutoffDate },
                status: { in: ["COMPLETED", "CANCELLED"] },
            },
        });
        this.logger.log(`Cleaned up ${result.count} old queue messages`);
        return result.count;
    }
    async purgeFailedMessages(connectionId) {
        const where = { status: "FAILED" };
        if (connectionId) {
            where.connectionId = connectionId;
        }
        const result = await this.prisma.apiXMessageQueue.deleteMany({ where });
        this.logger.log(`Purged ${result.count} failed messages`);
        return result.count;
    }
    async handleMessageError(queuedMessage, error) {
        const errorMessage = error.message || error.toString();
        if (queuedMessage.attempts >= queuedMessage.maxAttempts) {
            await this.markMessageFailed(queuedMessage.id, errorMessage);
        }
        else {
            const nextRetryAt = this.calculateNextRetryTime(queuedMessage.attempts);
            await this.prisma.apiXMessageQueue.update({
                where: { id: queuedMessage.id },
                data: {
                    status: "PENDING",
                    nextRetryAt,
                    error: errorMessage,
                },
            });
            this.logger.warn(`Message ${queuedMessage.id} failed (attempt ${queuedMessage.attempts}/${queuedMessage.maxAttempts}), retrying at ${nextRetryAt}: ${errorMessage}`);
        }
    }
    async markMessageFailed(messageId, error) {
        await this.prisma.apiXMessageQueue.update({
            where: { id: messageId },
            data: {
                status: "FAILED",
                error,
                processedAt: new Date(),
            },
        });
        await this.cacheManager.del(`queue:${messageId}`);
        this.logger.error(`Message failed permanently: ${messageId} - ${error}`);
    }
    calculateNextRetryTime(attempts) {
        const config = this.defaultRetryConfig;
        let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempts - 1);
        delay = Math.min(delay, config.maxDelay);
        if (config.jitter) {
            delay = delay * (0.5 + Math.random() * 0.5);
        }
        return new Date(Date.now() + delay);
    }
    startRetryProcessor() {
        this.retryTimer = setInterval(() => {
            this.processQueue().catch((error) => {
                this.logger.error("Error in retry processor:", error);
            });
        }, 30000);
        setInterval(() => {
            this.cleanupOldMessages().catch((error) => {
                this.logger.error("Error cleaning up old messages:", error);
            });
        }, 24 * 60 * 60 * 1000);
    }
    onModuleDestroy() {
        if (this.retryTimer) {
            clearInterval(this.retryTimer);
        }
    }
};
exports.MessageQueueService = MessageQueueService;
exports.MessageQueueService = MessageQueueService = MessageQueueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, event_router_service_1.EventRouterService,
        connection_manager_service_1.ConnectionManagerService])
], MessageQueueService);
//# sourceMappingURL=message-queue.service.js.map