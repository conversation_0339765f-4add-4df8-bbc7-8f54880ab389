import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class TenantGuard implements CanActivate {
  constructor(private prisma: PrismaService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Extract tenant/organization context from request
    const organizationId = request.params.organizationId || 
                          request.body.organizationId || 
                          request.query.organizationId ||
                          user.organizationId;

    if (!organizationId) {
      // If no specific organization is requested, use user's organization
      request.organizationId = user.organizationId;
      return true;
    }

    // Check if user belongs to the requested organization
    if (user.organizationId !== organizationId) {
      // Super admins can access any organization
      if (user.role === 'SUPER_ADMIN') {
        request.organizationId = organizationId;
        return true;
      }

      throw new ForbiddenException('Access denied to this organization');
    }

    // Verify organization exists and is active
    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new ForbiddenException('Organization not found');
    }

    // Add organization context to request
    request.organizationId = organizationId;
    request.organization = organization;

    return true;
  }
}