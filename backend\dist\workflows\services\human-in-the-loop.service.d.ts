import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cache } from 'cache-manager';
export interface HumanInputRequest {
    id: string;
    executionId: string;
    nodeId: string;
    sessionId: string;
    userId: string;
    organizationId: string;
    prompt: string;
    inputType: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'file' | 'json';
    validationRules?: {
        required?: boolean;
        minLength?: number;
        maxLength?: number;
        pattern?: string;
        options?: string[];
        fileTypes?: string[];
        maxFileSize?: number;
    };
    timeout: number;
    allowSkip: boolean;
    skipValue?: any;
    status: 'pending' | 'completed' | 'expired' | 'cancelled' | 'skipped';
    response?: any;
    metadata?: Record<string, any>;
    createdAt: Date;
    expiresAt: Date;
    completedAt?: Date;
}
export interface HumanInputResponse {
    requestId: string;
    userInput: any;
    inputType: string;
    skipped: boolean;
    validationPassed: boolean;
    validationErrors?: string[];
    metadata?: Record<string, any>;
    completedAt: Date;
}
export declare class HumanInTheLoopService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private eventEmitter;
    private cacheManager;
    private readonly logger;
    private activeRequests;
    private requestTimeouts;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService, eventEmitter: EventEmitter2, cacheManager: Cache);
    requestHumanInput(executionId: string, nodeId: string, sessionId: string, prompt: string, inputType?: string, options?: {
        timeout?: number;
        allowSkip?: boolean;
        skipValue?: any;
        validationRules?: any;
        metadata?: Record<string, any>;
    }): Promise<string>;
    provideHumanInput(requestId: string, userInput: any, userId: string, metadata?: Record<string, any>): Promise<HumanInputResponse>;
    skipHumanInput(requestId: string, userId: string, reason?: string): Promise<HumanInputResponse>;
    cancelHumanInput(requestId: string, userId: string, reason?: string): Promise<void>;
    getRequest(requestId: string): Promise<HumanInputRequest | null>;
    getUserPendingRequests(userId: string): Promise<HumanInputRequest[]>;
    getExecutionRequests(executionId: string): Promise<HumanInputRequest[]>;
    private handleTimeout;
    private validateInput;
    private processInput;
    private cleanupExpiredRequests;
    getHumanInputAnalytics(organizationId: string, timeRange?: {
        start: Date;
        end: Date;
    }): Promise<{
        totalRequests: any;
        completedRequests: any;
        skippedRequests: any;
        expiredRequests: any;
        cancelledRequests: any;
        completionRate: number;
        skipRate: number;
        timeoutRate: number;
        avgResponseTime: number;
        inputTypeDistribution: any;
    }>;
    getActiveRequestsCount(): number;
    getActiveRequestsForUser(userId: string): HumanInputRequest[];
}
