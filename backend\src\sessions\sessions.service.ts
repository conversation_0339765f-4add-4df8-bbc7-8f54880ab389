import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

export interface SessionData {
  loginMethod: string;
  userAgent?: string;
  ipAddress?: string;
  mfaVerified?: boolean;
  rememberMe?: boolean;
  deviceFingerprint?: string;
  location?: {
    country?: string;
    city?: string;
    timezone?: string;
  };
}

export interface SessionContext {
  currentWorkflow?: string;
  currentAgent?: string;
  activeTools?: string[];
  breadcrumbs?: Array<{
    path: string;
    timestamp: number;
  }>;
  preferences?: any;
}

export interface SessionMemory {
  conversationHistory?: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
  }>;
  workflowState?: any;
  agentMemory?: any;
  userInputs?: any;
  temporaryData?: any;
}

export interface HybridWorkflowContext {
  executionId: string;
  nodeId: string;
  agentContext: Record<string, any>;
  toolContexts: Record<string, any>;
  sharedVariables: Record<string, any>;
  executionHistory: Array<{
    timestamp: number;
    component: 'agent' | 'tool';
    componentId: string;
    action: string;
    data: any;
  }>;
  syncPoints: Record<string, any>;
  lastUpdated: Date;
}

export interface CrossComponentMemory {
  agentMemories: Record<string, any>;
  toolMemories: Record<string, any>;
  sharedContext: Record<string, any>;
  executionState: Record<string, any>;
  coordinationData: Record<string, any>;
}

@Injectable()
export class SessionsService {
  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async createSession(
    userId: string,
    organizationId: string,
    sessionData: SessionData,
    expirationHours = 24,
  ) {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expirationHours);

    const session = await this.prisma.session.create({
      data: {
        userId,
        organizationId,
        sessionData: sessionData as any,
        context: {},
        memory: {},
        expiresAt,
        isActive: true,
      },
    });

    // Cache session for quick access
    await this.cacheManager.set(
      `session:${session.id}`,
      session,
      expirationHours * 60 * 60 * 1000, // Convert to milliseconds
    );

    // Cache user's active session
    await this.cacheManager.set(
      `user_session:${userId}`,
      session.id,
      expirationHours * 60 * 60 * 1000,
    );

    return session;
  }

  async getSession(sessionId: string) {
    // Try cache first
    let session = await this.cacheManager.get(`session:${sessionId}`);
    
    if (!session) {
      // Fallback to database
      session = await this.prisma.session.findUnique({
        where: { id: sessionId },
        include: {
          user: {
            include: {
              organization: true,
            },
          },
        },
      });

      if (session) {
        // Cache for future requests
        await this.cacheManager.set(
          `session:${sessionId}`,
          session,
          3600000, // 1 hour
        );
      }
    }

    return session;
  }

  async getUserActiveSession(userId: string) {
    // Try cache first
    const cachedSessionId = await this.cacheManager.get(`user_session:${userId}`);
    
    if (cachedSessionId) {
      return this.getSession(cachedSessionId as string);
    }

    // Fallback to database
    const session = await this.prisma.session.findFirst({
      where: {
        userId,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        user: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (session) {
      // Cache for future requests
      await this.cacheManager.set(
        `user_session:${userId}`,
        session.id,
        3600000, // 1 hour
      );
    }

    return session;
  }

  async updateSessionContext(sessionId: string, context: Partial<SessionContext>) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const updatedContext = {
      ...session.context,
      ...context,
      lastUpdated: new Date(),
    };

    const updatedSession = await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        context: updatedContext as any,
        updatedAt: new Date(),
      },
    });

    // Update cache
    await this.cacheManager.set(
      `session:${sessionId}`,
      updatedSession,
      3600000, // 1 hour
    );

    return updatedSession;
  }

  async updateSessionMemory(sessionId: string, memory: Partial<SessionMemory>) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const updatedMemory = {
      ...session.memory,
      ...memory,
      lastUpdated: new Date(),
    };

    // Implement memory pruning to prevent unlimited growth
    const prunedMemory = this.pruneMemory(updatedMemory);

    const updatedSession = await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        memory: prunedMemory as any,
        updatedAt: new Date(),
      },
    });

    // Update cache
    await this.cacheManager.set(
      `session:${sessionId}`,
      updatedSession,
      3600000, // 1 hour
    );

    return updatedSession;
  }

  async addConversationMessage(
    sessionId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    metadata?: any,
  ) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const currentMemory = session.memory as SessionMemory;
    const conversationHistory = currentMemory.conversationHistory || [];

    const newMessage = {
      role,
      content,
      timestamp: Date.now(),
      metadata,
    };

    conversationHistory.push(newMessage);

    // Keep only last 100 messages to prevent memory bloat
    const trimmedHistory = conversationHistory.slice(-100);

    return this.updateSessionMemory(sessionId, {
      conversationHistory: trimmedHistory,
    });
  }

  async setWorkflowState(sessionId: string, workflowId: string, state: any) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const currentMemory = session.memory as SessionMemory;
    const workflowState = currentMemory.workflowState || {};

    workflowState[workflowId] = {
      ...state,
      lastUpdated: Date.now(),
    };

    return this.updateSessionMemory(sessionId, {
      workflowState,
    });
  }

  async getWorkflowState(sessionId: string, workflowId: string) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const currentMemory = session.memory as SessionMemory;
    return currentMemory.workflowState?.[workflowId] || null;
  }

  async setAgentMemory(sessionId: string, agentId: string, memory: any) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const currentMemory = session.memory as SessionMemory;
    const agentMemory = currentMemory.agentMemory || {};

    agentMemory[agentId] = {
      ...memory,
      lastUpdated: Date.now(),
    };

    return this.updateSessionMemory(sessionId, {
      agentMemory,
    });
  }

  async getAgentMemory(sessionId: string, agentId: string) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const currentMemory = session.memory as SessionMemory;
    return currentMemory.agentMemory?.[agentId] || null;
  }

  async invalidateSession(sessionId: string) {
    await this.prisma.session.update({
      where: { id: sessionId },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    // Remove from cache
    await this.cacheManager.del(`session:${sessionId}`);
  }

  async invalidateUserSessions(userId: string) {
    // Update all user sessions in database
    await this.prisma.session.updateMany({
      where: {
        userId,
        isActive: true,
      },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    // Remove from cache
    await this.cacheManager.del(`user_session:${userId}`);

    // Note: Individual session caches will expire naturally or be updated on next access
  }

  async getUserSessions(userId: string, includeInactive = false) {
    const whereClause: any = { userId };
    
    if (!includeInactive) {
      whereClause.isActive = true;
      whereClause.expiresAt = { gt: new Date() };
    }

    return this.prisma.session.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        sessionData: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        expiresAt: true,
      },
    });
  }

  async getOrganizationSessions(organizationId: string, limit = 100) {
    return this.prisma.session.findMany({
      where: {
        organizationId,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async cleanupExpiredSessions() {
    const expiredSessions = await this.prisma.session.findMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { 
            isActive: false,
            updatedAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 7 days old
          },
        ],
      },
      select: { id: true },
    });

    if (expiredSessions.length > 0) {
      const sessionIds = expiredSessions.map(s => s.id);
      
      // Delete from database
      await this.prisma.session.deleteMany({
        where: {
          id: { in: sessionIds },
        },
      });

      // Remove from cache
      for (const sessionId of sessionIds) {
        await this.cacheManager.del(`session:${sessionId}`);
      }

      return sessionIds.length;
    }

    return 0;
  }

  async getSessionAnalytics(organizationId: string, days = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const sessions = await this.prisma.session.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        createdAt: true,
        updatedAt: true,
        sessionData: true,
        isActive: true,
      },
    });

    const totalSessions = sessions.length;
    const activeSessions = sessions.filter(s => s.isActive).length;
    const avgSessionDuration = sessions.reduce((acc, session) => {
      const duration = session.updatedAt.getTime() - session.createdAt.getTime();
      return acc + duration;
    }, 0) / totalSessions;

    // Group by day
    const dailyStats = sessions.reduce((acc, session) => {
      const day = session.createdAt.toISOString().split('T')[0];
      if (!acc[day]) {
        acc[day] = { count: 0, active: 0 };
      }
      acc[day].count++;
      if (session.isActive) {
        acc[day].active++;
      }
      return acc;
    }, {} as Record<string, { count: number; active: number }>);

    return {
      totalSessions,
      activeSessions,
      avgSessionDuration: Math.round(avgSessionDuration / 1000 / 60), // in minutes
      dailyStats,
    };
  }

  private pruneMemory(memory: any): any {
    const prunedMemory = { ...memory };

    // Limit conversation history
    if (prunedMemory.conversationHistory && prunedMemory.conversationHistory.length > 100) {
      prunedMemory.conversationHistory = prunedMemory.conversationHistory.slice(-100);
    }

    // Limit workflow states (keep only last 10 workflows)
    if (prunedMemory.workflowState) {
      const workflowEntries = Object.entries(prunedMemory.workflowState);
      if (workflowEntries.length > 10) {
        const sortedEntries = workflowEntries.sort((a, b) => 
          (b[1] as any).lastUpdated - (a[1] as any).lastUpdated
        );
        prunedMemory.workflowState = Object.fromEntries(sortedEntries.slice(0, 10));
      }
    }

    // Limit agent memory (keep only last 5 agents)
    if (prunedMemory.agentMemory) {
      const agentEntries = Object.entries(prunedMemory.agentMemory);
      if (agentEntries.length > 5) {
        const sortedEntries = agentEntries.sort((a, b) => 
          (b[1] as any).lastUpdated - (a[1] as any).lastUpdated
        );
        prunedMemory.agentMemory = Object.fromEntries(sortedEntries.slice(0, 5));
      }
    }

    // Remove temporary data older than 1 hour
    if (prunedMemory.temporaryData) {
      const oneHourAgo = Date.now() - 3600000;
      const filteredTempData = Object.entries(prunedMemory.temporaryData).reduce((acc, [key, value]) => {
        if ((value as any).timestamp && (value as any).timestamp > oneHourAgo) {
          acc[key] = value;
        }
        return acc;
      }, {} as any);
      prunedMemory.temporaryData = filteredTempData;
    }

    return prunedMemory;
  }

  async initializeHybridContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
    initialContext: Record<string, any> = {},
  ): Promise<HybridWorkflowContext> {
    try {
      const hybridContext: HybridWorkflowContext = {
        executionId,
        nodeId,
        agentContext: {},
        toolContexts: {},
        sharedVariables: { ...initialContext },
        executionHistory: [],
        syncPoints: {},
        lastUpdated: new Date(),
      };

      // Store in session memory
      await this.updateSessionMemory(sessionId, {
        temporaryData: {
          [`hybrid_context:${executionId}:${nodeId}`]: hybridContext,
        },
      });

      // Cache for quick access
      await this.cacheManager.set(
        `hybrid_context:${sessionId}:${executionId}:${nodeId}`,
        hybridContext,
        1800000, // 30 minutes
      );

      this.logger.log(`Hybrid context initialized for execution: ${executionId}, node: ${nodeId}`);
      return hybridContext;

    } catch (error) {
      this.logger.error(`Failed to initialize hybrid context: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getHybridContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
  ): Promise<HybridWorkflowContext | null> {
    try {
      // Try cache first
      const cacheKey = `hybrid_context:${sessionId}:${executionId}:${nodeId}`;
      let context = await this.cacheManager.get(cacheKey) as HybridWorkflowContext;

      if (!context) {
        // Get from session memory
        const session = await this.getSession(sessionId);
        const contextKey = `hybrid_context:${executionId}:${nodeId}`;
        context = session?.memory?.temporaryData?.[contextKey];

        if (context) {
          // Update cache
          await this.cacheManager.set(cacheKey, context, 1800000);
        }
      }

      return context || null;

    } catch (error) {
      this.logger.error(`Failed to get hybrid context: ${error.message}`, error.stack);
      return null;
    }
  }

  async updateHybridContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
    updates: Partial<HybridWorkflowContext>,
  ): Promise<HybridWorkflowContext> {
    try {
      const currentContext = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!currentContext) {
        throw new Error(`Hybrid context not found for execution: ${executionId}, node: ${nodeId}`);
      }

      const updatedContext: HybridWorkflowContext = {
        ...currentContext,
        ...updates,
        lastUpdated: new Date(),
      };

      // Update session memory
      await this.updateSessionMemory(sessionId, {
        temporaryData: {
          [`hybrid_context:${executionId}:${nodeId}`]: updatedContext,
        },
      });

      // Update cache
      const cacheKey = `hybrid_context:${sessionId}:${executionId}:${nodeId}`;
      await this.cacheManager.set(cacheKey, updatedContext, 1800000);

      return updatedContext;

    } catch (error) {
      this.logger.error(`Failed to update hybrid context: ${error.message}`, error.stack);
      throw error;
    }
  }

  async addHybridExecutionHistory(
    sessionId: string,
    executionId: string,
    nodeId: string,
    historyEntry: {
      component: 'agent' | 'tool';
      componentId: string;
      action: string;
      data: any;
    },
  ): Promise<void> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        throw new Error(`Hybrid context not found for execution: ${executionId}, node: ${nodeId}`);
      }

      const newEntry = {
        ...historyEntry,
        timestamp: Date.now(),
      };

      context.executionHistory.push(newEntry);

      // Keep only last 100 entries
      if (context.executionHistory.length > 100) {
        context.executionHistory = context.executionHistory.slice(-100);
      }

      await this.updateHybridContext(sessionId, executionId, nodeId, { executionHistory: context.executionHistory });

    } catch (error) {
      this.logger.error(`Failed to add hybrid execution history: ${error.message}`, error.stack);
      throw error;
    }
  }

  async shareContextBetweenComponents(
    sessionId: string,
    executionId: string,
    nodeId: string,
    fromComponent: string,
    toComponent: string,
    contextData: Record<string, any>,
    strategy: 'full' | 'summary' | 'selective' = 'full',
  ): Promise<void> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        throw new Error(`Hybrid context not found for execution: ${executionId}, node: ${nodeId}`);
      }

      let sharedData: Record<string, any>;

      switch (strategy) {
        case 'full':
          sharedData = contextData;
          break;
        
        case 'summary':
          sharedData = this.createContextSummary(contextData);
          break;
        
        case 'selective':
          sharedData = this.selectiveContextShare(contextData);
          break;
        
        default:
          sharedData = contextData;
      }

      // Update shared variables
      context.sharedVariables = {
        ...context.sharedVariables,
        [`${fromComponent}_to_${toComponent}`]: sharedData,
        [`last_shared_from_${fromComponent}`]: sharedData,
      };

      // Add to execution history
      await this.addHybridExecutionHistory(sessionId, executionId, nodeId, {
        component: fromComponent.startsWith('agent') ? 'agent' : 'tool',
        componentId: fromComponent,
        action: 'context_shared',
        data: {
          toComponent,
          strategy,
          dataSize: JSON.stringify(sharedData).length,
        },
      });

      await this.updateHybridContext(sessionId, executionId, nodeId, context);

    } catch (error) {
      this.logger.error(`Failed to share context between components: ${error.message}`, error.stack);
      throw error;
    }
  }

  async setSyncPoint(
    sessionId: string,
    executionId: string,
    nodeId: string,
    syncPointName: string,
    data: any,
  ): Promise<void> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        throw new Error(`Hybrid context not found for execution: ${executionId}, node: ${nodeId}`);
      }

      context.syncPoints[syncPointName] = {
        data,
        timestamp: Date.now(),
        reached: true,
      };

      await this.updateHybridContext(sessionId, executionId, nodeId, { syncPoints: context.syncPoints });

    } catch (error) {
      this.logger.error(`Failed to set sync point: ${error.message}`, error.stack);
      throw error;
    }
  }

  async waitForSyncPoint(
    sessionId: string,
    executionId: string,
    nodeId: string,
    syncPointName: string,
    timeoutMs: number = 30000,
  ): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (context?.syncPoints[syncPointName]?.reached) {
        return context.syncPoints[syncPointName].data;
      }

      // Wait 100ms before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    throw new Error(`Sync point timeout: ${syncPointName}`);
  }

  async getComponentContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
    componentType: 'agent' | 'tool',
    componentId: string,
  ): Promise<Record<string, any>> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        return {};
      }

      if (componentType === 'agent') {
        return context.agentContext[componentId] || {};
      } else {
        return context.toolContexts[componentId] || {};
      }

    } catch (error) {
      this.logger.error(`Failed to get component context: ${error.message}`, error.stack);
      return {};
    }
  }

  async updateComponentContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
    componentType: 'agent' | 'tool',
    componentId: string,
    contextUpdates: Record<string, any>,
  ): Promise<void> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        throw new Error(`Hybrid context not found for execution: ${executionId}, node: ${nodeId}`);
      }

      if (componentType === 'agent') {
        context.agentContext[componentId] = {
          ...context.agentContext[componentId],
          ...contextUpdates,
          lastUpdated: Date.now(),
        };
      } else {
        context.toolContexts[componentId] = {
          ...context.toolContexts[componentId],
          ...contextUpdates,
          lastUpdated: Date.now(),
        };
      }

      await this.updateHybridContext(sessionId, executionId, nodeId, context);

    } catch (error) {
      this.logger.error(`Failed to update component context: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCrossComponentMemory(sessionId: string): Promise<CrossComponentMemory> {
    try {
      const session = await this.getSession(sessionId);
      
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      const memory = session.memory as any;
      
      return {
        agentMemories: memory.agentMemory || {},
        toolMemories: memory.toolMemories || {},
        sharedContext: memory.sharedContext || {},
        executionState: memory.executionState || {},
        coordinationData: memory.coordinationData || {},
      };

    } catch (error) {
      this.logger.error(`Failed to get cross-component memory: ${error.message}`, error.stack);
      return {
        agentMemories: {},
        toolMemories: {},
        sharedContext: {},
        executionState: {},
        coordinationData: {},
      };
    }
  }

  async updateCrossComponentMemory(
    sessionId: string,
    updates: Partial<CrossComponentMemory>,
  ): Promise<void> {
    try {
      const currentMemory = await this.getCrossComponentMemory(sessionId);
      
      const updatedMemory = {
        agentMemories: { ...currentMemory.agentMemories, ...updates.agentMemories },
        toolMemories: { ...currentMemory.toolMemories, ...updates.toolMemories },
        sharedContext: { ...currentMemory.sharedContext, ...updates.sharedContext },
        executionState: { ...currentMemory.executionState, ...updates.executionState },
        coordinationData: { ...currentMemory.coordinationData, ...updates.coordinationData },
      };

      await this.updateSessionMemory(sessionId, updatedMemory);

    } catch (error) {
      this.logger.error(`Failed to update cross-component memory: ${error.message}`, error.stack);
      throw error;
    }
  }

  async cleanupHybridContext(
    sessionId: string,
    executionId: string,
    nodeId: string,
  ): Promise<void> {
    try {
      // Remove from cache
      const cacheKey = `hybrid_context:${sessionId}:${executionId}:${nodeId}`;
      await this.cacheManager.del(cacheKey);

      // Remove from session memory
      const session = await this.getSession(sessionId);
      if (session?.memory?.temporaryData) {
        const contextKey = `hybrid_context:${executionId}:${nodeId}`;
        const tempData = { ...session.memory.temporaryData };
        delete tempData[contextKey];

        await this.updateSessionMemory(sessionId, {
          temporaryData: tempData,
        });
      }

      this.logger.log(`Hybrid context cleaned up for execution: ${executionId}, node: ${nodeId}`);

    } catch (error) {
      this.logger.error(`Failed to cleanup hybrid context: ${error.message}`, error.stack);
    }
  }

  async getHybridExecutionAnalytics(
    sessionId: string,
    executionId: string,
    nodeId: string,
  ): Promise<{
    totalExecutionTime: number;
    componentBreakdown: Record<string, number>;
    contextSharingEvents: number;
    syncPointsReached: number;
    executionHistory: any[];
  }> {
    try {
      const context = await this.getHybridContext(sessionId, executionId, nodeId);
      
      if (!context) {
        return {
          totalExecutionTime: 0,
          componentBreakdown: {},
          contextSharingEvents: 0,
          syncPointsReached: 0,
          executionHistory: [],
        };
      }

      const history = context.executionHistory;
      const componentBreakdown: Record<string, number> = {};
      let contextSharingEvents = 0;

      // Analyze execution history
      history.forEach(entry => {
        const key = `${entry.component}:${entry.componentId}`;
        componentBreakdown[key] = (componentBreakdown[key] || 0) + 1;
        
        if (entry.action === 'context_shared') {
          contextSharingEvents++;
        }
      });

      const totalExecutionTime = history.length > 0 
        ? history[history.length - 1].timestamp - history[0].timestamp 
        : 0;

      return {
        totalExecutionTime,
        componentBreakdown,
        contextSharingEvents,
        syncPointsReached: Object.keys(context.syncPoints).length,
        executionHistory: history,
      };

    } catch (error) {
      this.logger.error(`Failed to get hybrid execution analytics: ${error.message}`, error.stack);
      return {
        totalExecutionTime: 0,
        componentBreakdown: {},
        contextSharingEvents: 0,
        syncPointsReached: 0,
        executionHistory: [],
      };
    }
  }

  private createContextSummary(contextData: Record<string, any>): Record<string, any> {
    // Create a summary of the context data
    const summary: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(contextData)) {
      if (typeof value === 'string') {
        summary[key] = value.length > 100 ? value.substring(0, 100) + '...' : value;
      } else if (Array.isArray(value)) {
        summary[key] = { type: 'array', length: value.length, sample: value.slice(0, 3) };
      } else if (typeof value === 'object' && value !== null) {
        summary[key] = { type: 'object', keys: Object.keys(value).slice(0, 5) };
      } else {
        summary[key] = value;
      }
    }
    
    return summary;
  }

  private selectiveContextShare(contextData: Record<string, any>): Record<string, any> {
    // Select only important context data based on predefined rules
    const importantKeys = ['result', 'output', 'data', 'response', 'status', 'error'];
    const selective: Record<string, any> = {};
    
    for (const key of importantKeys) {
      if (contextData[key] !== undefined) {
        selective[key] = contextData[key];
      }
    }
    
    // Include any keys that contain 'result' or 'output'
    for (const [key, value] of Object.entries(contextData)) {
      if (key.toLowerCase().includes('result') || key.toLowerCase().includes('output')) {
        selective[key] = value;
      }
    }
    
    return selective;
  }
}