import { PrismaService } from '../../prisma/prisma.service';
export interface NodeDefinition {
    type: string;
    category: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    inputs: Array<{
        name: string;
        type: string;
        description: string;
        required: boolean;
        default?: any;
    }>;
    outputs: Array<{
        name: string;
        type: string;
        description: string;
    }>;
    config: {
        schema: any;
        uiSchema?: any;
    };
}
export declare class NodeRegistryService {
    private prisma;
    private nodeTypes;
    constructor(prisma: PrismaService);
    initialize(): Promise<void>;
    registerNode(nodeDefinition: NodeDefinition): void;
    getNodeDefinition(type: string): NodeDefinition | undefined;
    getAllNodeDefinitions(): NodeDefinition[];
    getNodesByCategory(category: string): NodeDefinition[];
    private registerCoreNodes;
    private loadCustomNodes;
}
