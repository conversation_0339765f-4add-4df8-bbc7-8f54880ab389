import { PrismaService } from '../prisma/prisma.service';
import { CreateTenantDto, UpdateTenantDto, CreateUserDto } from './dto/tenant.dto';
export declare class TenantsService {
    private prisma;
    constructor(prisma: PrismaService);
    createTenant(createTenantDto: CreateTenantDto): Promise<{
        organization: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: import("@prisma/client/runtime/library").JsonValue;
            branding: import("@prisma/client/runtime/library").JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        };
        user: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string;
            preferences: import("@prisma/client/runtime/library").JsonValue;
            email: string;
            password: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
            role: import(".prisma/client").$Enums.Role;
            lastLoginAt: Date | null;
            mfaEnabled: boolean;
            mfaSecret: string | null;
            backupCodes: string[];
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            emailVerified: boolean;
            emailVerificationToken: string | null;
            loginAttempts: number;
            lockoutUntil: Date | null;
        };
    }>;
    getTenants(page?: number, limit?: number, search?: string): Promise<{
        data: ({
            _count: {
                users: number;
                workflows: number;
                agents: number;
                tools: number;
            };
        } & {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: import("@prisma/client/runtime/library").JsonValue;
            branding: import("@prisma/client/runtime/library").JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getTenantById(id: string): Promise<{
        _count: {
            sessions: number;
            workflows: number;
            agents: number;
            tools: number;
        };
        users: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            email: string;
            firstName: string;
            lastName: string;
            role: import(".prisma/client").$Enums.Role;
            lastLoginAt: Date;
        }[];
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        domain: string | null;
        slug: string;
        settings: import("@prisma/client/runtime/library").JsonValue;
        branding: import("@prisma/client/runtime/library").JsonValue;
        maxUsers: number | null;
        features: string[];
        plan: string;
        planExpires: Date | null;
    }>;
    updateTenant(id: string, updateTenantDto: UpdateTenantDto): Promise<{
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        domain: string | null;
        slug: string;
        settings: import("@prisma/client/runtime/library").JsonValue;
        branding: import("@prisma/client/runtime/library").JsonValue;
        maxUsers: number | null;
        features: string[];
        plan: string;
        planExpires: Date | null;
    }>;
    deleteTenant(id: string): Promise<{
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        name: string;
        domain: string | null;
        slug: string;
        settings: import("@prisma/client/runtime/library").JsonValue;
        branding: import("@prisma/client/runtime/library").JsonValue;
        maxUsers: number | null;
        features: string[];
        plan: string;
        planExpires: Date | null;
    }>;
    getTenantUsers(organizationId: string, page?: number, limit?: number): Promise<{
        data: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            preferences: import("@prisma/client/runtime/library").JsonValue;
            email: string;
            firstName: string;
            lastName: string;
            role: import(".prisma/client").$Enums.Role;
            lastLoginAt: Date;
        }[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    createTenantUser(organizationId: string, createUserDto: CreateUserDto): Promise<{
        id: string;
        isActive: boolean;
        createdAt: Date;
        email: string;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.Role;
    }>;
    updateTenantUser(organizationId: string, userId: string, updateData: Partial<CreateUserDto>): Promise<{
        id: string;
        isActive: boolean;
        updatedAt: Date;
        email: string;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.Role;
    }>;
    deleteTenantUser(organizationId: string, userId: string): Promise<{
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        preferences: import("@prisma/client/runtime/library").JsonValue;
        email: string;
        password: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.Role;
        lastLoginAt: Date | null;
        mfaEnabled: boolean;
        mfaSecret: string | null;
        backupCodes: string[];
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        emailVerified: boolean;
        emailVerificationToken: string | null;
        loginAttempts: number;
        lockoutUntil: Date | null;
    }>;
    getTenantAnalytics(organizationId: string): Promise<{
        users: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
            _count: {
                id: number;
            };
        })[];
        workflows: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.WorkflowGroupByOutputType, "isActive"[]> & {
            _count: {
                id: number;
            };
        })[];
        agents: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.AgentGroupByOutputType, "type"[]> & {
            _count: {
                id: number;
            };
        })[];
        activeSessions: number;
        recentActivity: ({
            user: {
                email: string;
                firstName: string;
                lastName: string;
            };
        } & {
            id: string;
            userId: string;
            ipAddress: string | null;
            userAgent: string | null;
            createdAt: Date;
            organizationId: string;
            resource: string;
            action: string;
            resourceId: string | null;
            details: import("@prisma/client/runtime/library").JsonValue;
        })[];
    }>;
}
