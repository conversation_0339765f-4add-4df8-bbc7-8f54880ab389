import { PrismaService } from '../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cache } from 'cache-manager';
export interface HybridAnalyticsMetrics {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    patternDistribution: Record<string, number>;
    agentUtilization: Record<string, number>;
    toolUtilization: Record<string, number>;
    errorAnalysis: {
        totalErrors: number;
        errorTypes: Record<string, number>;
        errorRate: number;
        commonErrors: Array<{
            message: string;
            count: number;
        }>;
    };
    performanceMetrics: {
        p50: number;
        p95: number;
        p99: number;
        minDuration: number;
        maxDuration: number;
    };
    contextSharingStats: {
        totalShares: number;
        averageShareSize: number;
        sharesByStrategy: Record<string, number>;
    };
    humanInteractionStats: {
        totalRequests: number;
        completionRate: number;
        averageResponseTime: number;
        skipRate: number;
        timeoutRate: number;
    };
    resourceUtilization: {
        averageMemoryUsage: number;
        averageTokenUsage: number;
        costAnalysis: {
            totalCost: number;
            costByComponent: Record<string, number>;
            costTrends: Array<{
                date: string;
                cost: number;
            }>;
        };
    };
}
export interface ExecutionPattern {
    pattern: string;
    count: number;
    successRate: number;
    averageDuration: number;
    commonAgents: string[];
    commonTools: string[];
    trends: Array<{
        date: string;
        count: number;
        successRate: number;
    }>;
}
export interface ComponentAnalytics {
    componentId: string;
    componentType: 'agent' | 'tool';
    name: string;
    totalExecutions: number;
    successfulExecutions: number;
    averageExecutionTime: number;
    errorRate: number;
    utilizationRate: number;
    performanceScore: number;
    trends: Array<{
        date: string;
        executions: number;
        successRate: number;
    }>;
    commonPartners: Array<{
        id: string;
        name: string;
        count: number;
    }>;
}
export declare class HybridWorkflowAnalyticsService {
    private prisma;
    private eventEmitter;
    private cacheManager;
    private readonly logger;
    private metricsCache;
    private realTimeMetrics;
    constructor(prisma: PrismaService, eventEmitter: EventEmitter2, cacheManager: Cache);
    getHybridAnalytics(organizationId: string, timeRange: {
        start: Date;
        end: Date;
    }, filters?: {
        agentIds?: string[];
        toolIds?: string[];
        executionPatterns?: string[];
        nodeIds?: string[];
    }): Promise<HybridAnalyticsMetrics>;
    getExecutionPatternAnalysis(organizationId: string, timeRange: {
        start: Date;
        end: Date;
    }): Promise<ExecutionPattern[]>;
    getComponentAnalytics(organizationId: string, componentType: 'agent' | 'tool', timeRange: {
        start: Date;
        end: Date;
    }): Promise<ComponentAnalytics[]>;
    getPerformanceInsights(organizationId: string, timeRange: {
        start: Date;
        end: Date;
    }): Promise<{
        bottlenecks: Array<{
            component: string;
            type: string;
            impact: number;
            recommendation: string;
        }>;
        optimizations: Array<{
            area: string;
            potential: number;
            description: string;
        }>;
        trends: Array<{
            metric: string;
            trend: 'improving' | 'declining' | 'stable';
            change: number;
        }>;
    }>;
    getRealTimeMetrics(organizationId: string): Promise<{
        activeExecutions: number;
        executionsPerMinute: number;
        averageResponseTime: number;
        errorRate: number;
        topPatterns: Array<{
            pattern: string;
            count: number;
        }>;
    }>;
    private generateAnalytics;
    private calculatePatternTrends;
    private calculateComponentTrends;
    private getComponentName;
    private calculatePerformanceScore;
    private calculateUtilizationRate;
    private identifyBottlenecks;
    private identifyOptimizations;
    private analyzeTrends;
    private categorizeError;
    private initializeRealTimeMetrics;
    private updateRealTimeMetrics;
    handleExecutionStarted(payload: any): void;
    handleExecutionCompleted(payload: any): void;
    handleExecutionFailed(payload: any): void;
    exportAnalytics(organizationId: string, timeRange: {
        start: Date;
        end: Date;
    }, format?: 'json' | 'csv' | 'pdf'): Promise<any>;
    private convertToCSV;
    private generatePDFReport;
    clearCache(): void;
    getCacheStats(): {
        size: number;
        keys: string[];
    };
}
