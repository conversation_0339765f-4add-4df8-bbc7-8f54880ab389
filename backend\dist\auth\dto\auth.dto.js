"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAPIKeyDto = exports.PermissionCheckDto = exports.SSOConfigDto = exports.UpdateUserRoleDto = exports.AcceptInviteDto = exports.InviteUserDto = exports.UpdateProfileDto = exports.VerifyMFADto = exports.EnableMFADto = exports.ChangePasswordDto = exports.ResetPasswordDto = exports.ForgotPasswordDto = exports.RefreshTokenDto = exports.RegisterDto = exports.LoginDto = exports.CreateAPIKeySchema = exports.PermissionCheckSchema = exports.SSOConfigSchema = exports.UpdateUserRoleSchema = exports.AcceptInviteSchema = exports.InviteUserSchema = exports.UpdateProfileSchema = exports.VerifyMFASchema = exports.EnableMFASchema = exports.ChangePasswordSchema = exports.ResetPasswordSchema = exports.ForgotPasswordSchema = exports.RefreshTokenSchema = exports.RegisterSchema = exports.LoginSchema = void 0;
const zod_1 = require("zod");
const nestjs_zod_1 = require("nestjs-zod");
exports.LoginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
    organizationSlug: zod_1.z.string().optional(),
    mfaCode: zod_1.z.string().length(6, 'MFA code must be 6 digits').optional(),
    rememberMe: zod_1.z.boolean().default(false),
});
exports.RegisterSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain uppercase, lowercase, number and special character'),
    firstName: zod_1.z.string().min(1, 'First name is required').max(50),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(50),
    organizationName: zod_1.z.string().min(1, 'Organization name is required').max(100),
    organizationSlug: zod_1.z.string()
        .min(3, 'Organization slug must be at least 3 characters')
        .max(50)
        .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
    acceptTerms: zod_1.z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
});
exports.RefreshTokenSchema = zod_1.z.object({
    refreshToken: zod_1.z.string().min(1, 'Refresh token is required'),
});
exports.ForgotPasswordSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    organizationSlug: zod_1.z.string().optional(),
});
exports.ResetPasswordSchema = zod_1.z.object({
    token: zod_1.z.string().min(1, 'Reset token is required'),
    password: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain uppercase, lowercase, number and special character'),
    confirmPassword: zod_1.z.string(),
}).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.ChangePasswordSchema = zod_1.z.object({
    currentPassword: zod_1.z.string().min(1, 'Current password is required'),
    newPassword: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain uppercase, lowercase, number and special character'),
    confirmPassword: zod_1.z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.EnableMFASchema = zod_1.z.object({
    secret: zod_1.z.string().min(1, 'MFA secret is required'),
    code: zod_1.z.string().length(6, 'MFA code must be 6 digits'),
});
exports.VerifyMFASchema = zod_1.z.object({
    code: zod_1.z.string().length(6, 'MFA code must be 6 digits'),
});
exports.UpdateProfileSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(1, 'First name is required').max(50).optional(),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(50).optional(),
    avatar: zod_1.z.string().url('Invalid avatar URL').optional(),
    preferences: zod_1.z.object({
        theme: zod_1.z.enum(['light', 'dark', 'system']).optional(),
        language: zod_1.z.string().optional(),
        timezone: zod_1.z.string().optional(),
        notifications: zod_1.z.boolean().optional(),
        viewMode: zod_1.z.enum(['grid', 'list']).optional(),
    }).optional(),
});
exports.InviteUserSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    role: zod_1.z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']),
    firstName: zod_1.z.string().min(1, 'First name is required').max(50),
    lastName: zod_1.z.string().min(1, 'Last name is required').max(50),
    message: zod_1.z.string().max(500).optional(),
});
exports.AcceptInviteSchema = zod_1.z.object({
    token: zod_1.z.string().min(1, 'Invite token is required'),
    password: zod_1.z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain uppercase, lowercase, number and special character'),
    confirmPassword: zod_1.z.string(),
}).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.UpdateUserRoleSchema = zod_1.z.object({
    userId: zod_1.z.string().cuid('Invalid user ID'),
    role: zod_1.z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']),
});
exports.SSOConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['SAML', 'OAUTH', 'ACTIVE_DIRECTORY']),
    config: zod_1.z.object({
        entityId: zod_1.z.string().optional(),
        ssoUrl: zod_1.z.string().url().optional(),
        certificate: zod_1.z.string().optional(),
        clientId: zod_1.z.string().optional(),
        clientSecret: zod_1.z.string().optional(),
        domain: zod_1.z.string().optional(),
        tenantId: zod_1.z.string().optional(),
    }),
    isEnabled: zod_1.z.boolean().default(true),
});
exports.PermissionCheckSchema = zod_1.z.object({
    userId: zod_1.z.string().cuid('Invalid user ID'),
    resource: zod_1.z.string().min(1, 'Resource is required'),
    action: zod_1.z.string().min(1, 'Action is required'),
    scope: zod_1.z.record(zod_1.z.any()).optional(),
});
exports.CreateAPIKeySchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'API key name is required').max(100),
    description: zod_1.z.string().max(500).optional(),
    permissions: zod_1.z.array(zod_1.z.string()).min(1, 'At least one permission is required'),
    expiresAt: zod_1.z.date().optional(),
});
class LoginDto extends (0, nestjs_zod_1.createZodDto)(exports.LoginSchema) {
}
exports.LoginDto = LoginDto;
class RegisterDto extends (0, nestjs_zod_1.createZodDto)(exports.RegisterSchema) {
}
exports.RegisterDto = RegisterDto;
class RefreshTokenDto extends (0, nestjs_zod_1.createZodDto)(exports.RefreshTokenSchema) {
}
exports.RefreshTokenDto = RefreshTokenDto;
class ForgotPasswordDto extends (0, nestjs_zod_1.createZodDto)(exports.ForgotPasswordSchema) {
}
exports.ForgotPasswordDto = ForgotPasswordDto;
class ResetPasswordDto extends (0, nestjs_zod_1.createZodDto)(exports.ResetPasswordSchema) {
}
exports.ResetPasswordDto = ResetPasswordDto;
class ChangePasswordDto extends (0, nestjs_zod_1.createZodDto)(exports.ChangePasswordSchema) {
}
exports.ChangePasswordDto = ChangePasswordDto;
class EnableMFADto extends (0, nestjs_zod_1.createZodDto)(exports.EnableMFASchema) {
}
exports.EnableMFADto = EnableMFADto;
class VerifyMFADto extends (0, nestjs_zod_1.createZodDto)(exports.VerifyMFASchema) {
}
exports.VerifyMFADto = VerifyMFADto;
class UpdateProfileDto extends (0, nestjs_zod_1.createZodDto)(exports.UpdateProfileSchema) {
}
exports.UpdateProfileDto = UpdateProfileDto;
class InviteUserDto extends (0, nestjs_zod_1.createZodDto)(exports.InviteUserSchema) {
}
exports.InviteUserDto = InviteUserDto;
class AcceptInviteDto extends (0, nestjs_zod_1.createZodDto)(exports.AcceptInviteSchema) {
}
exports.AcceptInviteDto = AcceptInviteDto;
class UpdateUserRoleDto extends (0, nestjs_zod_1.createZodDto)(exports.UpdateUserRoleSchema) {
}
exports.UpdateUserRoleDto = UpdateUserRoleDto;
class SSOConfigDto extends (0, nestjs_zod_1.createZodDto)(exports.SSOConfigSchema) {
}
exports.SSOConfigDto = SSOConfigDto;
class PermissionCheckDto extends (0, nestjs_zod_1.createZodDto)(exports.PermissionCheckSchema) {
}
exports.PermissionCheckDto = PermissionCheckDto;
class CreateAPIKeyDto extends (0, nestjs_zod_1.createZodDto)(exports.CreateAPIKeySchema) {
}
exports.CreateAPIKeyDto = CreateAPIKeyDto;
//# sourceMappingURL=auth.dto.js.map