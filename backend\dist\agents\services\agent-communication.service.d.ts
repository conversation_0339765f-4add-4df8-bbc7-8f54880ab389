import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { EventEmitter2 } from '@nestjs/event-emitter';
export interface AgentMessage {
    id: string;
    fromAgentId: string;
    toAgentId: string;
    type: 'request' | 'response' | 'broadcast' | 'notification';
    content: any;
    metadata?: {
        priority?: 'low' | 'normal' | 'high' | 'urgent';
        requiresResponse?: boolean;
        timeout?: number;
        correlationId?: string;
        sessionId?: string;
        workflowId?: string;
    };
    timestamp: Date;
    status: 'pending' | 'delivered' | 'acknowledged' | 'failed';
}
export interface AgentCommunicationChannel {
    id: string;
    name: string;
    participants: string[];
    type: 'direct' | 'group' | 'broadcast';
    organizationId: string;
    isActive: boolean;
    metadata?: any;
}
export declare class AgentCommunicationService {
    private prisma;
    private apixGateway;
    private eventEmitter;
    private readonly logger;
    private messageQueue;
    private activeChannels;
    private agentSubscriptions;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, eventEmitter: EventEmitter2);
    private initializeService;
    private loadActiveChannels;
    private setupEventListeners;
    sendMessage(fromAgentId: string, toAgentId: string, content: any, options?: {
        type?: 'request' | 'response' | 'notification';
        priority?: 'low' | 'normal' | 'high' | 'urgent';
        requiresResponse?: boolean;
        timeout?: number;
        correlationId?: string;
        sessionId?: string;
        workflowId?: string;
    }): Promise<AgentMessage>;
    respondToMessage(messageId: string, respondingAgentId: string, response: any): Promise<AgentMessage>;
    broadcastMessage(fromAgentId: string, organizationId: string, content: any, options?: {
        targetAgentIds?: string[];
        excludeAgentIds?: string[];
        priority?: 'low' | 'normal' | 'high' | 'urgent';
        sessionId?: string;
        workflowId?: string;
    }): Promise<AgentMessage[]>;
    createCommunicationChannel(name: string, participantIds: string[], organizationId: string, type?: 'direct' | 'group' | 'broadcast', metadata?: any): Promise<AgentCommunicationChannel>;
    sendChannelMessage(channelId: string, fromAgentId: string, content: any, options?: {
        priority?: 'low' | 'normal' | 'high' | 'urgent';
        sessionId?: string;
        workflowId?: string;
    }): Promise<AgentMessage[]>;
    getAgentMessages(agentId: string, options?: {
        limit?: number;
        offset?: number;
        status?: 'pending' | 'delivered' | 'acknowledged' | 'failed';
        type?: 'request' | 'response' | 'broadcast' | 'notification';
        fromDate?: Date;
        toDate?: Date;
    }): Promise<{
        messages: AgentMessage[];
        total: number;
    }>;
    subscribeToAgent(agentId: string, subscriberAgentId: string): Promise<void>;
    unsubscribeFromAgent(agentId: string, subscriberAgentId: string): Promise<void>;
    private handleAgentStatusChange;
    private handleWorkflowCommunication;
    private handleMessageTimeout;
    cleanup(): Promise<void>;
}
