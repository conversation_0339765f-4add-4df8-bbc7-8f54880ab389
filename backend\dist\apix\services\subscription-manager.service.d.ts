import { PrismaService } from "../../prisma/prisma.service";
import { Cache } from "cache-manager";
import { ChannelType } from "../dto/apix.dto";
import { ConnectionManagerService } from "./connection-manager.service";
interface ChannelPermission {
    read: boolean;
    write: boolean;
    admin: boolean;
    roles?: string[];
    users?: string[];
    organizations?: string[];
}
interface SubscriptionFilter {
    eventTypes?: string[];
    priority?: string[];
    metadata?: Record<string, any>;
}
export declare class SubscriptionManagerService {
    private prisma;
    private cacheManager;
    private connectionManager;
    private readonly logger;
    private channelPermissions;
    private subscriptions;
    constructor(prisma: PrismaService, cacheManager: Cache, connectionManager: ConnectionManagerService);
    subscribeToChannel(socketId: string, channelName: string, filters?: SubscriptionFilter): Promise<boolean>;
    unsubscribeFromChannel(socketId: string, channelName: string): Promise<boolean>;
    unsubscribeAll(socketId: string): Promise<void>;
    getSubscriptions(socketId: string): Map<string, SubscriptionFilter>;
    getChannelSubscribers(channelName: string): Promise<string[]>;
    createChannel(name: string, type: ChannelType, organizationId?: string, permissions?: ChannelPermission, metadata?: Record<string, any>): Promise<boolean>;
    updateChannelPermissions(channelName: string, permissions: ChannelPermission): Promise<boolean>;
    deleteChannel(channelName: string): Promise<boolean>;
    getChannelStats(organizationId?: string): Promise<{
        totalChannels: number;
        channelsByType: Record<ChannelType, number>;
        totalSubscriptions: number;
        activeSubscriptions: number;
        averageSubscribersPerChannel: number;
        topChannelsByActivity: Array<{
            name: string;
            subscribers: number;
        }>;
    }>;
    private hasChannelPermission;
    private getOrCreateChannel;
    private inferChannelType;
    private getDefaultPermissions;
    private cacheSubscription;
    private initializeDefaultChannels;
}
export {};
