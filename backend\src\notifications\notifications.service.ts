import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LoggingService } from '../logging/logging.service';
import { EmailService } from './email.service';
import { PushService } from './push.service';
import { WebSocketService } from './websocket.service';

export interface NotificationDto {
    id: string;
    type: 'email' | 'push' | 'websocket' | 'sms';
    title: string;
    message: string;
    data?: Record<string, any>;
    userId?: string;
    organizationId: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    status: 'pending' | 'sent' | 'failed' | 'read';
    scheduledAt?: Date;
    sentAt?: Date;
    readAt?: Date;
    createdAt: Date;
}

export interface CreateNotificationDto {
    type: 'email' | 'push' | 'websocket' | 'sms';
    title: string;
    message: string;
    data?: Record<string, any>;
    userId?: string;
    organizationId: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    scheduledAt?: Date;
    template?: string;
    templateData?: Record<string, any>;
}

@Injectable()
export class NotificationsService {
    constructor(
        private prisma: PrismaService,
        private logger: LoggingService,
        private emailService: EmailService,
        private pushService: PushService,
        private websocketService: WebSocketService,
    ) { }

    async sendNotification(notification: CreateNotificationDto): Promise<NotificationDto> {
        try {
            // Create notification record
            const notificationRecord = await this.prisma.notification.create({
                data: {
                    type: notification.type,
                    title: notification.title,
                    message: notification.message,
                    data: notification.data || {},
                    userId: notification.userId,
                    organizationId: notification.organizationId,
                    priority: notification.priority || 'medium',
                    status: 'pending',
                    scheduledAt: notification.scheduledAt,
                },
            });

            // Send immediately if not scheduled
            if (!notification.scheduledAt || notification.scheduledAt <= new Date()) {
                await this.deliverNotification(notificationRecord.id);
            }

            return this.mapToNotificationDto(notificationRecord);
        } catch (error) {
            this.logger.error('Failed to send notification', error.message, 'NotificationsService', {
                notification,
            });
            throw error;
        }
    }

    async deliverNotification(notificationId: string): Promise<void> {
        try {
            const notification = await this.prisma.notification.findUnique({
                where: { id: notificationId },
                include: {
                    user: {
                        select: {
                            email: true,
                            firstName: true,
                            lastName: true,
                            preferences: true,
                        },
                    },
                },
            });

            if (!notification) {
                throw new Error('Notification not found');
            }

            let success = false;

            switch (notification.type) {
                case 'email':
                    success = await this.emailService.sendEmail({
                        to: notification.user?.email || '',
                        subject: notification.title,
                        html: notification.message,
                        data: notification.data,
                    });
                    break;

                case 'push':
                    if (notification.userId) {
                        success = await this.pushService.sendPushNotification(
                            notification.userId,
                            notification.title,
                            notification.message,
                            notification.data
                        );
                    }
                    break;

                case 'websocket':
                    if (notification.userId) {
                        success = await this.websocketService.sendToUser(
                            notification.userId,
                            {
                                type: 'notification',
                                title: notification.title,
                                message: notification.message,
                                data: notification.data,
                            }
                        );
                    }
                    break;

                case 'sms':
                    // TODO: Implement SMS service
                    success = false;
                    break;
            }

            // Update notification status
            await this.prisma.notification.update({
                where: { id: notificationId },
                data: {
                    status: success ? 'sent' : 'failed',
                    sentAt: success ? new Date() : null,
                },
            });

            if (success) {
                this.logger.log(`Notification ${notificationId} delivered successfully`, 'NotificationsService');
            } else {
                this.logger.error(`Failed to deliver notification ${notificationId}`, '', 'NotificationsService');
            }
        } catch (error) {
            await this.prisma.notification.update({
                where: { id: notificationId },
                data: { status: 'failed' },
            });

            this.logger.error('Failed to deliver notification', error.message, 'NotificationsService', {
                notificationId,
            });
        }
    }

    async getUserNotifications(
        userId: string,
        organizationId: string,
        page = 1,
        limit = 20
    ): Promise<{
        notifications: NotificationDto[];
        total: number;
        unread: number;
    }> {
        const skip = (page - 1) * limit;

        const [notifications, total, unread] = await Promise.all([
            this.prisma.notification.findMany({
                where: {
                    userId,
                    organizationId,
                },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.notification.count({
                where: {
                    userId,
                    organizationId,
                },
            }),
            this.prisma.notification.count({
                where: {
                    userId,
                    organizationId,
                    readAt: null,
                },
            }),
        ]);

        return {
            notifications: notifications.map(this.mapToNotificationDto),
            total,
            unread,
        };
    }

    async markAsRead(notificationId: string, userId: string): Promise<void> {
        await this.prisma.notification.updateMany({
            where: {
                id: notificationId,
                userId,
                readAt: null,
            },
            data: {
                readAt: new Date(),
                status: 'read',
            },
        });
    }

    async markAllAsRead(userId: string, organizationId: string): Promise<void> {
        await this.prisma.notification.updateMany({
            where: {
                userId,
                organizationId,
                readAt: null,
            },
            data: {
                readAt: new Date(),
                status: 'read',
            },
        });
    }

    async deleteNotification(notificationId: string, userId: string): Promise<void> {
        await this.prisma.notification.deleteMany({
            where: {
                id: notificationId,
                userId,
            },
        });
    }

    // Convenience methods for common notification types
    async sendWelcomeNotification(userId: string, organizationId: string): Promise<void> {
        await this.sendNotification({
            type: 'email',
            title: 'Welcome to SynapseAI',
            message: 'Welcome to SynapseAI! Get started by creating your first workflow.',
            userId,
            organizationId,
            priority: 'medium',
        });
    }

    async sendWorkflowCompletionNotification(
        userId: string,
        organizationId: string,
        workflowName: string,
        status: 'success' | 'failed'
    ): Promise<void> {
        await this.sendNotification({
            type: 'websocket',
            title: `Workflow ${status === 'success' ? 'Completed' : 'Failed'}`,
            message: `Your workflow "${workflowName}" has ${status === 'success' ? 'completed successfully' : 'failed'}.`,
            userId,
            organizationId,
            priority: status === 'failed' ? 'high' : 'medium',
            data: {
                workflowName,
                status,
            },
        });
    }

    async sendSecurityAlert(
        userId: string,
        organizationId: string,
        alertType: string,
        details: Record<string, any>
    ): Promise<void> {
        await this.sendNotification({
            type: 'email',
            title: 'Security Alert',
            message: `A security event has been detected: ${alertType}`,
            userId,
            organizationId,
            priority: 'urgent',
            data: {
                alertType,
                details,
            },
        });
    }

    async sendSystemMaintenanceNotification(organizationId: string, message: string): Promise<void> {
        // Send to all users in organization
        const users = await this.prisma.user.findMany({
            where: { organizationId, isActive: true },
            select: { id: true },
        });

        for (const user of users) {
            await this.sendNotification({
                type: 'websocket',
                title: 'System Maintenance',
                message,
                userId: user.id,
                organizationId,
                priority: 'medium',
            });
        }
    }

    private mapToNotificationDto(notification: any): NotificationDto {
        return {
            id: notification.id,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            data: notification.data || {},
            userId: notification.userId,
            organizationId: notification.organizationId,
            priority: notification.priority,
            status: notification.status,
            scheduledAt: notification.scheduledAt,
            sentAt: notification.sentAt,
            readAt: notification.readAt,
            createdAt: notification.createdAt,
        };
    }
}