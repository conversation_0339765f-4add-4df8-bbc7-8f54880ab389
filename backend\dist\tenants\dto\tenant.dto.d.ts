import { Role } from '@prisma/client';
export declare class CreateUserDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: Role;
}
export declare class CreateTenantDto {
    name: string;
    slug: string;
    domain?: string;
    adminUser: CreateUserDto;
}
export declare class UpdateTenantDto {
    name?: string;
    domain?: string;
    settings?: any;
    branding?: any;
}
