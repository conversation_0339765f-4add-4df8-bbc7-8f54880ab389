import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Role, User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import {
    CreateUserDto,
    UpdateUserDto,
    UpdateUserRoleDto,
    UserProfileDto,
    UserListQueryDto,
    UserListResponseDto,
    UserStatsDto,
    BulkUserActionDto,
    UserPreferencesDto,
    UserSecurityDto,
    ResetUserPasswordDto,
    UserAuditLogDto
} from './dto/users.dto';

@Injectable()
export class UsersService {
    constructor(private prisma: PrismaService) { }

    async create(createUserDto: CreateUserDto, createdBy: string): Promise<UserProfileDto> {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: createUserDto.email }
        });

        if (existingUser) {
            throw new BadRequestException('User with this email already exists');
        }

        const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

        const user = await this.prisma.user.create({
            data: {
                email: createUserDto.email,
                password: hashedPassword,
                firstName: createUserDto.firstName,
                lastName: createUserDto.lastName,
                role: createUserDto.role || Role.VIEWER,
                organizationId: createUserDto.organizationId,
                avatar: createUserDto.avatar,
                preferences: createUserDto.preferences || {},
                emailVerified: createUserDto.emailVerified || false,
            },
            include: {
                organization: {
                    select: { id: true, name: true, slug: true }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(createdBy, 'USER_CREATED', user.id, {
            email: user.email,
            role: user.role,
            organizationId: user.organizationId
        });

        return this.mapToUserProfile(user);
    }

    async findAll(query: UserListQueryDto, organizationId: string): Promise<UserListResponseDto> {
        const {
            page = 1,
            limit = 20,
            search,
            role,
            isActive,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = query;

        const skip = (page - 1) * limit;

        const where: any = {
            organizationId,
            ...(search && {
                OR: [
                    { firstName: { contains: search, mode: 'insensitive' } },
                    { lastName: { contains: search, mode: 'insensitive' } },
                    { email: { contains: search, mode: 'insensitive' } }
                ]
            }),
            ...(role && { role }),
            ...(isActive !== undefined && { isActive })
        };

        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where,
                skip,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    organization: {
                        select: { id: true, name: true, slug: true }
                    },
                    sessions: {
                        where: { isActive: true },
                        select: { id: true, updatedAt: true }
                    },
                    _count: {
                        select: {
                            createdWorkflows: true,
                            createdAgents: true,
                            createdTools: true,
                            sessions: true
                        }
                    }
                }
            }),
            this.prisma.user.count({ where })
        ]);

        return {
            users: users.map(user => this.mapToUserProfile(user as any)),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }

    async findOne(id: string, organizationId: string): Promise<UserProfileDto> {
        const user = await this.prisma.user.findFirst({
            where: { id, organizationId },
            include: {
                organization: {
                    select: { id: true, name: true, slug: true }
                },
                sessions: {
                    where: { isActive: true },
                    select: { id: true, createdAt: true, updatedAt: true }
                },
                _count: {
                    select: {
                        createdWorkflows: true,
                        createdAgents: true,
                        createdTools: true,
                        sessions: true,
                        auditLogs: true
                    }
                }
            }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        return this.mapToUserProfile(user as any);
    }

    async update(id: string, updateUserDto: UpdateUserDto, updatedBy: string, organizationId: string): Promise<UserProfileDto> {
        const existingUser = await this.prisma.user.findFirst({
            where: { id, organizationId }
        });

        if (!existingUser) {
            throw new NotFoundException('User not found');
        }

        // Check for email uniqueness if email is being updated
        if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
            const emailExists = await this.prisma.user.findUnique({
                where: { email: updateUserDto.email }
            });

            if (emailExists) {
                throw new BadRequestException('Email already in use');
            }
        }

        const updateData: any = {};

        if (updateUserDto.firstName) updateData.firstName = updateUserDto.firstName;
        if (updateUserDto.lastName) updateData.lastName = updateUserDto.lastName;
        if (updateUserDto.email) updateData.email = updateUserDto.email;
        if (updateUserDto.avatar !== undefined) updateData.avatar = updateUserDto.avatar;
        if (updateUserDto.isActive !== undefined) updateData.isActive = updateUserDto.isActive;
        if (updateUserDto.preferences) updateData.preferences = updateUserDto.preferences;

        const user = await this.prisma.user.update({
            where: { id },
            data: updateData,
            include: {
                organization: {
                    select: { id: true, name: true, slug: true }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(updatedBy, 'USER_UPDATED', user.id, {
            changes: updateData,
            previousValues: {
                firstName: existingUser.firstName,
                lastName: existingUser.lastName,
                email: existingUser.email,
                isActive: existingUser.isActive
            }
        });

        return this.mapToUserProfile(user);
    }

    async updateRole(id: string, updateRoleDto: UpdateUserRoleDto, updatedBy: string, organizationId: string): Promise<UserProfileDto> {
        const existingUser = await this.prisma.user.findFirst({
            where: { id, organizationId }
        });

        if (!existingUser) {
            throw new NotFoundException('User not found');
        }

        const user = await this.prisma.user.update({
            where: { id },
            data: { role: updateRoleDto.role },
            include: {
                organization: {
                    select: { id: true, name: true, slug: true }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(updatedBy, 'USER_ROLE_UPDATED', user.id, {
            newRole: updateRoleDto.role,
            previousRole: existingUser.role
        });

        return this.mapToUserProfile(user);
    }

    async remove(id: string, deletedBy: string, organizationId: string): Promise<void> {
        const user = await this.prisma.user.findFirst({
            where: { id, organizationId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Soft delete by deactivating
        await this.prisma.user.update({
            where: { id },
            data: { isActive: false }
        });

        // Create audit log
        await this.createAuditLog(deletedBy, 'USER_DELETED', user.id, {
            email: user.email,
            role: user.role
        });
    }

    async getUserStats(organizationId: string): Promise<UserStatsDto> {
        const [totalUsers, activeUsers, usersByRole, recentUsers] = await Promise.all([
            this.prisma.user.count({
                where: { organizationId }
            }),
            this.prisma.user.count({
                where: { organizationId, isActive: true }
            }),
            this.prisma.user.groupBy({
                by: ['role'],
                where: { organizationId },
                _count: { role: true }
            }),
            this.prisma.user.count({
                where: {
                    organizationId,
                    createdAt: {
                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
                    }
                }
            })
        ]);

        return {
            totalUsers,
            activeUsers,
            inactiveUsers: totalUsers - activeUsers,
            usersByRole: usersByRole.reduce((acc, group) => {
                acc[group.role] = group._count.role;
                return acc;
            }, {} as Record<string, number>),
            recentUsers,
            growthRate: 0 // TODO: Calculate based on historical data
        };
    }

    async bulkAction(bulkActionDto: BulkUserActionDto, actionBy: string, organizationId: string): Promise<{ success: number; failed: number }> {
        const { userIds, action, data } = bulkActionDto;
        let success = 0;
        let failed = 0;

        for (const userId of userIds) {
            try {
                switch (action) {
                    case 'activate':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId },
                            data: { isActive: true }
                        });
                        break;
                    case 'deactivate':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId },
                            data: { isActive: false }
                        });
                        break;
                    case 'updateRole':
                        if (data?.role) {
                            await this.prisma.user.update({
                                where: { id: userId, organizationId },
                                data: { role: data.role }
                            });
                        }
                        break;
                    case 'delete':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId },
                            data: { isActive: false }
                        });
                        break;
                }
                success++;

                // Create audit log
                await this.createAuditLog(actionBy, `BULK_${action.toUpperCase()}`, userId, data);
            } catch (error) {
                failed++;
            }
        }

        return { success, failed };
    }

    async updatePreferences(userId: string, preferences: UserPreferencesDto, organizationId: string): Promise<UserProfileDto> {
        const user = await this.prisma.user.findFirst({
            where: { id: userId, organizationId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        const updatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: { preferences },
            include: {
                organization: {
                    select: { id: true, name: true, slug: true }
                }
            }
        });

        return this.mapToUserProfile(updatedUser);
    }

    async getUserSecurity(userId: string, organizationId: string): Promise<UserSecurityDto> {
        const user = await this.prisma.user.findFirst({
            where: { id: userId, organizationId },
            include: {
                refreshTokens: {
                    where: { isActive: true },
                    select: { id: true, createdAt: true, expiresAt: true, ipAddress: true, userAgent: true }
                },
                apiKeys: {
                    where: { isActive: true },
                    select: { id: true, name: true, permissions: true, lastUsedAt: true, createdAt: true }
                },
                sessions: {
                    where: { isActive: true },
                    select: { id: true, createdAt: true, updatedAt: true, ipAddress: true, userAgent: true }
                }
            }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        return {
            mfaEnabled: user.mfaEnabled,
            emailVerified: user.emailVerified,
            lastLoginAt: user.lastLoginAt,
            loginAttempts: user.loginAttempts,
            lockoutUntil: user.lockoutUntil,
            activeSessions: user.sessions.length,
            activeTokens: user.refreshTokens.length,
            activeApiKeys: user.apiKeys.length,
            sessions: user.sessions,
            apiKeys: user.apiKeys
        };
    }

    async resetPassword(userId: string, resetPasswordDto: ResetUserPasswordDto, resetBy: string, organizationId: string): Promise<void> {
        const user = await this.prisma.user.findFirst({
            where: { id: userId, organizationId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        const hashedPassword = await bcrypt.hash(resetPasswordDto.newPassword, 12);

        await this.prisma.user.update({
            where: { id: userId },
            data: {
                password: hashedPassword,
                passwordResetToken: null,
                passwordResetExpires: null,
                loginAttempts: 0,
                lockoutUntil: null
            }
        });

        // Revoke all refresh tokens
        await this.prisma.refreshToken.updateMany({
            where: { userId },
            data: { isActive: false, revokedAt: new Date() }
        });

        // Create audit log
        await this.createAuditLog(resetBy, 'USER_PASSWORD_RESET', userId, {
            resetBy,
            forcedLogout: true
        });
    }

    async getUserAuditLogs(userId: string, organizationId: string, page = 1, limit = 50): Promise<{ logs: UserAuditLogDto[]; total: number }> {
        const skip = (page - 1) * limit;

        const [logs, total] = await Promise.all([
            this.prisma.auditLog.findMany({
                where: { userId, organizationId },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: { firstName: true, lastName: true, email: true }
                    }
                }
            }),
            this.prisma.auditLog.count({
                where: { userId, organizationId }
            })
        ]);

        return {
            logs: logs.map(log => ({
                id: log.id,
                action: log.action,
                resource: log.resource,
                resourceId: log.resourceId,
                details: log.details as Record<string, any>,
                ipAddress: log.ipAddress,
                userAgent: log.userAgent,
                createdAt: log.createdAt,
                performedBy: log.user ? `${log.user.firstName} ${log.user.lastName}` : 'System'
            })),
            total
        };
    }

    private async createAuditLog(userId: string, action: string, resourceId: string, details: any) {
        await this.prisma.auditLog.create({
            data: {
                userId,
                action,
                resource: 'USER',
                resourceId,
                details,
                organizationId: await this.getUserOrganizationId(userId)
            }
        });
    }

    private async getUserOrganizationId(userId: string): Promise<string> {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { organizationId: true }
        });
        return user?.organizationId || '';
    }

    private mapToUserProfile(user: any): UserProfileDto {
        return {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: `${user.firstName} ${user.lastName}`,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
            emailVerified: user.emailVerified,
            mfaEnabled: user.mfaEnabled,
            lastLoginAt: user.lastLoginAt,
            preferences: user.preferences,
            organization: user.organization,
            activeSessions: user.sessions?.length || 0,
            stats: user._count ? {
                workflows: user._count.createdWorkflows || 0,
                agents: user._count.createdAgents || 0,
                tools: user._count.createdTools || 0,
                sessions: user._count.sessions || 0
            } : undefined,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
}