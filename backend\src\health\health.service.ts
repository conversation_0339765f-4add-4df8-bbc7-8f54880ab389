import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import {
    HealthIndicatorResult,
    HealthCheckService,
    PrismaHealthIndicator,
    MemoryHealthIndicator,
    DiskHealthIndicator
} from '@nestjs/terminus';
import Redis from 'ioredis';
import * as os from 'os';
import * as fs from 'fs/promises';

export interface SystemHealth {
    status: 'ok' | 'error' | 'shutting_down';
    checks: {
        database: HealthIndicatorResult;
        redis: HealthIndicatorResult;
        memory: HealthIndicatorResult;
        disk: HealthIndicatorResult;
        external_apis?: HealthIndicatorResult;
    };
    info: {
        uptime: number;
        timestamp: number;
        version: string;
        environment: string;
    };
    details: {
        memory: {
            used: number;
            free: number;
            total: number;
            percentage: number;
        };
        cpu: {
            cores: number;
            load: number[];
            usage: number;
        };
        disk: {
            used: number;
            free: number;
            total: number;
            percentage: number;
        };
        process: {
            pid: number;
            uptime: number;
            memoryUsage: NodeJS.MemoryUsage;
        };
    };
}

@Injectable()
export class HealthService {
    private redis: Redis | null = null;
    private startTime: number = Date.now();

    constructor(
        private configService: ConfigService,
        private prisma: PrismaService,
        private health: HealthCheckService,
        private prismaHealth: PrismaHealthIndicator,
        private memoryHealth: MemoryHealthIndicator,
        private diskHealth: DiskHealthIndicator,
    ) {
        this.initializeRedis();
    }

    private initializeRedis() {
        const redisUrl = this.configService.get<string>('REDIS_URL');
        if (redisUrl) {
            this.redis = new Redis(redisUrl);
            this.redis.on('error', (error) => {
                console.error('Redis health check error:', error);
            });
        }
    }

    async getHealthStatus(): Promise<SystemHealth> {
        try {
            // Perform all health checks
            const [
                databaseCheck,
                redisCheck,
                memoryCheck,
                diskCheck,
                externalApiCheck
            ] = await Promise.allSettled([
                this.checkDatabase(),
                this.checkRedis(),
                this.checkMemory(),
                this.checkDisk(),
                this.checkExternalAPIs()
            ]);

            // FIXED: Proper HealthIndicatorResult construction
            const checks = {
                database: this.getResultFromSettled(databaseCheck, 'database'),
                redis: this.getResultFromSettled(redisCheck, 'redis'),
                memory: this.getResultFromSettled(memoryCheck, 'memory'),
                disk: this.getResultFromSettled(diskCheck, 'disk'),
                external_apis: this.getResultFromSettled(externalApiCheck, 'external_apis')
            };

            // Determine overall status
            const hasErrors = Object.values(checks).some(check =>
                check && check.status === 'down'
            );

            const systemDetails = await this.getSystemDetails();

            return {
                status: hasErrors ? 'error' : 'ok',
                checks,
                info: {
                    uptime: Date.now() - this.startTime,
                    timestamp: Date.now(),
                    version: this.configService.get('APP_VERSION', '1.0.0'),
                    environment: this.configService.get('NODE_ENV', 'development'),
                },
                details: systemDetails,
            };
        } catch (error) {
            console.error('Health check failed:', error);
            // FIXED: Return proper HealthIndicatorResult structure
            return {
                status: 'error',
                checks: {
                    database: { status: 'down', message: error.message },
                    redis: { status: 'down', message: 'Health check failed' },
                    memory: { status: 'down', message: 'Health check failed' },
                    disk: { status: 'down', message: 'Health check failed' },
                },
                info: {
                    uptime: Date.now() - this.startTime,
                    timestamp: Date.now(),
                    version: this.configService.get('APP_VERSION', '1.0.0'),
                    environment: this.configService.get('NODE_ENV', 'development'),
                },
                details: {
                    memory: { used: 0, free: 0, total: 0, percentage: 0 },
                    cpu: { cores: 0, load: [], usage: 0 },
                    disk: { used: 0, free: 0, total: 0, percentage: 0 },
                    process: {
                        pid: process.pid,
                        uptime: process.uptime(),
                        memoryUsage: process.memoryUsage(),
                    },
                },
            };
        }
    }

    // FIXED: Helper to convert PromiseSettledResult to HealthIndicatorResult
    private getResultFromSettled(
        result: PromiseSettledResult<HealthIndicatorResult>,
        checkName: string
    ): HealthIndicatorResult {
        if (result.status === 'fulfilled') {
            return result.value;
        } else {
            return {
                status: 'down',
                message: `${checkName} check failed: ${result.reason?.message || 'Unknown error'}`,
                error: result.reason?.message
            };
        }
    }

    private async checkDatabase(): Promise<HealthIndicatorResult> {
        try {
            return await this.prismaHealth.pingCheck('database', this.prisma);
        } catch (error) {
            return {
                status: 'down',
                message: `Database connection failed: ${error.message}`,
                error: error.message
            };
        }
    }

    private async checkRedis(): Promise<HealthIndicatorResult> {
        if (!this.redis) {
            return {
                status: 'down',
                message: 'Redis not configured',
                error: 'Redis connection not initialized'
            };
        }

        try {
            const start = Date.now();
            await this.redis.ping();
            const responseTime = Date.now() - start;

            return {
                status: 'up',
                message: 'Redis is healthy',
                responseTime: `${responseTime}ms`
            };
        } catch (error) {
            return {
                status: 'down',
                message: `Redis connection failed: ${error.message}`,
                error: error.message
            };
        }
    }

    private async checkMemory(): Promise<HealthIndicatorResult> {
        try {
            // Using memory health indicator with proper thresholds
            return await this.memoryHealth.checkHeap('memory', 1024 * 1024 * 1024); // 1GB limit
        } catch (error) {
            return {
                status: 'down',
                message: `Memory check failed: ${error.message}`,
                error: error.message
            };
        }
    }

    private async checkDisk(): Promise<HealthIndicatorResult> {
        try {
            // Using disk health indicator with proper thresholds
            return await this.diskHealth.checkStorage('disk', {
                thresholdPercent: 0.9, // 90% threshold
                path: '/'
            });
        } catch (error) {
            return {
                status: 'down',
                message: `Disk check failed: ${error.message}`,
                error: error.message
            };
        }
    }

    private async checkExternalAPIs(): Promise<HealthIndicatorResult> {
        try {
            // Check critical external services
            const checks = [];

            // Example: Check OpenAI API
            const openaiKey = this.configService.get('OPENAI_API_KEY');
            if (openaiKey) {
                checks.push(this.checkOpenAI());
            }

            if (checks.length === 0) {
                return {
                    status: 'up',
                    message: 'No external APIs configured'
                };
            }

            const results = await Promise.allSettled(checks);
            const hasFailures = results.some(result => result.status === 'rejected');

            return {
                status: hasFailures ? 'down' : 'up',
                message: hasFailures ? 'Some external APIs are down' : 'All external APIs are healthy',
                checks: results.length
            };
        } catch (error) {
            return {
                status: 'down',
                message: `External API check failed: ${error.message}`,
                error: error.message
            };
        }
    }

    private async checkOpenAI(): Promise<void> {
        // Simple connectivity check to OpenAI
        const response = await fetch('https://api.openai.com/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${this.configService.get('OPENAI_API_KEY')}`,
            },
            signal: AbortSignal.timeout(5000), // 5 second timeout
        });

        if (!response.ok) {
            throw new Error(`OpenAI API returned ${response.status}`);
        }
    }

    private async getSystemDetails() {
        const memoryInfo = process.memoryUsage();
        const systemMemory = {
            used: os.totalmem() - os.freemem(),
            free: os.freemem(),
            total: os.totalmem(),
            percentage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
        };

        let diskInfo = {
            used: 0,
            free: 0,
            total: 0,
            percentage: 0,
        };

        try {
            const stats = await fs.stat('/');
            // This is a simplified disk check - in production you'd use statvfs or similar
            diskInfo = {
                used: 0, // Would need statvfs for actual disk usage
                free: 0,
                total: 0,
                percentage: 0,
            };
        } catch (error) {
            // Ignore disk stat errors
        }

        return {
            memory: systemMemory,
            cpu: {
                cores: os.cpus().length,
                load: os.loadavg(),
                usage: os.loadavg()[0] / os.cpus().length * 100,
            },
            disk: diskInfo,
            process: {
                pid: process.pid,
                uptime: process.uptime(),
                memoryUsage: memoryInfo,
            },
        };
    }

    async isHealthy(): Promise<boolean> {
        try {
            const health = await this.getHealthStatus();
            return health.status === 'ok';
        } catch (error) {
            return false;
        }
    }

    async getReadinessStatus(): Promise<{
        ready: boolean;
        checks: Record<string, boolean>;
    }> {
        try {
            const health = await this.getHealthStatus();

            return {
                ready: health.status === 'ok',
                checks: {
                    database: health.checks.database?.status === 'up',
                    redis: health.checks.redis?.status === 'up',
                    memory: health.checks.memory?.status === 'up',
                    disk: health.checks.disk?.status === 'up',
                },
            };
        } catch (error) {
            return {
                ready: false,
                checks: {
                    database: false,
                    redis: false,
                    memory: false,
                    disk: false,
                },
            };
        }
    }

    async getLivenessStatus(): Promise<{
        alive: boolean;
        uptime: number;
    }> {
        return {
            alive: true,
            uptime: Date.now() - this.startTime,
        };
    }

    onModuleDestroy() {
        if (this.redis) {
            this.redis.disconnect();
        }
    }
}