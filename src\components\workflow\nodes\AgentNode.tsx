import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Bot, CheckCircle, XCircle, Activity, MessageSquare, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AgentNodeData {
  label: string;
  description?: string;
  config?: {
    agentId?: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
  };
  status?: 'idle' | 'running' | 'success' | 'error' | 'thinking';
  isConfigValid?: boolean;
  lastOutput?: string;
  errorMessage?: string;
  progress?: number;
}

const AgentNode: React.FC<NodeProps<AgentNodeData>> = ({ data, selected }) => {
  const getStatusIcon = () => {
    switch (data.status) {
      case 'running':
        return <Activity className="h-3 w-3 text-blue-600 animate-pulse" />;
      case 'thinking':
        return <MessageSquare className="h-3 w-3 text-purple-600 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'thinking':
        return 'border-purple-500 bg-purple-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  return (
    <Card
      className={cn(
        'min-w-[200px] max-w-[300px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor(),
        !data.isConfigValid && 'border-red-300 bg-red-50'
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      
      <CardContent className="p-4">
        {/* Progress bar for running status */}
        {(data.status === 'running' || data.status === 'thinking') && data.progress !== undefined && (
          <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-t-lg overflow-hidden">
            <div 
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${data.progress}%` }}
            />
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Bot className="h-4 w-4 text-blue-600" />
            {getStatusIcon()}
          </div>
          {!data.isConfigValid && (
            <AlertTriangle className="h-4 w-4 text-red-500" />
          )}
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1 truncate">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs mb-2">
          <Bot className="h-2 w-2 mr-1" />
          Agent
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
            {data.description}
          </p>
        )}

        {/* Status-specific content */}
        {data.status === 'error' && data.errorMessage && (
          <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <XCircle className="h-3 w-3 text-red-600" />
              <span className="font-medium text-red-800">Error</span>
            </div>
            <p className="text-red-700 line-clamp-2">{data.errorMessage}</p>
          </div>
        )}

        {/* Output */}
        {(data.status === 'success' || data.status === 'thinking') && data.lastOutput && (
          <div className="mb-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <MessageSquare className="h-3 w-3 text-gray-600" />
              <span className="font-medium text-gray-800">
                {data.status === 'thinking' ? 'Thinking' : 'Output'}
              </span>
            </div>
            <p className="text-gray-700 line-clamp-3 font-mono">
              {data.lastOutput}
              {data.status === 'thinking' && (
                <span className="animate-pulse">|</span>
              )}
            </p>
          </div>
        )}

        {/* Configuration summary */}
        {data.config && (
          <div className="text-xs text-muted-foreground space-y-1">
            {data.config.maxTokens && (
              <div>Max tokens: {data.config.maxTokens}</div>
            )}
            {data.config.temperature !== undefined && (
              <div>Temperature: {data.config.temperature}</div>
            )}
          </div>
        )}
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(AgentNode);