import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  <PERSON><PERSON><PERSON>,
  IsN<PERSON>ber,
  IsObject,
  ValidateNested,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export enum ClientType {
  WEB_APP = "WEB_APP",
  MOBILE_APP = "MOBILE_APP",
  SDK_WIDGET = "SDK_WIDGET",
  API_CLIENT = "API_CLIENT",
  INTERNAL_SERVICE = "INTERNAL_SERVICE",
}

export enum ConnectionStatus {
  CONNECTED = "CONNECTED",
  DISCONNECTED = "DISCONNECTED",
  RECONNECTING = "RECONNECTING",
  SUSPENDED = "SUSPENDED",
}

export enum ChannelType {
  AGENT_EVENTS = "AGENT_EVENTS",
  TOOL_EVENTS = "TOOL_EVENTS",
  WORKFLOW_EVENTS = "WORKFLOW_EVENTS",
  PROVIDER_EVENTS = "PROVIDER_EVENTS",
  SYSTEM_EVENTS = "SYSTEM_EVENTS",
  SESSION_EVENTS = "SESSION_EVENTS",
  PRIVATE_USER = "PRIVATE_USER",
  ORGANIZATION = "ORGANIZATION",
  PUBLIC = "PUBLIC",
}

export enum EventPriority {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH",
  CRITICAL = "CRITICAL",
}

export enum HybridEventType {
  HYBRID_EXECUTION_STARTED = 'hybrid_execution_started',
  HYBRID_EXECUTION_COMPLETED = 'hybrid_execution_completed',
  HYBRID_EXECUTION_FAILED = 'hybrid_execution_failed',
  HYBRID_EXECUTION_CANCELLED = 'hybrid_execution_cancelled',
  
  AGENT_FIRST_STARTED = 'agent_first_started',
  AGENT_FIRST_COMPLETED = 'agent_first_completed',
  
  TOOL_FIRST_STARTED = 'tool_first_started',
  TOOL_FIRST_COMPLETED = 'tool_first_completed',
  
  PARALLEL_EXECUTION_STARTED = 'parallel_execution_started',
  PARALLEL_EXECUTION_COMPLETED = 'parallel_execution_completed',
  
  ORCHESTRATION_STARTED = 'orchestration_started',
  ORCHESTRATION_DECISION = 'orchestration_decision',
  ORCHESTRATION_COMPLETED = 'orchestration_completed',
  
  AGENT_EXECUTION_STARTED = 'agent_execution_started',
  AGENT_EXECUTION_COMPLETED = 'agent_execution_completed',
  AGENT_EXECUTION_FAILED = 'agent_execution_failed',
  
  TOOL_EXECUTION_STARTED = 'tool_execution_started',
  TOOL_EXECUTION_COMPLETED = 'tool_execution_completed',
  TOOL_EXECUTION_FAILED = 'tool_execution_failed',
  
  SYNC_POINT_REACHED = 'sync_point_reached',
  CONTEXT_SHARED = 'context_shared',
  CONTEXT_UPDATED = 'context_updated',
  
  FALLBACK_TRIGGERED = 'fallback_triggered',
  FALLBACK_COMPLETED = 'fallback_completed',
  FALLBACK_FAILED = 'fallback_failed',
  
  HUMAN_INPUT_REQUESTED = 'human_input_requested',
  HUMAN_INPUT_RECEIVED = 'human_input_received',
  HUMAN_INPUT_TIMEOUT = 'human_input_timeout',
  
  STATE_SYNCHRONIZATION = 'state_synchronization',
  EXECUTION_HANDOFF = 'execution_handoff',
  COORDINATION_EVENT = 'coordination_event',
}

export class ApiXConnectionDto {
  @ApiProperty()
  @IsString()
  sessionId: string;

  @ApiProperty({ enum: ClientType })
  @IsEnum(ClientType)
  clientType: ClientType;

  @ApiProperty()
  @IsString()
  token: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  subscriptions?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ApiXEventDto {
  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  channel: string;

  @ApiProperty()
  payload: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  correlationId?: string;

  @ApiProperty({ enum: EventPriority, required: false })
  @IsOptional()
  @IsEnum(EventPriority)
  priority?: EventPriority;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  compressed?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ApiXSubscriptionDto {
  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  channels: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  acknowledgment?: boolean;
}

export class ApiXChannelDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: ChannelType })
  @IsEnum(ChannelType)
  type: ChannelType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  permissions?: Record<string, any>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ApiXLatencyMetricDto {
  @ApiProperty()
  @IsString()
  connectionId: string;

  @ApiProperty()
  @IsString()
  eventType: string;

  @ApiProperty()
  @IsNumber()
  latencyMs: number;

  @ApiProperty()
  @IsString()
  organizationId: string;
}

export class ApiXEventReplayDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  since?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  eventTypes?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  limit?: number;
}

export class ApiXHeartbeatDto {
  @ApiProperty()
  @IsNumber()
  timestamp: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  latency?: number;
}

export class ApiXErrorDto {
  @ApiProperty()
  @IsString()
  message: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  stack?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

export class HybridExecutionEventDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  workflowId: string;

  @ApiProperty()
  @IsString()
  organizationId: string;

  @ApiProperty({ enum: HybridEventType })
  @IsEnum(HybridEventType)
  eventType: HybridEventType;

  @ApiProperty()
  payload: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  toolIds?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  executionPattern?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  iteration?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class HybridCoordinationEventDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  coordinationType: 'state_sync' | 'context_share' | 'execution_handoff' | 'sync_point';

  @ApiProperty()
  payload: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sourceComponent?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  targetComponent?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  contextData?: Record<string, any>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class HumanInputRequestDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  sessionId: string;

  @ApiProperty()
  @IsString()
  prompt: string;

  @ApiProperty()
  @IsString()
  inputType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  timeout?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  allowSkip?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  validationRules?: Record<string, any>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class HumanInputResponseDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  sessionId: string;

  @ApiProperty()
  userInput: any;

  @ApiProperty()
  @IsString()
  inputType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  skipped?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class WorkflowStateUpdateDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  stateType: 'node_started' | 'node_completed' | 'node_failed' | 'execution_progress' | 'context_updated';

  @ApiProperty()
  currentState: any;

  @ApiProperty({ required: false })
  @IsOptional()
  previousState?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  stateChanges?: Record<string, any>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  progress?: number;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class ExecutionHandoffDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  fromComponent: string;

  @ApiProperty()
  @IsString()
  toComponent: string;

  @ApiProperty()
  @IsString()
  handoffType: 'agent_to_tool' | 'tool_to_agent' | 'tool_to_tool' | 'agent_to_agent';

  @ApiProperty()
  contextData: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  handoffMetadata?: Record<string, any>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}

export class HybridAnalyticsEventDto {
  @ApiProperty()
  @IsString()
  executionId: string;

  @ApiProperty()
  @IsString()
  nodeId: string;

  @ApiProperty()
  @IsString()
  metricType: 'performance' | 'error_rate' | 'usage_pattern' | 'resource_utilization';

  @ApiProperty()
  metricValue: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  dimensions?: Record<string, any>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsObject()
  tags?: Record<string, string>;

  @ApiProperty()
  @IsNumber()
  timestamp: number;
}