/**
 * 🎯 Centralized API Handler
 * Production-grade API route handler with authentication, validation, and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema } from 'zod';
import { Role } from '@/lib/types/auth';
import { getAuthenticatedSession, hasRequiredRole, hasPermission } from '@/lib/auth/auth-service';

// Import middleware functions
import {
    AuthenticatedRequest,
    ValidatedRequest,
    ValidationOptions,
    withAuth,
    withValidation,
    withAuthAndValidation,
    unauthorizedResponse,
    forbiddenResponse,
    errorResponse
} from '@/lib/middleware/auth.middleware';
import { handleApiError } from '@/lib/middleware/error.middleware';

export interface ApiHandlerOptions {
    requiredRoles?: Role[];
    requiredPermissions?: string[];
    allowPublic?: boolean;
    validation?: ValidationOptions;
    rateLimit?: {
        requests: number;
        window: number; // in seconds
    };
    cache?: {
        ttl: number; // in seconds
        key?: string;
    };
}

export type ApiHandler = (
    request: AuthenticatedRequest & ValidatedRequest,
    context?: any
) => Promise<NextResponse>;

/**
 * Create an API handler with authentication and validation
 */
export function createApiHandler(
    handler: ApiHandler,
    options: ApiHandlerOptions = {}
) {
    // If public API, only apply validation if needed
    if (options.allowPublic) {
        if (options.validation) {
            return withValidation(handler as any, options.validation);
        }
        return handler;
    }

    // Otherwise apply both auth and validation
    return withAuthAndValidation(
        handler,
        {
            requiredRoles: options.requiredRoles,
            requiredPermissions: options.requiredPermissions
        },
        options.validation
    );
}

/**
 * Create a public API handler (no authentication required)
 */
export const createPublicApi = (handler: ApiHandler, validation?: ValidationOptions) =>
    createApiHandler(handler, { allowPublic: true, validation });

/**
 * Create a developer API handler (requires DEVELOPER role or higher)
 */
export const createDeveloperApi = (handler: ApiHandler, validation?: ValidationOptions) =>
    createApiHandler(handler, { requiredRoles: ['DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN'], validation });

/**
 * Create an admin API handler (requires ORG_ADMIN role or higher)
 */
export const createAdminApi = (handler: ApiHandler, validation?: ValidationOptions) =>
    createApiHandler(handler, { requiredRoles: ['ORG_ADMIN', 'SUPER_ADMIN'], validation });

/**
 * Create a user API handler (requires VIEWER role or higher)
 */
export const createUserApi = (handler: ApiHandler, validation?: ValidationOptions) =>
    createApiHandler(handler, { requiredRoles: ['VIEWER', 'DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN'], validation });

/**
 * Create an API handler that requires specific permissions
 */
export const createPermissionApi = (
    handler: ApiHandler,
    requiredPermissions: string[],
    validation?: ValidationOptions
) =>
    createApiHandler(handler, { requiredPermissions, validation });

/**
 * HTTP method handlers with validation
 */
export const GET = (handler: ApiHandler, options?: ApiHandlerOptions) => ({
    GET: createApiHandler(handler, options)
});

export const POST = (handler: ApiHandler, bodySchema?: ZodSchema, options?: ApiHandlerOptions) => ({
    POST: createApiHandler(handler, {
        ...options,
        validation: {
            ...options?.validation,
            body: bodySchema || options?.validation?.body
        }
    })
});

export const PUT = (handler: ApiHandler, bodySchema?: ZodSchema, options?: ApiHandlerOptions) => ({
    PUT: createApiHandler(handler, {
        ...options,
        validation: {
            ...options?.validation,
            body: bodySchema || options?.validation?.body
        }
    })
});

export const PATCH = (handler: ApiHandler, bodySchema?: ZodSchema, options?: ApiHandlerOptions) => ({
    PATCH: createApiHandler(handler, {
        ...options,
        validation: {
            ...options?.validation,
            body: bodySchema || options?.validation?.body
        }
    })
});

export const DELETE = (handler: ApiHandler, options?: ApiHandlerOptions) => ({
    DELETE: createApiHandler(handler, options)
});

/**
 * Schema types for CRUD operations
 */
export interface CrudSchemas {
    create: ZodSchema;
    update: ZodSchema;
    list?: ZodSchema; // for query params
    params?: ZodSchema; // for route params
}

/**
 * Create a complete CRUD API
 */
export function createCrudApi(handlers: {
    list?: ApiHandler;
    create?: ApiHandler;
    read?: ApiHandler;
    update?: ApiHandler;
    delete?: ApiHandler;
}, schemas: CrudSchemas, options: ApiHandlerOptions = {}) {
    return {
        ...(handlers.list ? GET(handlers.list, {
            ...options,
            validation: { ...options?.validation, query: schemas.list || options?.validation?.query }
        }) : {}),
        ...(handlers.create ? POST(handlers.create, schemas.create, options) : {}),
        ...(handlers.read ? GET(handlers.read, options) : {}),
        ...(handlers.update ? PUT(handlers.update, schemas.update, options) : {}),
        ...(handlers.delete ? DELETE(handlers.delete, options) : {})
    };
}

/**
 * Create a resource API (for single resource operations)
 */
export function createResourceApi(handlers: {
    read?: ApiHandler;
    update?: ApiHandler;
    delete?: ApiHandler;
}, schemas: CrudSchemas, options: ApiHandlerOptions = {}) {
    return {
        ...(handlers.read ? GET(handlers.read, options) : {}),
        ...(handlers.update ? PUT(handlers.update, schemas.update, options) : {}),
        ...(handlers.delete ? DELETE(handlers.delete, options) : {})
    };
}