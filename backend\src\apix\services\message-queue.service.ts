import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject } from "@nestjs/common";
import { Cache } from "cache-manager";
import { EventPriority } from "../dto/apix.dto";
import { EventRouterService } from "./event-router.service";
import { ConnectionManagerService } from "./connection-manager.service";

interface QueuedMessage {
  id: string;
  connectionId: string;
  eventId: string;
  priority: EventPriority;
  attempts: number;
  maxAttempts: number;
  nextRetryAt?: Date;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED";
  error?: string;
  createdAt: Date;
}

interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
}

@Injectable()
export class MessageQueueService {
  private readonly logger = new Logger(MessageQueueService.name);
  private processingQueue = new Map<string, QueuedMessage>();
  private retryTimer: NodeJS.Timeout | null = null;
  private isProcessing = false;

  private readonly defaultRetryConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 300000, // 5 minutes
    backoffMultiplier: 2,
    jitter: true,
  };

  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private eventRouter: EventRouterService,
    private connectionManager: ConnectionManagerService,
  ) {
    this.startRetryProcessor();
  }

  async queueMessage(
    connectionId: string,
    eventId: string,
    priority: EventPriority = EventPriority.NORMAL,
    retryConfig?: Partial<RetryConfig>,
  ): Promise<string> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };

    const queuedMessage = await this.prisma.apiXMessageQueue.create({
      data: {
        connectionId,
        eventId,
        priority,
        maxAttempts: config.maxAttempts,
        status: "PENDING",
      },
    });

    // Cache for quick access
    await this.cacheManager.set(
      `queue:${queuedMessage.id}`,
      queuedMessage,
      3600,
    );

    this.logger.debug(
      `Message queued: ${queuedMessage.id} for connection ${connectionId}`,
    );

    // Trigger immediate processing for high priority messages
    if (
      priority === EventPriority.HIGH ||
      priority === EventPriority.CRITICAL
    ) {
      setImmediate(() => this.processQueue());
    }

    return queuedMessage.id;
  }

  async processQueue(): Promise<void> {
    if (this.isProcessing) return;

    this.isProcessing = true;

    try {
      // Get pending messages ordered by priority and creation time
      const pendingMessages = await this.prisma.apiXMessageQueue.findMany({
        where: {
          status: "PENDING",
          OR: [{ nextRetryAt: null }, { nextRetryAt: { lte: new Date() } }],
        },
        orderBy: [{ priority: "desc" }, { createdAt: "asc" }],
        take: 100, // Process in batches
      });

      for (const message of pendingMessages) {
        await this.processMessage(message);
      }

      this.logger.debug(`Processed ${pendingMessages.length} queued messages`);
    } catch (error) {
      this.logger.error("Error processing message queue:", error);
    } finally {
      this.isProcessing = false;
    }
  }

  async processMessage(queuedMessage: any): Promise<void> {
    try {
      // Mark as processing
      await this.prisma.apiXMessageQueue.update({
        where: { id: queuedMessage.id },
        data: {
          status: "PROCESSING",
          attempts: { increment: 1 },
        },
      });

      // Get the event to send
      const event = await this.prisma.apiXEvent.findUnique({
        where: { id: queuedMessage.eventId },
      });

      if (!event) {
        await this.markMessageFailed(queuedMessage.id, "Event not found");
        return;
      }

      // Check if connection is still active
      const connection = this.connectionManager.getConnection(
        queuedMessage.connectionId,
      );
      if (!connection) {
        await this.markMessageFailed(queuedMessage.id, "Connection not found");
        return;
      }

      // Route the event
      await this.eventRouter.routeEvent({
        id: event.id,
        type: event.eventType,
        channel: event.channel,
        payload: event.payload,
        sessionId: event.sessionId || undefined,
        userId: event.userId || undefined,
        organizationId: event.organizationId || undefined,
        priority: event.priority,
        compressed: event.compressed,
        correlationId: event.correlationId || undefined,
        metadata: event.metadata as Record<string, any>,
        timestamp: event.createdAt.getTime(),
      });

      // Mark as completed
      await this.prisma.apiXMessageQueue.update({
        where: { id: queuedMessage.id },
        data: {
          status: "COMPLETED",
          processedAt: new Date(),
        },
      });

      // Remove from cache
      await this.cacheManager.del(`queue:${queuedMessage.id}`);

      this.logger.debug(`Message processed successfully: ${queuedMessage.id}`);
    } catch (error) {
      await this.handleMessageError(queuedMessage, error);
    }
  }

  async retryMessage(messageId: string): Promise<boolean> {
    const message = await this.prisma.apiXMessageQueue.findUnique({
      where: { id: messageId },
    });

    if (!message || message.status !== "FAILED") {
      return false;
    }

    if (message.attempts >= message.maxAttempts) {
      this.logger.warn(`Message ${messageId} has exceeded max retry attempts`);
      return false;
    }

    const nextRetryAt = this.calculateNextRetryTime(message.attempts);

    await this.prisma.apiXMessageQueue.update({
      where: { id: messageId },
      data: {
        status: "PENDING",
        nextRetryAt,
        error: null,
      },
    });

    this.logger.log(
      `Message ${messageId} scheduled for retry at ${nextRetryAt}`,
    );
    return true;
  }

  async cancelMessage(messageId: string): Promise<boolean> {
    try {
      await this.prisma.apiXMessageQueue.update({
        where: { id: messageId },
        data: {
          status: "CANCELLED",
          processedAt: new Date(),
        },
      });

      await this.cacheManager.del(`queue:${messageId}`);

      this.logger.log(`Message cancelled: ${messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel message ${messageId}:`, error);
      return false;
    }
  }

  async getQueueStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    cancelled: number;
    totalByPriority: Record<EventPriority, number>;
    averageProcessingTime: number;
    successRate: number;
    throughputPerHour: number;
    oldestPendingMessage: Date | null;
  }> {
    const [statusCounts, priorityCounts, processingStats] = await Promise.all([
      this.prisma.apiXMessageQueue.groupBy({
        by: ["status"],
        _count: { status: true },
      }),
      this.prisma.apiXMessageQueue.groupBy({
        by: ["priority"],
        _count: { priority: true },
      }),
      this.prisma.apiXMessageQueue.aggregate({
        where: {
          status: "COMPLETED",
          processedAt: { not: null },
        },
        _avg: {
          attempts: true,
        },
      }),
    ]);

    // Get oldest pending message
    const oldestPending = await this.prisma.apiXMessageQueue.findFirst({
      where: { status: "PENDING" },
      orderBy: { createdAt: "asc" },
      select: { createdAt: true },
    });

    // Calculate throughput (messages processed in last hour)
    const oneHourAgo = new Date(Date.now() - 3600000);
    const recentlyProcessed = await this.prisma.apiXMessageQueue.count({
      where: {
        status: "COMPLETED",
        processedAt: { gte: oneHourAgo },
      },
    });

    const stats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      totalByPriority: {} as Record<EventPriority, number>,
      averageProcessingTime: processingStats._avg.attempts || 0,
      successRate: 0,
      throughputPerHour: recentlyProcessed,
      oldestPendingMessage: oldestPending?.createdAt || null,
    };

    // Initialize priority counts
    Object.values(EventPriority).forEach((priority) => {
      stats.totalByPriority[priority] = 0;
    });

    // Populate status counts
    for (const count of statusCounts) {
      const status = count.status.toLowerCase();
      if (status in stats) {
        (stats as any)[status] = count._count.status;
      }
    }

    // Populate priority counts
    for (const count of priorityCounts) {
      stats.totalByPriority[count.priority as EventPriority] =
        count._count.priority;
    }

    // Calculate success rate
    const totalProcessed = stats.completed + stats.failed;
    stats.successRate =
      totalProcessed > 0 ? (stats.completed / totalProcessed) * 100 : 0;

    return stats;
  }

  async getConnectionQueueStats(connectionId: string): Promise<{
    pending: number;
    processing: number;
    failed: number;
    avgRetryAttempts: number;
  }> {
    const [statusCounts, avgAttempts] = await Promise.all([
      this.prisma.apiXMessageQueue.groupBy({
        by: ["status"],
        where: { connectionId },
        _count: { status: true },
      }),
      this.prisma.apiXMessageQueue.aggregate({
        where: { connectionId },
        _avg: { attempts: true },
      }),
    ]);

    const stats = {
      pending: 0,
      processing: 0,
      failed: 0,
      avgRetryAttempts: avgAttempts._avg.attempts || 0,
    };

    for (const count of statusCounts) {
      const status = count.status.toLowerCase();
      if (status in stats) {
        (stats as any)[status] = count._count.status;
      }
    }

    return stats;
  }

  async cleanupOldMessages(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.prisma.apiXMessageQueue.deleteMany({
      where: {
        createdAt: { lt: cutoffDate },
        status: { in: ["COMPLETED", "CANCELLED"] },
      },
    });

    this.logger.log(`Cleaned up ${result.count} old queue messages`);
    return result.count;
  }

  async purgeFailedMessages(connectionId?: string): Promise<number> {
    const where: any = { status: "FAILED" };
    if (connectionId) {
      where.connectionId = connectionId;
    }

    const result = await this.prisma.apiXMessageQueue.deleteMany({ where });

    this.logger.log(`Purged ${result.count} failed messages`);
    return result.count;
  }

  private async handleMessageError(
    queuedMessage: any,
    error: any,
  ): Promise<void> {
    const errorMessage = error.message || error.toString();

    if (queuedMessage.attempts >= queuedMessage.maxAttempts) {
      await this.markMessageFailed(queuedMessage.id, errorMessage);
    } else {
      // Schedule retry
      const nextRetryAt = this.calculateNextRetryTime(queuedMessage.attempts);

      await this.prisma.apiXMessageQueue.update({
        where: { id: queuedMessage.id },
        data: {
          status: "PENDING",
          nextRetryAt,
          error: errorMessage,
        },
      });

      this.logger.warn(
        `Message ${queuedMessage.id} failed (attempt ${queuedMessage.attempts}/${queuedMessage.maxAttempts}), retrying at ${nextRetryAt}: ${errorMessage}`,
      );
    }
  }

  private async markMessageFailed(
    messageId: string,
    error: string,
  ): Promise<void> {
    await this.prisma.apiXMessageQueue.update({
      where: { id: messageId },
      data: {
        status: "FAILED",
        error,
        processedAt: new Date(),
      },
    });

    await this.cacheManager.del(`queue:${messageId}`);

    this.logger.error(`Message failed permanently: ${messageId} - ${error}`);
  }

  private calculateNextRetryTime(attempts: number): Date {
    const config = this.defaultRetryConfig;
    let delay =
      config.baseDelay * Math.pow(config.backoffMultiplier, attempts - 1);

    // Cap the delay
    delay = Math.min(delay, config.maxDelay);

    // Add jitter to prevent thundering herd
    if (config.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    return new Date(Date.now() + delay);
  }

  private startRetryProcessor(): void {
    // Process queue every 30 seconds
    this.retryTimer = setInterval(() => {
      this.processQueue().catch((error) => {
        this.logger.error("Error in retry processor:", error);
      });
    }, 30000);

    // Cleanup old messages daily
    setInterval(
      () => {
        this.cleanupOldMessages().catch((error) => {
          this.logger.error("Error cleaning up old messages:", error);
        });
      },
      24 * 60 * 60 * 1000,
    ); // 24 hours
  }

  onModuleDestroy(): void {
    if (this.retryTimer) {
      clearInterval(this.retryTimer);
    }
  }
}
