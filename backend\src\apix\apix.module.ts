import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ApixGateway } from "./apix.gateway";
import { EventRouterService } from "./services/event-router.service";
import { SubscriptionManagerService } from "./services/subscription-manager.service";
import { MessageQueueService } from "./services/message-queue.service";
import { ConnectionManagerService } from "./services/connection-manager.service";
import { PrismaModule } from "../prisma/prisma.module";
import { JwtModule } from "@nestjs/jwt";
import { CacheModule } from "@nestjs/cache-manager";
import { ConfigModule, ConfigService } from "@nestjs/config";
import * as redisStore from "cache-manager-redis-store";

@Module({
  imports: [
    PrismaModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>("JWT_SECRET"),
        signOptions: { expiresIn: "24h" },
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        store: redisStore,
        host: configService.get<string>("REDIS_HOST", "localhost"),
        port: configService.get<number>("REDIS_PORT", 6379),
        password: configService.get<string>("REDIS_PASSWORD"),
        db: configService.get<number>("REDIS_DB", 0),
        ttl: 3600, // 1 hour default TTL
        max: 10000, // Maximum number of items in cache
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    ApixGateway,
    EventRouterService,
    SubscriptionManagerService,
    MessageQueueService,
    ConnectionManagerService,
  ],
  exports: [
    ApixGateway,
    EventRouterService,
    SubscriptionManagerService,
    MessageQueueService,
    ConnectionManagerService,
  ],
})
export class ApixModule {}
