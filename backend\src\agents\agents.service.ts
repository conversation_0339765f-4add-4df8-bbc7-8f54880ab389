import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AgentStatus, TaskStatus } from '@prisma/client';

@Injectable()
export class AgentsService {
  constructor(private prisma: PrismaService) {}

  async findAll(organizationId: string, filters?: any) {
    const where: any = { organizationId };
    
    if (filters?.status) {
      where.status = filters.status;
    }
    
    if (filters?.type) {
      where.type = filters.type;
    }
    
    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }
    
    const [agents, total] = await Promise.all([
      this.prisma.agentInstance.findMany({
        where,
        include: {
          template: {
            select: { id: true, name: true, category: true },
          },
          creator: {
            select: { id: true, firstName: true, lastName: true },
          },
          _count: {
            select: {
              sessions: true,
              tasks: true,
              conversations: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: filters?.limit || 20,
        skip: filters?.offset || 0,
      }),
      this.prisma.agentInstance.count({ where }),
    ]);
    
    return {
      agents,
      total,
      hasMore: (filters?.offset || 0) + agents.length < total,
    };
  }

  async findOne(id: string, organizationId: string) {
    const agent = await this.prisma.agentInstance.findFirst({
      where: { id, organizationId },
      include: {
        template: true,
        creator: {
          select: { id: true, firstName: true, lastName: true, email: true },
        },
        sessions: {
          take: 5,
          orderBy: { createdAt: 'desc' },
        },
        tasks: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        metrics: {
          take: 30,
          orderBy: { date: 'desc' },
        },
        _count: {
          select: {
            sessions: true,
            tasks: true,
            conversations: true,
            collaborations: true,
          },
        },
      },
    });
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    return agent;
  }

  async getAgentSessions(agentId: string, organizationId: string, options?: { limit?: number; offset?: number }) {
    // Verify agent belongs to organization
    const agent = await this.prisma.agentInstance.findFirst({
      where: { id: agentId, organizationId },
    });
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    const [sessions, total] = await Promise.all([
      this.prisma.agentSessionNew.findMany({
        where: { agentId },
        orderBy: { createdAt: 'desc' },
        take: options?.limit || 20,
        skip: options?.offset || 0,
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true },
          },
          _count: {
            select: { messages: true, tasks: true },
          },
        },
      }),
      this.prisma.agentSessionNew.count({ where: { agentId } }),
    ]);
    
    return {
      sessions,
      total,
      hasMore: (options?.offset || 0) + sessions.length < total,
    };
  }

  async getAgentTasks(agentId: string, organizationId: string, options?: { status?: string; limit?: number; offset?: number }) {
    // Verify agent belongs to organization
    const agent = await this.prisma.agentInstance.findFirst({
      where: { id: agentId, organizationId },
    });
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    const where: any = { agentId };
    
    if (options?.status) {
      where.status = options.status as TaskStatus;
    }
    
    const [tasks, total] = await Promise.all([
      this.prisma.agentTask.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: options?.limit || 20,
        skip: options?.offset || 0,
      }),
      this.prisma.agentTask.count({ where }),
    ]);
    
    return {
      tasks,
      total,
      hasMore: (options?.offset || 0) + tasks.length < total,
    };
  }

  async getAgentCollaborations(agentId: string, organizationId: string, options?: { status?: string; limit?: number; offset?: number }) {
    // Verify agent belongs to organization
    const agent = await this.prisma.agentInstance.findFirst({
      where: { id: agentId, organizationId },
    });
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    const where: any = {
      OR: [
        { coordinatorId: agentId },
        { members: { some: { agentId } } },
      ],
    };
    
    if (options?.status) {
      where.status = options.status;
    }
    
    const [collaborations, total] = await Promise.all([
      this.prisma.agentCollaboration.findMany({
        where,
        include: {
          coordinator: {
            select: { id: true, name: true },
          },
          members: {
            include: {
              agent: {
                select: { id: true, name: true, status: true },
              },
            },
          },
          tasks: {
            take: 5,
            orderBy: { createdAt: 'desc' },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: options?.limit || 20,
        skip: options?.offset || 0,
      }),
      this.prisma.agentCollaboration.count({ where }),
    ]);
    
    return {
      collaborations,
      total,
      hasMore: (options?.offset || 0) + collaborations.length < total,
    };
  }

  async getAgentMetrics(agentId: string, organizationId: string, dateRange?: { from: Date; to: Date }) {
    // Verify agent belongs to organization
    const agent = await this.prisma.agentInstance.findFirst({
      where: { id: agentId, organizationId },
    });
    
    if (!agent) {
      throw new NotFoundException('Agent not found');
    }
    
    const where: any = { agentId };
    
    if (dateRange) {
      where.date = {
        gte: dateRange.from,
        lte: dateRange.to,
      };
    }
    
    const metrics = await this.prisma.agentMetrics.findMany({
      where,
      orderBy: { date: 'desc' },
    });
    
    // Calculate aggregated metrics
    const aggregated = metrics.reduce((agg, metric) => {
      agg.totalConversations += metric.conversations;
      agg.totalMessages += metric.messages;
      agg.totalTokens += metric.totalTokens;
      agg.totalCost += metric.totalCost || 0;
      
      if (metric.avgResponseTime) {
        agg.responseTimes.push(metric.avgResponseTime);
      }
      
      if (metric.successRate) {
        agg.successRates.push(metric.successRate);
      }
      
      if (metric.errorRate) {
        agg.errorRates.push(metric.errorRate);
      }
      
      return agg;
    }, {
      totalConversations: 0,
      totalMessages: 0,
      totalTokens: 0,
      totalCost: 0,
      responseTimes: [],
      successRates: [],
      errorRates: [],
    });
    
    // Calculate averages
    const avgResponseTime = aggregated.responseTimes.length > 0
      ? aggregated.responseTimes.reduce((sum, time) => sum + time, 0) / aggregated.responseTimes.length
      : null;
      
    const avgSuccessRate = aggregated.successRates.length > 0
      ? aggregated.successRates.reduce((sum, rate) => sum + rate, 0) / aggregated.successRates.length
      : null;
      
    const avgErrorRate = aggregated.errorRates.length > 0
      ? aggregated.errorRates.reduce((sum, rate) => sum + rate, 0) / aggregated.errorRates.length
      : null;
    
    return {
      metrics,
      aggregated: {
        ...aggregated,
        avgResponseTime,
        avgSuccessRate,
        avgErrorRate,
      },
    };
  }
}