import { NextRequest, NextResponse } from 'next/server';
import  apiClient  from '@/lib/api-client';
import { Tool } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

      const response = await apiClient.post(`/tools/${params.id}/test`, body);

    const data = await response as Tool;
    return NextResponse.json(data);
  } catch (error) {
    console.error('Test Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}