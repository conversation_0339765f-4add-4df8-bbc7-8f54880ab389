import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
    HealthService,
    SystemHealthDto,
    ServiceStatusDto,
    MetricsDto
} from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
    constructor(private readonly healthService: HealthService) { }

    @Get()
    @ApiOperation({ summary: 'Get comprehensive system health status' })
    @ApiResponse({ status: 200, description: 'System health retrieved successfully' })
    async getHealth(): Promise<SystemHealthDto> {
        return this.healthService.getSystemHealth();
    }

    @Get('check')
    @ApiOperation({ summary: 'Simple health check endpoint' })
    @ApiResponse({ status: 200, description: 'Health check successful' })
    async healthCheck(): Promise<{ status: string; timestamp: string }> {
        const isHealthy = await this.healthService.isHealthy();
        return {
            status: isHealthy ? 'ok' : 'error',
            timestamp: new Date().toISOString(),
        };
    }

    @Get('ready')
    @ApiOperation({ summary: 'Readiness probe for Kubernetes' })
    @ApiResponse({ status: 200, description: 'Service is ready to accept traffic' })
    @ApiResponse({ status: 503, description: 'Service is not ready' })
    async readiness(): Promise<{ ready: boolean; checks: any }> {
        return this.healthService.getReadiness();
    }

    @Get('live')
    @ApiOperation({ summary: 'Liveness probe for Kubernetes' })
    @ApiResponse({ status: 200, description: 'Service is alive' })
    async liveness(): Promise<{ alive: boolean; uptime: number }> {
        return this.healthService.getLiveness();
    }

    @Get('services')
    @ApiOperation({ summary: 'Get status of all external services' })
    @ApiResponse({ status: 200, description: 'Service statuses retrieved successfully' })
    async getServiceStatus(): Promise<ServiceStatusDto[]> {
        return this.healthService.getServiceStatus();
    }

    @Get('metrics')
    @ApiOperation({ summary: 'Get system performance metrics' })
    @ApiResponse({ status: 200, description: 'System metrics retrieved successfully' })
    async getMetrics(): Promise<MetricsDto> {
        return this.healthService.getMetrics();
    }
}