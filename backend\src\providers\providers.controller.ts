import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantGuard } from '../auth/tenant.guard';
import { ProvidersService } from './providers.service';
import {
  CreateProviderDto,
  UpdateProviderDto,
  CreateProviderModelDto,
  CreateRoutingRuleDto,
  AiRequestDto,
  ProviderTestDto,
  BulkProviderActionDto,
  ProviderAnalyticsQueryDto,
} from './dto/provider.dto';
import { Role } from '@prisma/client';

@ApiTags('providers')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('providers')
export class ProvidersController {
  constructor(private readonly providersService: ProvidersService) {}

  // Provider CRUD Operations
  @Post()
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Create a new AI provider' })
  @ApiResponse({ status: 201, description: 'Provider created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createProvider(@Request() req, @Body() dto: CreateProviderDto) {
    return this.providersService.createProvider(req.user.organizationId, dto);
  }

  @Get()
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get all providers for organization' })
  @ApiQuery({ name: 'includeInactive', required: false, type: Boolean })
  @ApiResponse({ status: 200, description: 'Providers retrieved successfully' })
  async getProviders(
    @Request() req,
    @Query('includeInactive') includeInactive?: boolean,
  ) {
    return this.providersService.getProviders(req.user.organizationId, includeInactive);
  }

  @Get(':id')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get provider by ID' })
  @ApiResponse({ status: 200, description: 'Provider retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async getProvider(@Request() req, @Param('id') id: string) {
    return this.providersService.getProvider(req.user.organizationId, id);
  }

  @Put(':id')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Update provider' })
  @ApiResponse({ status: 200, description: 'Provider updated successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async updateProvider(
    @Request() req,
    @Param('id') id: string,
    @Body() dto: UpdateProviderDto,
  ) {
    return this.providersService.updateProvider(req.user.organizationId, id, dto);
  }

  @Delete(':id')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete provider' })
  @ApiResponse({ status: 204, description: 'Provider deleted successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async deleteProvider(@Request() req, @Param('id') id: string) {
    await this.providersService.deleteProvider(req.user.organizationId, id);
  }

  // Provider Model Management
  @Post(':id/models')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Add model to provider' })
  @ApiResponse({ status: 201, description: 'Model added successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async createProviderModel(
    @Request() req,
    @Param('id') providerId: string,
    @Body() dto: CreateProviderModelDto,
  ) {
    return this.providersService.createProviderModel(req.user.organizationId, providerId, dto);
  }

  @Get(':id/models')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get models for provider' })
  @ApiResponse({ status: 200, description: 'Models retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async getProviderModels(@Request() req, @Param('id') providerId: string) {
    return this.providersService.getProviderModels(req.user.organizationId, providerId);
  }

  // Smart Routing
  @Post('routing-rules')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Create routing rule' })
  @ApiResponse({ status: 201, description: 'Routing rule created successfully' })
  async createRoutingRule(@Request() req, @Body() dto: CreateRoutingRuleDto) {
    return this.providersService.createRoutingRule(req.user.organizationId, dto);
  }

  @Get('routing-rules')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get routing rules' })
  @ApiResponse({ status: 200, description: 'Routing rules retrieved successfully' })
  async getRoutingRules(@Request() req) {
    return this.providersService.getRoutingRules(req.user.organizationId);
  }

  // AI Request Processing
  @Post('execute')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Execute AI request with smart routing' })
  @ApiResponse({ status: 200, description: 'Request executed successfully' })
  @ApiResponse({ status: 400, description: 'No suitable provider found' })
  async executeAiRequest(@Request() req, @Body() dto: AiRequestDto) {
    return this.providersService.processAiRequest(req.user.organizationId, req.user.id, dto);
  }

  // Provider Testing
  @Post(':id/test')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Test provider with sample request' })
  @ApiResponse({ status: 200, description: 'Provider test completed' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async testProvider(
    @Request() req,
    @Param('id') providerId: string,
    @Body() dto: ProviderTestDto,
  ) {
    return this.providersService.testProvider(req.user.organizationId, providerId, dto);
  }

  // Bulk Operations
  @Post('bulk-action')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Perform bulk action on providers' })
  @ApiResponse({ status: 200, description: 'Bulk action completed' })
  @ApiResponse({ status: 400, description: 'Some providers not found' })
  async bulkProviderAction(@Request() req, @Body() dto: BulkProviderActionDto) {
    return this.providersService.bulkProviderAction(req.user.organizationId, dto);
  }

  // Analytics and Reporting
  @Get('analytics/usage')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get provider usage analytics' })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'providerIds', required: false, type: [String] })
  @ApiQuery({ name: 'modelIds', required: false, type: [String] })
  @ApiQuery({ name: 'executorTypes', required: false, type: [String] })
  @ApiQuery({ name: 'groupBy', required: false, enum: ['provider', 'model', 'executorType', 'date', 'hour'] })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getProviderAnalytics(@Request() req, @Query() query: ProviderAnalyticsQueryDto) {
    return this.providersService.getProviderAnalytics(req.user.organizationId, query);
  }

  // Health Monitoring
  @Get('health/status')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get provider health status' })
  @ApiResponse({ status: 200, description: 'Health status retrieved successfully' })
  async getProviderHealthStatus(@Request() req) {
    const providers = await this.providersService.getProviders(req.user.organizationId);
    
    const healthStatus = providers.map(provider => ({
      id: provider.id,
      name: provider.name,
      type: provider.type,
      isActive: provider.isActive,
      health: provider.healthMetrics[0] || null,
      modelsCount: provider.models.length,
      usageCount: provider._count.usageLogs,
    }));

    return {
      providers: healthStatus,
      summary: {
        total: providers.length,
        active: providers.filter(p => p.isActive).length,
        healthy: providers.filter(p => p.healthMetrics[0]?.status === 'HEALTHY').length,
        unhealthy: providers.filter(p => p.healthMetrics[0]?.status === 'UNHEALTHY').length,
      },
    };
  }

  @Get(':id/health/history')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get provider health history' })
  @ApiQuery({ name: 'hours', required: false, type: Number, description: 'Hours of history to retrieve (default: 24)' })
  @ApiResponse({ status: 200, description: 'Health history retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Provider not found' })
  async getProviderHealthHistory(
    @Request() req,
    @Param('id') providerId: string,
    @Query('hours') hours = 24,
  ) {
    const provider = await this.providersService.getProvider(req.user.organizationId, providerId);
    
    const healthHistory = await this.providersService['prisma'].providerHealth.findMany({
      where: {
        providerId,
        createdAt: {
          gte: new Date(Date.now() - hours * 60 * 60 * 1000),
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    return {
      provider: {
        id: provider.id,
        name: provider.name,
        type: provider.type,
      },
      history: healthHistory,
      summary: {
        totalChecks: healthHistory.length,
        avgUptime: healthHistory.length > 0 
          ? healthHistory.reduce((sum, h) => sum + h.uptime, 0) / healthHistory.length 
          : 0,
        avgLatency: healthHistory.length > 0 
          ? healthHistory.reduce((sum, h) => sum + h.avgLatencyMs, 0) / healthHistory.length 
          : 0,
        avgErrorRate: healthHistory.length > 0 
          ? healthHistory.reduce((sum, h) => sum + h.errorRate, 0) / healthHistory.length 
          : 0,
      },
    };
  }

  // Usage Logs
  @Get('usage/logs')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get AI usage logs' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'providerId', required: false, type: String })
  @ApiQuery({ name: 'modelId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT'] })
  @ApiQuery({ name: 'executorType', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Usage logs retrieved successfully' })
  async getUsageLogs(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
    @Query('providerId') providerId?: string,
    @Query('modelId') modelId?: string,
    @Query('status') status?: string,
    @Query('executorType') executorType?: string,
  ) {
    const skip = (page - 1) * limit;
    const where: any = {
      organizationId: req.user.organizationId,
      ...(providerId && { providerId }),
      ...(modelId && { modelId }),
      ...(status && { status }),
      ...(executorType && { executorType }),
    };

    const [logs, total] = await Promise.all([
      this.providersService['prisma'].aiUsageLog.findMany({
        where,
        include: {
          provider: {
            select: { id: true, name: true, type: true },
          },
          model: {
            select: { id: true, modelId: true, displayName: true },
          },
          user: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.providersService['prisma'].aiUsageLog.count({ where }),
    ]);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  // Cost Analysis
  @Get('analytics/costs')
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Get cost analysis' })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiQuery({ name: 'groupBy', required: false, enum: ['provider', 'model', 'user', 'executorType', 'date'] })
  @ApiResponse({ status: 200, description: 'Cost analysis retrieved successfully' })
  async getCostAnalysis(
    @Request() req,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
    @Query('groupBy') groupBy = 'provider',
  ) {
    const where: any = {
      organizationId: req.user.organizationId,
      status: 'COMPLETED',
      ...(startDate && { createdAt: { gte: startDate } }),
      ...(endDate && { createdAt: { lte: endDate } }),
    };

    const logs = await this.providersService['prisma'].aiUsageLog.findMany({
      where,
      include: {
        provider: {
          select: { id: true, name: true, type: true },
        },
        model: {
          select: { id: true, modelId: true, displayName: true },
        },
        user: {
          select: { id: true, firstName: true, lastName: true },
        },
      },
    });

    // Group by specified field
    const grouped = new Map();
    logs.forEach(log => {
      let key;
      switch (groupBy) {
        case 'provider':
          key = log.provider?.name || 'Unknown';
          break;
        case 'model':
          key = log.model?.displayName || 'Unknown';
          break;
        case 'user':
          key = log.user ? `${log.user.firstName} ${log.user.lastName}` : 'System';
          break;
        case 'executorType':
          key = log.executorType;
          break;
        case 'date':
          key = log.createdAt.toISOString().split('T')[0];
          break;
        default:
          key = 'all';
      }

      if (!grouped.has(key)) {
        grouped.set(key, {
          key,
          totalCost: 0,
          inputCost: 0,
          outputCost: 0,
          totalTokens: 0,
          inputTokens: 0,
          outputTokens: 0,
          requests: 0,
        });
      }

      const group = grouped.get(key);
      group.totalCost += log.totalCost;
      group.inputCost += log.inputCost;
      group.outputCost += log.outputCost;
      group.totalTokens += log.totalTokens;
      group.inputTokens += log.inputTokens;
      group.outputTokens += log.outputTokens;
      group.requests++;
    });

    const costData = Array.from(grouped.values()).sort((a, b) => b.totalCost - a.totalCost);

    return {
      summary: {
        totalCost: logs.reduce((sum, log) => sum + log.totalCost, 0),
        totalRequests: logs.length,
        totalTokens: logs.reduce((sum, log) => sum + log.totalTokens, 0),
        avgCostPerRequest: logs.length > 0 ? logs.reduce((sum, log) => sum + log.totalCost, 0) / logs.length : 0,
        avgCostPerToken: logs.reduce((sum, log) => sum + log.totalTokens, 0) > 0 
          ? logs.reduce((sum, log) => sum + log.totalCost, 0) / logs.reduce((sum, log) => sum + log.totalTokens, 0)
          : 0,
      },
      breakdown: costData,
    };
  }
}