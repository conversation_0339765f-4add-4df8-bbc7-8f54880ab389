import { PrismaService } from "../../prisma/prisma.service";
import { Cache } from "cache-manager";
import { EventPriority } from "../dto/apix.dto";
import { ConnectionManagerService } from "./connection-manager.service";
import { Server } from "socket.io";
interface RouteableEvent {
    id?: string;
    type: string;
    channel: string;
    payload: any;
    sessionId?: string;
    userId?: string;
    organizationId?: string;
    priority: EventPriority;
    compressed?: boolean;
    correlationId?: string;
    metadata?: Record<string, any>;
    timestamp: number;
}
interface EventFilter {
    eventTypes?: string[];
    channels?: string[];
    userId?: string;
    organizationId?: string;
    sessionId?: string;
    since?: Date;
    limit?: number;
}
export declare class EventRouterService {
    private prisma;
    private cacheManager;
    private connectionManager;
    private readonly logger;
    private server;
    private eventHistory;
    private compressionThreshold;
    private maxEventHistory;
    constructor(prisma: PrismaService, cacheManager: Cache, connectionManager: ConnectionManagerService);
    setServer(server: Server): void;
    routeEvent(event: RouteableEvent): Promise<void>;
    routeToUser(userId: string, event: RouteableEvent): Promise<void>;
    routeToOrganization(organizationId: string, event: RouteableEvent): Promise<void>;
    routeToChannel(channel: string, event: RouteableEvent): Promise<void>;
    routeToSession(sessionId: string, event: RouteableEvent): Promise<void>;
    getEventHistory(filter: EventFilter): Promise<RouteableEvent[]>;
    replayEvents(socketId: string, filter: EventFilter): Promise<void>;
    broadcastSystemEvent(event: Omit<RouteableEvent, "timestamp">): Promise<void>;
    getEventStats(organizationId?: string): {
        totalEvents: number;
        eventsByType: Record<string, number>;
        eventsByChannel: Record<string, number>;
        eventsByPriority: Record<EventPriority, number>;
    };
    private storeEvent;
    private addToHistory;
    private routeToChannels;
    private routeToTargets;
    private sendEventToSocket;
    private prepareEventForTransmission;
    private shouldCompress;
}
export {};
