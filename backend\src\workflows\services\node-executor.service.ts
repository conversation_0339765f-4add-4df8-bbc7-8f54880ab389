import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { AgentsService } from '../../agents/agents.service';
import { ToolsService } from '../../tools/tools.service';

export interface NodeExecutionContext {
  executionId: string;
  nodeId: string;
  organizationId: string;
  userId: string;
}

@Injectable()
export class NodeExecutorService {
  private readonly logger = new Logger(NodeExecutorService.name);

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private agentsService: AgentsService,
    private toolsService: ToolsService,
  ) { }

  async executeNode(
    nodeType: string,
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    this.logger.log(`Executing ${nodeType} node: ${context.nodeId}`);

    switch (nodeType) {
      case 'agent':
        return this.executeAgentNode(config, variables, context);

      case 'tool':
        return this.executeToolNode(config, variables, context);

      case 'condition':
        return this.executeConditionNode(config, variables, context);

      case 'parallel':
        return this.executeParallelNode(config, variables, context);

      case 'human_input':
        return this.executeHumanInputNode(config, variables, context);

      case 'delay':
        return this.executeDelayNode(config, variables, context);

      case 'hybrid':
        return this.executeHybridNode(config, variables, context);

      default:
        throw new Error(`Unknown node type: ${nodeType}`);
    }
  }

  private async executeAgentNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { agentId, systemPrompt, maxTokens, temperature } = config;

    if (!agentId) {
      throw new Error('Agent ID is required');
    }

    // Get agent
    const agent = await this.prisma.agent.findFirst({
      where: { id: agentId, organizationId: context.organizationId },
    });

    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    // Emit thinking status
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'agent_thinking',
      {
        nodeId: context.nodeId,
        agentId,
        status: 'processing',
        progress: 0,
      }
    );

    try {
      // Execute agent
      const result = await this.agentsService.executeAgent(
        agentId,
        {
          input: variables.input || variables.message || 'Process the given data',
          systemPrompt: systemPrompt || agent.systemPrompt,
          maxTokens: maxTokens || 2000,
          temperature: temperature || 0.7,
          variables,
        },
        context.organizationId
      );

      // Emit agent response
      await this.apixGateway.emitToRoom(
        `workflow:${context.executionId}`,
        'agent_response',
        {
          nodeId: context.nodeId,
          agentId,
          response: result.response,
          usage: result.usage,
        }
      );

      return {
        response: result.response,
        usage: result.usage,
        agentId,
      };

    } catch (error) {
      await this.apixGateway.emitToRoom(
        `workflow:${context.executionId}`,
        'agent_error',
        {
          nodeId: context.nodeId,
          agentId,
          error: error.message,
        }
      );
      throw error;
    }
  }

  private async executeToolNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { toolId, parameters, timeout } = config;

    if (!toolId) {
      throw new Error('Tool ID is required');
    }

    // Get tool
    const tool = await this.prisma.tool.findFirst({
      where: { id: toolId, organizationId: context.organizationId },
    });

    if (!tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    // Emit tool call start
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'tool_call_start',
      {
        nodeId: context.nodeId,
        toolId,
        input: parameters,
      }
    );

    try {
      // Execute tool with timeout
      const timeoutMs = timeout || 30000;
      const result = await Promise.race([
        this.toolsService.executeTool(
          toolId,
          { ...parameters, ...variables },
          {
            organizationId: context.organizationId,
            executorType: 'workflow',
            executorId: context.executionId,
            sessionId: context.sessionId,
          }
        ),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Tool execution timeout')), timeoutMs)
        )
      ]);

      // Emit tool call result
      await this.apixGateway.emitToRoom(
        `workflow:${context.executionId}`,
        'tool_call_result',
        {
          nodeId: context.nodeId,
          toolId,
          result,
        }
      );

      return result;

    } catch (error) {
      await this.apixGateway.emitToRoom(
        `workflow:${context.executionId}`,
        'tool_call_error',
        {
          nodeId: context.nodeId,
          toolId,
          error: error.message,
        }
      );
      throw error;
    }
  }

  private async executeConditionNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { condition, conditionType, truePath, falsePath } = config;

    if (!condition) {
      throw new Error('Condition is required');
    }

    let result: boolean;

    try {
      switch (conditionType) {
        case 'javascript':
          // Execute JavaScript condition
          const func = new Function('variables', `return ${condition}`);
          result = Boolean(func(variables));
          break;

        case 'jsonpath':
          // Execute JSONPath condition (simplified)
          const jsonpath = require('jsonpath');
          const value = jsonpath.query(variables, condition);
          result = Boolean(value && value.length > 0);
          break;

        case 'simple':
        default:
          // Execute simple condition (variable comparison)
          result = this.evaluateSimpleCondition(condition, variables);
          break;
      }

      // Emit condition result
      await this.apixGateway.emitToRoom(
        `workflow:${context.executionId}`,
        'condition_evaluated',
        {
          nodeId: context.nodeId,
          condition,
          result,
          nextPath: result ? truePath : falsePath,
        }
      );

      return {
        conditionResult: result,
        nextPath: result ? truePath : falsePath,
      };

    } catch (error) {
      throw new Error(`Condition evaluation failed: ${error.message}`);
    }
  }

  private evaluateSimpleCondition(condition: string, variables: Record<string, any>): boolean {
    // Parse simple conditions like: ${variable} == "value"
    const match = condition.match(/\$\{([^}]+)\}\s*(==|!=|>|<|>=|<=)\s*(.+)/);

    if (!match) {
      throw new Error('Invalid simple condition format');
    }

    const [, variablePath, operator, valueStr] = match;
    const variableValue = this.getNestedValue(variables, variablePath);

    // Parse value (handle strings, numbers, booleans)
    let compareValue: any = valueStr.trim();
    if (compareValue.startsWith('"') && compareValue.endsWith('"')) {
      compareValue = compareValue.slice(1, -1);
    } else if (compareValue === 'true') {
      compareValue = true;
    } else if (compareValue === 'false') {
      compareValue = false;
    } else if (!isNaN(Number(compareValue))) {
      compareValue = Number(compareValue);
    }

    // Evaluate condition
    switch (operator) {
      case '==': return variableValue == compareValue;
      case '!=': return variableValue != compareValue;
      case '>': return variableValue > compareValue;
      case '<': return variableValue < compareValue;
      case '>=': return variableValue >= compareValue;
      case '<=': return variableValue <= compareValue;
      default: throw new Error(`Unknown operator: ${operator}`);
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private async executeParallelNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { nodes, executionMode, aggregateResults } = config;

    if (!nodes || nodes.length === 0) {
      throw new Error('Parallel node requires at least one sub-node');
    }

    // Emit parallel execution start
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'parallel_started',
      {
        nodeId: context.nodeId,
        nodeCount: nodes.length,
        executionMode,
      }
    );

    const enabledNodes = nodes.filter((node: any) => node.enabled);
    const promises = enabledNodes.map(async (node: any) => {
      try {
        // Execute each parallel node
        const result = await this.executeNode(
          node.type || 'tool',
          node.config || {},
          variables,
          { ...context, nodeId: node.id }
        );
        return { nodeId: node.id, result, success: true };
      } catch (error) {
        return { nodeId: node.id, error: error.message, success: false };
      }
    });

    let results: any[];

    switch (executionMode) {
      case 'all':
        results = await Promise.all(promises);
        break;

      case 'race':
        const firstResult = await Promise.race(promises);
        results = [firstResult];
        break;

      case 'some':
        // Wait for a specific number of completions
        const someCount = config.someCount || Math.ceil(enabledNodes.length / 2);
        results = await this.waitForSome(promises, someCount);
        break;

      default:
        results = await Promise.all(promises);
    }

    // Emit parallel execution completed
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'parallel_completed',
      {
        nodeId: context.nodeId,
        results: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
      }
    );

    if (aggregateResults) {
      // Aggregate results based on strategy
      return this.aggregateParallelResults(results, config.aggregationStrategy || 'merge');
    }

    return results;
  }

  private async waitForSome(promises: Promise<any>[], count: number): Promise<any[]> {
    const results: any[] = [];
    const settled = promises.map(async (promise, index) => {
      try {
        const result = await promise;
        return { index, result, success: true };
      } catch (error) {
        return { index, error, success: false };
      }
    });

    for await (const result of settled) {
      results.push(result);
      if (results.filter(r => r.success).length >= count) {
        break;
      }
    }

    return results;
  }

  private aggregateParallelResults(results: any[], strategy: string): any {
    const successfulResults = results.filter(r => r.success).map(r => r.result);

    switch (strategy) {
      case 'merge':
        return successfulResults.reduce((acc, result) => ({ ...acc, ...result }), {});

      case 'array':
        return successfulResults;

      case 'custom':
        // Custom aggregation would be implemented based on config
        return { results: successfulResults, count: successfulResults.length };

      default:
        return { results: successfulResults, count: successfulResults.length };
    }
  }

  private async executeHumanInputNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { prompt, inputType, timeout, allowSkip } = config;

    // Emit human input request
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'request_user_input',
      {
        nodeId: context.nodeId,
        prompt,
        inputType,
        timeout: timeout || 300000, // 5 minutes default
        allowSkip: allowSkip || false,
      }
    );

    // Create a pending human input record
    const humanInput = await this.prisma.workflowHumanInput.create({
      data: {
        executionId: context.executionId,
        nodeId: context.nodeId,
        prompt,
        inputType,
        status: 'PENDING',
        expiresAt: new Date(Date.now() + (timeout || 300000)),
      },
    });

    // Return a promise that resolves when input is provided
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(async () => {
        const updated = await this.prisma.workflowHumanInput.findUnique({
          where: { id: humanInput.id },
        });

        if (updated?.status === 'COMPLETED') {
          clearInterval(checkInterval);
          resolve({
            userInput: updated.response,
            inputType: updated.inputType,
            completedAt: updated.completedAt,
          });
        } else if (updated?.status === 'EXPIRED' || updated?.status === 'CANCELLED') {
          clearInterval(checkInterval);
          if (allowSkip) {
            resolve({ userInput: config.skipValue || null, skipped: true });
          } else {
            reject(new Error('Human input timeout or cancelled'));
          }
        }
      }, 1000); // Check every second

      // Set timeout
      setTimeout(() => {
        clearInterval(checkInterval);
        if (allowSkip) {
          resolve({ userInput: config.skipValue || null, skipped: true });
        } else {
          reject(new Error('Human input timeout'));
        }
      }, timeout || 300000);
    });
  }

  private async executeDelayNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    let { delay, unit, dynamic, variableName } = config;

    // Calculate delay in milliseconds
    let delayMs: number;

    if (dynamic && variableName) {
      delayMs = variables[variableName] || delay || 1000;
    } else {
      switch (unit) {
        case 'seconds':
          delayMs = delay * 1000;
          break;
        case 'minutes':
          delayMs = delay * 60 * 1000;
          break;
        default:
          delayMs = delay;
      }
    }

    // Emit delay start
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'delay_started',
      {
        nodeId: context.nodeId,
        delayMs,
        unit,
      }
    );

    // Wait for the specified delay
    await new Promise(resolve => setTimeout(resolve, delayMs));

    // Emit delay completed
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'delay_completed',
      {
        nodeId: context.nodeId,
        actualDelay: delayMs,
      }
    );

    return {
      delayCompleted: true,
      actualDelay: delayMs,
      unit,
    };
  }

  private async executeHybridNode(
    config: any,
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    const { agentId, toolIds, executionPattern, maxIterations } = config;

    if (!agentId || !toolIds || toolIds.length === 0) {
      throw new Error('Hybrid node requires both agent and tools');
    }

    // Emit hybrid execution start
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'hybrid_started',
      {
        nodeId: context.nodeId,
        agentId,
        toolIds,
        executionPattern,
      }
    );

    let result: any;

    switch (executionPattern) {
      case 'agent-first':
        result = await this.executeAgentFirstPattern(agentId, toolIds, variables, context);
        break;

      case 'tool-first':
        result = await this.executeToolFirstPattern(agentId, toolIds, variables, context);
        break;

      case 'parallel':
        result = await this.executeParallelPattern(agentId, toolIds, variables, context);
        break;

      case 'multi-tool-orchestration':
        result = await this.executeOrchestrationPattern(agentId, toolIds, variables, context, maxIterations || 5);
        break;

      default:
        throw new Error(`Unknown execution pattern: ${executionPattern}`);
    }

    // Emit hybrid execution completed
    await this.apixGateway.emitToRoom(
      `workflow:${context.executionId}`,
      'hybrid_completed',
      {
        nodeId: context.nodeId,
        result,
        pattern: executionPattern,
      }
    );

    return result;
  }

  private async executeAgentFirstPattern(
    agentId: string,
    toolIds: string[],
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    // First, execute the agent
    const agentResult = await this.executeAgentNode(
      { agentId },
      variables,
      context
    );

    // Then execute tools based on agent output
    const toolResults = [];
    for (const toolId of toolIds) {
      try {
        const toolResult = await this.executeToolNode(
          { toolId, parameters: agentResult },
          { ...variables, ...agentResult },
          context
        );
        toolResults.push({ toolId, result: toolResult, success: true });
      } catch (error) {
        toolResults.push({ toolId, error: error.message, success: false });
      }
    }

    return {
      agentResult,
      toolResults,
      pattern: 'agent-first',
    };
  }

  private async executeToolFirstPattern(
    agentId: string,
    toolIds: string[],
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    // First, execute all tools
    const toolResults = [];
    for (const toolId of toolIds) {
      try {
        const toolResult = await this.executeToolNode(
          { toolId, parameters: variables },
          variables,
          context
        );
        toolResults.push({ toolId, result: toolResult, success: true });
      } catch (error) {
        toolResults.push({ toolId, error: error.message, success: false });
      }
    }

    // Aggregate tool results
    const aggregatedData = toolResults
      .filter(r => r.success)
      .reduce((acc, r) => ({ ...acc, ...r.result }), {});

    // Then execute agent with tool results
    const agentResult = await this.executeAgentNode(
      { agentId },
      { ...variables, toolResults: aggregatedData },
      context
    );

    return {
      toolResults,
      agentResult,
      pattern: 'tool-first',
    };
  }

  private async executeParallelPattern(
    agentId: string,
    toolIds: string[],
    variables: Record<string, any>,
    context: NodeExecutionContext
  ): Promise<any> {
    // Execute agent and tools in parallel
    const promises = [
      this.executeAgentNode({ agentId }, variables, context),
      ...toolIds.map(toolId =>
        this.executeToolNode({ toolId, parameters: variables }, variables, context)
      ),
    ];

    const results = await Promise.allSettled(promises);

    const agentResult = results[0].status === 'fulfilled' ? results[0].value : null;
    const toolResults = results.slice(1).map((result, index) => ({
      toolId: toolIds[index],
      result: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null,
      success: result.status === 'fulfilled',
    }));

    return {
      agentResult,
      toolResults,
      pattern: 'parallel',
    };
  }

  private async executeOrchestrationPattern(
    agentId: string,
    toolIds: string[],
    variables: Record<string, any>,
    context: NodeExecutionContext,
    maxIterations: number
  ): Promise<any> {
    let currentVariables = { ...variables };
    const executionHistory = [];

    for (let iteration = 0; iteration < maxIterations; iteration++) {
      // Agent decides what to do next
      const agentResult = await this.executeAgentNode(
        {
          agentId,
          systemPrompt: `You are orchestrating tools. Available tools: ${toolIds.join(', ')}. 
                        Current data: ${JSON.stringify(currentVariables)}. 
                        Decide which tool to use next or if the task is complete.`
        },
        currentVariables,
        context
      );

      executionHistory.push({
        iteration,
        type: 'agent',
        result: agentResult,
      });

      // Parse agent decision (simplified)
      const decision = this.parseAgentDecision(agentResult.response, toolIds);

      if (decision.complete) {
        break;
      }

      if (decision.toolId) {
        try {
          const toolResult = await this.executeToolNode(
            { toolId: decision.toolId, parameters: decision.parameters || {} },
            currentVariables,
            context
          );

          executionHistory.push({
            iteration,
            type: 'tool',
            toolId: decision.toolId,
            result: toolResult,
          });

          // Update variables with tool result
          currentVariables = { ...currentVariables, ...toolResult };
        } catch (error) {
          executionHistory.push({
            iteration,
            type: 'tool',
            toolId: decision.toolId,
            error: error.message,
          });
        }
      }
    }

    return {
      finalResult: currentVariables,
      executionHistory,
      iterations: executionHistory.length,
      pattern: 'multi-tool-orchestration',
    };
  }

  private parseAgentDecision(response: string, availableTools: string[]): {
    complete: boolean;
    toolId?: string;
    parameters?: any;
  } {
    // Simplified decision parsing - in production, this would be more sophisticated
    const lowerResponse = response.toLowerCase();

    if (lowerResponse.includes('complete') || lowerResponse.includes('done') || lowerResponse.includes('finished')) {
      return { complete: true };
    }

    for (const toolId of availableTools) {
      if (lowerResponse.includes(toolId.toLowerCase())) {
        return {
          complete: false,
          toolId,
          parameters: {}, // Would extract parameters from response
        };
      }
    }

    return { complete: true }; // Default to complete if no tool is mentioned
  }
}