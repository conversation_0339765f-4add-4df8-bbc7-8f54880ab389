import { Strategy } from 'passport-jwt';
import { PrismaService } from '../prisma/prisma.service';
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private prisma;
    constructor(prisma: PrismaService);
    validate(payload: any): Promise<{
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        organizationId: string;
        organization: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: import("@prisma/client/runtime/library").JsonValue;
            branding: import("@prisma/client/runtime/library").JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        };
    }>;
}
export {};
