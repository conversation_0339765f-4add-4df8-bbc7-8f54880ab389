import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ToolExecutionStatus, ToolType, ToolCacheStrategy } from '@prisma/client';
import { z } from 'zod';
import * as crypto from 'crypto';
import axios from 'axios';

export interface ToolExecutionContext {
  toolId: string;
  input: any;
  executorType: string;
  executorId?: string;
  sessionId?: string;
  organizationId: string;
  metadata?: any;
  timeout?: number;
  retryPolicy?: any;
}

export interface ToolExecutionResult {
  id: string;
  status: ToolExecutionStatus;
  output?: any;
  error?: string;
  duration?: number;
  tokensUsed?: number;
  cost?: number;
  cached?: boolean;
}

const ExecutionInputSchema = z.object({
  toolId: z.string(),
  input: z.any(),
  executorType: z.string(),
  executorId: z.string().optional(),
  sessionId: z.string().optional(),
  organizationId: z.string(),
  metadata: z.any().optional(),
  timeout: z.number().min(1000).max(300000).optional(),
  retryPolicy: z.any().optional(),
});

@Injectable()
export class ToolExecutionService {
  constructor(private prisma: PrismaService) { }

  // ============================================================================
  // MAIN EXECUTION ENGINE
  // ============================================================================

  async executeTool(context: ToolExecutionContext): Promise<ToolExecutionResult> {
    const validatedContext = ExecutionInputSchema.parse(context);
    const startTime = Date.now();

    // Get tool definition
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id: validatedContext.toolId,
        isActive: true,
        OR: [
          { organizationId: validatedContext.organizationId },
          { isPublic: true },
        ],
      },
      include: {
        versions: {
          where: { isStable: true },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found or not accessible');
    }

    // Create execution record
    const execution = await this.prisma.toolExecution.create({
      data: {
        toolId: validatedContext.toolId,
        executorType: validatedContext.executorType,
        executorId: validatedContext.executorId,
        sessionId: validatedContext.sessionId,
        organizationId: validatedContext.organizationId,
        status: ToolExecutionStatus.PENDING,
        input: validatedContext.input,
        metadata: validatedContext.metadata || {},
        startedAt: new Date(),
      },
    });

    try {
      // Update status to running
      await this.updateExecutionStatus(execution.id, ToolExecutionStatus.RUNNING);

      // Check cache first
      const cacheResult = await this.checkCache(tool, validatedContext.input);
      if (cacheResult) {
        const duration = Date.now() - startTime;

        await this.prisma.toolExecution.update({
          where: { id: execution.id },
          data: {
            status: ToolExecutionStatus.COMPLETED,
            output: cacheResult.output,
            completedAt: new Date(),
            duration,
            cached: true,
          },
        });

        return {
          id: execution.id,
          status: ToolExecutionStatus.COMPLETED,
          output: cacheResult.output,
          duration,
          cached: true,
        };
      }

      // Execute tool based on type
      let result: any;
      const timeout = validatedContext.timeout || tool.timeout || 30000;

      switch (tool.type) {
        case ToolType.FUNCTION_CALL:
          result = await this.executeFunctionCall(tool, validatedContext.input, timeout);
          break;
        case ToolType.API_FETCH:
          result = await this.executeAPIFetch(tool, validatedContext.input, timeout);
          break;
        case ToolType.RAG:
          result = await this.executeRAGQuery(tool, validatedContext.input, timeout);
          break;
        case ToolType.DATABASE:
          result = await this.executeDBQuery(tool, validatedContext.input, timeout);
          break;
        case ToolType.BROWSER_AUTOMATION:
          result = await this.executeBrowserAutomation(tool, validatedContext.input, timeout);
          break;
        case ToolType.CUSTOM_LOGIC:
          result = await this.executeCustomScript(tool, validatedContext.input, timeout);
          break;
        default:
          throw new BadRequestException(`Unsupported tool type: ${tool.type}`);
      }

      const duration = Date.now() - startTime;

      // Update execution with results
      await this.prisma.toolExecution.update({
        where: { id: execution.id },
        data: {
          status: ToolExecutionStatus.COMPLETED,
          output: result.output,
          completedAt: new Date(),
          duration,
          tokensUsed: result.tokensUsed || 0,
          cost: result.cost || 0,
          memoryUsed: result.memoryUsed,
          cpuTime: result.cpuTime,
        },
      });

      // Cache result if applicable
      await this.cacheResult(tool, validatedContext.input, result.output);

      // Update tool metrics
      await this.updateToolMetrics(tool.id, duration, true);

      return {
        id: execution.id,
        status: ToolExecutionStatus.COMPLETED,
        output: result.output,
        duration,
        tokensUsed: result.tokensUsed,
        cost: result.cost,
        cached: false,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      await this.prisma.toolExecution.update({
        where: { id: execution.id },
        data: {
          status: ToolExecutionStatus.FAILED,
          error: errorMessage,
          completedAt: new Date(),
          duration,
        },
      });

      // Update tool metrics
      await this.updateToolMetrics(tool.id, duration, false);

      // Check if retry is needed
      const retryPolicy = validatedContext.retryPolicy || tool.retryPolicy;
      if (this.shouldRetry(error, execution.retryCount, retryPolicy)) {
        await this.prisma.toolExecution.update({
          where: { id: execution.id },
          data: { retryCount: execution.retryCount + 1 },
        });

        // Retry with exponential backoff
        const delay = Math.pow(2, execution.retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));

        return this.executeTool({
          ...validatedContext,
          retryPolicy: { ...retryPolicy, currentRetry: execution.retryCount + 1 },
        });
      }

      return {
        id: execution.id,
        status: ToolExecutionStatus.FAILED,
        error: errorMessage,
        duration,
      };
    }
  }

  // ============================================================================
  // TOOL TYPE EXECUTORS
  // ============================================================================

  private async executeFunctionCall(tool: any, input: any, timeout: number) {
    // Validate input against schema
    await this.validateInput(tool.inputSchema, input);

    // Execute function based on configuration
    const config = tool.config;
    const functionCode = config.code || config.function;

    if (!functionCode) {
      throw new BadRequestException('Function code not provided in tool configuration');
    }

    // Create sandboxed execution environment
    const sandbox = this.createSandbox(input, config.environment || {});

    try {
      // Execute with timeout
      const result = await this.executeWithTimeout(
        () => this.runInSandbox(functionCode, sandbox),
        timeout
      );

      return {
        output: result,
        tokensUsed: 0,
        cost: 0,
      };
    } catch (error) {
      throw new BadRequestException(`Function execution failed: ${error.message}`);
    }
  }

  private async executeAPIFetch(tool: any, input: any, timeout: number) {
    await this.validateInput(tool.inputSchema, input);

    const config = tool.config;
    const { url, method = 'GET', headers = {}, auth } = config;

    if (!url) {
      throw new BadRequestException('API URL not provided in tool configuration');
    }

    // Replace placeholders in URL and headers
    const processedUrl = this.replacePlaceholders(url, input);
    const processedHeaders = this.replacePlaceholders(headers, input);

    // Add authentication if configured
    if (auth) {
      if (auth.type === 'bearer' && auth.token) {
        processedHeaders.Authorization = `Bearer ${auth.token}`;
      } else if (auth.type === 'api_key' && auth.key && auth.value) {
        processedHeaders[auth.key] = auth.value;
      }
    }

    try {
      const response = await axios({
        url: processedUrl,
        method: method.toUpperCase(),
        headers: processedHeaders,
        data: method.toUpperCase() !== 'GET' ? input.body : undefined,
        params: method.toUpperCase() === 'GET' ? input.params : undefined,
        timeout,
        validateStatus: () => true, // Don't throw on HTTP errors
      });

      return {
        output: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data,
        },
        tokensUsed: 0,
        cost: 0,
      };
    } catch (error) {
      throw new BadRequestException(`API request failed: ${error.message}`);
    }
  }

  private async executeRAGQuery(tool: any, input: any, timeout: number) {
    await this.validateInput(tool.inputSchema, input);

    const config = tool.config;
    const { query, collection, topK = 5, threshold = 0.7 } = input;

    if (!query) {
      throw new BadRequestException('Query is required for RAG operations');
    }

    // This would integrate with your vector database
    // For now, returning a mock structure
    const searchResults = await this.performVectorSearch(query, collection, topK, threshold, config);

    return {
      output: {
        query,
        results: searchResults,
        metadata: {
          collection,
          topK,
          threshold,
          resultCount: searchResults.length,
        },
      },
      tokensUsed: this.estimateTokens(query),
      cost: this.calculateRAGCost(query, searchResults),
    };
  }

  private async executeDBQuery(tool: any, input: any, timeout: number) {
    await this.validateInput(tool.inputSchema, input);

    const config = tool.config;
    const { query, parameters = [] } = input;

    if (!query) {
      throw new BadRequestException('SQL query is required');
    }

    // Validate query safety (prevent dangerous operations)
    if (!this.isQuerySafe(query)) {
      throw new BadRequestException('Query contains potentially dangerous operations');
    }

    try {
      // Execute query with parameters
      const result = await this.executeDBQuerySafely(query, parameters, config, timeout);

      return {
        output: result,
        tokensUsed: 0,
        cost: 0,
      };
    } catch (error) {
      throw new BadRequestException(`Database query failed: ${error.message}`);
    }
  }

  private async executeBrowserAutomation(tool: any, input: any, timeout: number) {
    await this.validateInput(tool.inputSchema, input);

    const config = tool.config;
    const { actions, url, options = {} } = input;

    if (!actions || !Array.isArray(actions)) {
      throw new BadRequestException('Actions array is required for browser automation');
    }

    try {
      // This would integrate with Playwright or Puppeteer
      const result = await this.runBrowserAutomation(url, actions, options, timeout);

      return {
        output: result,
        tokensUsed: 0,
        cost: this.calculateBrowserCost(actions.length),
      };
    } catch (error) {
      throw new BadRequestException(`Browser automation failed: ${error.message}`);
    }
  }

  private async executeCustomScript(tool: any, input: any, timeout: number) {
    await this.validateInput(tool.inputSchema, input);

    const config = tool.config;
    const { script, language = 'javascript' } = config;

    if (!script) {
      throw new BadRequestException('Script code not provided');
    }

    try {
      const result = await this.executeScript(script, language, input, timeout);

      return {
        output: result,
        tokensUsed: 0,
        cost: 0,
      };
    } catch (error) {
      throw new BadRequestException(`Script execution failed: ${error.message}`);
    }
  }

  // ============================================================================
  // CACHING SYSTEM
  // ============================================================================

  private async checkCache(tool: any, input: any) {
    if (tool.cacheStrategy === ToolCacheStrategy.NONE) {
      return null;
    }

    const cacheKey = this.generateCacheKey(tool, input);

    const cached = await this.prisma.toolCache.findFirst({
      where: {
        toolId: tool.id,
        inputHash: cacheKey,
        isValid: true,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (cached) {
      // Update cache hit count
      await this.prisma.toolCache.update({
        where: { id: cached.id },
        data: {
          hits: cached.hits + 1,
          lastAccessed: new Date(),
        },
      });

      return {
        output: cached.output,
        cached: true,
      };
    }

    return null;
  }

  private async cacheResult(tool: any, input: any, output: any) {
    if (tool.cacheStrategy === ToolCacheStrategy.NONE) {
      return;
    }

    const cacheKey = this.generateCacheKey(tool, input);
    const expiresAt = new Date(Date.now() + (tool.cacheTTL * 1000));

    await this.prisma.toolCache.upsert({
      where: {
        toolId_inputHash: {
          toolId: tool.id,
          inputHash: cacheKey,
        },
      },
      update: {
        output,
        lastAccessed: new Date(),
        expiresAt,
        isValid: true,
      },
      create: {
        toolId: tool.id,
        inputHash: cacheKey,
        input,
        output,
        strategy: tool.cacheStrategy,
        ttl: tool.cacheTTL,
        expiresAt,
      },
    });
  }

  private generateCacheKey(tool: any, input: any): string {
    const data = JSON.stringify({ toolId: tool.id, input });
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private async validateInput(schema: any, input: any) {
    if (!schema || Object.keys(schema).length === 0) {
      return; // No validation schema provided
    }

    try {
      // Convert JSON schema to Zod schema and validate
      // This is a simplified version - you'd want a more robust JSON Schema to Zod converter
      const zodSchema = this.jsonSchemaToZod(schema);
      zodSchema.parse(input);
    } catch (error) {
      throw new BadRequestException(`Input validation failed: ${error.message}`);
    }
  }

  private jsonSchemaToZod(schema: any): z.ZodSchema {
    // Simplified JSON Schema to Zod conversion
    // In production, use a proper library like json-schema-to-zod
    if (schema.type === 'object') {
      const shape: any = {};
      for (const [key, prop] of Object.entries(schema.properties || {})) {
        shape[key] = this.jsonSchemaToZod(prop as any);
      }
      return z.object(shape);
    } else if (schema.type === 'string') {
      return z.string();
    } else if (schema.type === 'number') {
      return z.number();
    } else if (schema.type === 'boolean') {
      return z.boolean();
    } else if (schema.type === 'array') {
      return z.array(this.jsonSchemaToZod(schema.items));
    }
    return z.any();
  }

  private createSandbox(input: any, environment: any) {
    return {
      input,
      env: environment,
      console: {
        log: (...args: any[]) => console.log('[TOOL]', ...args),
        error: (...args: any[]) => console.error('[TOOL]', ...args),
      },
      // Add other safe globals as needed
    };
  }

  private async runInSandbox(code: string, sandbox: any) {
    // In production, use a proper sandboxing solution like vm2 or isolated-vm
    const func = new Function('sandbox', `
      with (sandbox) {
        ${code}
      }
    `);
    return func(sandbox);
  }

  private async executeWithTimeout<T>(fn: () => Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      fn(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Execution timeout')), timeout)
      ),
    ]);
  }

  private replacePlaceholders(template: any, input: any): any {
    if (typeof template === 'string') {
      return template.replace(/\{\{(\w+)\}\}/g, (match, key) => input[key] || match);
    } else if (typeof template === 'object' && template !== null) {
      const result: any = {};
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.replacePlaceholders(value, input);
      }
      return result;
    }
    return template;
  }

  private shouldRetry(error: any, retryCount: number, retryPolicy: any): boolean {
    if (!retryPolicy || retryCount >= (retryPolicy.maxRetries || 3)) {
      return false;
    }

    // Check if error is retryable
    const retryableErrors = retryPolicy.retryableErrors || ['TIMEOUT', 'NETWORK_ERROR', 'RATE_LIMIT'];
    return retryableErrors.some((errorType: string) =>
      error.message.includes(errorType) || error.code === errorType
    );
  }

  private async updateExecutionStatus(executionId: string, status: ToolExecutionStatus) {
    await this.prisma.toolExecution.update({
      where: { id: executionId },
      data: { status },
    });
  }

  private async updateToolMetrics(toolId: string, duration: number, success: boolean) {
    const tool = await this.prisma.toolDefinition.findUnique({
      where: { id: toolId },
    });

    if (!tool) return;

    const newUsageCount = tool.usageCount + 1;
    const newSuccessRate = success
      ? ((tool.successRate * tool.usageCount) + 1) / newUsageCount
      : (tool.successRate * tool.usageCount) / newUsageCount;
    const newAvgLatency = ((tool.avgLatency * tool.usageCount) + duration) / newUsageCount;

    await this.prisma.toolDefinition.update({
      where: { id: toolId },
      data: {
        usageCount: newUsageCount,
        successRate: newSuccessRate,
        avgLatency: newAvgLatency,
      },
    });
  }

  // Real implementations for complex operations
  private async performVectorSearch(query: string, collection: string, topK: number, threshold: number, config: any) {
    try {
      // Real vector search implementation
      const { default: { Pinecone } } = await import('@pinecone-database/pinecone');

      const pinecone = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY || '',
      });

      const index = pinecone.index(collection);

      // Generate embeddings for the query
      const embedding = await this.generateEmbedding(query, config.embeddingModel || 'text-embedding-ada-002');

      const queryResponse = await index.query({
        vector: embedding,
        topK,
        includeMetadata: true,
        filter: config.filter,
      });

      return queryResponse.matches
        .filter(match => match.score && match.score >= threshold)
        .map(match => ({
          id: match.id,
          content: match.metadata?.content || '',
          score: match.score || 0,
          metadata: match.metadata || {},
        }));
    } catch (error) {
      throw new Error(`Vector search failed: ${error.message}`);
    }
  }

  private estimateTokens(text: string): number {
    // More accurate token estimation
    const words = text.split(/\s+/).length;
    const chars = text.length;
    return Math.ceil((words * 1.3) + (chars * 0.25));
  }

  private calculateRAGCost(query: string, results: any[]): number {
    const queryTokens = this.estimateTokens(query);
    const resultTokens = results.reduce((sum, result) => sum + this.estimateTokens(result.content), 0);
    return (queryTokens * 0.0001) + (resultTokens * 0.0001);
  }

  private isQuerySafe(query: string): boolean {
    const dangerousKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE', 'EXEC', 'EXECUTE'];
    const upperQuery = query.toUpperCase();
    return !dangerousKeywords.some(keyword => upperQuery.includes(keyword));
  }

  private async executeDBQuerySafely(query: string, parameters: any[], config: any, timeout: number) {
    try {
      const { Pool } = await import('pg');

      const pool = new Pool({
        connectionString: config.connectionString,
        ssl: config.ssl ? { rejectUnauthorized: false } : false,
      });

      const client = await pool.connect();

      try {
        const result = await client.query(query, parameters);
        return {
          rows: result.rows,
          rowCount: result.rowCount,
        };
      } finally {
        client.release();
      }
    } catch (error) {
      throw new Error(`Database query failed: ${error.message}`);
    }
  }

  private async runBrowserAutomation(url: string, actions: any[], options: any, timeout: number) {
    try {
      const puppeteer = await import('puppeteer');

      const browser = await puppeteer.default.launch({
        headless: options.headless !== false,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setViewport({ width: 1280, height: 720 });

      const results = {
        url,
        actions: actions.length,
        screenshots: [],
        data: {},
        success: true,
      };

      try {
        await page.goto(url, { waitUntil: 'networkidle0', timeout });

        for (const action of actions) {
          switch (action.type) {
            case 'click':
              await page.click(action.selector);
              break;
            case 'type':
              await page.type(action.selector, action.text);
              break;
            case 'screenshot':
              const screenshot = await page.screenshot({ fullPage: action.fullPage });
              results.screenshots.push(screenshot);
              break;
            case 'extract':
              const data = await page.evaluate((selector) => {
                const element = document.querySelector(selector);
                return element ? element.textContent : null;
              }, action.selector);
              results.data[action.name] = data;
              break;
          }
        }
      } finally {
        await browser.close();
      }

      return results;
    } catch (error) {
      throw new Error(`Browser automation failed: ${error.message}`);
    }
  }

  private calculateBrowserCost(actionCount: number): number {
    return actionCount * 0.01; // $0.01 per action
  }

  private async executeScript(script: string, language: string, input: any, timeout: number) {
    try {
      const { spawn } = await import('child_process');
      const { promisify } = await import('util');

      const exec = promisify(spawn);

      let command: string;
      let args: string[];

      switch (language) {
        case 'python':
          command = 'python';
          args = ['-c', script];
          break;
        case 'javascript':
          command = 'node';
          args = ['-e', script];
          break;
        case 'bash':
          command = 'bash';
          args = ['-c', script];
          break;
        default:
          throw new Error(`Unsupported language: ${language}`);
      }

      const { stdout, stderr } = await exec(command, args, {
        timeout,
        env: { ...process.env, ...input },
      });

      return {
        result: stdout.toString(),
        error: stderr.toString(),
        language,
        success: !stderr.toString(),
      };
    } catch (error) {
      throw new Error(`Script execution failed: ${error.message}`);
    }
  }

  private async generateEmbedding(text: string, model: string): Promise<number[]> {
    // Real embedding generation
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: text,
        model: model,
      }),
    });

    const data = await response.json();
    return data.data[0].embedding;
  }
}