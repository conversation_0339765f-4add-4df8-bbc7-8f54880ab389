import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject } from "@nestjs/common";
import { Cache } from "cache-manager";
import { ClientType, ConnectionStatus } from "../dto/apix.dto";

interface ConnectionContext {
  id: string;
  sessionId: string;
  userId?: string;
  organizationId: string;
  clientType: ClientType;
  channels: Set<string>;
  lastActivity: number;
  latencyMetrics: Map<string, number[]>;
  metadata: Record<string, any>;
  reconnectCount: number;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class ConnectionManagerService {
  private readonly logger = new Logger(ConnectionManagerService.name);
  private connections = new Map<string, ConnectionContext>();
  private userConnections = new Map<string, Set<string>>();
  private organizationConnections = new Map<string, Set<string>>();

  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.startCleanupTimer();
  }

  async createConnection(
    socketId: string,
    sessionId: string,
    userId: string | undefined,
    organizationId: string,
    clientType: ClientType,
    metadata: Record<string, any> = {},
    ipAddress?: string,
    userAgent?: string,
  ): Promise<ConnectionContext> {
    const connection = await this.prisma.apiXConnection.create({
      data: {
        sessionId,
        organizationId,
        userId,
        clientType,
        channels: [],
        metadata,
        ipAddress,
        userAgent,
        status: ConnectionStatus.CONNECTED,
      },
    });

    const context: ConnectionContext = {
      id: connection.id,
      sessionId,
      userId,
      organizationId,
      clientType,
      channels: new Set(),
      lastActivity: Date.now(),
      latencyMetrics: new Map(),
      metadata,
      reconnectCount: 0,
      ipAddress,
      userAgent,
    };

    this.connections.set(socketId, context);

    if (userId) {
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId)!.add(socketId);
    }

    if (!this.organizationConnections.has(organizationId)) {
      this.organizationConnections.set(organizationId, new Set());
    }
    this.organizationConnections.get(organizationId)!.add(socketId);

    await this.cacheConnection(socketId, context);

    this.logger.log(
      `Connection created: ${socketId} for user ${userId} in org ${organizationId}`,
    );
    return context;
  }

  async updateConnection(
    socketId: string,
    updates: Partial<{
      status: ConnectionStatus;
      lastActivity: number;
      reconnectCount: number;
      latencyMs: number;
      metadata: Record<string, any>;
    }>,
  ): Promise<void> {
    const context = this.connections.get(socketId);
    if (!context) return;

    if (updates.lastActivity) {
      context.lastActivity = updates.lastActivity;
    }
    if (updates.reconnectCount !== undefined) {
      context.reconnectCount = updates.reconnectCount;
    }
    if (updates.metadata) {
      context.metadata = { ...context.metadata, ...updates.metadata };
    }

    await this.prisma.apiXConnection.update({
      where: { id: context.id },
      data: {
        status: updates.status,
        lastHeartbeat: updates.lastActivity
          ? new Date(updates.lastActivity)
          : undefined,
        reconnectCount: updates.reconnectCount,
        latencyMs: updates.latencyMs,
        metadata: context.metadata,
      },
    });

    await this.cacheConnection(socketId, context);
  }

  async removeConnection(socketId: string): Promise<void> {
    const context = this.connections.get(socketId);
    if (!context) return;

    await this.prisma.apiXConnection.update({
      where: { id: context.id },
      data: {
        status: ConnectionStatus.DISCONNECTED,
        metadata: {
          ...context.metadata,
          disconnectedAt: new Date().toISOString(),
          duration: Date.now() - context.lastActivity,
        },
      },
    });

    this.connections.delete(socketId);

    if (context.userId) {
      const userConnections = this.userConnections.get(context.userId);
      if (userConnections) {
        userConnections.delete(socketId);
        if (userConnections.size === 0) {
          this.userConnections.delete(context.userId);
        }
      }
    }

    const orgConnections = this.organizationConnections.get(
      context.organizationId,
    );
    if (orgConnections) {
      orgConnections.delete(socketId);
      if (orgConnections.size === 0) {
        this.organizationConnections.delete(context.organizationId);
      }
    }

    await this.cacheManager.del(`connection:${socketId}`);

    this.logger.log(`Connection removed: ${socketId}`);
  }

  getConnection(socketId: string): ConnectionContext | undefined {
    return this.connections.get(socketId);
  }

  async getConnectionFromCache(
    socketId: string,
  ): Promise<ConnectionContext | null> {
    const cached = await this.cacheManager.get<ConnectionContext>(
      `connection:${socketId}`,
    );
    return cached || null;
  }

  getUserConnections(userId: string): string[] {
    const connections = this.userConnections.get(userId);
    return connections ? Array.from(connections) : [];
  }

  getOrganizationConnections(organizationId: string): string[] {
    const connections = this.organizationConnections.get(organizationId);
    return connections ? Array.from(connections) : [];
  }

  getAllConnections(): Map<string, ConnectionContext> {
    return new Map(this.connections);
  }

  getConnectionStats(): {
    total: number;
    byClientType: Record<ClientType, number>;
    byOrganization: Record<string, number>;
    activeUsers: number;
    averageLatency: number;
    connectionsPerUser: number;
    healthyConnections: number;
    reconnectingConnections: number;
  } {
    const now = Date.now();
    const healthyThreshold = 300000; // 5 minutes
    let totalLatency = 0;
    let latencyCount = 0;
    let healthyCount = 0;
    let reconnectingCount = 0;

    const stats = {
      total: this.connections.size,
      byClientType: {} as Record<ClientType, number>,
      byOrganization: {} as Record<string, number>,
      activeUsers: this.userConnections.size,
      averageLatency: 0,
      connectionsPerUser: 0,
      healthyConnections: 0,
      reconnectingConnections: 0,
    };

    Object.values(ClientType).forEach((type) => {
      stats.byClientType[type] = 0;
    });

    for (const context of this.connections.values()) {
      stats.byClientType[context.clientType]++;
      stats.byOrganization[context.organizationId] =
        (stats.byOrganization[context.organizationId] || 0) + 1;

      // Calculate health metrics
      if (now - context.lastActivity < healthyThreshold) {
        healthyCount++;
      }

      if (context.reconnectCount > 0) {
        reconnectingCount++;
      }

      // Calculate average latency from metrics
      for (const metrics of context.latencyMetrics.values()) {
        if (metrics.length > 0) {
          totalLatency += metrics.reduce((sum, val) => sum + val, 0);
          latencyCount += metrics.length;
        }
      }
    }

    stats.averageLatency = latencyCount > 0 ? totalLatency / latencyCount : 0;
    stats.connectionsPerUser =
      stats.activeUsers > 0 ? stats.total / stats.activeUsers : 0;
    stats.healthyConnections = healthyCount;
    stats.reconnectingConnections = reconnectingCount;

    return stats;
  }

  async addChannelToConnection(
    socketId: string,
    channel: string,
  ): Promise<void> {
    const context = this.connections.get(socketId);
    if (!context) return;

    context.channels.add(channel);

    await this.prisma.apiXConnection.update({
      where: { id: context.id },
      data: {
        channels: Array.from(context.channels),
      },
    });

    await this.cacheConnection(socketId, context);
  }

  async removeChannelFromConnection(
    socketId: string,
    channel: string,
  ): Promise<void> {
    const context = this.connections.get(socketId);
    if (!context) return;

    context.channels.delete(channel);

    await this.prisma.apiXConnection.update({
      where: { id: context.id },
      data: {
        channels: Array.from(context.channels),
      },
    });

    await this.cacheConnection(socketId, context);
  }

  trackLatency(socketId: string, eventType: string, latency: number): void {
    const context = this.connections.get(socketId);
    if (!context) return;

    if (!context.latencyMetrics.has(eventType)) {
      context.latencyMetrics.set(eventType, []);
    }

    const metrics = context.latencyMetrics.get(eventType)!;
    metrics.push(latency);

    if (metrics.length > 100) {
      metrics.shift();
    }

    this.prisma.apiXLatencyMetric
      .create({
        data: {
          connectionId: context.id,
          eventType,
          latencyMs: latency,
          organizationId: context.organizationId,
        },
      })
      .catch((error) => {
        this.logger.error("Failed to store latency metric:", error);
      });
  }

  getLatencyStats(
    socketId: string,
    eventType?: string,
  ): {
    avg: number;
    min: number;
    max: number;
    count: number;
  } | null {
    const context = this.connections.get(socketId);
    if (!context) return null;

    const metrics = eventType
      ? context.latencyMetrics.get(eventType)
      : Array.from(context.latencyMetrics.values()).flat();

    if (!metrics || metrics.length === 0) return null;

    return {
      avg: metrics.reduce((a, b) => a + b, 0) / metrics.length,
      min: Math.min(...metrics),
      max: Math.max(...metrics),
      count: metrics.length,
    };
  }

  async updateHeartbeat(socketId: string, latency?: number): Promise<void> {
    const context = this.connections.get(socketId);
    if (!context) return;

    context.lastActivity = Date.now();

    await this.updateConnection(socketId, {
      lastActivity: context.lastActivity,
      latencyMs: latency,
    });

    if (latency) {
      this.trackLatency(socketId, "heartbeat", latency);
    }
  }

  private async cacheConnection(
    socketId: string,
    context: ConnectionContext,
  ): Promise<void> {
    await this.cacheManager.set(`connection:${socketId}`, context, 3600);
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 300000); // Every 5 minutes
  }

  private async cleanupInactiveConnections(): Promise<void> {
    const now = Date.now();
    const timeout = 1800000; // 30 minutes
    const toRemove: string[] = [];

    for (const [socketId, context] of this.connections.entries()) {
      if (now - context.lastActivity > timeout) {
        toRemove.push(socketId);
      }
    }

    for (const socketId of toRemove) {
      await this.removeConnection(socketId);
      this.logger.log(`Cleaned up inactive connection: ${socketId}`);
    }

    if (toRemove.length > 0) {
      this.logger.log(`Cleaned up ${toRemove.length} inactive connections`);
    }
  }
}
