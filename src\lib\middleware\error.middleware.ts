/**
 * 🚨 Centralized Error Handling Middleware - FIXED
 * Production-ready error handling with proper logging and response formatting
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';

export interface ApiError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
}

export class ApiErrors {
  static badRequest(message: string, details?: any): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 400;
    error.code = 'BAD_REQUEST';
    error.details = details;
    return error;
  }

  static unauthorized(message = 'Unauthorized'): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 401;
    error.code = 'UNAUTHORIZED';
    return error;
  }

  static forbidden(message = 'Forbidden'): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 403;
    error.code = 'FORBIDDEN';
    return error;
  }

  static notFound(message = 'Resource not found'): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 404;
    error.code = 'NOT_FOUND';
    return error;
  }

  static conflict(message: string, details?: any): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 409;
    error.code = 'CONFLICT';
    error.details = details;
    return error;
  }

  static internal(message = 'Internal server error'): ApiError {
    const error = new Error(message) as ApiError;
    error.statusCode = 500;
    error.code = 'INTERNAL_ERROR';
    return error;
  }
}

/**
 * Helper function to throw if resource not found
 */
export function throwIfNotFound<T>(resource: T | null | undefined, message?: string): asserts resource is T {
  if (!resource) {
    throw ApiErrors.notFound(message);
  }
}

/**
 * Main error handler for API routes
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Handle known error types
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          code: issue.code
        }))
      },
      { status: 400 }
    );
  }

  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        return NextResponse.json(
          {
            error: 'Unique constraint violation',
            code: 'DUPLICATE_RESOURCE',
            details: error.meta
          },
          { status: 409 }
        );
      case 'P2025':
        return NextResponse.json(
          {
            error: 'Record not found',
            code: 'NOT_FOUND',
            details: error.meta
          },
          { status: 404 }
        );
      default:
        return NextResponse.json(
          {
            error: 'Database error',
            code: 'DATABASE_ERROR',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
          },
          { status: 500 }
        );
    }
  }

  // Handle custom API errors
  if (error instanceof Error && 'statusCode' in error) {
    const apiError = error as ApiError;
    return NextResponse.json(
      {
        error: apiError.message,
        code: apiError.code || 'API_ERROR',
        details: apiError.details
      },
      { status: apiError.statusCode || 500 }
    );
  }

  // Handle generic errors
  if (error instanceof Error) {
    return NextResponse.json(
      {
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }

  // Handle unknown error types
  return NextResponse.json(
    {
      error: 'Internal server error',
      code: 'UNKNOWN_ERROR'
    },
    { status: 500 }
  );
}

/**
 * Wrap async handlers with error catching
 */
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      throw error; // Re-throw to be handled by the main error handler
    }
  };
}