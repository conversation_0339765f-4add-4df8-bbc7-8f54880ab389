import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query, 
  UseGuards,
  Request,
  HttpStatus,
  HttpException,
  UploadedFile,
  UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { TenantGuard } from '../auth/tenant.guard';
import { Roles } from '../auth/roles.decorator';
import { ToolsService } from './tools.service';
import { 
  CreateToolDto, 
  UpdateToolDto, 
  ExecuteToolDto, 
  ToolSearchDto, 
  ImportToolDto,
  ToolExecutionResultDto 
} from './dto/tool.dto';

@ApiTags('tools')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('tools')
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  // ============================================================================
  // TOOL CRUD OPERATIONS
  // ============================================================================

  @Post()
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(@Body() createToolDto: CreateToolDto, @Request() req) {
    try {
      const tool = await this.toolsService.create(
        createToolDto, 
        req.user.id, 
        req.user.organizationId
      );
      return {
        success: true,
        data: tool,
        message: 'Tool created successfully'
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all tools with optional filtering' })
  @ApiResponse({ status: 200, description: 'Tools retrieved successfully' })
  async findAll(@Query() searchDto: ToolSearchDto, @Request() req) {
    try {
      const tools = await this.toolsService.findAll(req.user.organizationId);
      return {
        success: true,
        data: tools,
        total: tools.length
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('search')
  @ApiOperation({ summary: 'Search tools with advanced filtering' })
  @ApiResponse({ status: 200, description: 'Search results retrieved successfully' })
  async search(@Query() searchDto: ToolSearchDto, @Request() req) {
    try {
      const results = await this.toolsService.searchTools(
        searchDto, 
        req.user.organizationId
      );
      return {
        success: true,
        data: results.tools,
        total: results.total,
        page: searchDto.page || 1,
        limit: searchDto.limit || 20
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('popular')
  @ApiOperation({ summary: 'Get popular tools' })
  @ApiResponse({ status: 200, description: 'Popular tools retrieved successfully' })
  async getPopular(@Query('limit') limit: number = 10, @Request() req) {
    try {
      const tools = await this.toolsService.getPopularTools(
        req.user.organizationId, 
        limit
      );
      return {
        success: true,
        data: tools
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get tool statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStats(@Request() req) {
    try {
      const stats = await this.toolsService.getToolStats(req.user.organizationId);
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get all tool categories' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  async getCategories(@Request() req) {
    try {
      const categories = await this.toolsService.getCategories(req.user.organizationId);
      return {
        success: true,
        data: categories
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const tool = await this.toolsService.findOne(id, req.user.organizationId);
      if (!tool) {
        throw new HttpException(
          { success: false, message: 'Tool not found' },
          HttpStatus.NOT_FOUND
        );
      }
      return {
        success: true,
        data: tool
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch(':id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  @ApiOperation({ summary: 'Update a tool' })
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id') id: string, 
    @Body() updateToolDto: UpdateToolDto, 
    @Request() req
  ) {
    try {
      const tool = await this.toolsService.update(
        id, 
        updateToolDto, 
        req.user.id, 
        req.user.organizationId
      );
      return {
        success: true,
        data: tool,
        message: 'Tool updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(':id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  @ApiOperation({ summary: 'Delete a tool' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await this.toolsService.remove(id, req.user.id, req.user.organizationId);
      return {
        success: true,
        message: 'Tool deleted successfully'
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  // ============================================================================
  // TOOL EXECUTION
  // ============================================================================

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute a tool with given input' })
  @ApiResponse({ 
    status: 200, 
    description: 'Tool executed successfully',
    type: ToolExecutionResultDto
  })
  @ApiResponse({ status: 400, description: 'Invalid input or execution error' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async execute(
    @Param('id') id: string, 
    @Body() executeDto: ExecuteToolDto, 
    @Request() req
  ) {
    try {
      const result = await this.toolsService.executeTool(id, executeDto.input, {
        executorType: 'user',
        executorId: req.user.id,
        sessionId: executeDto.sessionId,
        organizationId: req.user.organizationId,
        metadata: executeDto.metadata,
        timeout: executeDto.timeout
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test a tool with sample input' })
  @ApiResponse({ status: 200, description: 'Tool test completed' })
  async test(
    @Param('id') id: string, 
    @Body() testData: { input: Record<string, any> }, 
    @Request() req
  ) {
    try {
      const result = await this.toolsService.testTool(
        id, 
        testData.input, 
        req.user.organizationId
      );
      return {
        success: true,
        data: result
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get execution history for a tool' })
  @ApiResponse({ status: 200, description: 'Execution history retrieved' })
  async getExecutions(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Request() req
  ) {
    try {
      const executions = await this.toolsService.getExecutionHistory(
        id, 
        req.user.organizationId,
        { page, limit }
      );
      return {
        success: true,
        data: executions
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // ============================================================================
  // TOOL IMPORT/EXPORT
  // ============================================================================

  @Post('import')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  @ApiOperation({ summary: 'Import a tool from external source' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: 'Tool imported successfully' })
  async import(
    @Body() importDto: ImportToolDto,
    @UploadedFile() file: Express.Multer.File,
    @Request() req
  ) {
    try {
      const tool = await this.toolsService.importTool(
        { ...importDto, file },
        req.user.id,
        req.user.organizationId
      );
      return {
        success: true,
        data: tool,
        message: 'Tool imported successfully'
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(':id/export')
  @ApiOperation({ summary: 'Export a tool in specified format' })
  @ApiResponse({ status: 200, description: 'Tool exported successfully' })
  async export(
    @Param('id') id: string,
    @Query('format') format: string = 'json',
    @Request() req
  ) {
    try {
      const exportData = await this.toolsService.exportTool(
        id, 
        format, 
        req.user.organizationId
      );
      return {
        success: true,
        data: exportData
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // ============================================================================
  // TOOL TEMPLATES
  // ============================================================================

  @Get('templates/gallery')
  @ApiOperation({ summary: 'Get tool template gallery' })
  @ApiResponse({ status: 200, description: 'Template gallery retrieved' })
  async getTemplateGallery(@Request() req) {
    try {
      const templates = await this.toolsService.getTemplateGallery(
        req.user.organizationId
      );
      return {
        success: true,
        data: templates
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('templates/:templateId/create')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  @ApiOperation({ summary: 'Create tool from template' })
  @ApiResponse({ status: 201, description: 'Tool created from template' })
  async createFromTemplate(
    @Param('templateId') templateId: string,
    @Body() customization: Record<string, any>,
    @Request() req
  ) {
    try {
      const tool = await this.toolsService.createFromTemplate(
        templateId,
        customization,
        req.user.id,
        req.user.organizationId
      );
      return {
        success: true,
        data: tool,
        message: 'Tool created from template successfully'
      };
    } catch (error) {
      throw new HttpException(
        { success: false, message: error.message },
        HttpStatus.BAD_REQUEST
      );
    }
  }
}