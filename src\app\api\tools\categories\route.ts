import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession, unauthorizedResponse } from '@/lib/auth';
import apiClient from '@/lib/api-client';
import { SkillCategory } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    const session = await getAuthenticatedSession();
    if (!session?.user?.id) {
      return unauthorizedResponse();
    }

    const response = await apiClient.get('/tools/categories');

    const data = await response as SkillCategory[];
    return NextResponse.json(data);
  } catch (error) {
    console.error('Tool Categories API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getAuthenticatedSession();
    if (!session?.user?.id) {
      return unauthorizedResponse();
    }

    const response = await apiClient.post('/tools/categories', {
      name: 'New Category',
      description: 'Description of the new category',
    });

    const data = await response as SkillCategory;
    return NextResponse.json(data);
  } catch (error) {
    console.error('Tool Categories API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}