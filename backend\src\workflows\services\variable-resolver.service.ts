import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class VariableResolverService {
  private readonly logger = new Logger(VariableResolverService.name);

  constructor(private prisma: PrismaService) {}

  async resolveVariables(
    template: any,
    context: {
      executionId: string;
      workflowId: string;
      sessionId: string;
      organizationId: string;
      variables: Record<string, any>;
      nodeResults?: Map<string, any>;
    },
  ): Promise<any> {
    try {
      // Combine variables from context and session
      const sessionVariables = await this.getSessionVariables(context.sessionId);
      const workflowVariables = await this.getWorkflowVariables(context.workflowId);
      const systemVariables = this.getSystemVariables();
      
      const allVariables = {
        ...systemVariables,
        ...workflowVariables,
        ...sessionVariables,
        ...context.variables,
      };
      
      // Process the template with all variables
      return this.processTemplate(template, allVariables, context.nodeResults);
    } catch (error) {
      this.logger.error(`Failed to resolve variables: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async getSessionVariables(sessionId: string): Promise<Record<string, any>> {
    try {
      const session = await this.prisma.session.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        return {};
      }

      // Extract variables from session context
      const sessionContext = session.context as Record<string, any>;
      return sessionContext.variables || {};
    } catch (error) {
      this.logger.error(`Failed to get session variables: ${error.message}`, error.stack);
      return {};
    }
  }

  private async getWorkflowVariables(workflowId: string): Promise<Record<string, any>> {
    try {
      const workflow = await this.prisma.workflow.findUnique({
        where: { id: workflowId },
      });

      if (!workflow) {
        return {};
      }

      // Extract variables from workflow definition
      const definition = workflow.definition as Record<string, any>;
      return definition.variables || {};
    } catch (error) {
      this.logger.error(`Failed to get workflow variables: ${error.message}`, error.stack);
      return {};
    }
  }

  private getSystemVariables(): Record<string, any> {
    // System variables available to all workflows
    return {
      now: new Date().toISOString(),
      timestamp: Date.now(),
      random: Math.random(),
      uuid: this.generateUUID(),
    };
  }

  private processTemplate(
    template: any,
    variables: Record<string, any>,
    nodeResults?: Map<string, any>,
  ): any {
    if (typeof template === 'string') {
      // Handle string interpolation with ${variable} syntax
      return template.replace(/\${(\w+)}/g, (match, varName) => {
        return variables[varName] !== undefined ? 
          JSON.stringify(variables[varName]) : match;
      });
    } else if (Array.isArray(template)) {
      // Process each item in array
      return template.map(item => this.processTemplate(item, variables, nodeResults));
    } else if (template && typeof template === 'object') {
      // Process each property in object
      const result = {};
      for (const key in template) {
        result[key] = this.processTemplate(template[key], variables, nodeResults);
      }
      return result;
    } else if (template === '$nodeResults' && nodeResults) {
      // Special case for accessing all node results
      return Object.fromEntries(nodeResults);
    } else if (typeof template === 'string' && template.startsWith('$nodeResult.') && nodeResults) {
      // Special case for accessing specific node result
      const nodeId = template.split('.')[1];
      return nodeResults.get(nodeId) || null;
    }
    
    // Return unchanged for other types
    return template;
  }

  private generateUUID(): string {
    // Simple UUID v4 implementation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Method to update session variables
  async updateSessionVariables(
    sessionId: string,
    variables: Record<string, any>,
  ): Promise<void> {
    try {
      const session = await this.prisma.session.findUnique({
        where: { id: sessionId },
      });

      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }

      // Update session context with new variables
      const sessionContext = session.context as Record<string, any>;
      const updatedContext = {
        ...sessionContext,
        variables: {
          ...(sessionContext.variables || {}),
          ...variables,
        },
      };

      await this.prisma.session.update({
        where: { id: sessionId },
        data: {
          context: updatedContext,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to update session variables: ${error.message}`, error.stack);
      throw error;
    }
  }
}