import apiClient from '@/lib/api-client';
import { createGet<PERSON><PERSON><PERSON>, createPut<PERSON>and<PERSON>, createDeleteHandler } from '@/lib/utils/api-handler';
import { successResponse } from '@/lib/utils/api-responses';
import { z } from 'zod';

// Validation schema for params
const paramsSchema = z.object({
  id: z.string().uuid('Invalid tool ID'),
});

export const GET = createGetHandler(
  async (req) => {
    const { id } = req.validatedParams;

    const tool = await apiClient.get(`/tools/${id}`);
    return successResponse(tool);
  },
  {
    paramsSchema,
    requiredPermissions: ['tool:read'],
  }
);

// Tool update schema
const updateToolSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  config: z.record(z.string(), z.any()).optional(),
  isActive: z.boolean().optional(),
});

export const PATCH = createPutHandler(
  async (req) => {
    const { id } = req.validatedParams;
    const updateData = req.validatedBody;

    const tool = await apiClient.patch(`/tools/${id}`, updateData);
    return successResponse(tool, 'Tool updated successfully');
  },
  {
    paramsSchema,
    bodySchema: updateToolSchema,
    requiredPermissions: ['tool:update'],
  }
);

export const DELETE = createDeleteHandler(
  async (req) => {
    const { id } = req.validatedParams;

    await apiClient.delete(`/tools/${id}`);
    return successResponse(null, 'Tool deleted successfully');
  },
  {
    paramsSchema,
    requiredPermissions: ['tool:delete'],
  }
);