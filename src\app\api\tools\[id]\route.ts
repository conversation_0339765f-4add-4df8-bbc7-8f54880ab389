import { NextRequest, NextResponse } from 'next/server';
import  apiClient  from '@/lib/api-client';
import { Tool } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = await apiClient.get(`/tools/${params.id}`);

    const data = await response as Tool;
    return NextResponse.json(data);
  } catch (error) {
    console.error('Get Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    const response = await apiClient.patch(`/tools/${params.id}`, body);

    const data = await response as Tool;
    return NextResponse.json(data);
  } catch (error) {
    console.error('Update Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = await apiClient.delete(`/tools/${params.id}`);

    const data = await response as Tool;
    return NextResponse.json(data);
  } catch (error) {
    console.error('Delete Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}