import { PrismaService } from '../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cache } from 'cache-manager';
export interface SessionMemoryContext {
    sessionId: string;
    agentId: string;
    userId?: string;
    organizationId: string;
    conversationHistory: Array<{
        role: 'user' | 'assistant' | 'system' | 'tool';
        content: string;
        timestamp: Date;
        metadata?: any;
    }>;
    variables: Record<string, any>;
    metadata: Record<string, any>;
    lastActivity: Date;
    messageCount: number;
    tokenUsage: {
        total: number;
        input: number;
        output: number;
    };
}
export declare class SessionMemoryService {
    private prisma;
    private eventEmitter;
    private cacheManager;
    private readonly logger;
    constructor(prisma: PrismaService, eventEmitter: EventEmitter2, cacheManager: Cache);
    initializeAgentMemory(agentId: string, config: any): Promise<void>;
    getOrCreateSession(agentId: string, sessionId: string, userId?: string): Promise<SessionMemoryContext>;
    addMessage(sessionId: string, message: {
        role: 'user' | 'assistant' | 'system' | 'tool';
        content: string;
        type?: string;
        tokens?: number;
        cost?: number;
        metadata?: any;
    }): Promise<void>;
    getConversationHistory(sessionId: string, limit?: number): Promise<Array<{
        role: string;
        content: string;
        timestamp: Date;
    }>>;
    updateSessionMemory(sessionId: string, updates: {
        lastInput?: any;
        lastOutput?: any;
        lastActivity?: Date;
        messageCount?: number;
        tokenUsage?: any;
        variables?: Record<string, any>;
        metadata?: Record<string, any>;
    }): Promise<void>;
    updateAgentMemory(agentId: string, updates: {
        systemPrompt?: string;
        instructions?: string;
        capabilities?: any;
    }): Promise<void>;
    clearAgentMemory(agentId: string): Promise<void>;
    getSessionStats(sessionId: string): Promise<{
        messageCount: number;
        tokenUsage: any;
        duration: number;
        lastActivity: Date;
    } | null>;
    cleanupInactiveSessions(inactiveThreshold?: number): Promise<number>;
}
