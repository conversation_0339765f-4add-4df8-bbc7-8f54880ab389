"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SessionMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionMemoryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let SessionMemoryService = SessionMemoryService_1 = class SessionMemoryService {
    constructor(prisma, eventEmitter, cacheManager) {
        this.prisma = prisma;
        this.eventEmitter = eventEmitter;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(SessionMemoryService_1.name);
    }
    async initializeAgentMemory(agentId, config) {
        try {
            const memoryConfig = {
                agentId,
                type: config.type,
                systemPrompt: config.systemPrompt,
                instructions: config.instructions,
                capabilities: config.capabilities,
                memoryWindow: config.capabilities?.memoryWindow || 10,
                createdAt: new Date(),
            };
            await this.cacheManager.set(`agent_memory:${agentId}`, memoryConfig, 3600000);
            this.eventEmitter.emit('agent.memory.initialized', {
                agentId,
                config: memoryConfig,
                timestamp: Date.now(),
            });
            this.logger.log(`Agent memory initialized for agent: ${agentId}`);
        }
        catch (error) {
            this.logger.error(`Failed to initialize agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getOrCreateSession(agentId, sessionId, userId) {
        try {
            let session = await this.cacheManager.get(`session:${sessionId}`);
            if (!session) {
                const dbSession = await this.prisma.agentSessionNew.findUnique({
                    where: { id: sessionId },
                    include: {
                        messages: {
                            orderBy: { createdAt: 'asc' },
                            take: 50,
                        },
                    },
                });
                if (dbSession) {
                    session = {
                        sessionId: dbSession.id,
                        agentId: dbSession.agentId,
                        userId: dbSession.userId,
                        organizationId: dbSession.organizationId,
                        conversationHistory: dbSession.messages.map(msg => ({
                            role: msg.role,
                            content: msg.content,
                            timestamp: msg.createdAt,
                            metadata: msg.metadata,
                        })),
                        variables: dbSession.variables || {},
                        metadata: dbSession.metadata || {},
                        lastActivity: dbSession.lastActivity,
                        messageCount: dbSession.messageCount,
                        tokenUsage: dbSession.tokenUsage || { total: 0, input: 0, output: 0 },
                    };
                }
                else {
                    const agent = await this.prisma.agentInstance.findUnique({
                        where: { id: agentId },
                        select: { organizationId: true },
                    });
                    if (!agent) {
                        throw new Error(`Agent ${agentId} not found`);
                    }
                    const newSession = await this.prisma.agentSessionNew.create({
                        data: {
                            id: sessionId,
                            agentId,
                            userId,
                            organizationId: agent.organizationId,
                            status: 'active',
                            variables: {},
                            metadata: {},
                            messageCount: 0,
                            tokenUsage: { total: 0, input: 0, output: 0 },
                            lastActivity: new Date(),
                        },
                    });
                    session = {
                        sessionId: newSession.id,
                        agentId: newSession.agentId,
                        userId: newSession.userId,
                        organizationId: newSession.organizationId,
                        conversationHistory: [],
                        variables: {},
                        metadata: {},
                        lastActivity: newSession.lastActivity,
                        messageCount: 0,
                        tokenUsage: { total: 0, input: 0, output: 0 },
                    };
                    this.eventEmitter.emit('session.created', {
                        sessionId,
                        agentId,
                        userId,
                        organizationId: agent.organizationId,
                        timestamp: Date.now(),
                    });
                }
                await this.cacheManager.set(`session:${sessionId}`, session, 1800000);
            }
            return session;
        }
        catch (error) {
            this.logger.error(`Failed to get or create session: ${error.message}`, error.stack);
            throw error;
        }
    }
    async addMessage(sessionId, message) {
        try {
            await this.prisma.sessionMessage.create({
                data: {
                    sessionId,
                    role: message.role,
                    content: message.content,
                    type: message.type || 'text',
                    tokens: message.tokens,
                    cost: message.cost,
                    metadata: message.metadata || {},
                },
            });
            await this.prisma.agentSessionNew.update({
                where: { id: sessionId },
                data: {
                    messageCount: { increment: 1 },
                    lastActivity: new Date(),
                    tokenUsage: message.tokens ? {
                        update: {
                            total: { increment: message.tokens },
                            [message.role === 'user' ? 'input' : 'output']: { increment: message.tokens },
                        }
                    } : undefined,
                },
            });
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`);
            if (cachedSession) {
                cachedSession.conversationHistory.push({
                    role: message.role,
                    content: message.content,
                    timestamp: new Date(),
                    metadata: message.metadata,
                });
                cachedSession.messageCount += 1;
                cachedSession.lastActivity = new Date();
                if (message.tokens) {
                    cachedSession.tokenUsage.total += message.tokens;
                    if (message.role === 'user') {
                        cachedSession.tokenUsage.input += message.tokens;
                    }
                    else {
                        cachedSession.tokenUsage.output += message.tokens;
                    }
                }
                if (cachedSession.conversationHistory.length > 50) {
                    cachedSession.conversationHistory = cachedSession.conversationHistory.slice(-50);
                }
                await this.cacheManager.set(`session:${sessionId}`, cachedSession, 1800000);
            }
            this.eventEmitter.emit('session.message.added', {
                sessionId,
                message: {
                    role: message.role,
                    content: message.content,
                    tokens: message.tokens,
                    timestamp: Date.now(),
                },
            });
        }
        catch (error) {
            this.logger.error(`Failed to add message to session: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getConversationHistory(sessionId, limit = 20) {
        try {
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`);
            if (cachedSession) {
                return cachedSession.conversationHistory.slice(-limit);
            }
            const messages = await this.prisma.sessionMessage.findMany({
                where: { sessionId },
                orderBy: { createdAt: 'desc' },
                take: limit,
                select: {
                    role: true,
                    content: true,
                    createdAt: true,
                },
            });
            return messages.reverse().map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.createdAt,
            }));
        }
        catch (error) {
            this.logger.error(`Failed to get conversation history: ${error.message}`, error.stack);
            return [];
        }
    }
    async updateSessionMemory(sessionId, updates) {
        try {
            await this.prisma.agentSessionNew.update({
                where: { id: sessionId },
                data: {
                    lastActivity: updates.lastActivity || new Date(),
                    messageCount: updates.messageCount,
                    tokenUsage: updates.tokenUsage,
                    variables: updates.variables,
                    metadata: updates.metadata,
                },
            });
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`);
            if (cachedSession) {
                if (updates.lastActivity)
                    cachedSession.lastActivity = updates.lastActivity;
                if (updates.messageCount)
                    cachedSession.messageCount = updates.messageCount;
                if (updates.tokenUsage)
                    cachedSession.tokenUsage = { ...cachedSession.tokenUsage, ...updates.tokenUsage };
                if (updates.variables)
                    cachedSession.variables = { ...cachedSession.variables, ...updates.variables };
                if (updates.metadata)
                    cachedSession.metadata = { ...cachedSession.metadata, ...updates.metadata };
                await this.cacheManager.set(`session:${sessionId}`, cachedSession, 1800000);
            }
            this.eventEmitter.emit('session.updated', {
                sessionId,
                updates,
                timestamp: Date.now(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to update session memory: ${error.message}`, error.stack);
            throw error;
        }
    }
    async updateAgentMemory(agentId, updates) {
        try {
            const cachedConfig = await this.cacheManager.get(`agent_memory:${agentId}`);
            if (cachedConfig) {
                if (updates.systemPrompt)
                    cachedConfig.systemPrompt = updates.systemPrompt;
                if (updates.instructions)
                    cachedConfig.instructions = updates.instructions;
                if (updates.capabilities)
                    cachedConfig.capabilities = { ...cachedConfig.capabilities, ...updates.capabilities };
                await this.cacheManager.set(`agent_memory:${agentId}`, cachedConfig, 3600000);
            }
            this.eventEmitter.emit('agent.memory.updated', {
                agentId,
                updates,
                timestamp: Date.now(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to update agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }
    async clearAgentMemory(agentId) {
        try {
            await this.cacheManager.del(`agent_memory:${agentId}`);
            const sessions = await this.prisma.agentSessionNew.findMany({
                where: { agentId },
                select: { id: true },
            });
            for (const session of sessions) {
                await this.cacheManager.del(`session:${session.id}`);
            }
            this.eventEmitter.emit('agent.memory.cleared', {
                agentId,
                timestamp: Date.now(),
            });
            this.logger.log(`Agent memory cleared for agent: ${agentId}`);
        }
        catch (error) {
            this.logger.error(`Failed to clear agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getSessionStats(sessionId) {
        try {
            const session = await this.prisma.agentSessionNew.findUnique({
                where: { id: sessionId },
                select: {
                    messageCount: true,
                    tokenUsage: true,
                    createdAt: true,
                    lastActivity: true,
                },
            });
            if (!session)
                return null;
            return {
                messageCount: session.messageCount,
                tokenUsage: session.tokenUsage,
                duration: session.lastActivity.getTime() - session.createdAt.getTime(),
                lastActivity: session.lastActivity,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get session stats: ${error.message}`, error.stack);
            return null;
        }
    }
    async cleanupInactiveSessions(inactiveThreshold = 86400000) {
        try {
            const cutoffDate = new Date(Date.now() - inactiveThreshold);
            const result = await this.prisma.agentSessionNew.updateMany({
                where: {
                    lastActivity: { lt: cutoffDate },
                    status: 'active',
                },
                data: {
                    status: 'inactive',
                },
            });
            const inactiveSessions = await this.prisma.agentSessionNew.findMany({
                where: {
                    lastActivity: { lt: cutoffDate },
                    status: 'inactive',
                },
                select: { id: true },
            });
            for (const session of inactiveSessions) {
                await this.cacheManager.del(`session:${session.id}`);
            }
            this.logger.log(`Cleaned up ${result.count} inactive sessions`);
            return result.count;
        }
        catch (error) {
            this.logger.error(`Failed to cleanup inactive sessions: ${error.message}`, error.stack);
            return 0;
        }
    }
};
exports.SessionMemoryService = SessionMemoryService;
exports.SessionMemoryService = SessionMemoryService = SessionMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        event_emitter_1.EventEmitter2, Object])
], SessionMemoryService);
//# sourceMappingURL=session-memory.service.js.map