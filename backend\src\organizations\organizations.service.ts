import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Organization, Role } from '@prisma/client';
import {
    CreateOrganizationDto,
    UpdateOrganizationDto,
    OrganizationDto,
    OrganizationListQueryDto,
    OrganizationListResponseDto,
    OrganizationStatsDto,
    OrganizationSettingsDto,
    OrganizationBrandingDto,
    OrganizationUsageDto,
    OrganizationInviteDto,
    OrganizationMemberDto,
    OrganizationPermissionDto,
    BulkMemberActionDto
} from './dto/organizations.dto';
import * as crypto from 'crypto';

@Injectable()
export class OrganizationsService {
    constructor(private prisma: PrismaService) { }

    async create(createOrganizationDto: CreateOrganizationDto, createdBy: string): Promise<OrganizationDto> {
        // Check if slug is unique
        const existingOrg = await this.prisma.organization.findUnique({
            where: { slug: createOrganizationDto.slug }
        });

        if (existingOrg) {
            throw new ConflictException('Organization slug already exists');
        }

        // Check domain uniqueness if provided
        if (createOrganizationDto.domain) {
            const existingDomain = await this.prisma.organization.findFirst({
                where: { domain: createOrganizationDto.domain }
            });

            if (existingDomain) {
                throw new ConflictException('Domain already in use');
            }
        }

        const organization = await this.prisma.organization.create({
            data: {
                name: createOrganizationDto.name,
                slug: createOrganizationDto.slug,
                domain: createOrganizationDto.domain,
                settings: createOrganizationDto.settings || {},
                branding: createOrganizationDto.branding || {},
                plan: createOrganizationDto.plan || 'free',
                maxUsers: createOrganizationDto.maxUsers,
                features: createOrganizationDto.features || [],
            },
            include: {
                users: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        role: true,
                        isActive: true,
                        createdAt: true
                    }
                },
                _count: {
                    select: {
                        users: true,
                        workflows: true,
                        agents: true,
                        tools: true,
                        providers: true
                    }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(createdBy, 'ORGANIZATION_CREATED', organization.id, {
            name: organization.name,
            slug: organization.slug,
            plan: organization.plan
        });

        return this.mapToOrganizationDto(organization);
    }

    async findAll(query: OrganizationListQueryDto): Promise<OrganizationListResponseDto> {
        const {
            page = 1,
            limit = 20,
            search,
            plan,
            isActive,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = query;

        const skip = (page - 1) * limit;

        const where: any = {
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { slug: { contains: search, mode: 'insensitive' } },
                    { domain: { contains: search, mode: 'insensitive' } }
                ]
            }),
            ...(plan && { plan }),
            ...(isActive !== undefined && { isActive })
        };

        const [organizations, total] = await Promise.all([
            this.prisma.organization.findMany({
                where,
                skip,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    users: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                            role: true,
                            isActive: true,
                            lastLoginAt: true
                        }
                    },
                    _count: {
                        select: {
                            users: true,
                            workflows: true,
                            agents: true,
                            tools: true,
                            providers: true,
                            sessions: true
                        }
                    }
                }
            }),
            this.prisma.organization.count({ where })
        ]);

        return {
            organizations: organizations.map(org => this.mapToOrganizationDto(org as any)),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }

    async findOne(id: string): Promise<OrganizationDto> {
        const organization = await this.prisma.organization.findUnique({
            where: { id },
            include: {
                users: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        role: true,
                        isActive: true,
                        lastLoginAt: true,
                        createdAt: true
                    },
                    orderBy: { createdAt: 'desc' }
                },
                _count: {
                    select: {
                        users: true,
                        workflows: true,
                        agents: true,
                        tools: true,
                        providers: true,
                        sessions: true
                    }
                }
            }
        });

        if (!organization) {
            throw new NotFoundException('Organization not found');
        }

        return this.mapToOrganizationDto(organization as any);
    }

    async update(id: string, updateOrganizationDto: UpdateOrganizationDto, updatedBy: string): Promise<OrganizationDto> {
        const existingOrg = await this.prisma.organization.findUnique({
            where: { id }
        });

        if (!existingOrg) {
            throw new NotFoundException('Organization not found');
        }

        // Check slug uniqueness if being updated
        if (updateOrganizationDto.slug && updateOrganizationDto.slug !== existingOrg.slug) {
            const slugExists = await this.prisma.organization.findUnique({
                where: { slug: updateOrganizationDto.slug }
            });

            if (slugExists) {
                throw new ConflictException('Slug already in use');
            }
        }

        // Check domain uniqueness if being updated
        if (updateOrganizationDto.domain && updateOrganizationDto.domain !== existingOrg.domain) {
            const domainExists = await this.prisma.organization.findFirst({
                where: { domain: updateOrganizationDto.domain }
            });

            if (domainExists) {
                throw new ConflictException('Domain already in use');
            }
        }

        const updateData: any = {};

        if (updateOrganizationDto.name) updateData.name = updateOrganizationDto.name;
        if (updateOrganizationDto.slug) updateData.slug = updateOrganizationDto.slug;
        if (updateOrganizationDto.domain !== undefined) updateData.domain = updateOrganizationDto.domain;
        if (updateOrganizationDto.isActive !== undefined) updateData.isActive = updateOrganizationDto.isActive;
        if (updateOrganizationDto.plan) updateData.plan = updateOrganizationDto.plan;
        if (updateOrganizationDto.maxUsers !== undefined) updateData.maxUsers = updateOrganizationDto.maxUsers;
        if (updateOrganizationDto.features) updateData.features = updateOrganizationDto.features;
        if (updateOrganizationDto.planExpires !== undefined) updateData.planExpires = updateOrganizationDto.planExpires;

        const organization = await this.prisma.organization.update({
            where: { id },
            data: updateData,
            include: {
                users: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        role: true,
                        isActive: true,
                        lastLoginAt: true,
                        createdAt: true
                    }
                },
                _count: {
                    select: {
                        users: true,
                        workflows: true,
                        agents: true,
                        tools: true,
                        providers: true
                    }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(updatedBy, 'ORGANIZATION_UPDATED', organization.id, {
            changes: updateData,
            previousValues: {
                name: existingOrg.name,
                slug: existingOrg.slug,
                domain: existingOrg.domain,
                isActive: existingOrg.isActive,
                plan: existingOrg.plan
            }
        });

        return this.mapToOrganizationDto(organization as any);
    }

    async updateSettings(id: string, settings: OrganizationSettingsDto, updatedBy: string): Promise<OrganizationDto> {
        const organization = await this.prisma.organization.findUnique({
            where: { id }
        });

        if (!organization) {
            throw new NotFoundException('Organization not found');
        }

        const updatedOrg = await this.prisma.organization.update({
            where: { id },
            data: { settings },
            include: {
                users: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        role: true,
                        isActive: true,
                        lastLoginAt: true,
                        createdAt: true
                    }
                },
                _count: {
                    select: {
                        users: true,
                        workflows: true,
                        agents: true,
                        tools: true,
                        providers: true
                    }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(updatedBy, 'ORGANIZATION_SETTINGS_UPDATED', id, {
            newSettings: settings,
            previousSettings: organization.settings
        });

        return this.mapToOrganizationDto(updatedOrg as any);
    }

    async updateBranding(id: string, branding: OrganizationBrandingDto, updatedBy: string): Promise<OrganizationDto> {
        const organization = await this.prisma.organization.findUnique({
            where: { id }
        });

        if (!organization) {
            throw new NotFoundException('Organization not found');
        }

        const updatedOrg = await this.prisma.organization.update({
            where: { id },
            data: { branding },
            include: {
                users: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        role: true,
                        isActive: true,
                        lastLoginAt: true,
                        createdAt: true
                    }
                },
                _count: {
                    select: {
                        users: true,
                        workflows: true,
                        agents: true,
                        tools: true,
                        providers: true
                    }
                }
            }
        });

        // Create audit log
        await this.createAuditLog(updatedBy, 'ORGANIZATION_BRANDING_UPDATED', id, {
            newBranding: branding,
            previousBranding: organization.branding
        });

        return this.mapToOrganizationDto(updatedOrg as any);
    }

    async getStats(id: string): Promise<OrganizationStatsDto> {
        const [users, workflows, agents, tools, providers, sessions, recentActivity] = await Promise.all([
            this.prisma.user.groupBy({
                by: ['role', 'isActive'],
                where: { organizationId: id },
                _count: { role: true }
            }),
            this.prisma.workflow.count({
                where: { organizationId: id }
            }),
            this.prisma.agent.count({
                where: { organizationId: id }
            }),
            this.prisma.toolDefinition.count({
                where: { organizationId: id }
            }),
            this.prisma.provider.count({
                where: { organizationId: id }
            }),
            this.prisma.session.count({
                where: { organizationId: id, isActive: true }
            }),
            this.prisma.user.count({
                where: {
                    organizationId: id,
                    lastLoginAt: {
                        gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
                    }
                }
            })
        ]);

        const userStats = users.reduce((acc, group) => {
            const key = `${group.role.toLowerCase()}${group.isActive ? '' : '_inactive'}`;
            acc[key] = group._count.role;
            return acc;
        }, {} as Record<string, number>);

        const totalUsers = users.reduce((sum, group) => sum + group._count.role, 0);
        const activeUsers = users
            .filter(group => group.isActive)
            .reduce((sum, group) => sum + group._count.role, 0);

        return {
            totalUsers,
            activeUsers,
            inactiveUsers: totalUsers - activeUsers,
            usersByRole: userStats,
            workflows,
            agents,
            tools,
            providers,
            activeSessions: sessions,
            recentActivity,
            storageUsed: 0, // TODO: Calculate based on actual usage
            apiCalls: 0, // TODO: Calculate from usage logs
            costThisMonth: 0 // TODO: Calculate from billing data
        };
    }

    async getUsage(id: string, period: 'day' | 'week' | 'month' = 'month'): Promise<OrganizationUsageDto> {
        const startDate = new Date();

        switch (period) {
            case 'day':
                startDate.setDate(startDate.getDate() - 1);
                break;
            case 'week':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case 'month':
                startDate.setMonth(startDate.getMonth() - 1);
                break;
        }

        const [workflowExecutions, agentSessions, toolExecutions, apiUsage] = await Promise.all([
            this.prisma.workflowExecution.count({
                where: {
                    workflow: { organizationId: id },
                    startedAt: { gte: startDate }
                }
            }),
            this.prisma.agentSession.count({
                where: {
                    agent: { organizationId: id },
                    createdAt: { gte: startDate }
                }
            }),
            this.prisma.toolExecution.count({
                where: {
                    organizationId: id,
                    createdAt: { gte: startDate }
                }
            }),
            this.prisma.aiUsageLog.aggregate({
                where: {
                    organizationId: id,
                    createdAt: { gte: startDate }
                },
                _sum: {
                    totalTokens: true,
                    totalCost: true
                },
                _count: {
                    id: true
                }
            })
        ]);

        return {
            period,
            startDate,
            endDate: new Date(),
            workflowExecutions,
            agentSessions,
            toolExecutions,
            apiCalls: apiUsage._count.id || 0,
            totalTokens: apiUsage._sum.totalTokens || 0,
            totalCost: apiUsage._sum.totalCost || 0,
            storageUsed: 0, // TODO: Calculate actual storage
            bandwidthUsed: 0 // TODO: Calculate actual bandwidth
        };
    }

    async getMembers(id: string): Promise<OrganizationMemberDto[]> {
        const users = await this.prisma.user.findMany({
            where: { organizationId: id },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
                isActive: true,
                lastLoginAt: true,
                createdAt: true,
                avatar: true,
                _count: {
                    select: {
                        createdWorkflows: true,
                        createdAgents: true,
                        createdTools: true,
                        sessions: true
                    }
                }
            },
            orderBy: { createdAt: 'desc' }
        });

        return users.map(user => ({
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: `${user.firstName} ${user.lastName}`,
            email: user.email,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            joinedAt: user.createdAt,
            stats: {
                workflows: user._count.createdWorkflows,
                agents: user._count.createdAgents,
                tools: user._count.createdTools,
                sessions: user._count.sessions
            }
        }));
    }

    async bulkMemberAction(id: string, bulkActionDto: BulkMemberActionDto, actionBy: string): Promise<{ success: number; failed: number }> {
        const { userIds, action, data } = bulkActionDto;
        let success = 0;
        let failed = 0;

        for (const userId of userIds) {
            try {
                switch (action) {
                    case 'activate':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId: id },
                            data: { isActive: true }
                        });
                        break;
                    case 'deactivate':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId: id },
                            data: { isActive: false }
                        });
                        break;
                    case 'updateRole':
                        if (data?.role) {
                            await this.prisma.user.update({
                                where: { id: userId, organizationId: id },
                                data: { role: data.role }
                            });
                        }
                        break;
                    case 'remove':
                        await this.prisma.user.update({
                            where: { id: userId, organizationId: id },
                            data: { isActive: false }
                        });
                        break;
                }
                success++;

                // Create audit log
                await this.createAuditLog(actionBy, `MEMBER_BULK_${action.toUpperCase()}`, userId, data);
            } catch (error) {
                failed++;
            }
        }

        return { success, failed };
    }

    async remove(id: string, deletedBy: string): Promise<void> {
        const organization = await this.prisma.organization.findUnique({
            where: { id }
        });

        if (!organization) {
            throw new NotFoundException('Organization not found');
        }

        // Soft delete by deactivating
        await this.prisma.organization.update({
            where: { id },
            data: { isActive: false }
        });

        // Create audit log
        await this.createAuditLog(deletedBy, 'ORGANIZATION_DELETED', id, {
            name: organization.name,
            slug: organization.slug
        });
    }

    private async createAuditLog(userId: string, action: string, resourceId: string, details: any) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { organizationId: true }
            });

            if (user) {
                await this.prisma.auditLog.create({
                    data: {
                        userId,
                        action,
                        resource: 'ORGANIZATION',
                        resourceId,
                        details,
                        organizationId: user.organizationId
                    }
                });
            }
        } catch (error) {
            // Log error but don't fail the main operation
            console.error('Failed to create audit log:', error);
        }
    }

    private mapToOrganizationDto(organization: any): OrganizationDto {
        return {
            id: organization.id,
            name: organization.name,
            slug: organization.slug,
            domain: organization.domain,
            isActive: organization.isActive,
            plan: organization.plan,
            planExpires: organization.planExpires,
            maxUsers: organization.maxUsers,
            features: organization.features,
            settings: organization.settings,
            branding: organization.branding,
            members: organization.users?.map((user: any) => ({
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                fullName: `${user.firstName} ${user.lastName}`,
                email: user.email,
                role: user.role,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                joinedAt: user.createdAt
            })) || [],
            stats: organization._count ? {
                users: organization._count.users,
                workflows: organization._count.workflows,
                agents: organization._count.agents,
                tools: organization._count.tools,
                providers: organization._count.providers,
                sessions: organization._count.sessions || 0
            } : undefined,
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt
        };
    }
}