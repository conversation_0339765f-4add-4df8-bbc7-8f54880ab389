{"name": "synapseai-backend", "version": "1.0.0", "description": "SynapseAI Backend - Production AI Orchestration Platform", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "tsx prisma/seed.ts", "setup": "tsx scripts/setup.ts", "setup:force": "tsx scripts/setup.ts -- --force", "setup:production": "tsx scripts/setup.ts -- --production", "db:reset": "prisma migrate reset --force && npm run prisma:seed", "dev": "npm run setup && npm run start:dev"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/cache-manager": "^2.0.0", "@prisma/client": "^5.0.0", "cache-manager-redis-store": "^3.0.1", "redis": "^4.6.0", "bcryptjs": "^2.4.3", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13", "socket.io": "^4.7.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcryptjs": "^2.4.2", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.1", "typescript": "^5.1.3"}}