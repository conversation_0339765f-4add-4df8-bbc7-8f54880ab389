{"version": 3, "file": "auth.dto.js", "sourceRoot": "", "sources": ["../../../src/auth/dto/auth.dto.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,2CAA0C;AAG7B,QAAA,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC/C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;IACrE,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACvC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC,QAAQ,EAAE;IACrE,UAAU,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACvC,CAAC,CAAC;AAEU,QAAA,cAAc,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC/C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,KAAK,CAAC,iEAAiE,EACjE,0EAA0E,CAAC;IACpF,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC9D,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5D,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,+BAA+B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7E,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE;SACzB,GAAG,CAAC,CAAC,EAAE,iDAAiD,CAAC;SACzD,GAAG,CAAC,EAAE,CAAC;SACP,KAAK,CAAC,cAAc,EAAE,+DAA+D,CAAC;IACzF,WAAW,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE,0CAA0C,CAAC;CACjG,CAAC,CAAC;AAEU,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,2BAA2B,CAAC;CAC7D,CAAC,CAAC;AAEU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC/C,gBAAgB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAEU,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC;IACnD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,KAAK,CAAC,iEAAiE,EACjE,0EAA0E,CAAC;IACpF,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE;CAC5B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE;IACxD,OAAO,EAAE,uBAAuB;IAChC,IAAI,EAAE,CAAC,iBAAiB,CAAC;CAC1B,CAAC,CAAC;AAEU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,8BAA8B,CAAC;IAClE,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;SACpB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,KAAK,CAAC,iEAAiE,EACjE,0EAA0E,CAAC;IACpF,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE;CAC5B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,eAAe,EAAE;IAC3D,OAAO,EAAE,uBAAuB;IAChC,IAAI,EAAE,CAAC,iBAAiB,CAAC;CAC1B,CAAC,CAAC;AAEU,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC;IACnD,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,2BAA2B,CAAC;CACxD,CAAC,CAAC;AAEU,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,2BAA2B,CAAC;CACxD,CAAC,CAAC;AAEU,QAAA,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IACzE,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE;IACvE,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,QAAQ,EAAE;IACvD,WAAW,EAAE,OAAC,CAAC,MAAM,CAAC;QACpB,KAAK,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;QACrD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,aAAa,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACrC,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE;KAC9C,CAAC,CAAC,QAAQ,EAAE;CACd,CAAC,CAAC;AAEU,QAAA,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IACvC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC;IAC/C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IACjE,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wBAAwB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC9D,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5D,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAEU,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC;IACpD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;SACjB,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;SAChD,KAAK,CAAC,iEAAiE,EACjE,0EAA0E,CAAC;IACpF,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE;CAC5B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,eAAe,EAAE;IACxD,OAAO,EAAE,uBAAuB;IAChC,IAAI,EAAE,CAAC,iBAAiB,CAAC;CAC1B,CAAC,CAAC;AAEU,QAAA,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC3C,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC;IAC1C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;CAClE,CAAC,CAAC;AAEU,QAAA,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IACtC,QAAQ,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;IACvD,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;QACf,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACnC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAClC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC/B,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACnC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QAC7B,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAChC,CAAC;IACF,SAAS,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CACrC,CAAC,CAAC;AAGU,QAAA,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5C,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC;IAC1C,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACnD,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC;IAC/C,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;AAEU,QAAA,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IACzC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5D,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IAC3C,WAAW,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,qCAAqC,CAAC;IAC9E,SAAS,EAAE,OAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC/B,CAAC,CAAC;AAGH,MAAa,QAAS,SAAQ,IAAA,yBAAY,EAAC,mBAAW,CAAC;CAAG;AAA1D,4BAA0D;AAC1D,MAAa,WAAY,SAAQ,IAAA,yBAAY,EAAC,sBAAc,CAAC;CAAG;AAAhE,kCAAgE;AAChE,MAAa,eAAgB,SAAQ,IAAA,yBAAY,EAAC,0BAAkB,CAAC;CAAG;AAAxE,0CAAwE;AACxE,MAAa,iBAAkB,SAAQ,IAAA,yBAAY,EAAC,4BAAoB,CAAC;CAAG;AAA5E,8CAA4E;AAC5E,MAAa,gBAAiB,SAAQ,IAAA,yBAAY,EAAC,2BAAmB,CAAC;CAAG;AAA1E,4CAA0E;AAC1E,MAAa,iBAAkB,SAAQ,IAAA,yBAAY,EAAC,4BAAoB,CAAC;CAAG;AAA5E,8CAA4E;AAC5E,MAAa,YAAa,SAAQ,IAAA,yBAAY,EAAC,uBAAe,CAAC;CAAG;AAAlE,oCAAkE;AAClE,MAAa,YAAa,SAAQ,IAAA,yBAAY,EAAC,uBAAe,CAAC;CAAG;AAAlE,oCAAkE;AAClE,MAAa,gBAAiB,SAAQ,IAAA,yBAAY,EAAC,2BAAmB,CAAC;CAAG;AAA1E,4CAA0E;AAC1E,MAAa,aAAc,SAAQ,IAAA,yBAAY,EAAC,wBAAgB,CAAC;CAAG;AAApE,sCAAoE;AACpE,MAAa,eAAgB,SAAQ,IAAA,yBAAY,EAAC,0BAAkB,CAAC;CAAG;AAAxE,0CAAwE;AACxE,MAAa,iBAAkB,SAAQ,IAAA,yBAAY,EAAC,4BAAoB,CAAC;CAAG;AAA5E,8CAA4E;AAC5E,MAAa,YAAa,SAAQ,IAAA,yBAAY,EAAC,uBAAe,CAAC;CAAG;AAAlE,oCAAkE;AAClE,MAAa,kBAAmB,SAAQ,IAAA,yBAAY,EAAC,6BAAqB,CAAC;CAAG;AAA9E,gDAA8E;AAC9E,MAAa,eAAgB,SAAQ,IAAA,yBAAY,EAAC,0BAAkB,CAAC;CAAG;AAAxE,0CAAwE"}