"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TaskTrackerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskTrackerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const apix_gateway_1 = require("../../apix/apix.gateway");
const client_1 = require("@prisma/client");
let TaskTrackerService = TaskTrackerService_1 = class TaskTrackerService {
    constructor(prisma, cacheManager, apixGateway) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.apixGateway = apixGateway;
        this.logger = new common_1.Logger(TaskTrackerService_1.name);
    }
    async analyzeAndCreateTasks(agentId, sessionId, input) {
        const tasks = this.analyzeInput(input);
        const createdTasks = [];
        for (const taskDef of tasks) {
            const task = await this.createTask(agentId, sessionId, taskDef);
            createdTasks.push(task);
        }
        return createdTasks;
    }
    async createTask(agentId, sessionId, taskDef) {
        const task = await this.prisma.agentTask.create({
            data: {
                name: taskDef.name,
                description: taskDef.description,
                agentId,
                sessionId,
                type: taskDef.type,
                status: client_1.TaskStatus.PENDING,
                priority: taskDef.priority,
                input: taskDef.input,
                context: taskDef.context || {},
                metadata: taskDef.metadata || {},
                dependencies: taskDef.dependencies || [],
                maxRetries: taskDef.maxRetries || 3,
            },
        });
        await this.cacheManager.set(`task:${task.id}`, task, 3600000);
        await this.apixGateway.emitAgentEvent('system', agentId, 'task.created', {
            taskId: task.id,
            agentId,
            sessionId,
            type: taskDef.type,
        }, { priority: 'normal' });
        return task;
    }
    async updateTaskProgress(taskId, progress) {
        await this.prisma.agentTask.update({
            where: { id: taskId },
            data: {
                status: progress.status,
                ...(progress.output && { output: progress.output }),
                ...(progress.error && { error: progress.error }),
                ...(progress.status === client_1.TaskStatus.RUNNING && !progress.startedAt && { startedAt: new Date() }),
                ...(progress.status === client_1.TaskStatus.COMPLETED && { completedAt: new Date() }),
                updatedAt: new Date(),
            },
        });
        const cachedTask = await this.cacheManager.get(`task:${taskId}`);
        if (cachedTask) {
            Object.assign(cachedTask, progress);
            await this.cacheManager.set(`task:${taskId}`, cachedTask, 3600000);
        }
        const task = await this.prisma.agentTask.findUnique({
            where: { id: taskId },
            select: { agentId: true },
        });
        if (task) {
            await this.apixGateway.emitAgentEvent('system', task.agentId, 'task.progress', {
                taskId,
                ...progress,
            }, { priority: 'high' });
        }
    }
    analyzeInput(input) {
        const inputText = typeof input === 'string' ? input : JSON.stringify(input);
        const sentences = inputText.split(/[.!?]+/).filter(s => s.trim().length > 0);
        if (sentences.length <= 1) {
            return [{
                    name: 'Process Input',
                    description: 'Process the provided input',
                    type: 'general',
                    priority: 1,
                    input,
                }];
        }
        return sentences.map((sentence, index) => ({
            name: `Task ${index + 1}`,
            description: sentence.trim(),
            type: 'subtask',
            priority: index + 1,
            input: sentence.trim(),
        }));
    }
};
exports.TaskTrackerService = TaskTrackerService;
exports.TaskTrackerService = TaskTrackerService = TaskTrackerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, apix_gateway_1.ApixGateway])
], TaskTrackerService);
//# sourceMappingURL=task-tracker.service.js.map