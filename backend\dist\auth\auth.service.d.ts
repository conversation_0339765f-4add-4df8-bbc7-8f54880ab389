import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { SessionsService } from '../sessions/sessions.service';
import { LoginDto, RegisterDto, RefreshTokenDto, ForgotPasswordDto, ResetPasswordDto, ChangePasswordDto, EnableMFADto, VerifyMFADto, UpdateProfileDto, CreateAPIKeyDto, AuthResponse, MFASetupResponse, APIKeyResponse } from './dto/auth.dto';
import { Cache } from 'cache-manager';
export declare class AuthService {
    private prisma;
    private jwtService;
    private configService;
    private sessionsService;
    private cacheManager;
    private readonly jwtSecret;
    private readonly jwtRefreshSecret;
    private readonly jwtExpiresIn;
    private readonly jwtRefreshExpiresIn;
    constructor(prisma: PrismaService, jwtService: JwtService, configService: ConfigService, sessionsService: SessionsService, cacheManager: Cache);
    register(registerDto: RegisterDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse>;
    login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse>;
    refreshToken(refreshTokenDto: RefreshTokenDto, ipAddress?: string, userAgent?: string): Promise<AuthResponse>;
    logout(userId: string): Promise<void>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void>;
    changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void>;
    setupMFA(userId: string): Promise<MFASetupResponse>;
    enableMFA(userId: string, enableMFADto: EnableMFADto): Promise<void>;
    disableMFA(userId: string, verifyMFADto: VerifyMFADto): Promise<void>;
    updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<void>;
    validateUser(payload: any): Promise<{
        organization: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: import("@prisma/client/runtime/library").JsonValue;
            branding: import("@prisma/client/runtime/library").JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        };
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        preferences: import("@prisma/client/runtime/library").JsonValue;
        email: string;
        password: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.Role;
        lastLoginAt: Date | null;
        mfaEnabled: boolean;
        mfaSecret: string | null;
        backupCodes: string[];
        passwordResetToken: string | null;
        passwordResetExpires: Date | null;
        emailVerified: boolean;
        emailVerificationToken: string | null;
        loginAttempts: number;
        lockoutUntil: Date | null;
    }>;
    checkPermission(userId: string, resource: string, action: string, scope?: any): Promise<boolean>;
    createAPIKey(userId: string, createAPIKeyDto: CreateAPIKeyDto): Promise<APIKeyResponse>;
    private generateTokens;
    private getRolePermissions;
    private logAuditEvent;
}
