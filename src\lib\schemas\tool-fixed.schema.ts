import { z } from 'zod';

export const ToolTypeEnum = z.enum([
    'FUNCTION_CALL',
    'RAG',
    'API_FETCH',
    'BROWSER_AUTOMATION',
    'DATABASE',
    'CUSTOM_LOGIC'
]);

export const ToolExecutionStatusEnum = z.enum([
    'PENDING',
    'RUNNING',
    'COMPLETED',
    'FAILED',
    'CANCELLED',
    'TIMEOUT'
]);

export const ToolCacheStrategyEnum = z.enum([
    'NONE',
    'INPUT_HASH',
    'TIME_BASED',
    'DEPENDENCY_BASED',
    'CUSTOM'
]);

export const SkillCategoryEnum = z.enum([
    'COMMUNICATION',
    'DATA_ANALYSIS',
    'AUTOMATION',
    'CONTENT_GENERATION',
    'DECISION_MAKING',
    'INTEGRATION',
    'CUSTOM'
]);

export const ToolDefinitionSchema = z.object({
    id: z.string().optional(),
    name: z.string().min(1, 'Tool name is required'),
    description: z.string().optional(),
    category: SkillCategoryEnum.default('CUSTOM'),
    type: ToolTypeEnum.default('FUNCTION_CALL'),
    config: z.record(z.string(), z.any()).default({}),
    inputSchema: z.record(z.string(), z.any()).default({}),
    outputSchema: z.record(z.string(), z.any()).default({}),
    timeout: z.number().int().min(1000).max(300000).default(30000),
    retryPolicy: z.record(z.string(), z.any()).default({}),
    cacheStrategy: ToolCacheStrategyEnum.default('INPUT_HASH'),
    cacheTTL: z.number().int().min(0).default(3600),
    version: z.string().default('1.0.0'),
    tags: z.array(z.string()).default([]),
    documentation: z.string().optional(),
    examples: z.array(z.record(z.string(), z.any())).default([]),
    isPublic: z.boolean().default(false),
    isActive: z.boolean().default(true),
    dependencies: z.array(z.string()).default([]),
    requirements: z.record(z.string(), z.any()).default({}),
    organizationId: z.string().optional(),
});

export const CreateToolDefinitionSchema = ToolDefinitionSchema.omit({ id: true });

export const UpdateToolDefinitionSchema = ToolDefinitionSchema.partial().omit({
    id: true,
    createdBy: true,
    organizationId: true
});

export const ToolVersionSchema = z.object({
    id: z.string().optional(),
    toolId: z.string(),
    version: z.string().default('1.0.0'),
    config: z.record(z.string(), z.any()).default({}),
    inputSchema: z.record(z.string(), z.any()).default({}),
    outputSchema: z.record(z.string(), z.any()).default({}),
    changelog: z.string().optional(),
    isActive: z.boolean().default(true),
    requirements: z.record(z.string(), z.any()).default({}),
});

export const CreateToolVersionSchema = ToolVersionSchema.omit({ id: true });

export const ToolCompositionSchema = z.object({
    id: z.string().optional(),
    name: z.string().min(1, 'Composition name is required'),
    description: z.string().optional(),
    steps: z.array(z.string()).default([]),
    config: z.record(z.string(), z.any()).default({}),
    inputSchema: z.record(z.string(), z.any()).default({}),
    outputSchema: z.record(z.string(), z.any()).default({}),
    version: z.string().default('1.0.0'),
    tags: z.array(z.string()).default([]),
    isPublic: z.boolean().default(false),
    isActive: z.boolean().default(true),
    organizationId: z.string().optional(),
});

export const CreateToolCompositionSchema = ToolCompositionSchema.omit({ id: true });

export const ToolCompositionStepSchema = z.object({
    id: z.string().optional(),
    compositionId: z.string(),
    toolId: z.string(),
    stepIndex: z.number().int().min(0),
    stepName: z.string().optional(),
    inputMapping: z.record(z.string(), z.any()).default({}),
    outputMapping: z.record(z.string(), z.any()).default({}),
    conditions: z.record(z.string(), z.any()).default({}),
    dependencies: z.array(z.string()).default([]),
    continueOnError: z.boolean().default(false),
    retryCount: z.number().int().min(0).max(10).default(0),
    fallbackTool: z.string().optional(),
});

export const CreateToolCompositionStepSchema = ToolCompositionStepSchema.omit({ id: true });

export const ToolExecutionSchema = z.object({
    id: z.string().optional(),
    toolId: z.string(),
    input: z.record(z.string(), z.any()).default({}),
    output: z.record(z.string(), z.any()).optional(),
    status: ToolExecutionStatusEnum.default('PENDING'),
    startedAt: z.date().optional(),
    completedAt: z.date().optional(),
    duration: z.number().optional(),
    error: z.string().optional(),
    metadata: z.record(z.string(), z.any()).default({}),
    organizationId: z.string().optional(),
});

export const ToolExecuteRequestSchema = z.object({
    toolId: z.string(),
    input: z.record(z.string(), z.any()).default({}),
    executionId: z.string().optional(),
    sessionId: z.string().optional(),
    metadata: z.record(z.string(), z.any()).default({}),
    options: z.object({
        timeout: z.number().optional(),
        retryCount: z.number().optional(),
        useCache: z.boolean().default(false),
        priority: z.enum(['low', 'normal', 'high']).default('normal'),
    }).default(() => ({ useCache: false, priority: 'normal' as const })),
});

export const ToolCompositionExecuteRequestSchema = z.object({
    compositionId: z.string(),
    input: z.record(z.string(), z.any()).default({}),
    executionId: z.string().optional(),
    sessionId: z.string().optional(),
    metadata: z.record(z.string(), z.any()).default({}),
    options: z.object({
        timeout: z.number().optional(),
        parallel: z.boolean().optional(),
        continueOnError: z.boolean().default(false),
        priority: z.enum(['low', 'normal', 'high']).default('normal'),
    }).default(() => ({ continueOnError: false, priority: 'normal' as const })),
});

export const ToolCacheSchema = z.object({
    id: z.string().optional(),
    toolId: z.string(),
    inputHash: z.string(),
    input: z.record(z.string(), z.any()).default({}),
    output: z.record(z.string(), z.any()).default({}),
    duration: z.number().optional(),
    expiresAt: z.date().optional(),
    hitCount: z.number().default(0),
    organizationId: z.string().optional(),
});

export const ToolAnalyticsSchema = z.object({
    id: z.string().optional(),
    toolId: z.string(),
    date: z.date(),
    executionCount: z.number().default(0),
    successCount: z.number().default(0),
    errorCount: z.number().default(0),
    averageDuration: z.number().default(0),
    totalDuration: z.number().default(0),
    cacheHitRate: z.number().default(0),
    organizationId: z.string().optional(),
});

export const ToolAPIKeySchema = z.object({
    id: z.string().optional(),
    toolId: z.string(),
    name: z.string().min(1, 'API key name is required'),
    description: z.string().optional(),
    keyValue: z.string(),
    isActive: z.boolean().default(true),
    expiresAt: z.date().optional(),
    lastUsedAt: z.date().optional(),
    usageCount: z.number().default(0),
    organizationId: z.string().optional(),
});

// Validation schemas for API requests
export const CreateToolExecutionRequestSchema = z.object({
    toolId: z.string(),
    input: z.record(z.string(), z.any()),
    metadata: z.record(z.string(), z.any()).optional(),
});

export const UpdateToolExecutionRequestSchema = z.object({
    status: ToolExecutionStatusEnum.optional(),
    output: z.record(z.string(), z.any()).optional(),
    error: z.string().optional(),
    metadata: z.record(z.string(), z.any()).optional(),
});

// Export types
export type ToolDefinition = z.infer<typeof ToolDefinitionSchema>;
export type CreateToolDefinition = z.infer<typeof CreateToolDefinitionSchema>;
export type UpdateToolDefinition = z.infer<typeof UpdateToolDefinitionSchema>;
export type ToolExecution = z.infer<typeof ToolExecutionSchema>;
export type ToolExecuteRequest = z.infer<typeof ToolExecuteRequestSchema>;
export type ToolComposition = z.infer<typeof ToolCompositionSchema>;
export type ToolCompositionStep = z.infer<typeof ToolCompositionStepSchema>;
export type ToolCache = z.infer<typeof ToolCacheSchema>;
export type ToolAnalytics = z.infer<typeof ToolAnalyticsSchema>;
export type ToolAPIKey = z.infer<typeof ToolAPIKeySchema>;