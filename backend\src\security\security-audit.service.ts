import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { LoggingService } from '../logging/logging.service';

@Injectable()
export class SecurityAuditService {
    constructor(
        private prisma: PrismaService,
        private logger: LoggingService,
    ) { }

    async auditUserAccess(userId: string, organizationId: string) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId, organizationId },
            select: {
                id: true,
                email: true,
                role: true,
                lastLoginAt: true,

            },
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }



        return { success: true };
    }
}