import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { ConnectionManagerService } from './services/connection-manager.service';
import { SubscriptionManagerService } from './services/subscription-manager.service';
import * as pako from 'pako';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  user?: any;
}

interface APXEvent {
  type: string;
  channel?: string;
  data: any;
  timestamp: string;
  organizationId?: string;
  compressed?: boolean;
  retryCount?: number;
  priority?: 'low' | 'normal' | 'high';
  metadata?: Record<string, any>;
}

@Injectable()
@WebSocketGateway({
  namespace: '/apix',
  cors: {
    origin: '*',
  },
  transports: ['websocket', 'polling'],
})
export class ApixGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ApixGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();
  private userSockets = new Map<string, Set<string>>();
  private organizationSockets = new Map<string, Set<string>>();
  private roomSubscriptions = new Map<string, Set<string>>();
  private compressionThreshold = 1024;
  private eventHistory = new Map<string, APXEvent[]>();
  private maxHistorySize = 1000;

  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2,
    private connectionManager: ConnectionManagerService,
    private subscriptionManager: SubscriptionManagerService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('APIX Gateway initialized with production features');
    
    server.engine.generateId = () => {
      return `apix_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    };

    server.engine.compression = true;
    server.engine.perMessageDeflate = {
      threshold: this.compressionThreshold,
      concurrencyLimit: 10,
      memLevel: 7,
    };
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      this.logger.log(`Client attempting connection: ${client.id}`);

      const token = client.handshake.auth?.token;
      const organizationId = client.handshake.auth?.organizationId;

      if (!token || !organizationId) {
        this.logger.warn(`Unauthenticated connection attempt: ${client.id}`);
        client.disconnect();
        return;
      }

      const payload = await this.jwtService.verifyAsync(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (!user || user.organizationId !== organizationId) {
        this.logger.warn(`Invalid user or organization: ${client.id}`);
        client.disconnect();
        return;
      }

      client.userId = user.id;
      client.organizationId = organizationId;
      client.user = user;

      this.connectedClients.set(client.id, client);

      if (!this.userSockets.has(user.id)) {
        this.userSockets.set(user.id, new Set());
      }
      this.userSockets.get(user.id)!.add(client.id);

      if (!this.organizationSockets.has(organizationId)) {
        this.organizationSockets.set(organizationId, new Set());
      }
      this.organizationSockets.get(organizationId)!.add(client.id);

      client.join(`organization:${organizationId}`);

      await this.connectionManager.createConnection(
        client.id,
        client.id,
        user.id,
        organizationId,
        'WEB_CLIENT',
        {},
        client.handshake.address,
        client.handshake.headers['user-agent'],
      );

      client.emit('apix:connected', {
        clientId: client.id,
        userId: user.id,
        organizationId,
        features: {
          compression: true,
          latencyTracking: true,
          eventReplay: true,
          maxRetryAttempts: 5,
          heartbeatInterval: 30000,
        },
        serverTime: Date.now(),
      });

      const history = this.eventHistory.get(organizationId);
      if (history && history.length > 0) {
        client.emit('apix:replay', {
          events: history.slice(-50),
          totalCount: history.length,
          timestamp: Date.now(),
        });
      }

      this.logger.log(`Client connected successfully: ${client.id} (User: ${user.id}, Org: ${organizationId})`);

      this.eventEmitter.emit('client.connected', {
        clientId: client.id,
        userId: user.id,
        organizationId,
        timestamp: Date.now(),
      });

    } catch (error) {
      this.logger.error(`Connection authentication failed: ${error.message}`, error.stack);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Client disconnected: ${client.id}`);

    await this.connectionManager.removeConnection(client.id);
    await this.subscriptionManager.unsubscribeAll(client.id);

    this.connectedClients.delete(client.id);

    if (client.userId) {
      const userSockets = this.userSockets.get(client.userId);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.userSockets.delete(client.userId);
        }
      }
    }

    if (client.organizationId) {
      const orgSockets = this.organizationSockets.get(client.organizationId);
      if (orgSockets) {
        orgSockets.delete(client.id);
        if (orgSockets.size === 0) {
          this.organizationSockets.delete(client.organizationId);
        }
      }
    }

    for (const [roomId, socketIds] of this.roomSubscriptions.entries()) {
      socketIds.delete(client.id);
      if (socketIds.size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
    }

    this.eventEmitter.emit('client.disconnected', {
      clientId: client.id,
      userId: client.userId,
      organizationId: client.organizationId,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:event')
  async handleEvent(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() event: APXEvent,
  ) {
    try {
      event.organizationId = client.organizationId;
      event.timestamp = new Date().toISOString();

      if (event.compressed && event.data) {
        try {
          const decompressed = pako.inflate(event.data, { to: 'string' });
          event.data = JSON.parse(decompressed);
          event.compressed = false;
        } catch (error) {
          this.logger.error('Event decompression failed', error);
          return;
        }
      }

      this.storeEventInHistory(client.organizationId!, event);

      if (event.channel) {
        this.server.to(event.channel).emit(event.type, event.data);
      } else {
        this.server.to(`organization:${client.organizationId}`).emit(event.type, event.data);
      }

      this.eventEmitter.emit(`apix.${event.type}`, {
        ...event,
        clientId: client.id,
        userId: client.userId,
      });

    } catch (error) {
      this.logger.error(`Event handling failed: ${error.message}`, error.stack);
      client.emit('apix:error', {
        message: 'Event processing failed',
        originalEvent: event.type,
      });
    }
  }

  @SubscribeMessage('join:room')
  handleJoinRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() roomId: string,
  ) {
    client.join(roomId);
    
    if (!this.roomSubscriptions.has(roomId)) {
      this.roomSubscriptions.set(roomId, new Set());
    }
    this.roomSubscriptions.get(roomId)!.add(client.id);

    this.logger.log(`Client ${client.id} joined room: ${roomId}`);
    
    client.emit('room:joined', { roomId, timestamp: Date.now() });
  }

  @SubscribeMessage('leave:room')
  handleLeaveRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() roomId: string,
  ) {
    client.leave(roomId);
    
    const roomSockets = this.roomSubscriptions.get(roomId);
    if (roomSockets) {
      roomSockets.delete(client.id);
      if (roomSockets.size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
    }

    this.logger.log(`Client ${client.id} left room: ${roomId}`);
    
    client.emit('room:left', { roomId, timestamp: Date.now() });
  }

  @SubscribeMessage('apix:subscribe')
  async handleSubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[]; filters?: Record<string, any> },
  ) {
    for (const channel of data.channels) {
      await this.subscriptionManager.subscribeToChannel(client.id, channel, data.filters);
      client.join(channel);
      
      if (!this.roomSubscriptions.has(channel)) {
        this.roomSubscriptions.set(channel, new Set());
      }
      this.roomSubscriptions.get(channel)!.add(client.id);
    }

    client.emit('apix:subscribed', {
      channels: data.channels,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:unsubscribe')
  async handleUnsubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[] },
  ) {
    for (const channel of data.channels) {
      await this.subscriptionManager.unsubscribeFromChannel(client.id, channel);
      client.leave(channel);
      
      const channelSockets = this.roomSubscriptions.get(channel);
      if (channelSockets) {
        channelSockets.delete(client.id);
        if (channelSockets.size === 0) {
          this.roomSubscriptions.delete(channel);
        }
      }
    }

    client.emit('apix:unsubscribed', {
      channels: data.channels,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:request_replay')
  handleRequestReplay(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { since?: string; limit?: number },
  ) {
    const history = this.eventHistory.get(client.organizationId!);
    if (!history) {
      client.emit('apix:replay', { events: [], totalCount: 0, timestamp: Date.now() });
      return;
    }

    let events = history;
    
    if (data.since) {
      const sinceTime = new Date(data.since).getTime();
      events = history.filter(event => new Date(event.timestamp).getTime() > sinceTime);
    }

    if (data.limit) {
      events = events.slice(-data.limit);
    }

    client.emit('apix:replay', {
      events,
      totalCount: history.length,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:heartbeat')
  async handleHeartbeat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { timestamp: number },
  ) {
    const latency = Date.now() - data.timestamp;
    
    await this.connectionManager.updateHeartbeat(client.id, latency);
    
    client.emit('apix:heartbeat', {
      clientTimestamp: data.timestamp,
      serverTimestamp: Date.now(),
      latency,
    });
  }

  @SubscribeMessage('subscribe-tool-events')
  async handleToolEventSubscription(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { organizationId: string; toolId?: string }
  ): Promise<void> {
    const channel = data.toolId 
      ? `tool-events.${data.organizationId}.${data.toolId}`
      : `tool-events.${data.organizationId}`;

    await this.subscriptionManager.subscribeToChannel(client.id, channel);
    
    client.emit('subscription-confirmed', {
      channel,
      message: 'Subscribed to tool events',
    });

    this.logger.debug(`Client ${client.id} subscribed to tool events: ${channel}`);
  }

  @SubscribeMessage('subscribe-tool-execution')
  async handleToolExecutionSubscription(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { executionId: string }
  ): Promise<void> {
    const channel = `tool-execution.${data.executionId}`;
    await this.subscriptionManager.subscribeToChannel(client.id, channel);
    
    client.emit('subscription-confirmed', {
      channel,
      message: 'Subscribed to tool execution events',
    });

    this.logger.debug(`Client ${client.id} subscribed to execution: ${data.executionId}`);
  }

  @SubscribeMessage('unsubscribe-tool-events')
  async handleToolEventUnsubscription(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { organizationId: string; toolId?: string }
  ): Promise<void> {
    const channel = data.toolId 
      ? `tool-events.${data.organizationId}.${data.toolId}`
      : `tool-events.${data.organizationId}`;

    await this.subscriptionManager.unsubscribeFromChannel(client.id, channel);
    
    client.emit('unsubscription-confirmed', {
      channel,
      message: 'Unsubscribed from tool events',
    });

    this.logger.debug(`Client ${client.id} unsubscribed from tool events: ${channel}`);
  }

  // Production-grade event emission methods

  async emitToUser(userId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const userSockets = this.userSockets.get(userId);
    if (!userSockets || userSockets.size === 0) {
      this.logger.warn(`No active sockets for user: ${userId}`);
      return;
    }

    const event = this.createEvent(eventType, data, options);
    
    for (const socketId of userSockets) {
      const socket = this.connectedClients.get(socketId);
      if (socket) {
        this.emitToSocket(socket, event);
      }
    }
  }

  async emitToOrganization(organizationId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const event = this.createEvent(eventType, data, options);
    event.organizationId = organizationId;

    this.storeEventInHistory(organizationId, event);
    this.server.to(`organization:${organizationId}`).emit(eventType, data);
  }

  async emitToRoom(roomId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const event = this.createEvent(eventType, data, options);
    this.server.to(roomId).emit(eventType, data);
  }

  async emitWorkflowEvent(
    organizationId: string,
    workflowId: string,
    eventType: string,
    data: any,
    options: {
      compress?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ) {
    const enrichedData = {
      ...data,
      workflowId,
      organizationId,
      timestamp: Date.now(),
    };

    await this.emitToRoom(`workflow:${workflowId}`, eventType, enrichedData, options);
    await this.emitToOrganization(organizationId, eventType, enrichedData, options);
  }

  async emitAgentEvent(
    organizationId: string,
    agentId: string,
    eventType: string,
    data: any,
    options: {
      compress?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ) {
    const enrichedData = {
      ...data,
      agentId,
      organizationId,
      timestamp: Date.now(),
    };

    await this.emitToRoom(`agent:${agentId}`, eventType, enrichedData, options);
    await this.emitToOrganization(organizationId, eventType, enrichedData, options);
  }

  async emitToolEvent(
    organizationId: string,
    toolId: string,
    eventType: string,
    payload: any,
    options: { priority?: 'low' | 'normal' | 'high' | 'critical' } = {}
  ): Promise<void> {
    const event = {
      id: this.generateEventId(),
      type: eventType,
      channel: `tool-events.${organizationId}`,
      payload: {
        toolId,
        ...payload,
        timestamp: new Date().toISOString(),
      },
      priority: options.priority || 'normal',
      organizationId,
    };

    await this.storeEvent(event);

    const subscribers = await this.subscriptionManager.getChannelSubscribers(event.channel);
    
    for (const sessionId of subscribers) {
      this.server.to(sessionId).emit('tool-event', event);
    }

    this.logger.debug(`Emitted tool event: ${eventType} for tool ${toolId}`);
  }

  async emitToolExecutionEvent(
    organizationId: string,
    executionId: string,
    eventType: 'start' | 'progress' | 'complete' | 'error' | 'cancelled',
    payload: any
  ): Promise<void> {
    const event = {
      id: this.generateEventId(),
      type: `tool.execution.${eventType}`,
      channel: `tool-execution.${executionId}`,
      payload: {
        executionId,
        ...payload,
        timestamp: new Date().toISOString(),
      },
      priority: 'high',
      organizationId,
    };

    await this.storeEvent(event);

    const subscribers = await this.subscriptionManager.getChannelSubscribers(event.channel);
    
    for (const sessionId of subscribers) {
      this.server.to(sessionId).emit('tool-execution-event', event);
    }

    await this.emitToolEvent(organizationId, payload.toolId, `execution.${eventType}`, payload);
  }

  // Helper methods

  private createEvent(eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}): APXEvent {
    let processedData = data;
    let compressed = false;

    if (options.compress && JSON.stringify(data).length > this.compressionThreshold) {
      try {
        processedData = pako.deflate(JSON.stringify(data));
        compressed = true;
      } catch (error) {
        this.logger.warn('Failed to compress event data', error);
      }
    }

    return {
      type: eventType,
      data: processedData,
      timestamp: new Date().toISOString(),
      compressed,
      priority: options.priority || 'normal',
    };
  }

  private emitToSocket(socket: AuthenticatedSocket, event: APXEvent): void {
    socket.emit(event.type, event.data);
  }

  private storeEventInHistory(organizationId: string, event: APXEvent): void {
    if (!this.eventHistory.has(organizationId)) {
      this.eventHistory.set(organizationId, []);
    }

    const history = this.eventHistory.get(organizationId)!;
    history.push(event);

    if (history.length > this.maxHistorySize) {
      history.shift();
    }
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async storeEvent(event: any): Promise<void> {
    try {
      await this.prisma.apiXEvent.create({
        data: {
          id: event.id,
          type: event.type,
          channel: event.channel,
          payload: event.payload,
          priority: event.priority,
          organizationId: event.organizationId,
        },
      });
    } catch (error) {
      this.logger.error('Failed to store event in database', error);
    }
  }
}