export declare class BranchEvaluatorService {
    private readonly logger;
    evaluateCondition(condition: string, variables: Record<string, any>, nodeResult?: any): boolean;
    evaluateExpression(expression: string, variables: Record<string, any>, nodeResult?: any): any;
    evaluateSwitch(value: any, cases: Record<string, any>, defaultCase: any): any;
    evaluateLoop(items: any[], iterationLimit?: number): {
        valid: boolean;
        count: number;
    };
    private safeEval;
    isEqual(a: any, b: any): boolean;
    isNotEqual(a: any, b: any): boolean;
    isGreaterThan(a: any, b: any): boolean;
    isLessThan(a: any, b: any): boolean;
    isGreaterThanOrEqual(a: any, b: any): boolean;
    isLessThanOrEqual(a: any, b: any): boolean;
    contains(a: any, b: any): boolean;
    startsWith(a: string, b: string): boolean;
    endsWith(a: string, b: string): boolean;
    and(a: boolean, b: boolean): boolean;
    or(a: boolean, b: boolean): boolean;
    not(a: boolean): boolean;
}
