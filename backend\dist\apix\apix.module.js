"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApixModule = void 0;
const common_1 = require("@nestjs/common");
const apix_gateway_1 = require("./apix.gateway");
const event_router_service_1 = require("./services/event-router.service");
const subscription_manager_service_1 = require("./services/subscription-manager.service");
const message_queue_service_1 = require("./services/message-queue.service");
const connection_manager_service_1 = require("./services/connection-manager.service");
const prisma_module_1 = require("../prisma/prisma.module");
const jwt_1 = require("@nestjs/jwt");
const cache_manager_1 = require("@nestjs/cache-manager");
const config_1 = require("@nestjs/config");
const redisStore = require("cache-manager-redis-store");
let ApixModule = class ApixModule {
};
exports.ApixModule = ApixModule;
exports.ApixModule = ApixModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get("JWT_SECRET"),
                    signOptions: { expiresIn: "24h" },
                }),
                inject: [config_1.ConfigService],
            }),
            cache_manager_1.CacheModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    store: redisStore,
                    host: configService.get("REDIS_HOST", "localhost"),
                    port: configService.get("REDIS_PORT", 6379),
                    password: configService.get("REDIS_PASSWORD"),
                    db: configService.get("REDIS_DB", 0),
                    ttl: 3600,
                    max: 10000,
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        providers: [
            apix_gateway_1.ApixGateway,
            event_router_service_1.EventRouterService,
            subscription_manager_service_1.SubscriptionManagerService,
            message_queue_service_1.MessageQueueService,
            connection_manager_service_1.ConnectionManagerService,
        ],
        exports: [
            apix_gateway_1.ApixGateway,
            event_router_service_1.EventRouterService,
            subscription_manager_service_1.SubscriptionManagerService,
            message_queue_service_1.MessageQueueService,
            connection_manager_service_1.ConnectionManagerService,
        ],
    })
], ApixModule);
//# sourceMappingURL=apix.module.js.map