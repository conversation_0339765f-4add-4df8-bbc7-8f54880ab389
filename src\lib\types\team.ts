/**
 * 👥 Team & User Management Types
 * Centralized type definitions for team functionality
 */

export interface TeamUser {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    emailVerified: boolean;
    lastLoginAt: string | null;
    createdAt: string;
    organization: {
        id: string;
        name: string;
        slug: string;
    };
    profile: {
        phoneNumber?: string;
        jobTitle?: string;
        department?: string;
        timezone?: string;
        avatar?: string;
    };
    permissions: string[];
    sessions: {
        total: number;
        active: number;
        lastDevice?: string;
        lastLocation?: string;
    };
    isActive: boolean;
}

export type UserRole = 'USER' | 'ADMIN' | 'SUPER_ADMIN' | 'ORG_ADMIN';
export type UserStatus = 'active' | 'suspended' | 'inactive' | 'pending';

export interface TeamStats {
    total: number;
    active: number;
    inactive: number;
    pending: number;
    admins: number;
    recentJoins: number;
}

export interface TeamInvitation {
    id: string;
    email: string;
    role: UserRole;
    status: 'pending' | 'accepted' | 'expired' | 'cancelled';
    invitedBy: {
        id: string;
        name: string;
        avatar?: string;
    };
    invitedAt: string;
    expiresAt: string;
    acceptedAt?: string;
}

export interface UserFormData {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    phoneNumber?: string;
    jobTitle?: string;
    department?: string;
    sendInvitation: boolean;
}

export interface UpdateUserData {
    firstName?: string;
    lastName?: string;
    role?: UserRole;
    status?: UserStatus;
    phoneNumber?: string;
    jobTitle?: string;
    department?: string;
    permissions?: string[];
}

export interface TeamFilters {
    search?: string;
    role?: UserRole | 'all';
    status?: UserStatus | 'all';
    department?: string;
    sortBy?: 'name' | 'email' | 'role' | 'lastLogin' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
    page: number;
    limit: number;
}

export interface UserActivity {
    id: string;
    userId: string;
    action: string;
    resource: string;
    timestamp: string;
    ipAddress?: string;
    userAgent?: string;
    details?: Record<string, any>;
}

export interface Permission {
    id: string;
    name: string;
    description: string;
    category: string;
    isSystem: boolean;
}

export interface RoleDefinition {
    role: UserRole;
    name: string;
    description: string;
    permissions: string[];
    isSystem: boolean;
    canInviteUsers: boolean;
    canManageUsers: boolean;
    canViewReports: boolean;
    canManageSettings: boolean;
}