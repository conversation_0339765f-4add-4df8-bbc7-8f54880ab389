import { Controller, Get, Post, Body, UseGuards, Request, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SecurityService } from './security.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { TenantGuard } from '../auth/tenant.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';

@ApiTags('security')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('security')
export class SecurityController {
    constructor(private readonly securityService: SecurityService) { }

    @Get('policy')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get organization security policy' })
    @ApiResponse({ status: 200, description: 'Security policy retrieved successfully' })
    async getSecurityPolicy(@Request() req: any) {
        return this.securityService.getSecurityPolicy(req.user.organizationId);
    }

    @Post('scan')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Start security vulnerability scan' })
    @ApiResponse({ status: 200, description: 'Security scan completed' })
    @HttpCode(HttpStatus.OK)
    async securityScan(@Request() req: any) {
        return this.securityService.scanForVulnerabilities(req.user.organizationId);
    }

    @Get('report')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get security report' })
    @ApiResponse({ status: 200, description: 'Security report generated' })
    async getSecurityReport(@Request() req: any) {
        return this.securityService.generateSecurityReport(req.user.organizationId);
    }
}