"use client";

import { useState, useCallback, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import {
    Save,
    Play,
    Square,
    Undo,
    Redo,
    ZoomIn,
    ZoomOut,
    Download,
    Settings,
    Plus,
    Trash2,
} from "lucide-react";
import ReactFlow, {
    Node,
    Edge,
    addEdge,
    Background,
    Controls,
    MiniMap,
    useNodesState,
    useEdgesState,
    Connection,
    EdgeChange,
    NodeChange,
    ReactFlowProvider,
} from "reactflow";
import "reactflow/dist/style.css";

interface WorkflowBuilderProps {
    workflow?: any;
    onSave: () => void;
}

const nodeTypes = [
    { type: "trigger", label: "Trigger", color: "bg-blue-500" },
    { type: "action", label: "Action", color: "bg-green-500" },
    { type: "condition", label: "Condition", color: "bg-yellow-500" },
    { type: "loop", label: "Loop", color: "bg-purple-500" },
    { type: "delay", label: "Delay", color: "bg-orange-500" },
    { type: "variable", label: "Variable", color: "bg-pink-500" },
];

const initialNodes: Node[] = [
    {
        id: "1",
        type: "input",
        data: { label: "Start" },
        position: { x: 250, y: 0 },
        className: "bg-green-500 text-white",
    },
];

const initialEdges: Edge[] = [];

function WorkflowBuilderContent({ workflow, onSave }: WorkflowBuilderProps) {
    const { toast } = useToast();
    const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
    const [selectedNode, setSelectedNode] = useState<Node | null>(null);
    const [workflowName, setWorkflowName] = useState(workflow?.name || "");
    const [workflowDescription, setWorkflowDescription] = useState(workflow?.description || "");
    const reactFlowWrapper = useRef<HTMLDivElement>(null);
    const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

    const onConnect = useCallback(
        (params: Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    const onDragOver = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = "move";
    }, []);

    const onDrop = useCallback(
        (event: React.DragEvent) => {
            event.preventDefault();

            const type = event.dataTransfer.getData("application/reactflow");
            if (typeof type === "undefined" || !type) {
                return;
            }

            const position = reactFlowInstance?.screenToFlowPosition({
                x: event.clientX,
                y: event.clientY,
            });

            const newNode: Node = {
                id: `${Date.now()}`,
                type,
                position,
                data: { label: `${type} node` },
                className: nodeTypes.find(n => n.type === type)?.color + " text-white",
            };

            setNodes((nds) => nds.concat(newNode));
        },
        [reactFlowInstance, setNodes]
    );

    const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
        setSelectedNode(node);
    }, []);

    const addNode = (type: string) => {
        const newNode: Node = {
            id: `${Date.now()}`,
            type,
            position: { x: Math.random() * 400, y: Math.random() * 400 },
            data: { label: `${type} node` },
            className: nodeTypes.find(n => n.type === type)?.color + " text-white",
        };
        setNodes((nds) => nds.concat(newNode));
    };

    const deleteNode = (nodeId: string) => {
        setNodes((nds) => nds.filter(node => node.id !== nodeId));
        setEdges((eds) => eds.filter(edge => edge.source !== nodeId && edge.target !== nodeId));
        if (selectedNode?.id === nodeId) {
            setSelectedNode(null);
        }
    };

    const saveWorkflow = async () => {
        try {
            const workflowData = {
                name: workflowName,
                description: workflowDescription,
                definition: {
                    nodes,
                    edges,
                },
            };

            const response = await fetch(
                workflow ? `/api/workflows/${workflow.id}` : "/api/workflows",
                {
                    method: workflow ? "PUT" : "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(workflowData),
                }
            );

            if (!response.ok) throw new Error("Failed to save workflow");

            toast({
                title: "Success",
                description: `Workflow ${workflow ? "updated" : "created"} successfully`,
            });

            onSave();
        } catch (error) {
            toast({
                title: "Error",
                description: `Failed to ${workflow ? "update" : "create"} workflow`,
                variant: "destructive",
            });
        }
    };

    const executeWorkflow = async () => {
        if (!workflow) {
            toast({
                title: "Error",
                description: "Please save the workflow before executing",
                variant: "destructive",
            });
            return;
        }

        try {
            const response = await fetch(`/api/workflows/${workflow.id}/execute`, {
                method: "POST",
            });

            if (!response.ok) throw new Error("Failed to execute workflow");

            toast({
                title: "Success",
                description: "Workflow execution started",
            });
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to execute workflow",
                variant: "destructive",
            });
        }
    };

    return (
        <div className="h-[calc(100vh-12rem)] flex">
            {/* Left Sidebar - Node Palette */}
            <div className="w-64 border-r bg-card">
                <div className="p-4">
                    <h3 className="font-semibold mb-4">Node Palette</h3>
                    <div className="space-y-2">
                        {nodeTypes.map((nodeType) => (
                            <div
                                key={nodeType.type}
                                className={`p-3 rounded-lg cursor-pointer border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors ${nodeType.color} text-white text-center font-medium`}
                                draggable
                                onDragStart={(event) => {
                                    event.dataTransfer.setData("application/reactflow", nodeType.type);
                                    event.dataTransfer.effectAllowed = "move";
                                }}
                                onClick={() => addNode(nodeType.type)}
                            >
                                {nodeType.label}
                            </div>
                        ))}
                    </div>
                </div>

                <Separator />

                {/* Workflow Settings */}
                <div className="p-4">
                    <h3 className="font-semibold mb-4">Workflow Settings</h3>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="workflow-name">Name</Label>
                            <Input
                                id="workflow-name"
                                value={workflowName}
                                onChange={(e) => setWorkflowName(e.target.value)}
                                placeholder="Workflow name"
                            />
                        </div>
                        <div>
                            <Label htmlFor="workflow-description">Description</Label>
                            <Textarea
                                id="workflow-description"
                                value={workflowDescription}
                                onChange={(e) => setWorkflowDescription(e.target.value)}
                                placeholder="Workflow description"
                                rows={3}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Canvas */}
            <div className="flex-1 relative">
                {/* Toolbar */}
                <div className="absolute top-4 left-4 z-10 flex items-center gap-2 bg-background/90 backdrop-blur-sm p-2 rounded-lg border">
                    <Button size="sm" onClick={saveWorkflow} disabled={!workflowName}>
                        <Save className="h-4 w-4 mr-1" />
                        Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={executeWorkflow}>
                        <Play className="h-4 w-4 mr-1" />
                        Execute
                    </Button>
                    <Separator orientation="vertical" className="h-6" />
                    <Button size="sm" variant="ghost">
                        <Undo className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                        <Redo className="h-4 w-4" />
                    </Button>
                    <Separator orientation="vertical" className="h-6" />
                    <Button size="sm" variant="ghost">
                        <Download className="h-4 w-4" />
                    </Button>
                </div>

                <div ref={reactFlowWrapper} className="h-full">
                    <ReactFlow
                        nodes={nodes}
                        edges={edges}
                        onNodesChange={onNodesChange}
                        onEdgesChange={onEdgesChange}
                        onConnect={onConnect}
                        onInit={setReactFlowInstance}
                        onDrop={onDrop}
                        onDragOver={onDragOver}
                        onNodeClick={onNodeClick}
                        fitView
                    >
                        <Background />
                        <Controls />
                        <MiniMap />
                    </ReactFlow>
                </div>
            </div>

            {/* Right Sidebar - Node Properties */}
            <div className="w-80 border-l bg-card">
                <div className="p-4">
                    <h3 className="font-semibold mb-4">Node Properties</h3>
                    {selectedNode ? (
                        <div className="space-y-4">
                            <div>
                                <Label>Node ID</Label>
                                <Input value={selectedNode.id} disabled />
                            </div>
                            <div>
                                <Label>Node Type</Label>
                                <Input value={selectedNode.type || "default"} disabled />
                            </div>
                            <div>
                                <Label>Label</Label>
                                <Input
                                    value={selectedNode.data?.label || ""}
                                    onChange={(e) => {
                                        setNodes((nds) =>
                                            nds.map((node) =>
                                                node.id === selectedNode.id
                                                    ? { ...node, data: { ...node.data, label: e.target.value } }
                                                    : node
                                            )
                                        );
                                        setSelectedNode({
                                            ...selectedNode,
                                            data: { ...selectedNode.data, label: e.target.value },
                                        });
                                    }}
                                />
                            </div>
                            <div>
                                <Label>Position</Label>
                                <div className="grid grid-cols-2 gap-2">
                                    <Input
                                        placeholder="X"
                                        value={Math.round(selectedNode.position.x)}
                                        disabled
                                    />
                                    <Input
                                        placeholder="Y"
                                        value={Math.round(selectedNode.position.y)}
                                        disabled
                                    />
                                </div>
                            </div>
                            <Button
                                variant="destructive"
                                size="sm"
                                className="w-full"
                                onClick={() => deleteNode(selectedNode.id)}
                            >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Node
                            </Button>
                        </div>
                    ) : (
                        <div className="text-center text-muted-foreground py-8">
                            <Settings className="h-8 w-8 mx-auto mb-2" />
                            <p>Select a node to edit its properties</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default function WorkflowBuilder(props: WorkflowBuilderProps) {
    return (
        <ReactFlowProvider>
            <WorkflowBuilderContent {...props} />
        </ReactFlowProvider>
    );
}