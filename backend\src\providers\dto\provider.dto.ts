import { IsString, IsOptional, IsBoolean, IsN<PERSON>ber, IsEnum, IsObject, IsArray, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProviderType, ProviderStatus, RequestStatus } from '@prisma/client';

export class CreateProviderDto {
  @ApiProperty({ description: 'Provider name' })
  @IsString()
  name: string;

  @ApiProperty({ enum: ProviderType, description: 'Provider type' })
  @IsEnum(ProviderType)
  type: ProviderType;

  @ApiProperty({ description: 'Provider configuration' })
  @IsObject()
  config: Record<string, any>;

  @ApiPropertyOptional({ description: 'Provider priority for routing' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  priority?: number;

  @ApiPropertyOptional({ description: 'Whether provider is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateProviderDto {
  @ApiPropertyOptional({ description: 'Provider name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Provider configuration' })
  @IsOptional()
  @IsObject()
  config?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Provider priority for routing' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  priority?: number;

  @ApiPropertyOptional({ description: 'Whether provider is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateProviderModelDto {
  @ApiProperty({ description: 'Model identifier' })
  @IsString()
  modelId: string;

  @ApiProperty({ description: 'Human readable model name' })
  @IsString()
  displayName: string;

  @ApiPropertyOptional({ description: 'Model description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Model capabilities' })
  @IsObject()
  capabilities: Record<string, any>;

  @ApiPropertyOptional({ description: 'Context window size' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  contextWindow?: number;

  @ApiPropertyOptional({ description: 'Maximum tokens' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  @ApiPropertyOptional({ description: 'Input cost per 1K tokens' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  inputCostPer1k?: number;

  @ApiPropertyOptional({ description: 'Output cost per 1K tokens' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  outputCostPer1k?: number;

  @ApiPropertyOptional({ description: 'Custom endpoint URL' })
  @IsOptional()
  @IsString()
  customEndpoint?: string;

  @ApiPropertyOptional({ description: 'Custom headers' })
  @IsOptional()
  @IsObject()
  customHeaders?: Record<string, string>;

  @ApiPropertyOptional({ description: 'Whether model is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Whether model is default for provider' })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class CreateRoutingRuleDto {
  @ApiProperty({ description: 'Rule name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Rule description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Provider ID' })
  @IsOptional()
  @IsString()
  providerId?: string;

  @ApiPropertyOptional({ description: 'Model ID' })
  @IsOptional()
  @IsString()
  modelId?: string;

  @ApiProperty({ description: 'Rule conditions' })
  @IsObject()
  conditions: Record<string, any>;

  @ApiPropertyOptional({ description: 'Rule priority' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  priority?: number;

  @ApiPropertyOptional({ description: 'Fallback rules' })
  @IsOptional()
  @IsArray()
  fallbackRules?: Array<{ providerId: string; modelId?: string }>;

  @ApiPropertyOptional({ description: 'Whether rule is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AiRequestDto {
  @ApiProperty({ description: 'Request type' })
  @IsString()
  requestType: string;

  @ApiProperty({ description: 'Request input' })
  @IsObject()
  input: Record<string, any>;

  @ApiPropertyOptional({ description: 'Session ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: 'Executor type' })
  @IsOptional()
  @IsString()
  executorType?: string;

  @ApiPropertyOptional({ description: 'Executor ID' })
  @IsOptional()
  @IsString()
  executorId?: string;

  @ApiPropertyOptional({ description: 'Preferred provider ID' })
  @IsOptional()
  @IsString()
  preferredProviderId?: string;

  @ApiPropertyOptional({ description: 'Preferred model ID' })
  @IsOptional()
  @IsString()
  preferredModelId?: string;

  @ApiPropertyOptional({ description: 'Maximum latency in ms' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxLatencyMs?: number;

  @ApiPropertyOptional({ description: 'Maximum cost' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxCost?: number;

  @ApiPropertyOptional({ description: 'Required capabilities' })
  @IsOptional()
  @IsArray()
  requiredCapabilities?: string[];

  @ApiPropertyOptional({ description: 'Whether to enable streaming' })
  @IsOptional()
  @IsBoolean()
  streaming?: boolean;

  @ApiPropertyOptional({ description: 'Request metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ProviderHealthDto {
  @ApiProperty({ enum: ProviderStatus, description: 'Provider status' })
  @IsEnum(ProviderStatus)
  status: ProviderStatus;

  @ApiProperty({ description: 'Uptime percentage' })
  @IsNumber()
  @Min(0)
  @Max(100)
  uptime: number;

  @ApiProperty({ description: 'Average latency in ms' })
  @IsNumber()
  @Min(0)
  avgLatencyMs: number;

  @ApiProperty({ description: 'Error rate percentage' })
  @IsNumber()
  @Min(0)
  @Max(100)
  errorRate: number;

  @ApiPropertyOptional({ description: 'Rate limit remaining' })
  @IsOptional()
  @IsNumber()
  rateLimitRemaining?: number;

  @ApiPropertyOptional({ description: 'Rate limit reset time' })
  @IsOptional()
  rateLimitReset?: Date;

  @ApiPropertyOptional({ description: 'Last error message' })
  @IsOptional()
  @IsString()
  lastError?: string;

  @ApiPropertyOptional({ description: 'Consecutive errors count' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  consecutiveErrors?: number;

  @ApiPropertyOptional({ description: 'Health metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ProviderTestDto {
  @ApiProperty({ description: 'Test input' })
  @IsObject()
  input: Record<string, any>;

  @ApiPropertyOptional({ description: 'Model ID to test' })
  @IsOptional()
  @IsString()
  modelId?: string;
}

export class BulkProviderActionDto {
  @ApiProperty({ description: 'Provider IDs' })
  @IsArray()
  @IsString({ each: true })
  providerIds: string[];

  @ApiProperty({ description: 'Action to perform' })
  @IsString()
  action: 'activate' | 'deactivate' | 'delete' | 'test';
}

export class ProviderAnalyticsQueryDto {
  @ApiPropertyOptional({ description: 'Start date' })
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({ description: 'End date' })
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional({ description: 'Provider IDs to filter' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  providerIds?: string[];

  @ApiPropertyOptional({ description: 'Model IDs to filter' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  modelIds?: string[];

  @ApiPropertyOptional({ description: 'Executor types to filter' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  executorTypes?: string[];

  @ApiPropertyOptional({ description: 'Group by field' })
  @IsOptional()
  @IsString()
  groupBy?: 'provider' | 'model' | 'executorType' | 'date' | 'hour';
}