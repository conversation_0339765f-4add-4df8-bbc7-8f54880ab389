"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AgentCommunicationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCommunicationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const event_emitter_1 = require("@nestjs/event-emitter");
const client_1 = require("@prisma/client");
let AgentCommunicationService = AgentCommunicationService_1 = class AgentCommunicationService {
    constructor(prisma, apixGateway, eventEmitter) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(AgentCommunicationService_1.name);
        this.messageQueue = new Map();
        this.activeChannels = new Map();
        this.agentSubscriptions = new Map();
        this.initializeService();
    }
    async initializeService() {
        await this.loadActiveChannels();
        this.setupEventListeners();
    }
    async loadActiveChannels() {
        try {
            const channels = await this.prisma.agentCommunicationChannel.findMany({
                where: { isActive: true },
                include: {
                    participants: {
                        include: {
                            agent: {
                                select: { id: true, name: true, status: true }
                            }
                        }
                    }
                }
            });
            for (const channel of channels) {
                this.activeChannels.set(channel.id, {
                    id: channel.id,
                    name: channel.name,
                    participants: channel.participants.map(p => p.agentId),
                    type: channel.type,
                    organizationId: channel.organizationId,
                    isActive: channel.isActive,
                    metadata: channel.metadata,
                });
            }
            this.logger.log(`Loaded ${channels.length} active communication channels`);
        }
        catch (error) {
            this.logger.error('Failed to load communication channels', error);
        }
    }
    setupEventListeners() {
        this.eventEmitter.on('agent.status.changed', this.handleAgentStatusChange.bind(this));
        this.eventEmitter.on('workflow.agent.communication.required', this.handleWorkflowCommunication.bind(this));
    }
    async sendMessage(fromAgentId, toAgentId, content, options = {}) {
        const [fromAgent, toAgent] = await Promise.all([
            this.prisma.agentInstance.findUnique({
                where: { id: fromAgentId },
                select: { id: true, name: true, status: true, organizationId: true }
            }),
            this.prisma.agentInstance.findUnique({
                where: { id: toAgentId },
                select: { id: true, name: true, status: true, organizationId: true }
            })
        ]);
        if (!fromAgent || !toAgent) {
            throw new Error('One or both agents not found');
        }
        if (fromAgent.organizationId !== toAgent.organizationId) {
            throw new Error('Agents must be in the same organization');
        }
        if (toAgent.status !== client_1.AgentStatus.ACTIVE) {
            throw new Error('Target agent is not active');
        }
        const message = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            fromAgentId,
            toAgentId,
            type: options.type || 'notification',
            content,
            metadata: {
                priority: options.priority || 'normal',
                requiresResponse: options.requiresResponse || false,
                timeout: options.timeout || 30000,
                correlationId: options.correlationId,
                sessionId: options.sessionId,
                workflowId: options.workflowId,
            },
            timestamp: new Date(),
            status: 'pending',
        };
        await this.prisma.agentMessage.create({
            data: {
                id: message.id,
                fromAgentId: message.fromAgentId,
                toAgentId: message.toAgentId,
                type: message.type,
                content: message.content,
                metadata: message.metadata,
                status: message.status,
                organizationId: fromAgent.organizationId,
            }
        });
        if (!this.messageQueue.has(toAgentId)) {
            this.messageQueue.set(toAgentId, []);
        }
        this.messageQueue.get(toAgentId).push(message);
        await this.apixGateway.emitAgentEvent(fromAgent.organizationId, toAgentId, 'agent_message_received', {
            messageId: message.id,
            fromAgent: {
                id: fromAgent.id,
                name: fromAgent.name,
            },
            type: message.type,
            content: message.content,
            metadata: message.metadata,
            timestamp: message.timestamp,
        }, { priority: message.metadata?.priority === 'urgent' ? 'high' : 'normal' });
        await this.apixGateway.emitAgentEvent(fromAgent.organizationId, fromAgentId, 'agent_message_sent', {
            messageId: message.id,
            toAgent: {
                id: toAgent.id,
                name: toAgent.name,
            },
            type: message.type,
            status: 'delivered',
            timestamp: message.timestamp,
        });
        message.status = 'delivered';
        await this.prisma.agentMessage.update({
            where: { id: message.id },
            data: { status: 'delivered' }
        });
        if (message.metadata?.requiresResponse && message.metadata?.timeout) {
            setTimeout(async () => {
                const currentMessage = await this.prisma.agentMessage.findUnique({
                    where: { id: message.id }
                });
                if (currentMessage && currentMessage.status === 'delivered') {
                    await this.handleMessageTimeout(message);
                }
            }, message.metadata.timeout);
        }
        this.logger.log(`Message sent from ${fromAgent.name} to ${toAgent.name}: ${message.id}`);
        return message;
    }
    async respondToMessage(messageId, respondingAgentId, response) {
        const originalMessage = await this.prisma.agentMessage.findUnique({
            where: { id: messageId },
            include: {
                fromAgent: { select: { id: true, name: true, organizationId: true } },
                toAgent: { select: { id: true, name: true } }
            }
        });
        if (!originalMessage) {
            throw new Error('Original message not found');
        }
        if (originalMessage.toAgentId !== respondingAgentId) {
            throw new Error('Only the recipient can respond to this message');
        }
        const responseMessage = await this.sendMessage(respondingAgentId, originalMessage.fromAgentId, response, {
            type: 'response',
            correlationId: originalMessage.id,
            sessionId: originalMessage.metadata?.sessionId,
            workflowId: originalMessage.metadata?.workflowId,
        });
        await this.prisma.agentMessage.update({
            where: { id: messageId },
            data: { status: 'acknowledged' }
        });
        await this.apixGateway.emitAgentEvent(originalMessage.fromAgent.organizationId, originalMessage.fromAgentId, 'agent_message_acknowledged', {
            originalMessageId: messageId,
            responseMessageId: responseMessage.id,
            respondingAgent: {
                id: respondingAgentId,
                name: originalMessage.toAgent.name,
            },
            timestamp: new Date(),
        });
        return responseMessage;
    }
    async broadcastMessage(fromAgentId, organizationId, content, options = {}) {
        const fromAgent = await this.prisma.agentInstance.findUnique({
            where: { id: fromAgentId },
            select: { id: true, name: true, organizationId: true }
        });
        if (!fromAgent || fromAgent.organizationId !== organizationId) {
            throw new Error('Invalid sender agent or organization mismatch');
        }
        let targetAgents;
        if (options.targetAgentIds) {
            targetAgents = await this.prisma.agentInstance.findMany({
                where: {
                    id: { in: options.targetAgentIds },
                    organizationId,
                    status: client_1.AgentStatus.ACTIVE,
                },
                select: { id: true, name: true }
            });
        }
        else {
            targetAgents = await this.prisma.agentInstance.findMany({
                where: {
                    organizationId,
                    status: client_1.AgentStatus.ACTIVE,
                    id: {
                        notIn: [fromAgentId, ...(options.excludeAgentIds || [])]
                    }
                },
                select: { id: true, name: true }
            });
        }
        const messages = await Promise.all(targetAgents.map(agent => this.sendMessage(fromAgentId, agent.id, content, {
            type: 'broadcast',
            priority: options.priority,
            sessionId: options.sessionId,
            workflowId: options.workflowId,
        })));
        this.logger.log(`Broadcast message sent from ${fromAgent.name} to ${targetAgents.length} agents`);
        return messages;
    }
    async createCommunicationChannel(name, participantIds, organizationId, type = 'group', metadata) {
        const participants = await this.prisma.agentInstance.findMany({
            where: {
                id: { in: participantIds },
                organizationId,
                status: client_1.AgentStatus.ACTIVE,
            },
            select: { id: true, name: true }
        });
        if (participants.length !== participantIds.length) {
            throw new Error('Some participant agents not found or inactive');
        }
        const channel = await this.prisma.agentCommunicationChannel.create({
            data: {
                name,
                type,
                organizationId,
                isActive: true,
                metadata: metadata || {},
                participants: {
                    create: participantIds.map(agentId => ({ agentId }))
                }
            },
            include: {
                participants: {
                    include: {
                        agent: { select: { id: true, name: true } }
                    }
                }
            }
        });
        const channelObj = {
            id: channel.id,
            name: channel.name,
            participants: channel.participants.map(p => p.agentId),
            type: channel.type,
            organizationId: channel.organizationId,
            isActive: channel.isActive,
            metadata: channel.metadata,
        };
        this.activeChannels.set(channel.id, channelObj);
        for (const participant of participants) {
            await this.apixGateway.emitAgentEvent(organizationId, participant.id, 'agent_channel_created', {
                channelId: channel.id,
                channelName: name,
                type,
                participants: participants.map(p => ({ id: p.id, name: p.name })),
                timestamp: new Date(),
            });
        }
        this.logger.log(`Communication channel created: ${name} with ${participants.length} participants`);
        return channelObj;
    }
    async sendChannelMessage(channelId, fromAgentId, content, options = {}) {
        const channel = this.activeChannels.get(channelId);
        if (!channel) {
            throw new Error('Communication channel not found');
        }
        if (!channel.participants.includes(fromAgentId)) {
            throw new Error('Agent is not a participant in this channel');
        }
        const fromAgent = await this.prisma.agentInstance.findUnique({
            where: { id: fromAgentId },
            select: { id: true, name: true, organizationId: true }
        });
        if (!fromAgent) {
            throw new Error('Sender agent not found');
        }
        const targetAgentIds = channel.participants.filter(id => id !== fromAgentId);
        const messages = await Promise.all(targetAgentIds.map(agentId => this.sendMessage(fromAgentId, agentId, content, {
            type: 'notification',
            priority: options.priority,
            sessionId: options.sessionId,
            workflowId: options.workflowId,
        })));
        await this.apixGateway.emitToRoom(`channel:${channelId}`, 'channel_message', {
            channelId,
            channelName: channel.name,
            fromAgent: {
                id: fromAgent.id,
                name: fromAgent.name,
            },
            content,
            timestamp: new Date(),
            messageIds: messages.map(m => m.id),
        }, { priority: options.priority === 'urgent' ? 'high' : 'normal' });
        return messages;
    }
    async getAgentMessages(agentId, options = {}) {
        const where = {
            OR: [
                { fromAgentId: agentId },
                { toAgentId: agentId }
            ]
        };
        if (options.status) {
            where.status = options.status;
        }
        if (options.type) {
            where.type = options.type;
        }
        if (options.fromDate || options.toDate) {
            where.createdAt = {};
            if (options.fromDate) {
                where.createdAt.gte = options.fromDate;
            }
            if (options.toDate) {
                where.createdAt.lte = options.toDate;
            }
        }
        const [messages, total] = await Promise.all([
            this.prisma.agentMessage.findMany({
                where,
                include: {
                    fromAgent: { select: { id: true, name: true } },
                    toAgent: { select: { id: true, name: true } }
                },
                orderBy: { createdAt: 'desc' },
                take: options.limit || 50,
                skip: options.offset || 0,
            }),
            this.prisma.agentMessage.count({ where })
        ]);
        return {
            messages: messages.map(msg => ({
                id: msg.id,
                fromAgentId: msg.fromAgentId,
                toAgentId: msg.toAgentId,
                type: msg.type,
                content: msg.content,
                metadata: msg.metadata,
                timestamp: msg.createdAt,
                status: msg.status,
            })),
            total
        };
    }
    async subscribeToAgent(agentId, subscriberAgentId) {
        if (!this.agentSubscriptions.has(agentId)) {
            this.agentSubscriptions.set(agentId, new Set());
        }
        this.agentSubscriptions.get(agentId).add(subscriberAgentId);
        const [agent, subscriber] = await Promise.all([
            this.prisma.agentInstance.findUnique({
                where: { id: agentId },
                select: { id: true, name: true, organizationId: true }
            }),
            this.prisma.agentInstance.findUnique({
                where: { id: subscriberAgentId },
                select: { id: true, name: true }
            })
        ]);
        if (agent && subscriber) {
            await this.apixGateway.emitAgentEvent(agent.organizationId, agentId, 'agent_subscriber_added', {
                subscriber: {
                    id: subscriber.id,
                    name: subscriber.name,
                },
                timestamp: new Date(),
            });
        }
    }
    async unsubscribeFromAgent(agentId, subscriberAgentId) {
        const subscribers = this.agentSubscriptions.get(agentId);
        if (subscribers) {
            subscribers.delete(subscriberAgentId);
            if (subscribers.size === 0) {
                this.agentSubscriptions.delete(agentId);
            }
        }
    }
    async handleAgentStatusChange(payload) {
        const { agentId, status, organizationId } = payload;
        if (status === client_1.AgentStatus.INACTIVE || status === client_1.AgentStatus.ARCHIVED) {
            const subscribers = this.agentSubscriptions.get(agentId);
            if (subscribers) {
                for (const subscriberId of subscribers) {
                    await this.apixGateway.emitAgentEvent(organizationId, subscriberId, 'agent_status_changed', {
                        agentId,
                        status,
                        timestamp: new Date(),
                    });
                }
            }
            this.messageQueue.delete(agentId);
        }
    }
    async handleWorkflowCommunication(payload) {
        const { fromAgentId, toAgentId, workflowId, sessionId, content, type } = payload;
        await this.sendMessage(fromAgentId, toAgentId, content, {
            type: type || 'notification',
            workflowId,
            sessionId,
            priority: 'normal',
        });
    }
    async handleMessageTimeout(message) {
        await this.prisma.agentMessage.update({
            where: { id: message.id },
            data: { status: 'failed' }
        });
        const fromAgent = await this.prisma.agentInstance.findUnique({
            where: { id: message.fromAgentId },
            select: { organizationId: true }
        });
        if (fromAgent) {
            await this.apixGateway.emitAgentEvent(fromAgent.organizationId, message.fromAgentId, 'agent_message_timeout', {
                messageId: message.id,
                toAgentId: message.toAgentId,
                originalContent: message.content,
                timeout: message.metadata?.timeout,
                timestamp: new Date(),
            }, { priority: 'high' });
        }
    }
    async cleanup() {
        this.messageQueue.clear();
        this.activeChannels.clear();
        this.agentSubscriptions.clear();
    }
};
exports.AgentCommunicationService = AgentCommunicationService;
exports.AgentCommunicationService = AgentCommunicationService = AgentCommunicationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        event_emitter_1.EventEmitter2])
], AgentCommunicationService);
//# sourceMappingURL=agent-communication.service.js.map