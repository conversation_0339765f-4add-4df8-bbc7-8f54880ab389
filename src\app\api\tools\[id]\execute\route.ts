import { NextRequest, NextResponse } from 'next/server';
import apiClient from '@/lib/api-client';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    const data = await apiClient.post(`/tools/${params.id}/execute`, body);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Execute Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}