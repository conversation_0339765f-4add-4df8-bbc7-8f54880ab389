"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON>, <PERSON>, Monitor, Check } from "lucide-react";
import { useTheme } from "next-themes";
import apiClient from "@/lib/api-client";
import { useToast } from "@/components/ui/use-toast";

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);

  // Load theme preference from user settings
  useEffect(() => {
    const loadUserPreference = async () => {
      try {
        // Only try to load user preference if authenticated
        const token = document.cookie
          .split('; ')
          .find(row => row.startsWith('access_token='))
          ?.split('=')[1];

        if (token) {
          const userPreferences = await apiClient.get('/users/preferences') as any;
          if (userPreferences?.theme) {
            setTheme(userPreferences.theme);
          }
        }
      } catch (error) {
        // Silently fail and use local storage theme
        console.error('Failed to load user theme preference:', error);
      } finally {
        setMounted(true);
      }
    };

    loadUserPreference();
  }, [setTheme]);

  // Save theme preference to user settings
  const saveThemePreference = async (newTheme: string) => {
    try {
      setSaving(true);

      // Set theme immediately for better UX
      setTheme(newTheme);

      // Only try to save user preference if authenticated
      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('access_token='))
        ?.split('=')[1];

      if (token) {
        await apiClient.put('/users/preferences', {
          theme: newTheme
        });
      }
    } catch (error) {
      console.error('Failed to save theme preference:', error);
      toast({
        title: "Couldn't save preference",
        description: "Your theme preference will be lost when you clear browser data.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!mounted) {
    // Return a placeholder to avoid layout shift
    return (
      <Button variant="ghost" size="icon" disabled>
        <Sun className="h-5 w-5 opacity-50" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" disabled={saving}>
          {saving ? (
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          ) : theme === "light" ? (
            <Sun className="h-5 w-5" />
          ) : theme === "dark" ? (
            <Moon className="h-5 w-5" />
          ) : (
            <Monitor className="h-5 w-5" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => saveThemePreference("light")}>
          <Sun className="mr-2 h-4 w-4" />
          Light
          {theme === "light" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => saveThemePreference("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          Dark
          {theme === "dark" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => saveThemePreference("system")}>
          <Monitor className="mr-2 h-4 w-4" />
          System
          {theme === "system" && <Check className="ml-auto h-4 w-4" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}