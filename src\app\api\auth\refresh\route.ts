/**
 * Token Refresh API Route
 * Production-grade token refresh endpoint with MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { refreshTokens, setAuthCookies } from '@/lib/auth/auth-service';
import { trackSessionActivity } from '@/lib/mcp-tools';
import { jwtVerify } from 'jose';

export async function POST(req: NextRequest): Promise<NextResponse> {
    try {
        // Get refresh token from cookies
        const refreshToken = req.cookies.get('refresh_token')?.value;

        if (!refreshToken) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'No refresh token provided',
                    code: 'MISSING_REFRESH_TOKEN'
                },
                { status: 401 }
            );
        }

        // Extract user ID from token for tracking
        let userId = 'unknown';
        try {
            const secret = new TextEncoder().encode(
                process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret-for-dev'
            );
            const { payload } = await jwtVerify(refreshToken, secret, {
                algorithms: ['HS256']
            });

            if (payload.sub) {
                userId = payload.sub as string;
            }
        } catch (error) {
            // Continue with unknown user ID if token verification fails
        }

        // Refresh tokens
        const tokens = await refreshTokens(refreshToken);

        // Create response
        const response = NextResponse.json({
            success: true,
            tokens
        });

        // Set new auth cookies
        setAuthCookies(response, tokens);

        // Track token refresh in MCP Memory
        trackSessionActivity(userId, 'session_' + Date.now(), 'refreshed', {
            ip: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
            userAgent: req.headers.get('user-agent') || 'unknown',
            timestamp: new Date().toISOString()
        });

        return response;
    } catch (error: any) {
        console.error('Token refresh error:', error);

        // Clear cookies if refresh fails
        const response = NextResponse.json(
            {
                success: false,
                message: error.message || 'Token refresh failed',
                code: error.code || 'REFRESH_ERROR'
            },
            { status: 401 }
        );

        response.cookies.delete('access_token');
        response.cookies.delete('refresh_token');

        return response;
    }
}