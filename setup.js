#!/usr/bin/env node

/**
 * 🚀 SynapseAI Platform - Unified Setup Script
 * 
 * This script provides a complete production-ready setup for the SynapseAI platform:
 * - Environment configuration
 * - Database setup and seeding
 * - Frontend and backend initialization
 * - Health checks and validation
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SynapseAISetup {
    constructor() {
        this.startTime = Date.now();
        this.verbose = process.argv.includes('--verbose');
        this.force = process.argv.includes('--force');
        this.skipSeed = process.argv.includes('--skip-seed');
        this.production = process.argv.includes('--production');
    }

    log(message, level = 'info') {
        const icons = { info: '🔍', success: '✅', error: '❌', warn: '⚠️' };
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${icons[level]} [${timestamp}] ${message}`);
    }

    async runCommand(command, description, cwd = process.cwd()) {
        this.log(`Running: ${description}...`);

        try {
            const output = execSync(command, {
                cwd,
                stdio: this.verbose ? 'inherit' : 'pipe',
                encoding: 'utf8'
            });

            this.log(`${description} completed`, 'success');
            return output;
        } catch (error) {
            this.log(`${description} failed: ${error.message}`, 'error');
            throw error;
        }
    }

    generateSecureSecret(length = 64) {
        return crypto.randomBytes(length).toString('hex');
    }

    async setupEnvironment() {
        this.log('🔧 Setting up environment configuration...');

        // Backend environment
        const backendEnvPath = path.join('backend', '.env');
        const backendEnvExamplePath = path.join('backend', '.env.example');

        if (!fs.existsSync(backendEnvPath) || this.force) {
            if (fs.existsSync(backendEnvExamplePath)) {
                let envContent = fs.readFileSync(backendEnvExamplePath, 'utf8');

                // Replace placeholder secrets with secure ones
                envContent = envContent.replace(
                    /JWT_SECRET=".*"/,
                    `JWT_SECRET="${this.generateSecureSecret()}"`
                );
                envContent = envContent.replace(
                    /JWT_REFRESH_SECRET=".*"/,
                    `JWT_REFRESH_SECRET="${this.generateSecureSecret()}"`
                );
                envContent = envContent.replace(
                    /REDIS_PASSWORD=".*"/,
                    `REDIS_PASSWORD="${this.generateSecureSecret(32)}"`
                );
                envContent = envContent.replace(
                    /POSTGRES_PASSWORD=".*"/,
                    `POSTGRES_PASSWORD="${this.generateSecureSecret(32)}"`
                );

                // Set production environment if specified
                if (this.production) {
                    envContent = envContent.replace(
                        /NODE_ENV=".*"/,
                        'NODE_ENV="production"'
                    );
                }

                fs.writeFileSync(backendEnvPath, envContent);
                this.log('Backend .env file created with secure secrets', 'success');
            } else {
                this.log('Backend .env.example not found', 'warn');
            }
        }

        // Frontend environment
        const frontendEnvPath = '.env.local';
        const frontendEnvExamplePath = 'env.example';

        if (!fs.existsSync(frontendEnvPath) || this.force) {
            if (fs.existsSync(frontendEnvExamplePath)) {
                fs.copyFileSync(frontendEnvExamplePath, frontendEnvPath);
                this.log('Frontend .env.local file created', 'success');
            } else {
                this.log('Frontend env.example not found', 'warn');
            }
        }
    }

    async installDependencies() {
        this.log('📦 Installing dependencies...');

        // Install root dependencies
        await this.runCommand('npm install', 'Root dependencies installation');

        // Install backend dependencies
        await this.runCommand(
            'npm install',
            'Backend dependencies installation',
            path.join(process.cwd(), 'backend')
        );
    }

    async setupDatabase() {
        this.log('🗃️ Setting up database...');

        const backendPath = path.join(process.cwd(), 'backend');

        // Generate Prisma client
        await this.runCommand(
            'npx prisma generate',
            'Prisma client generation',
            backendPath
        );

        // Run database setup
        if (this.force) {
            await this.runCommand(
                'npm run db:reset',
                'Database reset and setup',
                backendPath
            );
        } else {
            try {
                await this.runCommand(
                    'npx prisma migrate deploy',
                    'Database migration',
                    backendPath
                );
            } catch (error) {
                this.log('Migration failed, attempting to push schema...', 'warn');
                await this.runCommand(
                    'npx prisma db push',
                    'Database schema push',
                    backendPath
                );
            }
        }

        // Run seeding if not skipped
        if (!this.skipSeed) {
            await this.runCommand(
                'npm run prisma:seed',
                'Database seeding',
                backendPath
            );
        }
    }

    async validateSetup() {
        this.log('🔍 Validating setup...');

        const backendPath = path.join(process.cwd(), 'backend');

        // Check if database is accessible
        try {
            await this.runCommand(
                'npx prisma db execute --command "SELECT 1" --schema prisma/schema.prisma',
                'Database connectivity test',
                backendPath
            );
        } catch (error) {
            throw new Error('Database validation failed');
        }

        // Check if environment files exist
        const requiredFiles = [
            'backend/.env',
            '.env.local',
        ];

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Required file ${file} is missing`);
            }
        }

        this.log('Setup validation passed', 'success');
    }

    async buildProject() {
        this.log('🔨 Building project...');

        // Build backend
        await this.runCommand(
            'npm run build',
            'Backend build',
            path.join(process.cwd(), 'backend')
        );

        // Build frontend
        await this.runCommand('npm run build', 'Frontend build');
    }

    async run() {
        console.log('🚀 SynapseAI Platform Setup');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`Mode: ${this.production ? 'Production' : 'Development'}`);
        console.log(`Options: ${[
            this.force && 'Force Reset',
            this.skipSeed && 'Skip Seed',
            this.verbose && 'Verbose'
        ].filter(Boolean).join(', ') || 'Default'}`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

        try {
            await this.setupEnvironment();
            await this.installDependencies();
            await this.setupDatabase();
            await this.validateSetup();

            if (this.production) {
                await this.buildProject();
            }

            const duration = Math.round((Date.now() - this.startTime) / 1000);

            console.log('\n🎉 SynapseAI Platform setup completed successfully!');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`⏱️  Total time: ${duration}s`);
            console.log('🔧 Your SynapseAI platform is ready!');

            console.log('\n🚀 Next steps:');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

            if (this.production) {
                console.log('1. Start backend: cd backend && npm run start:prod');
                console.log('2. Start frontend: npm run start');
                console.log('3. Configure reverse proxy (nginx/traefik)');
                console.log('4. Set up SSL certificates');
                console.log('5. Configure monitoring and backups');
            } else {
                console.log('1. Start backend: cd backend && npm run dev');
                console.log('2. Start frontend: npm run dev');
                console.log('3. Visit http://localhost:3000');
                console.log('4. Login with seeded credentials:');
                console.log('   • <EMAIL> : Admin123!@#');
                console.log('   • <EMAIL>   : Admin123!@#');
                console.log('   • <EMAIL> : Admin123!@#');
                console.log('   • <EMAIL>    : Admin123!@#');
            }

            console.log('\n📚 Resources:');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('• Documentation: https://docs.synapseai.com');
            console.log('• API Reference: http://localhost:3001/api/docs');
            console.log('• Database Studio: cd backend && npx prisma studio');
            console.log('• Support: https://github.com/synapseai/platform/issues');

            console.log('\n⚠️  Security Reminders:');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('• Change default passwords immediately');
            console.log('• Review and update environment variables');
            console.log('• Configure proper CORS origins for production');
            console.log('• Enable HTTPS and secure headers');
            console.log('• Set up regular database backups');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

        } catch (error) {
            const duration = Math.round((Date.now() - this.startTime) / 1000);

            console.log('\n💥 Setup failed!');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`⏱️  Time elapsed: ${duration}s`);
            console.log(`❌ Error: ${error.message}`);

            console.log('\n🔧 Troubleshooting:');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('1. Ensure PostgreSQL and Redis are running');
            console.log('2. Check database connection settings');
            console.log('3. Verify all required ports are available');
            console.log('4. Run with --verbose for detailed output');
            console.log('5. Use --force to reset and start fresh');

            console.log('\n📞 Get Help:');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('• Documentation: https://docs.synapseai.com/setup');
            console.log('• GitHub Issues: https://github.com/synapseai/platform/issues');
            console.log('• Discord: https://discord.gg/synapseai');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

            process.exit(1);
        }
    }
}

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 SynapseAI Platform Setup Script

Usage: node setup.js [options]

Options:
  --production    Setup for production deployment
  --force         Force reset database and regenerate environment
  --skip-seed     Skip database seeding
  --verbose       Show detailed output
  --help, -h      Show this help message

Examples:
  node setup.js                    # Standard development setup
  node setup.js --production       # Production setup
  node setup.js --force            # Reset and setup from scratch
  node setup.js --skip-seed        # Setup without seeding data
  node setup.js --verbose          # Detailed output

Prerequisites:
  • Node.js 18+ installed
  • PostgreSQL 15+ running
  • Redis 7+ running (optional but recommended)
  • Git installed

For more information, visit: https://docs.synapseai.com/setup
  `);
    process.exit(0);
}

// Run setup
const setup = new SynapseAISetup();
setup.run().catch(console.error);