import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface EndNodeData {
  label: string;
  description?: string;
  status?: 'idle' | 'success';
  isConfigValid?: boolean;
  finalOutput?: string;
}

const EndNode: React.FC<NodeProps<EndNodeData>> = ({ data, selected }) => {
  const getStatusColor = () => {
    switch (data.status) {
      case 'success':
        return 'border-green-500 bg-green-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <Card
      className={cn(
        'min-w-[150px] max-w-[200px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor()
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />

      <CardContent className="p-4 text-center">
        {/* Icon */}
        <div className="flex justify-center mb-2">
          <div className="p-2 bg-gray-100 rounded-full">
            {data.status === 'success' ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <ArrowRight className="h-4 w-4 text-gray-600" />
            )}
          </div>
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs bg-gray-100 text-gray-800 border-gray-300">
          End
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mt-2">
            {data.description}
          </p>
        )}

        {/* Final output */}
        {data.status === 'success' && data.finalOutput && (
          <div className="mt-2 p-2 bg-green-100 border border-green-200 rounded text-xs">
            <div className="font-medium text-green-800 mb-1">Final Result:</div>
            <p className="text-green-700 line-clamp-2 font-mono">
              {data.finalOutput}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default memo(EndNode);