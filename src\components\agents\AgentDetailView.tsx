"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Bot,
  ArrowLeft,
  Edit,
  Trash2,
  Play,
  Pause,
  Copy,
  MessageSquare,
  Activity,
  Clock,
  Settings,
  Code,
  Database,
  FileText,
  CheckCircle,
  AlertCircle,
  Users,
  Zap,
} from "lucide-react";
import { useAgent, useAgentSessions, useAgentTasks } from "@/hooks/useAgents";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

const AgentDetailView = () => {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const agentId = params.id as string;
  const [activeTab, setActiveTab] = useState("overview");
  const [isExecuteDialogOpen, setIsExecuteDialogOpen] = useState(false);
  const [executionInput, setExecutionInput] = useState("");

  const {
    agent,
    loading,
    error,
    executeAgent,
    updateAgent,
  } = useAgent(agentId);

  const {
    sessions,
    loading: sessionsLoading,
  } = useAgentSessions(agentId);

  const {
    tasks,
    loading: tasksLoading,
  } = useAgentTasks(agentId);

  const handleExecuteAgent = async () => {
    try {
      const result = await executeAgent({
        input: executionInput,
        sessionId: `session_${Date.now()}`,
      });

      toast({
        title: "Agent executed successfully",
        description: "Check the sessions tab for results",
      });

      setIsExecuteDialogOpen(false);
      setExecutionInput("");
      setActiveTab("sessions");
    } catch (error: any) {
      toast({
        title: "Execution failed",
        description: error.message || "An error occurred during execution",
        variant: "destructive",
      });
    }
  };

  const handleToggleStatus = async () => {
    try {
      await updateAgent({
        status: agent?.status === "ACTIVE" ? "INACTIVE" : "ACTIVE",
      });

      toast({
        title: "Status updated",
        description: `Agent is now ${agent?.status === "ACTIVE" ? "inactive" : "active"}`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to update status",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    }
  };

  const handleDeleteAgent = async () => {
    try {
      // Delete agent implementation
      toast({
        title: "Agent deleted",
        description: "Redirecting to agent list",
      });

      router.push("/dashboard/agents");
    } catch (error: any) {
      toast({
        title: "Failed to delete agent",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading agent details...</span>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Agent</h3>
          <p className="text-muted-foreground mb-4">{error || "Agent not found"}</p>
          <Button onClick={() => router.push("/dashboard/agents")} variant="outline">
            Back to Agents
          </Button>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "INACTIVE":
        return <Badge className="bg-yellow-100 text-yellow-800">Inactive</Badge>;
      case "ERROR":
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      case "PAUSED":
        return <Badge className="bg-blue-100 text-blue-800">Paused</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.push("/dashboard/agents")}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              {agent.name}
              <span className="ml-3">{getStatusBadge(agent.status)}</span>
            </h1>
            <p className="text-muted-foreground">{agent.description}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setIsExecuteDialogOpen(true)}>
            <Zap className="mr-2 h-4 w-4" />
            Execute
          </Button>
          <Button variant="outline" onClick={handleToggleStatus}>
            {agent.status === "ACTIVE" ? (
              <>
                <Pause className="mr-2 h-4 w-4" />
                Deactivate
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Activate
              </>
            )}
          </Button>
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDeleteAgent}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">
            <Bot className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="sessions">
            <MessageSquare className="mr-2 h-4 w-4" />
            Sessions ({agent._count?.sessions || 0})
          </TabsTrigger>
          <TabsTrigger value="tasks">
            <Activity className="mr-2 h-4 w-4" />
            Tasks ({agent._count?.tasks || 0})
          </TabsTrigger>
          <TabsTrigger value="configuration">
            <Settings className="mr-2 h-4 w-4" />
            Configuration
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Agent Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{agent.type.replace('_', ' ')}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {agent.type === "STANDALONE" && "Conversational agent without tools"}
                  {agent.type === "TOOL_DRIVEN" && "Agent with tool execution capabilities"}
                  {agent.type === "HYBRID" && "Combined conversational and tool execution"}
                  {agent.type === "MULTI_TASKING" && "Can handle multiple tasks concurrently"}
                  {agent.type === "MULTI_PROVIDER" && "Uses multiple AI providers"}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Provider</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{agent.provider}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Model: {agent.model}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Created</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {new Date(agent.createdAt).toLocaleDateString()}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  By {agent.creator?.firstName} {agent.creator?.lastName}
                </p>
              </CardContent>
            </Card>

            <Card className="md:col-span-3">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">System Prompt</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap font-mono text-sm">
                  {agent.systemPrompt || "No system prompt defined"}
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-3">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Capabilities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">Skills & Tools</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Skills:</span>
                        <span>{agent.skills.length}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Tools:</span>
                        <span>{agent.tools.length}</span>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {agent.skills.map((skill, index) => (
                          <Badge key={index} variant="outline">{skill}</Badge>
                        ))}
                        {agent.skills.length === 0 && (
                          <span className="text-sm text-muted-foreground">No skills defined</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">Collaboration</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Can Collaborate:</span>
                        <Badge variant={agent.capabilities.canCollaborate ? "default" : "outline"}>
                          {agent.capabilities.canCollaborate ? "Yes" : "No"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Share Context:</span>
                        <Badge variant={agent.capabilities.shareContext ? "default" : "outline"}>
                          {agent.capabilities.shareContext ? "Yes" : "No"}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Memory Window:</span>
                        <span>{agent.capabilities.memoryWindow} messages</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Sessions Tab */}
        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Session ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Messages</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Last Activity</TableHead>
                    <TableHead>User</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sessionsLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                          <span className="ml-2">Loading sessions...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : sessions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <MessageSquare className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">No sessions found</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsExecuteDialogOpen(true)}
                          >
                            <Zap className="mr-2 h-4 w-4" />
                            Execute Agent
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    sessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>
                          <div className="font-medium">{session.id.substring(0, 8)}...</div>
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            session.status === "active" ? "bg-green-100 text-green-800" :
                              session.status === "inactive" ? "bg-yellow-100 text-yellow-800" :
                                "bg-gray-100 text-gray-800"
                          }>
                            {session.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{session.messageCount}</TableCell>
                        <TableCell>{new Date(session.startedAt).toLocaleString()}</TableCell>
                        <TableCell>{new Date(session.lastActivityAt).toLocaleString()}</TableCell>
                        <TableCell>
                          {session.userId ? (
                            <div className="flex items-center">
                              <span>User {session.userId}</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">System</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Duration</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tasksLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                          <span className="ml-2">Loading tasks...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : tasks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <Activity className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">No tasks found</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    tasks.map((task) => (
                      <TableRow key={task.id}>
                        <TableCell>
                          <div className="font-medium">{task.name}</div>
                          <div className="text-sm text-muted-foreground">{task.description}</div>
                        </TableCell>
                        <TableCell>{task.type}</TableCell>
                        <TableCell>
                          <Badge className={
                            task.status === "COMPLETED" ? "bg-green-100 text-green-800" :
                              task.status === "RUNNING" ? "bg-blue-100 text-blue-800" :
                                task.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                                  task.status === "FAILED" ? "bg-red-100 text-red-800" :
                                    "bg-gray-100 text-gray-800"
                          }>
                            {task.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{task.priority}</TableCell>
                        <TableCell>{new Date(task.createdAt).toLocaleString()}</TableCell>
                        <TableCell>
                          {task.duration ? `${task.duration}ms` : "N/A"}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="configuration" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Model Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Provider</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.provider}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Model</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.model}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Temperature</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.temperature}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Max Tokens</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.maxTokens}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Capabilities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Collaboration</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.capabilities.canCollaborate ? "Enabled" : "Disabled"}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Context Sharing</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.capabilities.shareContext ? "Enabled" : "Disabled"}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Memory Window</Label>
                  <div className="flex items-center space-x-2 p-2 border rounded-md">
                    <span>{agent.capabilities.memoryWindow} messages</span>
                  </div>
                </div>
                {agent.capabilities.maxConcurrentTasks && (
                  <div className="space-y-2">
                    <Label>Max Concurrent Tasks</Label>
                    <div className="flex items-center space-x-2 p-2 border rounded-md">
                      <span>{agent.capabilities.maxConcurrentTasks}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>System Prompt</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap font-mono text-sm">
                  {agent.systemPrompt || "No system prompt defined"}
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Instructions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md whitespace-pre-wrap font-mono text-sm">
                  {agent.instructions || "No instructions defined"}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Execute Agent Dialog */}
      <Dialog open={isExecuteDialogOpen} onOpenChange={setIsExecuteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Execute Agent</DialogTitle>
            <DialogDescription>
              Send a message or task to the agent
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="input">Input</Label>
              <Textarea
                id="input"
                placeholder="Enter your message or task for the agent..."
                value={executionInput}
                onChange={(e) => setExecutionInput(e.target.value)}
                rows={5}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExecuteDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleExecuteAgent} disabled={!executionInput.trim()}>
              Execute
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentDetailView;