/**
 * MCP Memory Entities API Route
 * Production-grade MCP Memory integration for entities
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware/auth.middleware';
import { z } from 'zod';

// Entity schema validation
const entitySchema = z.object({
    name: z.string(),
    entityType: z.string(),
    observations: z.array(z.string())
});

const createEntitiesSchema = z.object({
    entities: z.array(entitySchema)
});

async function handler(req: NextRequest): Promise<NextResponse> {
    try {
        const body = await req.json();

        // Validate request body
        const validationResult = createEntitiesSchema.safeParse(body);
        if (!validationResult.success) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Invalid request body',
                    errors: validationResult.error.issues
                },
                { status: 400 }
            );
        }

        // Forward request to MCP Memory service
        const mcpResponse = await fetch(`${process.env.MCP_API_URL || 'http://localhost:3002'}/memory/entities`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.MCP_API_KEY || ''}`
            },
            body: JSON.stringify(body)
        });

        if (!mcpResponse.ok) {
            const errorData = await mcpResponse.json();
            return NextResponse.json(
                {
                    success: false,
                    message: errorData.message || 'Failed to create entities',
                    code: 'MCP_ERROR'
                },
                { status: mcpResponse.status }
            );
        }

        const data = await mcpResponse.json();
        return NextResponse.json({
            success: true,
            data
        });
    } catch (error: any) {
        console.error('MCP Memory entities error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to create entities',
                code: error.code || 'MCP_ERROR'
            },
            { status: 500 }
        );
    }
}

// Export with auth middleware - only authenticated users can access
export const POST = withAuth(handler);