/**
 * 🎯 API Response Utilities
 * Centralized helper functions for consistent API responses
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
import { 
  ApiResponse, 
  SuccessResponse, 
  ErrorResponse, 
  PaginatedResponse, 
  ListResponse 
} from '@/lib/types/api/responses';

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error Codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  DUPLICATE_RESOURCE: 'DUPLICATE_RESOURCE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  API_ERROR: 'API_ERROR',
} as const;

/**
 * Create a success response
 */
export function successResponse<T>(
  data?: T,
  message?: string,
  status: number = HTTP_STATUS.OK
): NextResponse {
  const response: SuccessResponse<T> = {
    success: true,
    data,
    message,
  };

  return NextResponse.json(response, { status });
}

/**
 * Create a created response
 */
export function createdResponse<T>(
  data?: T,
  message?: string
): NextResponse {
  return successResponse(data, message, HTTP_STATUS.CREATED);
}

/**
 * Create an error response
 */
export function errorResponse(
  message: string,
  code: string = ERROR_CODES.INTERNAL_ERROR,
  status: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
  details?: any
): NextResponse {
  const response: ErrorResponse = {
    success: false,
    error: message,
    code,
    details,
  };

  return NextResponse.json(response, { status });
}

/**
 * Create a validation error response
 */
export function validationErrorResponse(
  error: ZodError,
  message: string = 'Validation failed'
): NextResponse {
  const details = error.issues.map(issue => ({
    field: issue.path.join('.'),
    message: issue.message,
    code: issue.code,
  }));

  return errorResponse(
    message,
    ERROR_CODES.VALIDATION_ERROR,
    HTTP_STATUS.BAD_REQUEST,
    details
  );
}

/**
 * Create an unauthorized response
 */
export function unauthorizedResponse(
  message: string = 'Authentication required'
): NextResponse {
  return errorResponse(
    message,
    ERROR_CODES.AUTHENTICATION_ERROR,
    HTTP_STATUS.UNAUTHORIZED
  );
}

/**
 * Create a forbidden response
 */
export function forbiddenResponse(
  message: string = 'Access denied'
): NextResponse {
  return errorResponse(
    message,
    ERROR_CODES.AUTHORIZATION_ERROR,
    HTTP_STATUS.FORBIDDEN
  );
}

/**
 * Create a not found response
 */
export function notFoundResponse(
  message: string = 'Resource not found'
): NextResponse {
  return errorResponse(
    message,
    ERROR_CODES.NOT_FOUND,
    HTTP_STATUS.NOT_FOUND
  );
}

/**
 * Create a paginated response
 */
export function paginatedResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  message?: string
): NextResponse {
  const totalPages = Math.ceil(pagination.total / pagination.limit);
  
  const response: PaginatedResponse<T> = {
    success: true,
    data,
    message,
    pagination: {
      ...pagination,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrevious: pagination.page > 1,
    },
  };

  return NextResponse.json(response);
}

/**
 * Create a list response with stats
 */
export function listResponse<T, S>(
  data: T[],
  stats?: S,
  pagination?: {
    page: number;
    limit: number;
    total: number;
  },
  message?: string
): NextResponse {
  const response: ListResponse<T, S> = {
    success: true,
    data,
    message,
    stats,
  };

  if (pagination) {
    const totalPages = Math.ceil(pagination.total / pagination.limit);
    response.pagination = {
      ...pagination,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrevious: pagination.page > 1,
    };
  }

  return NextResponse.json(response);
}

/**
 * Handle Prisma errors
 */
export function handlePrismaError(error: Prisma.PrismaClientKnownRequestError): NextResponse {
  switch (error.code) {
    case 'P2002':
      return errorResponse(
        'Resource already exists',
        ERROR_CODES.DUPLICATE_RESOURCE,
        HTTP_STATUS.CONFLICT,
        error.meta
      );
    case 'P2025':
      return errorResponse(
        'Resource not found',
        ERROR_CODES.NOT_FOUND,
        HTTP_STATUS.NOT_FOUND,
        error.meta
      );
    default:
      return errorResponse(
        'Database error',
        ERROR_CODES.DATABASE_ERROR,
        HTTP_STATUS.INTERNAL_SERVER_ERROR,
        process.env.NODE_ENV === 'development' ? error.message : undefined
      );
  }
}

/**
 * Generic error handler for API routes
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return validationErrorResponse(error);
  }

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    return handlePrismaError(error);
  }

  // Handle custom API errors
  if (error instanceof Error && 'statusCode' in error) {
    const apiError = error as any;
    return errorResponse(
      apiError.message,
      apiError.code || ERROR_CODES.API_ERROR,
      apiError.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR,
      apiError.details
    );
  }

  // Handle generic errors
  if (error instanceof Error) {
    return errorResponse(
      process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
      ERROR_CODES.INTERNAL_ERROR,
      HTTP_STATUS.INTERNAL_SERVER_ERROR
    );
  }

  // Handle unknown error types
  return errorResponse(
    'Internal server error',
    ERROR_CODES.INTERNAL_ERROR,
    HTTP_STATUS.INTERNAL_SERVER_ERROR
  );
}
