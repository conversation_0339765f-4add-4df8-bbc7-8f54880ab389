import { PrismaService } from "../../prisma/prisma.service";
import { Cache } from "cache-manager";
import { EventPriority } from "../dto/apix.dto";
import { EventRouterService } from "./event-router.service";
import { ConnectionManagerService } from "./connection-manager.service";
interface RetryConfig {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    jitter: boolean;
}
export declare class MessageQueueService {
    private prisma;
    private cacheManager;
    private eventRouter;
    private connectionManager;
    private readonly logger;
    private processingQueue;
    private retryTimer;
    private isProcessing;
    private readonly defaultRetryConfig;
    constructor(prisma: PrismaService, cacheManager: Cache, eventRouter: EventRouterService, connectionManager: ConnectionManagerService);
    queueMessage(connectionId: string, eventId: string, priority?: EventPriority, retryConfig?: Partial<RetryConfig>): Promise<string>;
    processQueue(): Promise<void>;
    processMessage(queuedMessage: any): Promise<void>;
    retryMessage(messageId: string): Promise<boolean>;
    cancelMessage(messageId: string): Promise<boolean>;
    getQueueStats(): Promise<{
        pending: number;
        processing: number;
        completed: number;
        failed: number;
        cancelled: number;
        totalByPriority: Record<EventPriority, number>;
        averageProcessingTime: number;
        successRate: number;
        throughputPerHour: number;
        oldestPendingMessage: Date | null;
    }>;
    getConnectionQueueStats(connectionId: string): Promise<{
        pending: number;
        processing: number;
        failed: number;
        avgRetryAttempts: number;
    }>;
    cleanupOldMessages(olderThanDays?: number): Promise<number>;
    purgeFailedMessages(connectionId?: string): Promise<number>;
    private handleMessageError;
    private markMessageFailed;
    private calculateNextRetryTime;
    private startRetryProcessor;
    onModuleDestroy(): void;
}
export {};
