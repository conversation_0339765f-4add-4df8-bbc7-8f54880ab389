import { IsEmail, IsO<PERSON>al, IsString, IsBoolean, IsEnum, IsObject, IsArray, IsNumber, MinLength, IsU<PERSON>D, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Role } from '@prisma/client';

export class CreateUserDto {
    @IsEmail()
    email: string;

    @IsString()
    @MinLength(8)
    password: string;

    @IsString()
    firstName: string;

    @IsString()
    lastName: string;

    @IsOptional()
    @IsString()
    avatar?: string;

    @IsOptional()
    @IsEnum(Role)
    role?: Role;

    @IsUUID()
    organizationId: string;

    @IsOptional()
    @IsObject()
    preferences?: Record<string, any>;

    @IsOptional()
    @IsBoolean()
    emailVerified?: boolean;
}

export class UpdateUserDto {
    @IsOptional()
    @IsString()
    firstName?: string;

    @IsOptional()
    @IsString()
    lastName?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    avatar?: string;

    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsObject()
    preferences?: Record<string, any>;
}

export class UpdateUserRoleDto {
    @IsEnum(Role)
    role: Role;
}

export class UserListQueryDto {
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @IsOptional()
    @IsString()
    search?: string;

    @IsOptional()
    @IsEnum(Role)
    role?: Role;

    @IsOptional()
    @Transform(({ value }) => value === 'true')
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsString()
    sortBy?: string = 'createdAt';

    @IsOptional()
    @IsEnum(['asc', 'desc'])
    sortOrder?: 'asc' | 'desc' = 'desc';
}

export class UserProfileDto {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    fullName: string;
    avatar?: string;
    role: Role;
    isActive: boolean;
    emailVerified: boolean;
    mfaEnabled: boolean;
    lastLoginAt?: Date;
    preferences: Record<string, any>;
    organization: {
        id: string;
        name: string;
        slug: string;
    };
    activeSessions: number;
    stats?: {
        workflows: number;
        agents: number;
        tools: number;
        sessions: number;
    };
    createdAt: Date;
    updatedAt: Date;
}

export class UserListResponseDto {
    users: UserProfileDto[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export class UserStatsDto {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    usersByRole: Record<string, number>;
    recentUsers: number;
    growthRate: number;
}

export class BulkUserActionDto {
    @IsArray()
    @IsUUID(undefined, { each: true })
    userIds: string[];

    @IsEnum(['activate', 'deactivate', 'updateRole', 'delete'])
    action: 'activate' | 'deactivate' | 'updateRole' | 'delete';

    @IsOptional()
    @IsObject()
    data?: {
        role?: Role;
        [key: string]: any;
    };
}

export class UserPreferencesDto {
    @IsOptional()
    @IsString()
    theme?: 'light' | 'dark' | 'system';

    @IsOptional()
    @IsString()
    language?: string;

    @IsOptional()
    @IsString()
    timezone?: string;

    @IsOptional()
    @IsBoolean()
    emailNotifications?: boolean;

    @IsOptional()
    @IsBoolean()
    pushNotifications?: boolean;

    @IsOptional()
    @IsObject()
    dashboardLayout?: Record<string, any>;

    @IsOptional()
    @IsObject()
    workflowSettings?: Record<string, any>;

    @IsOptional()
    @IsObject()
    uiPreferences?: Record<string, any>;

    [key: string]: any;
}

export class UserSecurityDto {
    mfaEnabled: boolean;
    emailVerified: boolean;
    lastLoginAt?: Date;
    loginAttempts: number;
    lockoutUntil?: Date;
    activeSessions: number;
    activeTokens: number;
    activeApiKeys: number;
    sessions: Array<{
        id: string;
        createdAt: Date;
        lastActivityAt?: Date;
        ipAddress?: string;
        userAgent?: string;
    }>;
    apiKeys: Array<{
        id: string;
        name: string;
        permissions: string[];
        lastUsedAt?: Date;
        createdAt: Date;
    }>;
}

export class ResetUserPasswordDto {
    @IsString()
    @MinLength(8)
    newPassword: string;

    @IsOptional()
    @IsBoolean()
    forceLogout?: boolean = true;
}

export class UserAuditLogDto {
    id: string;
    action: string;
    resource: string;
    resourceId: string;
    details: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    performedBy: string;
}