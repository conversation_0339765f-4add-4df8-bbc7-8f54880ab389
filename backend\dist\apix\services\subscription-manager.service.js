"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SubscriptionManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionManagerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const apix_dto_1 = require("../dto/apix.dto");
const connection_manager_service_1 = require("./connection-manager.service");
let SubscriptionManagerService = SubscriptionManagerService_1 = class SubscriptionManagerService {
    constructor(prisma, cacheManager, connectionManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.connectionManager = connectionManager;
        this.logger = new common_1.Logger(SubscriptionManagerService_1.name);
        this.channelPermissions = new Map();
        this.subscriptions = new Map();
        this.initializeDefaultChannels();
    }
    async subscribeToChannel(socketId, channelName, filters = {}) {
        const connection = this.connectionManager.getConnection(socketId);
        if (!connection) {
            this.logger.warn(`Subscription failed: Connection not found for ${socketId}`);
            return false;
        }
        if (!(await this.hasChannelPermission(connection.userId, connection.organizationId, channelName, "read"))) {
            this.logger.warn(`Subscription denied: No read permission for ${channelName}`);
            return false;
        }
        const channel = await this.getOrCreateChannel(channelName, connection.organizationId);
        if (!channel) {
            this.logger.warn(`Subscription failed: Channel ${channelName} not found or created`);
            return false;
        }
        await this.prisma.apiXSubscription.upsert({
            where: {
                connectionId_channelId: {
                    connectionId: connection.id,
                    channelId: channel.id,
                },
            },
            update: {
                filters,
                isActive: true,
            },
            create: {
                connectionId: connection.id,
                channelId: channel.id,
                filters,
                isActive: true,
            },
        });
        if (!this.subscriptions.has(socketId)) {
            this.subscriptions.set(socketId, new Map());
        }
        this.subscriptions.get(socketId).set(channelName, filters);
        await this.connectionManager.addChannelToConnection(socketId, channelName);
        await this.prisma.apiXChannel.update({
            where: { id: channel.id },
            data: {
                subscribers: {
                    increment: 1,
                },
            },
        });
        await this.cacheSubscription(socketId, channelName, filters);
        this.logger.log(`Subscribed ${socketId} to channel ${channelName}`);
        return true;
    }
    async unsubscribeFromChannel(socketId, channelName) {
        const connection = this.connectionManager.getConnection(socketId);
        if (!connection)
            return false;
        const channel = await this.prisma.apiXChannel.findUnique({
            where: { name: channelName },
        });
        if (!channel)
            return false;
        await this.prisma.apiXSubscription.updateMany({
            where: {
                connectionId: connection.id,
                channelId: channel.id,
            },
            data: {
                isActive: false,
            },
        });
        const connectionSubs = this.subscriptions.get(socketId);
        if (connectionSubs) {
            connectionSubs.delete(channelName);
        }
        await this.connectionManager.removeChannelFromConnection(socketId, channelName);
        await this.prisma.apiXChannel.update({
            where: { id: channel.id },
            data: {
                subscribers: {
                    decrement: 1,
                },
            },
        });
        await this.cacheManager.del(`subscription:${socketId}:${channelName}`);
        this.logger.log(`Unsubscribed ${socketId} from channel ${channelName}`);
        return true;
    }
    async unsubscribeAll(socketId) {
        const connection = this.connectionManager.getConnection(socketId);
        if (!connection)
            return;
        const subscriptions = await this.prisma.apiXSubscription.findMany({
            where: {
                connectionId: connection.id,
                isActive: true,
            },
            include: {
                channel: true,
            },
        });
        await this.prisma.apiXSubscription.updateMany({
            where: {
                connectionId: connection.id,
            },
            data: {
                isActive: false,
            },
        });
        for (const sub of subscriptions) {
            await this.prisma.apiXChannel.update({
                where: { id: sub.channelId },
                data: {
                    subscribers: {
                        decrement: 1,
                    },
                },
            });
        }
        this.subscriptions.delete(socketId);
        for (const sub of subscriptions) {
            await this.cacheManager.del(`subscription:${socketId}:${sub.channel.name}`);
        }
        this.logger.log(`Unsubscribed ${socketId} from all channels`);
    }
    getSubscriptions(socketId) {
        return this.subscriptions.get(socketId) || new Map();
    }
    async getChannelSubscribers(channelName) {
        const channel = await this.prisma.apiXChannel.findUnique({
            where: { name: channelName },
            include: {
                subscriptions: {
                    where: { isActive: true },
                    include: {
                        connection: true,
                    },
                },
            },
        });
        if (!channel)
            return [];
        return channel.subscriptions
            .map((sub) => sub.connection.sessionId)
            .filter(Boolean);
    }
    async createChannel(name, type, organizationId, permissions = { read: true, write: false, admin: false }, metadata = {}) {
        try {
            const channel = await this.prisma.apiXChannel.create({
                data: {
                    name,
                    type,
                    organizationId,
                    permissions,
                    metadata,
                },
            });
            this.channelPermissions.set(name, permissions);
            await this.cacheManager.set(`channel:${name}`, channel, 3600);
            this.logger.log(`Channel created: ${name}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to create channel ${name}:`, error);
            return false;
        }
    }
    async updateChannelPermissions(channelName, permissions) {
        try {
            await this.prisma.apiXChannel.update({
                where: { name: channelName },
                data: { permissions },
            });
            this.channelPermissions.set(channelName, permissions);
            await this.cacheManager.del(`channel:${channelName}`);
            this.logger.log(`Channel permissions updated: ${channelName}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to update channel permissions ${channelName}:`, error);
            return false;
        }
    }
    async deleteChannel(channelName) {
        try {
            const channel = await this.prisma.apiXChannel.findUnique({
                where: { name: channelName },
                include: {
                    subscriptions: {
                        where: { isActive: true },
                    },
                },
            });
            if (channel) {
                await this.prisma.apiXSubscription.updateMany({
                    where: { channelId: channel.id },
                    data: { isActive: false },
                });
                await this.prisma.apiXChannel.update({
                    where: { id: channel.id },
                    data: { isActive: false },
                });
            }
            this.channelPermissions.delete(channelName);
            await this.cacheManager.del(`channel:${channelName}`);
            this.logger.log(`Channel deleted: ${channelName}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to delete channel ${channelName}:`, error);
            return false;
        }
    }
    async getChannelStats(organizationId) {
        const where = organizationId ? { organizationId } : {};
        const [channels, subscriptions] = await Promise.all([
            this.prisma.apiXChannel.findMany({
                where: { ...where, isActive: true },
                orderBy: { subscribers: "desc" },
                take: 10,
            }),
            this.prisma.apiXSubscription.findMany({
                where: {
                    channel: where,
                },
            }),
        ]);
        const allChannels = await this.prisma.apiXChannel.findMany({
            where: { ...where, isActive: true },
        });
        const stats = {
            totalChannels: allChannels.length,
            channelsByType: {},
            totalSubscriptions: subscriptions.length,
            activeSubscriptions: subscriptions.filter((s) => s.isActive).length,
            averageSubscribersPerChannel: allChannels.length > 0
                ? allChannels.reduce((sum, ch) => sum + ch.subscribers, 0) /
                    allChannels.length
                : 0,
            topChannelsByActivity: channels.map((ch) => ({
                name: ch.name,
                subscribers: ch.subscribers,
            })),
        };
        Object.values(apix_dto_1.ChannelType).forEach((type) => {
            stats.channelsByType[type] = 0;
        });
        for (const channel of allChannels) {
            stats.channelsByType[channel.type]++;
        }
        return stats;
    }
    async hasChannelPermission(userId, organizationId, channelName, permission) {
        if (channelName.startsWith("system:")) {
            return true;
        }
        if (channelName.startsWith("org:")) {
            const orgId = channelName.split(":")[1];
            return orgId === organizationId;
        }
        if (channelName.startsWith("user:")) {
            const targetUserId = channelName.split(":")[1];
            return targetUserId === userId;
        }
        const permissions = this.channelPermissions.get(channelName);
        if (!permissions) {
            const channel = await this.prisma.apiXChannel.findUnique({
                where: { name: channelName },
            });
            if (channel) {
                const perms = channel.permissions;
                this.channelPermissions.set(channelName, perms);
                return perms[permission] || false;
            }
            return false;
        }
        if (permissions[permission]) {
            return true;
        }
        if (permissions.roles && userId) {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (user && permissions.roles.includes(user.role)) {
                return true;
            }
        }
        if (permissions.users && userId && permissions.users.includes(userId)) {
            return true;
        }
        if (permissions.organizations &&
            permissions.organizations.includes(organizationId)) {
            return true;
        }
        return false;
    }
    async getOrCreateChannel(channelName, organizationId) {
        const cached = await this.cacheManager.get(`channel:${channelName}`);
        if (cached)
            return cached;
        let channel = await this.prisma.apiXChannel.findUnique({
            where: { name: channelName },
        });
        if (!channel) {
            const type = this.inferChannelType(channelName);
            if (type) {
                channel = await this.prisma.apiXChannel.create({
                    data: {
                        name: channelName,
                        type,
                        organizationId: type === apix_dto_1.ChannelType.ORGANIZATION ? organizationId : undefined,
                        permissions: this.getDefaultPermissions(type),
                    },
                });
            }
        }
        if (channel) {
            await this.cacheManager.set(`channel:${channelName}`, channel, 3600);
        }
        return channel;
    }
    inferChannelType(channelName) {
        if (channelName.startsWith("agent:"))
            return apix_dto_1.ChannelType.AGENT_EVENTS;
        if (channelName.startsWith("tool:"))
            return apix_dto_1.ChannelType.TOOL_EVENTS;
        if (channelName.startsWith("workflow:"))
            return apix_dto_1.ChannelType.WORKFLOW_EVENTS;
        if (channelName.startsWith("provider:"))
            return apix_dto_1.ChannelType.PROVIDER_EVENTS;
        if (channelName.startsWith("system:"))
            return apix_dto_1.ChannelType.SYSTEM_EVENTS;
        if (channelName.startsWith("session:"))
            return apix_dto_1.ChannelType.SESSION_EVENTS;
        if (channelName.startsWith("user:"))
            return apix_dto_1.ChannelType.PRIVATE_USER;
        if (channelName.startsWith("org:"))
            return apix_dto_1.ChannelType.ORGANIZATION;
        return apix_dto_1.ChannelType.PUBLIC;
    }
    getDefaultPermissions(type) {
        switch (type) {
            case apix_dto_1.ChannelType.PRIVATE_USER:
                return { read: false, write: false, admin: false, users: [] };
            case apix_dto_1.ChannelType.ORGANIZATION:
                return { read: true, write: false, admin: false };
            case apix_dto_1.ChannelType.SYSTEM_EVENTS:
                return { read: true, write: false, admin: false };
            default:
                return { read: true, write: false, admin: false };
        }
    }
    async cacheSubscription(socketId, channelName, filters) {
        await this.cacheManager.set(`subscription:${socketId}:${channelName}`, filters, 3600);
    }
    async initializeDefaultChannels() {
        const defaultChannels = [
            { name: "system:alerts", type: apix_dto_1.ChannelType.SYSTEM_EVENTS },
            { name: "system:maintenance", type: apix_dto_1.ChannelType.SYSTEM_EVENTS },
            { name: "system:health", type: apix_dto_1.ChannelType.SYSTEM_EVENTS },
        ];
        for (const channel of defaultChannels) {
            await this.createChannel(channel.name, channel.type, undefined, {
                read: true,
                write: false,
                admin: false,
            });
        }
    }
};
exports.SubscriptionManagerService = SubscriptionManagerService;
exports.SubscriptionManagerService = SubscriptionManagerService = SubscriptionManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, connection_manager_service_1.ConnectionManagerService])
], SubscriptionManagerService);
//# sourceMappingURL=subscription-manager.service.js.map