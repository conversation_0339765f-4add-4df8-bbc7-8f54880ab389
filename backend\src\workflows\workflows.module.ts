import { Module } from '@nestjs/common';
import { WorkflowsController } from './workflows.controller';
import { WorkflowsService } from './workflows.service';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';
import { WorkflowExecutorService } from './services/workflow-executor.service';
import { NodeRegistryService } from './services/node-registry.service';
import { FlowControllerService } from './services/flow-controller.service';
import { VariableResolverService } from './services/variable-resolver.service';
import { BranchEvaluatorService } from './services/branch-evaluator.service';

@Module({
  imports: [PrismaModule, ApixModule, SessionsModule],
  controllers: [WorkflowsController],
  providers: [
    WorkflowsService,
    WorkflowExecutorService,
    NodeRegistryService,
    FlowControllerService,
    VariableResolverService,
    BranchEvaluatorService,
  ],
  exports: [
    WorkflowsService,
    WorkflowExecutorService,
    NodeRegistryService,
    FlowControllerService,
    VariableResolverService,
    BranchEvaluatorService,
  ],
})
export class WorkflowsModule {}