import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { ExecutionStatus } from '@prisma/client';
import { WorkflowDefinition, WorkflowNode } from '../workflows.service';
import { HybridNodeExecutorService } from './hybrid-node-executor.service';
import { HumanInTheLoopService } from './human-in-the-loop.service';

@Injectable()
export class WorkflowExecutorService {
  private readonly logger = new Logger(WorkflowExecutorService.name);

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private sessionsService: SessionsService,
    private hybridNodeExecutor: HybridNodeExecutorService,
    private humanInTheLoopService: HumanInTheLoopService,
  ) {}

  async executeWorkflow(
    executionId: string,
    workflowId: string,
    sessionId: string,
    organizationId: string,
    input: Record<string, any> = {},
  ): Promise<any> {
    try {
      // Get workflow definition
      const workflow = await this.prisma.workflow.findUnique({
        where: { id: workflowId },
      });

      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      // Update execution status to running
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: { status: ExecutionStatus.RUNNING },
      });

      // Emit workflow execution started event
      await this.apixGateway.emitWorkflowEvent(
        organizationId,
        workflowId,
        'workflow_execution_started',
        {
          executionId,
          workflowName: workflow.name,
          input,
        }
      );

      const definition = workflow.definition as WorkflowDefinition;
      const executionContext = {
        executionId,
        sessionId,
        organizationId,
        workflowId,
        variables: { ...input },
        nodeResults: new Map<string, any>(),
      };

      // Find start nodes (nodes with no inputs or no incoming edges)
      const startNodes = definition.nodes.filter(node => 
        node.inputs.length === 0 || 
        !definition.edges.some(edge => edge.target === node.id)
      );

      if (startNodes.length === 0) {
        throw new Error('No start nodes found in workflow');
      }

      // Execute workflow starting from start nodes
      const results = await Promise.all(
        startNodes.map(node => this.executeNode(node, definition, executionContext))
      );

      // Mark execution as completed
      const completedAt = new Date();
      const duration = completedAt.getTime() - (await this.prisma.workflowExecution.findUnique({
        where: { id: executionId },
        select: { startedAt: true },
      }))!.startedAt.getTime();

      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: ExecutionStatus.COMPLETED,
          output: { results },
          completedAt,
          duration,
        },
      });

      // Emit workflow execution completed event
      await this.apixGateway.emitWorkflowEvent(
        organizationId,
        workflowId,
        'workflow_execution_completed',
        {
          executionId,
          workflowName: workflow.name,
          duration,
          results,
        }
      );

      return results;
    } catch (error) {
      // Mark execution as failed
      const completedAt = new Date();
      const duration = completedAt.getTime() - (await this.prisma.workflowExecution.findUnique({
        where: { id: executionId },
        select: { startedAt: true },
      }))!.startedAt.getTime();

      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: ExecutionStatus.FAILED,
          error: error.message,
          completedAt,
          duration,
        },
      });

      // Emit workflow execution failed event
      await this.apixGateway.emitWorkflowEvent(
        organizationId,
        workflowId,
        'workflow_execution_failed',
        {
          executionId,
          error: error.message,
          duration,
        }
      );

      throw error;
    }
  }

  private async executeNode(
    node: WorkflowNode,
    definition: WorkflowDefinition,
    context: any,
  ): Promise<any> {
    const stepStartTime = Date.now();

    // Create step record
    const step = await this.prisma.workflowStep.create({
      data: {
        executionId: context.executionId,
        stepId: node.id,
        name: node.name,
        type: node.type,
        status: ExecutionStatus.RUNNING,
        input: node.config,
      },
    });

    try {
      // Emit node execution started event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'workflow_node_started',
        {
          executionId: context.executionId,
          nodeId: node.id,
          nodeName: node.name,
          nodeType: node.type,
        }
      );

      let result: any;

      switch (node.type) {
        case 'agent':
          result = await this.executeAgentNode(node, context);
          break;
        case 'tool':
          result = await this.executeToolNode(node, context);
          break;
        case 'condition':
          result = await this.executeConditionNode(node, context);
          break;
        case 'parallel':
          result = await this.executeParallelNode(node, definition, context);
          break;
        case 'human_input':
          result = await this.executeHumanInputNode(node, context);
          break;
        case 'delay':
          result = await this.executeDelayNode(node, context);
          break;
        case 'hybrid':
          result = await this.executeHybridNode(node, definition, context);
          break;
        default:
          throw new Error(`Unknown node type: ${node.type}`);
      }

      // Update step as completed
      const completedAt = new Date();
      await this.prisma.workflowStep.update({
        where: { id: step.id },
        data: {
          status: ExecutionStatus.COMPLETED,
          output: result,
          completedAt,
          duration: completedAt.getTime() - stepStartTime,
        },
      });

      // Emit node execution completed event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'workflow_node_completed',
        {
          executionId: context.executionId,
          nodeId: node.id,
          nodeName: node.name,
          nodeType: node.type,
          duration: completedAt.getTime() - stepStartTime,
        }
      );

      // Store result for next nodes
      context.nodeResults.set(node.id, result);
      context.variables[`node_${node.id}_result`] = result;

      // Execute next nodes
      const nextNodes = this.getNextNodes(node.id, definition, result);
      if (nextNodes.length > 0) {
        await Promise.all(
          nextNodes.map(nextNode => this.executeNode(nextNode, definition, context))
        );
      }

      return result;

    } catch (error) {
      // Update step as failed
      const completedAt = new Date();
      await this.prisma.workflowStep.update({
        where: { id: step.id },
        data: {
          status: ExecutionStatus.FAILED,
          error: error.message,
          completedAt,
          duration: completedAt.getTime() - stepStartTime,
        },
      });

      // Emit node execution failed event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'workflow_node_failed',
        {
          executionId: context.executionId,
          nodeId: node.id,
          nodeName: node.name,
          nodeType: node.type,
          error: error.message,
        }
      );

      throw error;
    }
  }

  private async executeAgentNode(node: WorkflowNode, context: any): Promise<any> {
    const { agentId, input, systemPrompt, maxTokens } = node.config;
    
    // Emit agent execution start
    await this.apixGateway.emitAgentEvent(
      context.organizationId,
      agentId,
      'agent_execution_started',
      {
        nodeId: node.id,
        executionId: context.executionId,
        config: node.config,
      }
    );

    try {
      // Get agent from database
      const agent = await this.prisma.agent.findUnique({
        where: { id: agentId },
      });

      if (!agent) {
        throw new Error(`Agent ${agentId} not found`);
      }

      // Process input with variable substitution
      const processedInput = this.processVariables(input, context.variables);
      
      // In a real implementation, this would call the agent service
      // For now, simulate agent execution
      await new Promise(resolve => setTimeout(resolve, 1000));

      const result = {
        response: `Agent ${agentId} executed successfully with input: ${JSON.stringify(processedInput)}`,
        data: processedInput,
        timestamp: Date.now(),
      };

      // Emit agent execution completed
      await this.apixGateway.emitAgentEvent(
        context.organizationId,
        agentId,
        'agent_execution_completed',
        {
          nodeId: node.id,
          executionId: context.executionId,
          result,
        }
      );

      return result;
    } catch (error) {
      // Emit agent execution error
      await this.apixGateway.emitAgentEvent(
        context.organizationId,
        agentId,
        'agent_execution_failed',
        {
          nodeId: node.id,
          executionId: context.executionId,
          error: error.message,
        }
      );
      
      throw error;
    }
  }

  private async executeToolNode(node: WorkflowNode, context: any): Promise<any> {
    const { toolId, parameters } = node.config;
    
    // Emit tool execution start
    await this.apixGateway.emitToolEvent(
      context.organizationId,
      toolId,
      'tool_execution_started',
      {
        nodeId: node.id,
        executionId: context.executionId,
        config: node.config,
      }
    );

    try {
      // Get tool from database
      const tool = await this.prisma.tool.findUnique({
        where: { id: toolId },
      });

      if (!tool) {
        throw new Error(`Tool ${toolId} not found`);
      }

      // Process parameters with variable substitution
      const processedParameters = this.processVariables(parameters, context.variables);
      
      // In a real implementation, this would call the tool service
      // For now, simulate tool execution
      await new Promise(resolve => setTimeout(resolve, 500));

      const result = {
        output: `Tool ${toolId} executed successfully with parameters: ${JSON.stringify(processedParameters)}`,
        data: processedParameters,
        timestamp: Date.now(),
      };

      // Emit tool execution completed
      await this.apixGateway.emitToolEvent(
        context.organizationId,
        toolId,
        'tool_execution_completed',
        {
          nodeId: node.id,
          executionId: context.executionId,
          result,
        }
      );

      return result;
    } catch (error) {
      // Emit tool execution error
      await this.apixGateway.emitToolEvent(
        context.organizationId,
        toolId,
        'tool_execution_failed',
        {
          nodeId: node.id,
          executionId: context.executionId,
          error: error.message,
        }
      );
      
      throw error;
    }
  }

  private async executeConditionNode(node: WorkflowNode, context: any): Promise<any> {
    const { condition, paths } = node.config;
    
    // Process condition with variable substitution
    const processedCondition = this.processVariables(condition, context.variables);
    
    // Evaluate condition
    let result: boolean;
    try {
      // This is a simplified evaluation - in production, use a safe expression evaluator
      result = this.evaluateCondition(processedCondition, context.variables);
    } catch (error) {
      throw new Error(`Condition evaluation failed: ${error.message}`);
    }

    return {
      condition: processedCondition,
      result,
      evaluatedPath: result ? 'true' : 'false',
      timestamp: Date.now(),
    };
  }

  private async executeParallelNode(
    node: WorkflowNode,
    definition: WorkflowDefinition,
    context: any,
  ): Promise<any> {
    const { nodes: parallelNodeIds, aggregateResults } = node.config;
    
    // Execute all specified nodes in parallel
    const parallelResults = await Promise.all(
      parallelNodeIds.map(async (nodeId: string) => {
        const parallelNode = definition.nodes.find(n => n.id === nodeId);
        if (!parallelNode) {
          throw new Error(`Parallel node ${nodeId} not found`);
        }
        
        // Create a copy of the context to avoid race conditions
        const nodeContext = { ...context };
        
        return {
          nodeId,
          result: await this.executeNode(parallelNode, definition, nodeContext)
        };
      })
    );

    // Aggregate results if specified
    let aggregatedResult = parallelResults;
    if (aggregateResults) {
      // Implement custom aggregation logic based on aggregateResults config
      // For now, just return all results
    }

    return {
      parallelResults,
      aggregatedResult,
      timestamp: Date.now(),
    };
  }

  private async executeHumanInputNode(node: WorkflowNode, context: any): Promise<any> {
    const { prompt, inputType, timeout = 300000, allowSkip, skipValue, validationRules } = node.config;
    
    // Process prompt with variable substitution
    const processedPrompt = this.processVariables(prompt, context.variables);
    
    // Request human input using the service
    const requestId = await this.humanInTheLoopService.requestHumanInput(
      context.executionId,
      node.id,
      context.sessionId,
      processedPrompt,
      inputType || 'text',
      {
        timeout,
        allowSkip: allowSkip || false,
        skipValue,
        validationRules,
        metadata: {
          workflowId: context.workflowId,
          nodeId: node.id,
          nodeName: node.name,
        },
      }
    );

    // Wait for response by polling the request status
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(async () => {
        try {
          const request = await this.humanInTheLoopService.getRequest(requestId);
          
          if (!request) {
            clearInterval(checkInterval);
            reject(new Error('Human input request not found'));
            return;
          }

          if (request.status === 'completed') {
            clearInterval(checkInterval);
            resolve({
              userInput: request.response,
              inputType: request.inputType,
              completedAt: request.completedAt,
              skipped: false,
            });
          } else if (request.status === 'skipped') {
            clearInterval(checkInterval);
            resolve({
              userInput: request.response,
              inputType: request.inputType,
              completedAt: request.completedAt,
              skipped: true,
            });
          } else if (request.status === 'expired' || request.status === 'cancelled') {
            clearInterval(checkInterval);
            if (allowSkip) {
              resolve({ 
                userInput: skipValue || null, 
                skipped: true,
                expired: request.status === 'expired',
                cancelled: request.status === 'cancelled',
              });
            } else {
              reject(new Error(`Human input ${request.status}`));
            }
          }
        } catch (error) {
          clearInterval(checkInterval);
          reject(error);
        }
      }, 1000); // Check every second

      // Set overall timeout
      setTimeout(() => {
        clearInterval(checkInterval);
        if (allowSkip) {
          resolve({ userInput: skipValue || null, skipped: true, timeout: true });
        } else {
          reject(new Error('Human input timeout'));
        }
      }, timeout);
    });
  }

  private async executeHybridNode(
    node: WorkflowNode,
    definition: WorkflowDefinition,
    context: any,
  ): Promise<any> {
    const { agentId, toolIds, executionPattern, maxIterations, agentConfig, toolConfigs, coordination, performance, fallback } = node.config;
    
    // Validate hybrid node configuration
    if (!agentId || !toolIds || toolIds.length === 0) {
      throw new Error('Hybrid node requires both agent and tools');
    }

    if (!executionPattern) {
      throw new Error('Hybrid node requires execution pattern');
    }

    // Create hybrid execution context
    const hybridContext = {
      executionId: context.executionId,
      sessionId: context.sessionId,
      organizationId: context.organizationId,
      userId: context.userId,
      nodeId: node.id,
      workflowId: context.workflowId,
    };

    // Build hybrid node configuration
    const hybridConfig = {
      agentId,
      toolIds,
      executionPattern,
      maxIterations: maxIterations || 5,
      agentConfig: {
        systemPrompt: agentConfig?.systemPrompt,
        maxTokens: agentConfig?.maxTokens || 2000,
        temperature: agentConfig?.temperature || 0.7,
        contextWindow: agentConfig?.contextWindow || 8000,
      },
      toolConfigs: toolConfigs || {},
      coordination: {
        shareContext: coordination?.shareContext !== false,
        contextStrategy: coordination?.contextStrategy || 'full',
        syncPoints: coordination?.syncPoints || [],
        errorPropagation: coordination?.errorPropagation !== false,
      },
      performance: {
        parallelLimit: performance?.parallelLimit || 3,
        timeout: performance?.timeout || 120000,
        memoryLimit: performance?.memoryLimit || 50,
      },
      fallback: {
        enabled: fallback?.enabled !== false,
        fallbackAgent: fallback?.fallbackAgent,
        fallbackTools: fallback?.fallbackTools || [],
        conditions: fallback?.conditions || ['timeout', 'error', 'low_confidence'],
      },
    };

    // Execute hybrid node using the specialized service
    return await this.hybridNodeExecutor.executeHybridNode(
      hybridConfig,
      context.variables,
      hybridContext
    );
  }

  private async orchestrateMultipleTools(
    agentId: string,
    toolIds: string[],
    config: any,
    context: any,
  ): Promise<any> {
    const results = [];
    let currentInput = config.agentConfig?.input || {};
    
    // Initial agent call to determine first tool
    let agentResult = await this.executeAgentNode(
      {
        id: `${context.executionId}_agent_initial`,
        type: 'agent',
        name: 'Orchestrator Agent Initial',
        config: { agentId, input: currentInput },
        position: { x: 0, y: 0 },
        inputs: [],
        outputs: [],
      },
      context
    );
    
    results.push({ type: 'agent', result: agentResult });
    
    // Extract first tool to call
    let nextToolId = this.determineNextTool(agentResult, toolIds);
    let iterationCount = 0;
    const maxIterations = config.maxIterations || 5;
    
    // Continue until agent doesn't request more tools or max iterations reached
    while (nextToolId && iterationCount < maxIterations) {
      // Execute the tool
      const toolResult = await this.executeToolNode(
        {
          id: `${context.executionId}_tool_${nextToolId}_${iterationCount}`,
          type: 'tool',
          name: `Tool ${nextToolId} Execution`,
          config: { 
            toolId: nextToolId, 
            parameters: config.toolConfigs?.[nextToolId] || {},
          },
          position: { x: 0, y: 0 },
          inputs: [],
          outputs: [],
        },
        context
      );
      
      results.push({ type: 'tool', toolId: nextToolId, result: toolResult });
      
      // Update context with tool result
      context.variables[`tool_${nextToolId}_result`] = toolResult;
      
      // Ask agent for next step
      currentInput = {
        ...currentInput,
        lastToolResult: toolResult,
        availableTools: toolIds,
        executionHistory: results,
      };
      
      agentResult = await this.executeAgentNode(
        {
          id: `${context.executionId}_agent_iteration_${iterationCount}`,
          type: 'agent',
          name: `Orchestrator Agent Iteration ${iterationCount + 1}`,
          config: { agentId, input: currentInput },
          position: { x: 0, y: 0 },
          inputs: [],
          outputs: [],
        },
        context
      );
      
      results.push({ type: 'agent', result: agentResult });
      
      // Determine if we need another tool
      nextToolId = this.determineNextTool(agentResult, toolIds);
      iterationCount++;
    }
    
    // Final agent call to summarize results
    const finalAgentResult = await this.executeAgentNode(
      {
        id: `${context.executionId}_agent_final`,
        type: 'agent',
        name: 'Orchestrator Agent Final',
        config: { 
          agentId, 
          input: {
            ...currentInput,
            executionHistory: results,
            task: 'summarize_results',
          },
        },
        position: { x: 0, y: 0 },
        inputs: [],
        outputs: [],
      },
      context
    );
    
    results.push({ type: 'agent_final', result: finalAgentResult });
    
    return {
      iterations: iterationCount,
      results,
      summary: finalAgentResult,
    };
  }

  private determineNextTool(agentResult: any, availableToolIds: string[]): string | null {
    // In a real implementation, this would parse the agent's response to determine the next tool
    // For now, use a simple approach
    
    if (!agentResult || !agentResult.response) {
      return null;
    }
    
    // Check if agent response contains a tool request
    const response = agentResult.response.toLowerCase();
    
    for (const toolId of availableToolIds) {
      if (response.includes(toolId.toLowerCase())) {
        return toolId;
      }
    }
    
    // If no specific tool mentioned, check for generic "tool" request
    if (response.includes('use tool') || response.includes('execute tool')) {
      return availableToolIds[0]; // Default to first tool
    }
    
    return null; // No tool requested
  }

  private extractToolParameters(agentResult: any, toolIds: string[]): Record<string, any> {
    // In a real implementation, this would parse the agent's response to extract parameters
    // For now, return a simple object
    
    const params = {};
    
    toolIds.forEach(toolId => {
      params[toolId] = {
        query: `Extracted from agent response: ${agentResult.response.substring(0, 50)}...`,
        timestamp: Date.now(),
      };
    });
    
    return params;
  }

  private getNextNodes(
    currentNodeId: string,
    definition: WorkflowDefinition,
    nodeResult: any,
  ): WorkflowNode[] {
    const outgoingEdges = definition.edges.filter(edge => edge.source === currentNodeId);
    const nextNodes: WorkflowNode[] = [];

    for (const edge of outgoingEdges) {
      // Check edge condition if present
      if (edge.condition) {
        try {
          const conditionMet = this.evaluateEdgeCondition(edge.condition, nodeResult);
          if (!conditionMet) continue;
        } catch (error) {
          this.logger.warn(`Edge condition evaluation failed: ${error.message}`);
          continue;
        }
      }

      const nextNode = definition.nodes.find(node => node.id === edge.target);
      if (nextNode) {
        nextNodes.push(nextNode);
      }
    }

    return nextNodes;
  }

  private evaluateEdgeCondition(condition: string, result: any): boolean {
    // This is a simplified evaluation - in production, use a safe expression evaluator
    try {
      // Replace $result with the actual result
      const conditionWithResult = condition.replace(/\$result/g, JSON.stringify(result));
      return eval(conditionWithResult);
    } catch (error) {
      throw new Error(`Edge condition evaluation failed: ${error.message}`);
    }
  }

  private evaluateCondition(condition: string, variables: Record<string, any>): boolean {
    // This is a simplified evaluation - in production, use a safe expression evaluator
    try {
      // Create a function with variables as parameters
      const keys = Object.keys(variables);
      const values = Object.values(variables);
      
      // Create a safe evaluation context
      const evalFunction = new Function(...keys, `return ${condition};`);
      return evalFunction(...values);
    } catch (error) {
      throw new Error(`Condition evaluation failed: ${error.message}`);
    }
  }

  private processVariables(template: any, variables: Record<string, any>): any {
    if (typeof template === 'string') {
      return template.replace(/\${(\w+)}/g, (match, varName) => {
        return variables[varName] !== undefined ? variables[varName] : match;
      });
    } else if (Array.isArray(template)) {
      return template.map(item => this.processVariables(item, variables));
    } else if (template && typeof template === 'object') {
      const result = {};
      for (const key in template) {
        result[key] = this.processVariables(template[key], variables);
      }
      return result;
    }
    
    return template;
  }
}