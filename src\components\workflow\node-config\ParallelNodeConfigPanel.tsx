import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { CheckCircle, XCircle, AlertTriangle, Split, Plus, Trash2, Play, Pause } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const parallelConfigSchema = z.object({
  nodes: z.array(z.object({
    id: z.string(),
    nodeId: z.string(),
    label: z.string(),
    enabled: z.boolean(),
    timeout: z.number().min(1000).max(300000),
    priority: z.enum(['low', 'normal', 'high']),
    inputMapping: z.record(z.string(), z.string()),
    outputKey: z.string().optional(),
  })).min(1, 'At least one parallel node is required'),
  executionMode: z.enum(['all', 'race', 'some']),
  someCount: z.number().min(1).optional(),
  aggregateResults: z.boolean(),
  aggregationStrategy: z.enum(['merge', 'array', 'custom']),
  customAggregation: z.string().optional(),
  timeout: z.number().min(1000).max(600000),
  errorHandling: z.object({
    onError: z.enum(['stop_all', 'continue', 'ignore']),
    minSuccessCount: z.number().min(0),
    collectErrors: z.boolean(),
  }),
  concurrencyLimit: z.number().min(1).max(50),
  retryPolicy: z.object({
    enabled: z.boolean(),
    maxRetries: z.number().min(0).max(5),
    retryDelay: z.number().min(100).max(10000),
  }),
});

type ParallelConfigFormData = z.infer<typeof parallelConfigSchema>;

interface ParallelNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<ParallelConfigFormData>;
  onConfigChange: (config: ParallelConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
  availableNodes?: Array<{ id: string; name: string; type: string }>;
}

export default function ParallelNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
  availableNodes = [],
}: ParallelNodeConfigPanelProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm<ParallelConfigFormData>({
    resolver: zodResolver(parallelConfigSchema),
    defaultValues: {
      nodes: [],
      executionMode: 'all',
      someCount: 2,
      aggregateResults: true,
      aggregationStrategy: 'merge',
      customAggregation: '',
      timeout: 60000,
      errorHandling: {
        onError: 'stop_all',
        minSuccessCount: 0,
        collectErrors: true,
      },
      concurrencyLimit: 10,
      retryPolicy: {
        enabled: false,
        maxRetries: 2,
        retryDelay: 1000,
      },
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const nodes = watch('nodes');
  const executionMode = watch('executionMode');
  const aggregationStrategy = watch('aggregationStrategy');

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map((error: any) => error.message).filter(Boolean) as string[];
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, errors, onConfigChange, onValidationChange]);

  const addParallelNode = () => {
    const newNode = {
      id: `parallel_${Date.now()}`,
      nodeId: '',
      label: `Node ${nodes.length + 1}`,
      enabled: true,
      timeout: 30000,
      priority: 'normal' as const,
      inputMapping: {},
      outputKey: `result_${nodes.length + 1}`,
    };
    setValue('nodes', [...nodes, newNode]);
  };

  const removeParallelNode = (id: string) => {
    setValue('nodes', nodes.filter((n: any) => n.id !== id));
  };

  const updateParallelNode = (id: string, field: string, value: any) => {
    const updated = nodes.map((n: any) =>
      n.id === id ? { ...n, [field]: value } : n
    );
    setValue('nodes', updated);
  };

  const toggleNodeEnabled = (id: string) => {
    const node = nodes.find((n: any) => n.id === id);
    if (node) {
      updateParallelNode(id, 'enabled', !node.enabled);
    }
  };

  const getExecutionModeDescription = (mode: string) => {
    switch (mode) {
      case 'all':
        return 'Wait for all nodes to complete successfully';
      case 'race':
        return 'Complete when the first node finishes';
      case 'some':
        return 'Complete when a specified number of nodes finish';
      default:
        return '';
    }
  };

  const getAggregationDescription = (strategy: string) => {
    switch (strategy) {
      case 'merge':
        return 'Merge all results into a single object';
      case 'array':
        return 'Collect results as an array';
      case 'custom':
        return 'Use custom JavaScript aggregation function';
      default:
        return '';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-5xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <Split className="h-5 w-5" />
            <CardTitle>Parallel Node Configuration</CardTitle>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            {isValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
            <Badge variant="outline">
              {nodes.filter((n: any) => n.enabled).length} / {nodes.length} Active
            </Badge>
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="nodes" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="nodes">Parallel Nodes</TabsTrigger>
              <TabsTrigger value="execution">Execution</TabsTrigger>
              <TabsTrigger value="aggregation">Aggregation</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="nodes" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Parallel Execution Nodes</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addParallelNode}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Node
                  </Button>
                </div>

                <p className="text-sm text-muted-foreground">
                  Configure nodes to execute in parallel. Each node will run simultaneously with the others.
                </p>

                <div className="space-y-4">
                  {nodes.map((node: any, index: number ) => (
                    <Card key={node.id} className={`p-4 ${!node.enabled ? 'opacity-60' : ''}`}>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleNodeEnabled(node.id)}
                          >
                            {node.enabled ? (
                              <Play className="h-4 w-4 text-green-600" />
                            ) : (
                              <Pause className="h-4 w-4 text-gray-400" />
                            )}
                          </Button>
                          <h5 className="font-medium">Node {index + 1}</h5>
                          <Badge className={getPriorityColor(node.priority)}>
                            {node.priority}
                          </Badge>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeParallelNode(node.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="space-y-2">
                          <Label>Label</Label>
                          <Input
                            value={node.label}
                            onChange={(e) => updateParallelNode(node.id, 'label', e.target.value)}
                            placeholder="Node label"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Target Node</Label>
                          <Select
                            value={node.nodeId}
                            onValueChange={(value) => updateParallelNode(node.id, 'nodeId', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select node" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableNodes.map((availableNode: any ) => (
                                <SelectItem key={availableNode.id} value={availableNode.id}>
                                  <div className="flex items-center space-x-2">
                                    <span>{availableNode.name}</span>
                                    <Badge variant="outline" className="text-xs">
                                      {availableNode.type}
                                    </Badge>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Priority</Label>
                          <Select
                            value={node.priority}
                            onValueChange={(value: 'low' | 'normal' | 'high') =>
                              updateParallelNode(node.id, 'priority', value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="normal">Normal</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Output Key</Label>
                          <Input
                            value={node.outputKey || ''}
                            onChange={(e) => updateParallelNode(node.id, 'outputKey', e.target.value)}
                            placeholder="result_key"
                          />
                        </div>
                      </div>

                      <div className="mt-4 space-y-2">
                        <Label>Timeout (ms)</Label>
                        <div className="space-y-2">
                          <Slider
                            value={[node.timeout]}
                            onValueChange={([value]) => updateParallelNode(node.id, 'timeout', value)}
                            min={1000}
                            max={300000}
                            step={1000}
                            className="w-full"
                          />
                          <div className="text-xs text-muted-foreground">
                            {Math.round(node.timeout / 1000)}s timeout
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 space-y-2">
                        <Label>Input Mapping (JSON)</Label>
                        <Textarea
                          value={JSON.stringify(node.inputMapping, null, 2)}
                          onChange={(e) => {
                            try {
                              const parsed = JSON.parse(e.target.value);
                              updateParallelNode(node.id, 'inputMapping', parsed);
                            } catch (error) {
                              // Invalid JSON, don't update
                            }
                          }}
                          rows={3}
                          className="font-mono text-sm"
                          placeholder='{\n  "input_key": "${variable}"\n}'
                        />
                      </div>
                    </Card>
                  ))}

                  {nodes.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Split className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No parallel nodes configured</p>
                      <p className="text-sm">Add nodes to execute in parallel</p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="execution" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Execution Mode</h4>

                  <div className="space-y-2">
                    <Select
                      value={watch('executionMode')}
                      onValueChange={(value: 'all' | 'race' | 'some') =>
                        setValue('executionMode', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All (Wait for all)</SelectItem>
                        <SelectItem value="race">Race (First to complete)</SelectItem>
                        <SelectItem value="some">Some (Wait for N)</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      {getExecutionModeDescription(executionMode)}
                    </p>
                  </div>

                  {executionMode === 'some' && (
                    <div className="space-y-2">
                      <Label>Success Count</Label>
                      <div className="space-y-2">
                        <Slider
                          value={[watch('someCount') || 2]}
                          onValueChange={([value]) => setValue('someCount', value)}
                          min={1}
                          max={Math.max(1, nodes.length)}
                          step={1}
                          className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                          Wait for {watch('someCount')} nodes to complete
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Concurrency Control</h4>

                  <div className="space-y-2">
                    <Label>Concurrency Limit</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[watch('concurrencyLimit') || 10]}
                        onValueChange={([value]) => setValue('concurrencyLimit', value)}
                        min={1}
                        max={50}
                        step={1}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground">
                        Maximum {watch('concurrencyLimit')} nodes executing simultaneously
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Global Timeout</h4>

                  <div className="space-y-2">
                    <Label>Overall Timeout (ms)</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[watch('timeout') || 60000]}
                        onValueChange={([value]) => setValue('timeout', value)}
                        min={1000}
                        max={600000}
                        step={1000}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>1s</span>
                        <span>{Math.round((watch('timeout') || 60000) / 1000)}s</span>
                        <span>10m</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="aggregation" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('aggregateResults')}
                      onCheckedChange={(checked) => setValue('aggregateResults', checked)}
                    />
                    <Label>Aggregate Results</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Combine results from all parallel nodes into a single output
                  </p>
                </div>

                {watch('aggregateResults') && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Aggregation Strategy</Label>
                      <Select
                        value={watch('aggregationStrategy')}
                        onValueChange={(value: 'merge' | 'array' | 'custom') =>
                          setValue('aggregationStrategy', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="merge">Merge Objects</SelectItem>
                          <SelectItem value="array">Array Collection</SelectItem>
                          <SelectItem value="custom">Custom Function</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        {getAggregationDescription(aggregationStrategy)}
                      </p>
                    </div>

                    {aggregationStrategy === 'custom' && (
                      <div className="space-y-2">
                        <Label>Custom Aggregation Function</Label>
                        <Textarea
                          value={watch('customAggregation') || ''}
                          onChange={(e) => setValue('customAggregation', e.target.value)}
                          rows={6}
                          className="font-mono text-sm"
                          placeholder={`// Function to aggregate results
// Input: results array
// Output: aggregated result
function aggregate(results) {
  return results.reduce((acc, result, index) => {
    acc[\`node_\${index}\`] = result;
    return acc;
  }, {});
}`}
                        />
                        <p className="text-xs text-muted-foreground">
                          JavaScript function that takes an array of results and returns the aggregated result
                        </p>
                      </div>
                    )}

                    <div className="p-4 bg-muted rounded-lg">
                      <h5 className="font-medium mb-2">Preview</h5>
                      <div className="text-sm font-mono">
                        {aggregationStrategy === 'merge' && (
                          <div>
                            <span className="text-muted-foreground">// Merge strategy</span><br />
                            {`{ ...result1, ...result2, ...result3 }`}
                          </div>
                        )}
                        {aggregationStrategy === 'array' && (
                          <div>
                            <span className="text-muted-foreground">// Array strategy</span><br />
                            {`[result1, result2, result3]`}
                          </div>
                        )}
                        {aggregationStrategy === 'custom' && (
                          <div>
                            <span className="text-muted-foreground">// Custom function result</span><br />
                            {`aggregate([result1, result2, result3])`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Error Handling</h4>

                  <div className="space-y-2">
                    <Label>On Error</Label>
                    <Select
                      value={watch('errorHandling.onError')}
                      onValueChange={(value: 'stop_all' | 'continue' | 'ignore') =>
                        setValue('errorHandling.onError', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="stop_all">Stop all nodes</SelectItem>
                        <SelectItem value="continue">Continue with successful</SelectItem>
                        <SelectItem value="ignore">Ignore errors</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Minimum Success Count</Label>
                    <Slider
                      value={[watch('errorHandling.minSuccessCount') || 0]}
                      onValueChange={([value]) => setValue('errorHandling.minSuccessCount', value)}
                      min={0}
                      max={Math.max(1, nodes.length)}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      Require at least {watch('errorHandling.minSuccessCount')} successful executions
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('errorHandling.collectErrors')}
                      onCheckedChange={(checked) => setValue('errorHandling.collectErrors', checked)}
                    />
                    <Label>Collect error details</Label>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Retry Policy</h4>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('retryPolicy.enabled')}
                      onCheckedChange={(checked) => setValue('retryPolicy.enabled', checked)}
                    />
                    <Label>Enable retry for failed nodes</Label>
                  </div>

                  {watch('retryPolicy.enabled') && (
                    <div className="space-y-4 pl-6 border-l-2 border-muted">
                      <div className="space-y-2">
                        <Label>Max Retries</Label>
                        <Slider
                          value={[watch('retryPolicy.maxRetries') || 2]}
                          onValueChange={([value]) => setValue('retryPolicy.maxRetries', value)}
                          min={0}
                          max={5}
                          step={1}
                          className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                          {watch('retryPolicy.maxRetries')} retries per node
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Retry Delay (ms)</Label>
                        <Slider
                          value={[watch('retryPolicy.retryDelay') || 1000]}
                          onValueChange={([value]) => setValue('retryPolicy.retryDelay', value)}
                          min={100}
                          max={10000}
                          step={100}
                          className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                          {watch('retryPolicy.retryDelay')}ms delay between retries
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Performance Summary</h4>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Total Nodes:</span>
                        <span className="ml-2">{nodes.length}</span>
                      </div>
                      <div>
                        <span className="font-medium">Active Nodes:</span>
                        <span className="ml-2">{nodes.filter(n => n.enabled).length}</span>
                      </div>
                      <div>
                        <span className="font-medium">Execution Mode:</span>
                        <span className="ml-2">{executionMode}</span>
                      </div>
                      <div>
                        <span className="font-medium">Concurrency:</span>
                        <span className="ml-2">{watch('concurrencyLimit')}</span>
                      </div>
                      <div>
                        <span className="font-medium">Global Timeout:</span>
                        <span className="ml-2">{Math.round((watch('timeout') || 60000) / 1000)}s</span>
                      </div>
                      <div>
                        <span className="font-medium">Aggregation:</span>
                        <span className="ml-2">{watch('aggregateResults') ? 'Enabled' : 'Disabled'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}