import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

interface AnalyticsEvent {
  event: string;
  userId: string;
  organizationId: string;
  properties: Record<string, any>;
  timestamp: Date;
}

@Injectable()
export class AnalyticsService {
  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async trackEvent(event: AnalyticsEvent) {
    // Store in audit log for persistence
    await this.prisma.auditLog.create({
      data: {
        userId: event.userId,
        organizationId: event.organizationId,
        action: event.event,
        resource: 'analytics',
        details: event.properties,
      },
    });

    // Cache for real-time analytics
    const cacheKey = `analytics:${event.organizationId}:${event.event}:${new Date().toISOString().split('T')[0]}`;
    const currentCount = await this.cacheManager.get<number>(cacheKey) || 0;
    await this.cacheManager.set(cacheKey, currentCount + 1, 86400); // 24 hours

    return true;
  }

  async getDashboardAnalytics(organizationId: string) {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const [
      totalUsers,
      activeUsers,
      totalWorkflows,
      activeWorkflows,
      totalAgents,
      activeAgents,
      totalSessions,
      activeSessions,
      workflowExecutions,
      recentActivity,
    ] = await Promise.all([
      this.prisma.user.count({
        where: { organizationId },
      }),
      this.prisma.user.count({
        where: { 
          organizationId,
          isActive: true,
          lastLoginAt: { gte: lastWeek },
        },
      }),
      this.prisma.workflow.count({
        where: { organizationId },
      }),
      this.prisma.workflow.count({
        where: { 
          organizationId,
          isActive: true,
        },
      }),
      this.prisma.agent.count({
        where: { organizationId },
      }),
      this.prisma.agent.count({
        where: { 
          organizationId,
          isActive: true,
        },
      }),
      this.prisma.session.count({
        where: { organizationId },
      }),
      this.prisma.session.count({
        where: { 
          organizationId,
          isActive: true,
        },
      }),
      this.prisma.workflowExecution.count({
        where: {
          workflow: { organizationId },
          startedAt: { gte: lastMonth },
        },
      }),
      this.prisma.auditLog.findMany({
        where: { organizationId },
        take: 20,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      }),
    ]);

    return {
      overview: {
        totalUsers,
        activeUsers,
        totalWorkflows,
        activeWorkflows,
        totalAgents,
        activeAgents,
        totalSessions,
        activeSessions,
        workflowExecutions,
      },
      recentActivity,
    };
  }

  async getWorkflowAnalytics(organizationId: string, timeRange = '7d') {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
    }

    const [
      executionStats,
      statusDistribution,
      topWorkflows,
      executionTrends,
    ] = await Promise.all([
      this.prisma.workflowExecution.aggregate({
        where: {
          workflow: { organizationId },
          startedAt: { gte: startDate },
        },
        _count: { id: true },
        _avg: { duration: true },
      }),
      this.prisma.workflowExecution.groupBy({
        by: ['status'],
        where: {
          workflow: { organizationId },
          startedAt: { gte: startDate },
        },
        _count: { id: true },
      }),
      this.prisma.workflowExecution.groupBy({
        by: ['workflowId'],
        where: {
          workflow: { organizationId },
          startedAt: { gte: startDate },
        },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10,
      }),
      this.getExecutionTrends(organizationId, startDate, endDate),
    ]);

    return {
      stats: {
        totalExecutions: executionStats._count.id,
        avgDuration: executionStats._avg.duration,
      },
      statusDistribution,
      topWorkflows,
      trends: executionTrends,
    };
  }

  async getAgentAnalytics(organizationId: string, timeRange = '7d') {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
    }

    const [
      agentStats,
      typeDistribution,
      sessionStats,
      topAgents,
    ] = await Promise.all([
      this.prisma.agent.aggregate({
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.agent.groupBy({
        by: ['type'],
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.agentSession.aggregate({
        where: {
          agent: { organizationId },
          createdAt: { gte: startDate },
        },
        _count: { id: true },
      }),
      this.prisma.agentSession.groupBy({
        by: ['agentId'],
        where: {
          agent: { organizationId },
          createdAt: { gte: startDate },
        },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10,
      }),
    ]);

    return {
      stats: {
        totalAgents: agentStats._count.id,
        totalSessions: sessionStats._count.id,
      },
      typeDistribution,
      topAgents,
    };
  }

  async getUserAnalytics(organizationId: string, timeRange = '7d') {
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
    }

    const [
      userStats,
      roleDistribution,
      activeUsers,
      loginTrends,
    ] = await Promise.all([
      this.prisma.user.aggregate({
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.user.groupBy({
        by: ['role'],
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.user.count({
        where: {
          organizationId,
          lastLoginAt: { gte: startDate },
        },
      }),
      this.getLoginTrends(organizationId, startDate, endDate),
    ]);

    return {
      stats: {
        totalUsers: userStats._count.id,
        activeUsers,
      },
      roleDistribution,
      trends: loginTrends,
    };
  }

  async getSystemHealth(organizationId: string) {
    const [
      errorRate,
      avgResponseTime,
      uptime,
      resourceUsage,
    ] = await Promise.all([
      this.calculateErrorRate(organizationId),
      this.calculateAvgResponseTime(organizationId),
      this.calculateUptime(organizationId),
      this.getResourceUsage(organizationId),
    ]);

    return {
      errorRate,
      avgResponseTime,
      uptime,
      resourceUsage,
      status: this.getSystemStatus(errorRate, avgResponseTime, uptime),
    };
  }

  private async getExecutionTrends(organizationId: string, startDate: Date, endDate: Date) {
    // Implementation for execution trends over time
    const executions = await this.prisma.workflowExecution.findMany({
      where: {
        workflow: { organizationId },
        startedAt: { gte: startDate, lte: endDate },
      },
      select: {
        startedAt: true,
        status: true,
      },
    });

    // Group by day and status
    const trends: Record<string, Record<string, number>> = {};
    
    executions.forEach(execution => {
      const day = execution.startedAt.toISOString().split('T')[0];
      if (!trends[day]) {
        trends[day] = {};
      }
      trends[day][execution.status] = (trends[day][execution.status] || 0) + 1;
    });

    return trends;
  }

  private async getLoginTrends(organizationId: string, startDate: Date, endDate: Date) {
    const logins = await this.prisma.auditLog.findMany({
      where: {
        organizationId,
        action: 'USER_LOGIN',
        createdAt: { gte: startDate, lte: endDate },
      },
      select: {
        createdAt: true,
      },
    });

    const trends: Record<string, number> = {};
    
    logins.forEach(login => {
      const day = login.createdAt.toISOString().split('T')[0];
      trends[day] = (trends[day] || 0) + 1;
    });

    return trends;
  }

  private async calculateErrorRate(organizationId: string): Promise<number> {
    const totalExecutions = await this.prisma.workflowExecution.count({
      where: { workflow: { organizationId } },
    });

    const failedExecutions = await this.prisma.workflowExecution.count({
      where: { 
        workflow: { organizationId },
        status: 'FAILED',
      },
    });

    return totalExecutions > 0 ? (failedExecutions / totalExecutions) * 100 : 0;
  }

  private async calculateAvgResponseTime(organizationId: string): Promise<number> {
    const result = await this.prisma.workflowExecution.aggregate({
      where: { workflow: { organizationId } },
      _avg: { duration: true },
    });

    return result._avg.duration || 0;
  }

  private async calculateUptime(organizationId: string): Promise<number> {
    // Simplified uptime calculation based on successful executions
    const recentExecutions = await this.prisma.workflowExecution.count({
      where: {
        workflow: { organizationId },
        startedAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      },
    });

    const successfulExecutions = await this.prisma.workflowExecution.count({
      where: {
        workflow: { organizationId },
        status: 'COMPLETED',
        startedAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      },
    });

    return recentExecutions > 0 ? (successfulExecutions / recentExecutions) * 100 : 100;
  }

  private async getResourceUsage(organizationId: string) {
    // Simplified resource usage metrics
    const [sessions, workflows, agents] = await Promise.all([
      this.prisma.session.count({
        where: { organizationId, isActive: true },
      }),
      this.prisma.workflow.count({
        where: { organizationId, isActive: true },
      }),
      this.prisma.agent.count({
        where: { organizationId, isActive: true },
      }),
    ]);

    return {
      activeSessions: sessions,
      activeWorkflows: workflows,
      activeAgents: agents,
    };
  }

  private getSystemStatus(errorRate: number, avgResponseTime: number, uptime: number): string {
    if (errorRate > 5 || avgResponseTime > 5000 || uptime < 95) {
      return 'critical';
    } else if (errorRate > 2 || avgResponseTime > 2000 || uptime < 98) {
      return 'warning';
    } else {
      return 'healthy';
    }
  }
}