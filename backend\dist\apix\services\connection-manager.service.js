"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ConnectionManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionManagerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const apix_dto_1 = require("../dto/apix.dto");
let ConnectionManagerService = ConnectionManagerService_1 = class ConnectionManagerService {
    constructor(prisma, cacheManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(ConnectionManagerService_1.name);
        this.connections = new Map();
        this.userConnections = new Map();
        this.organizationConnections = new Map();
        this.startCleanupTimer();
    }
    async createConnection(socketId, sessionId, userId, organizationId, clientType, metadata = {}, ipAddress, userAgent) {
        const connection = await this.prisma.apiXConnection.create({
            data: {
                sessionId,
                organizationId,
                userId,
                clientType,
                channels: [],
                metadata,
                ipAddress,
                userAgent,
                status: apix_dto_1.ConnectionStatus.CONNECTED,
            },
        });
        const context = {
            id: connection.id,
            sessionId,
            userId,
            organizationId,
            clientType,
            channels: new Set(),
            lastActivity: Date.now(),
            latencyMetrics: new Map(),
            metadata,
            reconnectCount: 0,
            ipAddress,
            userAgent,
        };
        this.connections.set(socketId, context);
        if (userId) {
            if (!this.userConnections.has(userId)) {
                this.userConnections.set(userId, new Set());
            }
            this.userConnections.get(userId).add(socketId);
        }
        if (!this.organizationConnections.has(organizationId)) {
            this.organizationConnections.set(organizationId, new Set());
        }
        this.organizationConnections.get(organizationId).add(socketId);
        await this.cacheConnection(socketId, context);
        this.logger.log(`Connection created: ${socketId} for user ${userId} in org ${organizationId}`);
        return context;
    }
    async updateConnection(socketId, updates) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        if (updates.lastActivity) {
            context.lastActivity = updates.lastActivity;
        }
        if (updates.reconnectCount !== undefined) {
            context.reconnectCount = updates.reconnectCount;
        }
        if (updates.metadata) {
            context.metadata = { ...context.metadata, ...updates.metadata };
        }
        await this.prisma.apiXConnection.update({
            where: { id: context.id },
            data: {
                status: updates.status,
                lastHeartbeat: updates.lastActivity
                    ? new Date(updates.lastActivity)
                    : undefined,
                reconnectCount: updates.reconnectCount,
                latencyMs: updates.latencyMs,
                metadata: context.metadata,
            },
        });
        await this.cacheConnection(socketId, context);
    }
    async removeConnection(socketId) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        await this.prisma.apiXConnection.update({
            where: { id: context.id },
            data: {
                status: apix_dto_1.ConnectionStatus.DISCONNECTED,
                metadata: {
                    ...context.metadata,
                    disconnectedAt: new Date().toISOString(),
                    duration: Date.now() - context.lastActivity,
                },
            },
        });
        this.connections.delete(socketId);
        if (context.userId) {
            const userConnections = this.userConnections.get(context.userId);
            if (userConnections) {
                userConnections.delete(socketId);
                if (userConnections.size === 0) {
                    this.userConnections.delete(context.userId);
                }
            }
        }
        const orgConnections = this.organizationConnections.get(context.organizationId);
        if (orgConnections) {
            orgConnections.delete(socketId);
            if (orgConnections.size === 0) {
                this.organizationConnections.delete(context.organizationId);
            }
        }
        await this.cacheManager.del(`connection:${socketId}`);
        this.logger.log(`Connection removed: ${socketId}`);
    }
    getConnection(socketId) {
        return this.connections.get(socketId);
    }
    async getConnectionFromCache(socketId) {
        const cached = await this.cacheManager.get(`connection:${socketId}`);
        return cached || null;
    }
    getUserConnections(userId) {
        const connections = this.userConnections.get(userId);
        return connections ? Array.from(connections) : [];
    }
    getOrganizationConnections(organizationId) {
        const connections = this.organizationConnections.get(organizationId);
        return connections ? Array.from(connections) : [];
    }
    getAllConnections() {
        return new Map(this.connections);
    }
    getConnectionStats() {
        const now = Date.now();
        const healthyThreshold = 300000;
        let totalLatency = 0;
        let latencyCount = 0;
        let healthyCount = 0;
        let reconnectingCount = 0;
        const stats = {
            total: this.connections.size,
            byClientType: {},
            byOrganization: {},
            activeUsers: this.userConnections.size,
            averageLatency: 0,
            connectionsPerUser: 0,
            healthyConnections: 0,
            reconnectingConnections: 0,
        };
        Object.values(apix_dto_1.ClientType).forEach((type) => {
            stats.byClientType[type] = 0;
        });
        for (const context of this.connections.values()) {
            stats.byClientType[context.clientType]++;
            stats.byOrganization[context.organizationId] =
                (stats.byOrganization[context.organizationId] || 0) + 1;
            if (now - context.lastActivity < healthyThreshold) {
                healthyCount++;
            }
            if (context.reconnectCount > 0) {
                reconnectingCount++;
            }
            for (const metrics of context.latencyMetrics.values()) {
                if (metrics.length > 0) {
                    totalLatency += metrics.reduce((sum, val) => sum + val, 0);
                    latencyCount += metrics.length;
                }
            }
        }
        stats.averageLatency = latencyCount > 0 ? totalLatency / latencyCount : 0;
        stats.connectionsPerUser =
            stats.activeUsers > 0 ? stats.total / stats.activeUsers : 0;
        stats.healthyConnections = healthyCount;
        stats.reconnectingConnections = reconnectingCount;
        return stats;
    }
    async addChannelToConnection(socketId, channel) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        context.channels.add(channel);
        await this.prisma.apiXConnection.update({
            where: { id: context.id },
            data: {
                channels: Array.from(context.channels),
            },
        });
        await this.cacheConnection(socketId, context);
    }
    async removeChannelFromConnection(socketId, channel) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        context.channels.delete(channel);
        await this.prisma.apiXConnection.update({
            where: { id: context.id },
            data: {
                channels: Array.from(context.channels),
            },
        });
        await this.cacheConnection(socketId, context);
    }
    trackLatency(socketId, eventType, latency) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        if (!context.latencyMetrics.has(eventType)) {
            context.latencyMetrics.set(eventType, []);
        }
        const metrics = context.latencyMetrics.get(eventType);
        metrics.push(latency);
        if (metrics.length > 100) {
            metrics.shift();
        }
        this.prisma.apiXLatencyMetric
            .create({
            data: {
                connectionId: context.id,
                eventType,
                latencyMs: latency,
                organizationId: context.organizationId,
            },
        })
            .catch((error) => {
            this.logger.error("Failed to store latency metric:", error);
        });
    }
    getLatencyStats(socketId, eventType) {
        const context = this.connections.get(socketId);
        if (!context)
            return null;
        const metrics = eventType
            ? context.latencyMetrics.get(eventType)
            : Array.from(context.latencyMetrics.values()).flat();
        if (!metrics || metrics.length === 0)
            return null;
        return {
            avg: metrics.reduce((a, b) => a + b, 0) / metrics.length,
            min: Math.min(...metrics),
            max: Math.max(...metrics),
            count: metrics.length,
        };
    }
    async updateHeartbeat(socketId, latency) {
        const context = this.connections.get(socketId);
        if (!context)
            return;
        context.lastActivity = Date.now();
        await this.updateConnection(socketId, {
            lastActivity: context.lastActivity,
            latencyMs: latency,
        });
        if (latency) {
            this.trackLatency(socketId, "heartbeat", latency);
        }
    }
    async cacheConnection(socketId, context) {
        await this.cacheManager.set(`connection:${socketId}`, context, 3600);
    }
    startCleanupTimer() {
        setInterval(() => {
            this.cleanupInactiveConnections();
        }, 300000);
    }
    async cleanupInactiveConnections() {
        const now = Date.now();
        const timeout = 1800000;
        const toRemove = [];
        for (const [socketId, context] of this.connections.entries()) {
            if (now - context.lastActivity > timeout) {
                toRemove.push(socketId);
            }
        }
        for (const socketId of toRemove) {
            await this.removeConnection(socketId);
            this.logger.log(`Cleaned up inactive connection: ${socketId}`);
        }
        if (toRemove.length > 0) {
            this.logger.log(`Cleaned up ${toRemove.length} inactive connections`);
        }
    }
};
exports.ConnectionManagerService = ConnectionManagerService;
exports.ConnectionManagerService = ConnectionManagerService = ConnectionManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object])
], ConnectionManagerService);
//# sourceMappingURL=connection-manager.service.js.map