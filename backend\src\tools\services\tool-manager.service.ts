import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ToolType, ToolCacheStrategy, SkillCategory } from '@prisma/client';
import { z } from 'zod';
import * as crypto from 'crypto';

// Zod schemas for validation
const ToolDefinitionSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  category: z.nativeEnum(SkillCategory).default(SkillCategory.CUSTOM),
  type: z.nativeEnum(ToolType).default(ToolType.FUNCTION_CALL),
  config: z.record(z.any()).default({}),
  inputSchema: z.record(z.any()).default({}),
  outputSchema: z.record(z.any()).default({}),
  timeout: z.number().min(1000).max(300000).default(30000),
  retryPolicy: z.record(z.any()).default({}),
  cacheStrategy: z.nativeEnum(ToolCacheStrategy).default(ToolCacheStrategy.INPUT_HASH),
  cacheTTL: z.number().min(60).max(86400).default(3600),
  tags: z.array(z.string()).default([]),
  documentation: z.string().optional(),
  examples: z.array(z.any()).default([]),
  isPublic: z.boolean().default(false),
  dependencies: z.array(z.string()).default([]),
  requirements: z.record(z.any()).default({}),
});

const ToolSearchSchema = z.object({
  query: z.string().optional(),
  type: z.nativeEnum(ToolType).optional(),
  category: z.nativeEnum(SkillCategory).optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  organizationId: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['name', 'createdAt', 'usageCount', 'successRate']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export interface ToolSearchOptions {
  query?: string;
  type?: ToolType;
  category?: SkillCategory;
  tags?: string[];
  isPublic?: boolean;
  organizationId?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'createdAt' | 'usageCount' | 'successRate';
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class ToolManagerService {
  constructor(private prisma: PrismaService) {}

  // ============================================================================
  // CORE TOOL MANAGEMENT
  // ============================================================================

  async createTool(data: any, creatorId: string, organizationId?: string) {
    const validatedData = ToolDefinitionSchema.parse(data);
    
    // Check for duplicate names within organization
    const existingTool = await this.prisma.toolDefinition.findFirst({
      where: {
        name: validatedData.name,
        organizationId: organizationId || null,
      },
    });

    if (existingTool) {
      throw new BadRequestException('Tool with this name already exists in the organization');
    }

    // Validate dependencies exist
    if (validatedData.dependencies.length > 0) {
      const dependencyTools = await this.prisma.toolDefinition.findMany({
        where: {
          id: { in: validatedData.dependencies },
          OR: [
            { organizationId: organizationId || null },
            { isPublic: true },
          ],
        },
      });

      if (dependencyTools.length !== validatedData.dependencies.length) {
        throw new BadRequestException('One or more dependencies not found or not accessible');
      }
    }

    const tool = await this.prisma.toolDefinition.create({
      data: {
        ...validatedData,
        createdBy: creatorId,
        organizationId: organizationId || null,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            executions: true,
            versions: true,
          },
        },
      },
    });

    // Create initial version
    await this.createToolVersion(tool.id, {
      version: '1.0.0',
      description: 'Initial version',
      config: validatedData.config,
      inputSchema: validatedData.inputSchema,
      outputSchema: validatedData.outputSchema,
      requirements: validatedData.requirements,
      isStable: true,
    }, creatorId);

    return tool;
  }

  async getTool(id: string, organizationId?: string) {
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id,
        OR: [
          { organizationId: organizationId || null },
          { isPublic: true },
        ],
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        versions: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          include: {
            creator: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        analytics: {
          where: {
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
          orderBy: { date: 'desc' },
        },
        _count: {
          select: {
            executions: true,
            versions: true,
            compositions: true,
          },
        },
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return tool;
  }

  async updateTool(id: string, data: any, userId: string, organizationId: string) {
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    // Check permissions - only creator or org admin can update
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (tool.createdBy !== userId && user?.role !== 'ORG_ADMIN' && user?.role !== 'SUPER_ADMIN') {
      throw new ForbiddenException('Insufficient permissions to update this tool');
    }

    const validatedData = ToolDefinitionSchema.partial().parse(data);

    // If name is being changed, check for duplicates
    if (validatedData.name && validatedData.name !== tool.name) {
      const existingTool = await this.prisma.toolDefinition.findFirst({
        where: {
          name: validatedData.name,
          organizationId,
          id: { not: id },
        },
      });

      if (existingTool) {
        throw new BadRequestException('Tool with this name already exists in the organization');
      }
    }

    const updatedTool = await this.prisma.toolDefinition.update({
      where: { id },
      data: validatedData,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            executions: true,
            versions: true,
          },
        },
      },
    });

    // Create new version if significant changes
    const significantFields = ['config', 'inputSchema', 'outputSchema', 'requirements'];
    const hasSignificantChanges = significantFields.some(field => 
      validatedData[field] && JSON.stringify(validatedData[field]) !== JSON.stringify(tool[field])
    );

    if (hasSignificantChanges) {
      const latestVersion = await this.prisma.toolVersion.findFirst({
        where: { toolId: id },
        orderBy: { createdAt: 'desc' },
      });

      const newVersionNumber = this.incrementVersion(latestVersion?.version || '1.0.0');
      
      await this.createToolVersion(id, {
        version: newVersionNumber,
        description: 'Updated configuration',
        config: validatedData.config || tool.config,
        inputSchema: validatedData.inputSchema || tool.inputSchema,
        outputSchema: validatedData.outputSchema || tool.outputSchema,
        requirements: validatedData.requirements || tool.requirements,
      }, userId);
    }

    return updatedTool;
  }

  async deleteTool(id: string, userId: string, organizationId: string) {
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    // Check permissions
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (tool.createdBy !== userId && user?.role !== 'ORG_ADMIN' && user?.role !== 'SUPER_ADMIN') {
      throw new ForbiddenException('Insufficient permissions to delete this tool');
    }

    // Check if tool is being used in compositions
    const compositionUsage = await this.prisma.toolCompositionStep.count({
      where: { toolId: id },
    });

    if (compositionUsage > 0) {
      throw new BadRequestException('Cannot delete tool that is used in compositions');
    }

    // Soft delete by deactivating
    await this.prisma.toolDefinition.update({
      where: { id },
      data: { isActive: false },
    });

    return { message: 'Tool deactivated successfully' };
  }

  // ============================================================================
  // TOOL SEARCH AND DISCOVERY
  // ============================================================================

  async searchTools(options: ToolSearchOptions) {
    const validatedOptions = ToolSearchSchema.parse(options);
    const { query, type, category, tags, isPublic, organizationId, page, limit, sortBy, sortOrder } = validatedOptions;

    const where: any = {
      isActive: true,
    };

    // Organization and public access logic
    if (organizationId) {
      where.OR = [
        { organizationId },
        { isPublic: true },
      ];
    } else if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    // Search filters
    if (query) {
      where.OR = [
        ...(where.OR || []),
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { tags: { hasSome: [query] } },
      ];
    }

    if (type) where.type = type;
    if (category) where.category = category;
    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    // Sorting logic
    const orderBy: any = {};
    switch (sortBy) {
      case 'name':
        orderBy.name = sortOrder;
        break;
      case 'usageCount':
        orderBy.usageCount = sortOrder;
        break;
      case 'successRate':
        orderBy.successRate = sortOrder;
        break;
      default:
        orderBy.createdAt = sortOrder;
    }

    const [tools, total] = await Promise.all([
      this.prisma.toolDefinition.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              executions: true,
              versions: true,
            },
          },
        },
      }),
      this.prisma.toolDefinition.count({ where }),
    ]);

    return {
      tools,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async getPopularTools(organizationId?: string, limit = 10) {
    const where: any = {
      isActive: true,
    };

    if (organizationId) {
      where.OR = [
        { organizationId },
        { isPublic: true },
      ];
    } else {
      where.isPublic = true;
    }

    return this.prisma.toolDefinition.findMany({
      where,
      orderBy: [
        { usageCount: 'desc' },
        { successRate: 'desc' },
      ],
      take: limit,
      include: {
        creator: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
    });
  }

  async getToolStats(organizationId?: string) {
    const where: any = {
      isActive: true,
    };

    if (organizationId) {
      where.organizationId = organizationId;
    }

    const [
      totalTools,
      toolsByType,
      toolsByCategory,
      recentExecutions,
    ] = await Promise.all([
      this.prisma.toolDefinition.count({ where }),
      this.prisma.toolDefinition.groupBy({
        by: ['type'],
        where,
        _count: { type: true },
      }),
      this.prisma.toolDefinition.groupBy({
        by: ['category'],
        where,
        _count: { category: true },
      }),
      this.prisma.toolExecution.count({
        where: {
          organizationId,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    return {
      totalTools,
      toolsByType: toolsByType.map(t => ({ type: t.type, count: t._count.type })),
      toolsByCategory: toolsByCategory.map(c => ({ category: c.category, count: c._count.category })),
      recentExecutions,
    };
  }

  // ============================================================================
  // TOOL VERSIONING
  // ============================================================================

  async createToolVersion(toolId: string, versionData: any, creatorId: string) {
    const tool = await this.prisma.toolDefinition.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return this.prisma.toolVersion.create({
      data: {
        toolId,
        version: versionData.version,
        description: versionData.description,
        changes: versionData.changes || [],
        config: versionData.config,
        inputSchema: versionData.inputSchema,
        outputSchema: versionData.outputSchema,
        requirements: versionData.requirements,
        isStable: versionData.isStable || false,
        releaseNotes: versionData.releaseNotes,
        migrationPath: versionData.migrationPath,
        breakingChanges: versionData.breakingChanges || [],
        createdBy: creatorId,
      },
      include: {
        creator: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async getToolVersions(toolId: string, organizationId?: string) {
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id: toolId,
        OR: [
          { organizationId },
          { isPublic: true },
        ],
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return this.prisma.toolVersion.findMany({
      where: { toolId },
      orderBy: { createdAt: 'desc' },
      include: {
        creator: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  // ============================================================================
  // TOOL IMPORT/EXPORT
  // ============================================================================

  async importTool(importOptions: any, creatorId: string, organizationId?: string) {
    const { source, data, format = 'json' } = importOptions;

    let toolData;
    
    if (format === 'json') {
      toolData = typeof data === 'string' ? JSON.parse(data) : data;
    } else {
      throw new BadRequestException('Unsupported import format');
    }

    // Validate imported data
    const validatedData = ToolDefinitionSchema.parse(toolData);

    // Check for name conflicts
    const existingTool = await this.prisma.toolDefinition.findFirst({
      where: {
        name: validatedData.name,
        organizationId: organizationId || null,
      },
    });

    if (existingTool) {
      validatedData.name = `${validatedData.name} (Imported)`;
    }

    return this.createTool(validatedData, creatorId, organizationId);
  }

  async exportTool(toolId: string, format: string, organizationId?: string) {
    const tool = await this.getTool(toolId, organizationId);
    
    if (format === 'json') {
      const exportData = {
        name: tool.name,
        description: tool.description,
        category: tool.category,
        type: tool.type,
        config: tool.config,
        inputSchema: tool.inputSchema,
        outputSchema: tool.outputSchema,
        timeout: tool.timeout,
        retryPolicy: tool.retryPolicy,
        cacheStrategy: tool.cacheStrategy,
        cacheTTL: tool.cacheTTL,
        tags: tool.tags,
        documentation: tool.documentation,
        examples: tool.examples,
        dependencies: tool.dependencies,
        requirements: tool.requirements,
        version: tool.version,
        exportedAt: new Date().toISOString(),
        exportedBy: 'SynapseAI Tool System',
      };

      return {
        data: exportData,
        filename: `${tool.name.replace(/[^a-zA-Z0-9]/g, '_')}_v${tool.version}.json`,
        contentType: 'application/json',
      };
    }

    throw new BadRequestException('Unsupported export format');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private incrementVersion(version: string): string {
    const parts = version.split('.').map(Number);
    parts[2] = (parts[2] || 0) + 1;
    return parts.join('.');
  }

  async validateToolAccess(toolId: string, userId: string, organizationId?: string): Promise<boolean> {
    const tool = await this.prisma.toolDefinition.findFirst({
      where: {
        id: toolId,
        OR: [
          { organizationId },
          { isPublic: true },
        ],
      },
    });

    return !!tool;
  }

  async updateToolMetrics(toolId: string, metrics: {
    usageCount?: number;
    successRate?: number;
    avgLatency?: number;
  }) {
    await this.prisma.toolDefinition.update({
      where: { id: toolId },
      data: metrics,
    });
  }
}