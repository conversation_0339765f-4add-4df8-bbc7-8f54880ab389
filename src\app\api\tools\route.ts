import { NextRequest, NextResponse } from 'next/server';
import apiClient from '@/lib/api-client';
import { ValidatedRequest, AuthenticatedRequest, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { z } from 'zod';
import { Permissions, Role } from '@/lib/types/auth';

const getToolsSchema = z.object({

  query: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean().optional(),
});


  export const getTools = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const tools = await apiClient.get('/tools', {
          params: {
          ...(req.validatedQuery.query && { query: req.validatedQuery.query }),
          ...(req.validatedQuery.type && { query: { type: req.validatedQuery.type } }),
          ...(req.validatedQuery.category && { query: { category: req.validatedQuery.category } }),
          ...(req.validatedQuery.tags && { query: { tags: req.validatedQuery.tags } }),
          ...(req.validatedQuery.isPublic && { query: { isPublic: req.validatedQuery.isPublic } }),
          ...(req.validatedQuery.page && { query: { page: req.validatedQuery.page } }),
          ...(req.validatedQuery.limit && { query: { limit: req.validatedQuery.limit } }),
          ...(req.validatedQuery.sortBy && { query: { sortBy: req.validatedQuery.sortBy } }),
          ...(req.validatedQuery.sortOrder && { query: { sortOrder: req.validatedQuery.sortOrder } }),
        }
    });
    return NextResponse.json(tools);
  },
  {
    requiredRoles: [Role.SUPER_ADMIN],
    requiredPermissions: [Permissions.TOOL_READ],
  },
  {
    query: getToolsSchema,
  }
);

const createToolSchema = z.object({
  name: z.string(),
  description: z.string(),
  type: z.string(),
  category: z.string(),
  tags: z.string(),
  isPublic: z.boolean(),
});

export const createTool = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const tool = await apiClient.post('/tools', req.validatedBody);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: [Role.SUPER_ADMIN],
    requiredPermissions: [Permissions.TOOL_CREATE],
  },
  {
    body: createToolSchema,
  }
);

const updateToolSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean().optional(),
});

export const updateTool = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const tool = await apiClient.put('/tools', req.validatedBody);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: [Role.SUPER_ADMIN],
    requiredPermissions: [Permissions.TOOL_UPDATE],
  },
  {
    body: updateToolSchema,
  }
);

export const deleteTool = withAuthAndValidation(
  async (request: NextRequest) => {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');  
    const tool = await apiClient.delete(`/tools/${id}`);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: [Role.SUPER_ADMIN],
    requiredPermissions: [Permissions.TOOL_DELETE],
  }
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    const type = searchParams.get('type') || '';
    const category = searchParams.get('category') || '';
    const tags = searchParams.get('tags') || '';
    const isPublic = searchParams.get('isPublic') || '';
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const params = new URLSearchParams({
      ...(query && { query }),
      ...(type && { type }),
      ...(category && { category }),
      ...(tags && { tags }),
      ...(isPublic && { isPublic }),
      page,
      limit,
      sortBy,
      sortOrder,
    });

    const response = await fetch(`${API_BASE_URL}/api/tools?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
      },
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Tools API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const tool = await apiClient.post('/tools', body);
    return NextResponse.json(tool);
  } catch (error) {
    console.error('Create Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const tool = await apiClient.put('/tools', body);
    return NextResponse.json(tool);
  } catch (error) {
    console.error('Update Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');  
    const tool = await apiClient.delete(`/tools/${id}`);
    return NextResponse.json(tool);
  } catch (error) {
    console.error('Delete Tool API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}