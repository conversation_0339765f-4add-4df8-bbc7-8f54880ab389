import { NextRequest, NextResponse } from 'next/server';
import apiClient from '@/lib/api-client';
import { ValidatedRequest, AuthenticatedRequest, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { z } from 'zod';
import { Permissions, Role } from '@/lib/types/auth';

const getToolsSchema = z.object({
  query: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.string().optional().transform(val => val === 'true'),
  page: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  limit: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});


export const getTools = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (req.validatedQuery.query) queryParams.append('query', req.validatedQuery.query);
    if (req.validatedQuery.type) queryParams.append('type', req.validatedQuery.type);
    if (req.validatedQuery.category) queryParams.append('category', req.validatedQuery.category);
    if (req.validatedQuery.tags) queryParams.append('tags', req.validatedQuery.tags);
    if (req.validatedQuery.isPublic !== undefined) queryParams.append('isPublic', req.validatedQuery.isPublic.toString());
    if (req.validatedQuery.page) queryParams.append('page', req.validatedQuery.page.toString());
    if (req.validatedQuery.limit) queryParams.append('limit', req.validatedQuery.limit.toString());
    if (req.validatedQuery.sortBy) queryParams.append('sortBy', req.validatedQuery.sortBy);
    if (req.validatedQuery.sortOrder) queryParams.append('sortOrder', req.validatedQuery.sortOrder);

    const tools = await apiClient.get(`/tools?${queryParams.toString()}`);
    return NextResponse.json(tools);
  },
  {
    requiredRoles: ['SUPER_ADMIN'],
    requiredPermissions: [Permissions.TOOL_READ],
  },
  {
    query: getToolsSchema,
  }
);

const createToolSchema = z.object({
  name: z.string(),
  description: z.string(),
  type: z.string(),
  category: z.string(),
  tags: z.string(),
  isPublic: z.boolean(),
});

export const createTool = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const tool = await apiClient.post('/tools', req.validatedBody);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: ['SUPER_ADMIN'],
    requiredPermissions: [Permissions.TOOL_CREATE],
  },
  {
    body: createToolSchema,
  }
);

const updateToolSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.boolean().optional(),
});

export const updateTool = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const tool = await apiClient.put('/tools', req.validatedBody);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: ['SUPER_ADMIN'],
    requiredPermissions: [Permissions.TOOL_UPDATE],
  },
  {
    body: updateToolSchema,
  }
);

export const deleteTool = withAuthAndValidation(
  async (request: NextRequest) => {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const tool = await apiClient.delete(`/tools/${id}`);
    return NextResponse.json(tool);
  },
  {
    requiredRoles: ['SUPER_ADMIN'],
    requiredPermissions: [Permissions.TOOL_DELETE],
  }
);

// Export the wrapped functions as the HTTP method handlers
export const GET = getTools;
export const POST = createTool;
export const PUT = updateTool;
export const DELETE = deleteTool;