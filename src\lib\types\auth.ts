/**
 * Comprehensive Authentication Types
 * Production-grade type definitions for multi-tenant RBAC auth system
 */

// Role types matching Prisma schema
export type Role = 'SUPER_ADMIN' | 'ORG_ADMIN' | 'DEVELOPER' | 'VIEWER' | 'USER' | 'ADMIN';

// User status types
export type UserStatus = 'active' | 'suspended' | 'inactive' | 'pending';

// Organization type
export interface Organization {
    id: string;
    name: string;
    slug: string;
    domain?: string;
    settings?: Record<string, any>;
    branding?: Record<string, any>;
    isActive: boolean;
    plan?: string;
    features?: string[];
    createdAt: string;
    updatedAt: string;
}

// Complete User type
export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    role: Role;
    isActive: boolean;
    emailVerified: boolean;
    lastLoginAt?: string;
    preferences?: Record<string, any>;
    mfaEnabled: boolean;
    organizationId: string;
    organization: Organization;
    permissions: string[];
    createdAt: string;
    updatedAt: string;
    profile?: {
        phoneNumber?: string;
        jobTitle?: string;
        department?: string;
        timezone?: string;
    };
}

// Session User (subset of User with essential data)
export interface SessionUser {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: Role;
    organizationId: string;
    organization: {
        id: string;
        name: string;
        slug: string;
    };
    permissions: string[];
    emailVerified: boolean;
    isActive: boolean;
}

// Session with user data and token information
export interface Session {
    user: SessionUser;
    expires: string;
    accessToken: string;
}

// Auth tokens
export interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
}

// Authentication response
export interface AuthResponse {
    error: any;
    error: any;
    message: any;
    user: User;
    tokens: AuthTokens;
    mfaRequired: boolean;
    availableOrganizations?: Organization[];
}

// Login credentials
export interface LoginCredentials {
    email: string;
    password: string;
    organizationSlug?: string;
    mfaCode?: string;
    rememberMe?: boolean;
}

// Registration data
export interface RegisterData {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    company: string;
    subscribeNewsletter?: boolean;
}

// Auth error
export interface AuthError {
    code: string;
    message: string;
    details?: any;
}

// Authentication context for React components
export interface AuthContext {
    user: SessionUser | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    login: (credentials: LoginCredentials) => Promise<AuthResponse>;
    logout: () => Promise<void>;
    register: (data: RegisterData) => Promise<AuthResponse>;
    refreshAuth: () => Promise<void>;
    hasRole: (roles: Role[]) => boolean;
    hasPermission: (permission: string) => boolean;
    hasPermissions: (permissions: string[]) => boolean;
}

// Permission definitions
export const Permissions = {
    // User management
    USER_CREATE: 'user:create',
    USER_READ: 'user:read',
    USER_UPDATE: 'user:update',
    USER_DELETE: 'user:delete',

    // Organization management
    ORG_CREATE: 'org:create',
    ORG_READ: 'org:read',
    ORG_UPDATE: 'org:update',
    ORG_DELETE: 'org:delete',

    // Tool management
    TOOL_CREATE: 'tool:create',
    TOOL_READ: 'tool:read',
    TOOL_UPDATE: 'tool:update',
    TOOL_DELETE: 'tool:delete',
    TOOL_EXECUTE: 'tool:execute',

    // Agent management
    AGENT_CREATE: 'agent:create',
    AGENT_READ: 'agent:read',
    AGENT_UPDATE: 'agent:update',
    AGENT_DELETE: 'agent:delete',
    AGENT_EXECUTE: 'agent:execute',

    // Workflow management
    WORKFLOW_CREATE: 'workflow:create',
    WORKFLOW_READ: 'workflow:read',
    WORKFLOW_UPDATE: 'workflow:update',
    WORKFLOW_DELETE: 'workflow:delete',
    WORKFLOW_EXECUTE: 'workflow:execute',

    // Billing management
    BILLING_READ: 'billing:read',
    BILLING_UPDATE: 'billing:update',

    // Analytics
    ANALYTICS_VIEW: 'analytics:view',

    // Settings
    SETTINGS_READ: 'settings:read',
    SETTINGS_UPDATE: 'settings:update',

    // API keys
    API_KEY_CREATE: 'api-key:create',
    API_KEY_READ: 'api-key:read',
    API_KEY_DELETE: 'api-key:delete'
};

// Default permissions by role
export const DefaultPermissions: Record<Role, string[]> = {
    'SUPER_ADMIN': Object.values(Permissions),
    'ORG_ADMIN': [
        Permissions.USER_CREATE, Permissions.USER_READ, Permissions.USER_UPDATE, Permissions.USER_DELETE,
        Permissions.ORG_READ, Permissions.ORG_UPDATE,
        Permissions.TOOL_CREATE, Permissions.TOOL_READ, Permissions.TOOL_UPDATE, Permissions.TOOL_DELETE, Permissions.TOOL_EXECUTE,
        Permissions.AGENT_CREATE, Permissions.AGENT_READ, Permissions.AGENT_UPDATE, Permissions.AGENT_DELETE, Permissions.AGENT_EXECUTE,
        Permissions.WORKFLOW_CREATE, Permissions.WORKFLOW_READ, Permissions.WORKFLOW_UPDATE, Permissions.WORKFLOW_DELETE, Permissions.WORKFLOW_EXECUTE,
        Permissions.BILLING_READ, Permissions.BILLING_UPDATE,
        Permissions.ANALYTICS_VIEW,
        Permissions.SETTINGS_READ, Permissions.SETTINGS_UPDATE,
        Permissions.API_KEY_CREATE, Permissions.API_KEY_READ, Permissions.API_KEY_DELETE
    ],
    'DEVELOPER': [
        Permissions.USER_READ,
        Permissions.ORG_READ,
        Permissions.TOOL_CREATE, Permissions.TOOL_READ, Permissions.TOOL_UPDATE, Permissions.TOOL_EXECUTE,
        Permissions.AGENT_CREATE, Permissions.AGENT_READ, Permissions.AGENT_UPDATE, Permissions.AGENT_EXECUTE,
        Permissions.WORKFLOW_CREATE, Permissions.WORKFLOW_READ, Permissions.WORKFLOW_UPDATE, Permissions.WORKFLOW_EXECUTE,
        Permissions.ANALYTICS_VIEW,
        Permissions.SETTINGS_READ,
        Permissions.API_KEY_CREATE, Permissions.API_KEY_READ
    ],
    'VIEWER': [
        Permissions.USER_READ,
        Permissions.ORG_READ,
        Permissions.TOOL_READ, Permissions.TOOL_EXECUTE,
        Permissions.AGENT_READ, Permissions.AGENT_EXECUTE,
        Permissions.WORKFLOW_READ, Permissions.WORKFLOW_EXECUTE,
        Permissions.ANALYTICS_VIEW,
        Permissions.SETTINGS_READ
    ],
    'USER': [
        Permissions.TOOL_READ, Permissions.TOOL_EXECUTE,
        Permissions.AGENT_READ, Permissions.AGENT_EXECUTE,
        Permissions.WORKFLOW_READ, Permissions.WORKFLOW_EXECUTE
    ],
    'ADMIN': [
        Permissions.USER_READ,
        Permissions.ORG_READ,
        Permissions.TOOL_READ, Permissions.TOOL_EXECUTE,
        Permissions.AGENT_READ, Permissions.AGENT_EXECUTE,
        Permissions.WORKFLOW_READ, Permissions.WORKFLOW_EXECUTE,
        Permissions.ANALYTICS_VIEW,
        Permissions.SETTINGS_READ
    ]
};