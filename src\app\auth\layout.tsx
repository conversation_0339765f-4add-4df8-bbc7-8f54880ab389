import { Metadata } from "next";
import { AuthProvider } from "@/components/providers/auth-provider";
import Link from "next/link";
import Image from "next/image";

export const metadata: Metadata = {
    title: "Authentication",
    description: "Authentication pages for the platform",
};

export default function AuthLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <AuthProvider>
            <div className="flex min-h-screen flex-col">
                <header className="fixed top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="container flex h-14 max-w-screen-2xl items-center">
                        <Link href="/" className="flex items-center space-x-2">
                            <Image
                                src="/logo.svg"
                                alt="Logo"
                                width={32}
                                height={32}
                                className="h-8 w-8"
                                priority
                            />
                            <span className="font-bold">AI Agent Platform</span>
                        </Link>
                    </div>
                </header>
                <main className="flex-1 pt-14">{children}</main>
                <footer className="border-t border-border/40 bg-background py-6">
                    <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
                        <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
                            &copy; {new Date().getFullYear()} AI Agent Platform. All rights reserved.
                        </p>
                        <div className="flex items-center gap-4">
                            <Link
                                href="/terms"
                                className="text-sm text-muted-foreground hover:underline"
                            >
                                Terms
                            </Link>
                            <Link
                                href="/privacy"
                                className="text-sm text-muted-foreground hover:underline"
                            >
                                Privacy
                            </Link>
                            <Link
                                href="/contact"
                                className="text-sm text-muted-foreground hover:underline"
                            >
                                Contact
                            </Link>
                        </div>
                    </div>
                </footer>
            </div>
        </AuthProvider>
    );
}