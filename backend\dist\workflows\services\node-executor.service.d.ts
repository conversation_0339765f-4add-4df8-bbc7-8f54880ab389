import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { AgentsService } from '../../agents/agents.service';
import { ToolsService } from '../../tools/tools.service';
export interface NodeExecutionContext {
    executionId: string;
    nodeId: string;
    organizationId: string;
    userId: string;
}
export declare class NodeExecutorService {
    private prisma;
    private apixGateway;
    private agentsService;
    private toolsService;
    private readonly logger;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, agentsService: AgentsService, toolsService: ToolsService);
    executeNode(nodeType: string, config: any, variables: Record<string, any>, context: NodeExecutionContext): Promise<any>;
    private executeAgentNode;
    private executeToolNode;
    private executeConditionNode;
    private evaluateSimpleCondition;
    private getNestedValue;
    private executeParallelNode;
    private waitForSome;
    private aggregateParallelResults;
    private executeHumanInputNode;
    private executeDelayNode;
    private executeHybridNode;
    private executeAgentFirstPattern;
    private executeToolFirstPattern;
    private executeParallelPattern;
    private executeOrchestrationPattern;
    private parseAgentDecision;
}
