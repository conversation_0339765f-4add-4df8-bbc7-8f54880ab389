import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { LoggingService } from '../logging/logging.service';
import { EncryptionService } from './encryption.service';
import { fromPrismaJson, toPrismaJson, OrganizationSettings, DEFAULT_SECURITY_POLICY } from '../types/prisma-json.types';
import * as crypto from 'crypto';
import * as bcrypt from 'bcryptjs';

export interface SecurityPolicy {
    passwordPolicy: {
        minLength: number;
        requireUppercase: boolean;
        requireLowercase: boolean;
        requireNumbers: boolean;
        requireSymbols: boolean;
        preventReuse: number;
        maxAge: number;
    };
    sessionPolicy: {
        maxConcurrentSessions: number;
        sessionTimeout: number;
        idleTimeout: number;
        requireMFA: boolean;
    };
    accessPolicy: {
        maxLoginAttempts: number;
        lockoutDuration: number;
        passwordResetExpiry: number;
        emailVerificationExpiry: number;
    };
    complianceSettings: {
        enableAuditLog: boolean;
        dataRetentionDays: number;
        enableEncryption: boolean;
        requireTLS: boolean;
    };
}

export interface SecurityThreat {
    id: string;
    type: 'brute_force' | 'suspicious_activity' | 'data_breach' | 'malware' | 'phishing';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    source: string;
    targetResource: string;
    detectedAt: Date;
    status: 'active' | 'investigating' | 'resolved' | 'false_positive';
    metadata: Record<string, any>;
}

export interface SecurityScanResult {
    id: string;
    scanType: 'vulnerability' | 'compliance' | 'penetration' | 'code_analysis';
    status: 'running' | 'completed' | 'failed';
    startedAt: Date;
    completedAt?: Date;
    findings: SecurityFinding[];
    summary: {
        critical: number;
        high: number;
        medium: number;
        low: number;
        info: number;
    };
}

export interface SecurityFinding {
    id: string;
    title: string;
    description: string;
    severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
    category: string;
    resource: string;
    remediation: string;
    cvssScore?: number;
    cve?: string;
    references: string[];
}

@Injectable()
export class SecurityService {
    // FIXED: Using centralized default policy
    private readonly defaultPolicy: SecurityPolicy = DEFAULT_SECURITY_POLICY;

    constructor(
        private configService: ConfigService,
        private prisma: PrismaService,
        private logger: LoggingService,
        private encryption: EncryptionService,
    ) { }

    async getSecurityPolicy(organizationId: string): Promise<SecurityPolicy> {
        try {
            const organization = await this.prisma.organization.findUnique({
                where: { id: organizationId },
                select: { settings: true },
            });

            if (organization?.settings) {
                const settings = fromPrismaJson<OrganizationSettings>(organization.settings);
                // FIXED: Proper type checking for nested objects
                if (settings && typeof settings === 'object' && 'securityPolicy' in settings && settings.securityPolicy) {
                    return {
                        ...this.defaultPolicy,
                        ...settings.securityPolicy,
                    };
                }
            }

            return this.defaultPolicy;
        } catch (error) {
            this.logger.error('Failed to get security policy', error.message, 'SecurityService');
            return this.defaultPolicy;
        }
    }

    async updateSecurityPolicy(organizationId: string, policy: Partial<SecurityPolicy>): Promise<SecurityPolicy> {
        const currentOrg = await this.prisma.organization.findUnique({
            where: { id: organizationId },
            select: { settings: true },
        });

        const currentSettings = fromPrismaJson<OrganizationSettings>(currentOrg?.settings) || {};

        const updatedSettings: OrganizationSettings = {
            ...currentSettings,
            securityPolicy: {
                ...this.defaultPolicy,
                ...(currentSettings.securityPolicy || {}),
                ...policy,
            },
        };

        await this.prisma.organization.update({
            where: { id: organizationId },
            data: { settings: toPrismaJson(updatedSettings) },
        });

        await this.logger.logSecurityEvent(
            'SECURITY_POLICY_UPDATED',
            'medium',
            { policy, organizationId },
        );

        return updatedSettings.securityPolicy;
    }

    async validatePassword(password: string, policy?: SecurityPolicy['passwordPolicy']): Promise<{
        isValid: boolean;
        errors: string[];
    }> {
        const rules = policy || this.defaultPolicy.passwordPolicy;
        const errors: string[] = [];

        if (password.length < rules.minLength) {
            errors.push(`Password must be at least ${rules.minLength} characters long`);
        }

        if (rules.requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }

        if (rules.requireLowercase && !/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }

        if (rules.requireNumbers && !/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }

        if (rules.requireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    async checkPasswordHistory(userId: string, newPassword: string, preventReuse: number): Promise<boolean> {
        if (preventReuse === 0) return true;

        try {
            // Get recent password hashes (this would require storing password history)
            // For now, we'll just check against current password
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { password: true },
            });

            if (user && await bcrypt.compare(newPassword, user.password)) {
                return false; // Password reused
            }

            return true;
        } catch (error) {
            this.logger.error('Failed to check password history', error.message, 'SecurityService');
            return true; // Allow password change on error
        }
    }

    async detectSuspiciousActivity(userId: string, activity: {
        action: string;
        ipAddress?: string;
        userAgent?: string;
        location?: Record<string, any>;
        metadata?: Record<string, any>;
    }): Promise<SecurityThreat | null> {
        try {
            // Get recent activity for this user
            const recentActivity = await this.prisma.auditLog.findMany({
                where: {
                    userId,
                    createdAt: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: 100,
            });

            // Check for suspicious patterns
            const threats: SecurityThreat[] = [];

            // 1. Multiple failed login attempts
            const failedLogins = recentActivity.filter(log =>
                log.action.includes('LOGIN_FAILED') &&
                log.ipAddress === activity.ipAddress
            ).length;

            if (failedLogins >= 5) {
                threats.push({
                    id: crypto.randomUUID(),
                    type: 'brute_force',
                    severity: 'high',
                    description: `Multiple failed login attempts from IP ${activity.ipAddress}`,
                    source: activity.ipAddress || 'unknown',
                    targetResource: `user:${userId}`,
                    detectedAt: new Date(),
                    status: 'active',
                    metadata: { failedAttempts: failedLogins, ...activity.metadata },
                });
            }

            // 2. Login from unusual location
            const recentLogins = recentActivity.filter(log =>
                log.action.includes('LOGIN_SUCCESS')
            );

            if (recentLogins.length > 0 && activity.location) {
                const unusualLocation = this.isUnusualLocation(recentLogins, activity.location);
                if (unusualLocation) {
                    threats.push({
                        id: crypto.randomUUID(),
                        type: 'suspicious_activity',
                        severity: 'medium',
                        description: 'Login from unusual location detected',
                        source: activity.ipAddress || 'unknown',
                        targetResource: `user:${userId}`,
                        detectedAt: new Date(),
                        status: 'active',
                        metadata: { location: activity.location, ...activity.metadata },
                    });
                }
            }

            // 3. Rapid successive actions
            const recentActions = recentActivity.filter(log =>
                log.createdAt > new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
            );

            if (recentActions.length > 50) {
                threats.push({
                    id: crypto.randomUUID(),
                    type: 'suspicious_activity',
                    severity: 'medium',
                    description: 'Unusually high activity rate detected',
                    source: activity.ipAddress || 'unknown',
                    targetResource: `user:${userId}`,
                    detectedAt: new Date(),
                    status: 'active',
                    metadata: { actionCount: recentActions.length, ...activity.metadata },
                });
            }

            // Return the highest severity threat
            if (threats.length > 0) {
                const threat = threats.sort((a, b) => {
                    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
                    return severityOrder[b.severity] - severityOrder[a.severity];
                })[0];

                // Log the threat
                await this.logger.logSecurityEvent(
                    threat.type.toUpperCase(),
                    threat.severity,
                    threat.metadata,
                    userId,
                );

                return threat;
            }

            return null;
        } catch (error) {
            this.logger.error('Failed to detect suspicious activity', error.message, 'SecurityService');
            return null;
        }
    }

    private isUnusualLocation(recentLogins: any[], currentLocation: any): boolean {
        // Simple location comparison - in production, you'd use proper geolocation
        if (!currentLocation.country) return false;

        const recentCountries = recentLogins
            .map(log => {
                const details = fromPrismaJson(log.details);
                return details?.location?.country;
            })
            .filter(Boolean);

        if (recentCountries.length === 0) return false;

        return !recentCountries.includes(currentLocation.country);
    }

    async scanForVulnerabilities(organizationId: string): Promise<SecurityScanResult> {
        const scanId = crypto.randomUUID();
        const startTime = new Date();

        try {
            const findings: SecurityFinding[] = [];

            // 1. Check for weak passwords
            const weakPasswords = await this.checkWeakPasswords(organizationId);
            findings.push(...weakPasswords);

            // 2. Check for inactive users with admin privileges - FIXED: Use correct role enum
            const staleAdmins = await this.checkStaleAdminAccounts(organizationId);
            findings.push(...staleAdmins);

            // 3. Check for unencrypted sensitive data
            const unencryptedData = await this.checkUnencryptedData(organizationId);
            findings.push(...unencryptedData);

            // 4. Check for outdated security settings
            const outdatedSettings = await this.checkOutdatedSecuritySettings(organizationId);
            findings.push(...outdatedSettings);

            // 5. Check for exposed API keys
            const exposedKeys = await this.checkExposedAPIKeys(organizationId);
            findings.push(...exposedKeys);

            const summary = {
                critical: findings.filter(f => f.severity === 'critical').length,
                high: findings.filter(f => f.severity === 'high').length,
                medium: findings.filter(f => f.severity === 'medium').length,
                low: findings.filter(f => f.severity === 'low').length,
                info: findings.filter(f => f.severity === 'info').length,
            };

            const result: SecurityScanResult = {
                id: scanId,
                scanType: 'vulnerability',
                status: 'completed',
                startedAt: startTime,
                completedAt: new Date(),
                findings,
                summary,
            };

            await this.logger.logSystemEvent(
                'SECURITY_SCAN_COMPLETED',
                { scanId, summary, organizationId }
            );

            return result;
        } catch (error) {
            this.logger.error('Security scan failed', error.message, 'SecurityService');
            return {
                id: scanId,
                scanType: 'vulnerability',
                status: 'failed',
                startedAt: startTime,
                completedAt: new Date(),
                findings: [],
                summary: { critical: 0, high: 0, medium: 0, low: 0, info: 0 },
            };
        }
    }

    private async checkWeakPasswords(organizationId: string): Promise<SecurityFinding[]> {
        const findings: SecurityFinding[] = [];

        try {
            // Get users who haven't changed passwords in a long time
            const stalePasswords = await this.prisma.user.count({
                where: {
                    organizationId,
                    updatedAt: {
                        lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days
                    },
                },
            });

            if (stalePasswords > 0) {
                findings.push({
                    id: crypto.randomUUID(),
                    title: 'Stale Passwords Detected',
                    description: `${stalePasswords} users have not changed their passwords in over 90 days`,
                    severity: 'medium',
                    category: 'Password Security',
                    resource: `organization:${organizationId}`,
                    remediation: 'Implement password rotation policy and notify users to update passwords',
                    references: ['https://www.nist.gov/password-guidelines'],
                });
            }
        } catch (error) {
            this.logger.error('Failed to check weak passwords', error.message, 'SecurityService');
        }

        return findings;
    }

    private async checkStaleAdminAccounts(organizationId: string): Promise<SecurityFinding[]> {
        const findings: SecurityFinding[] = [];

        try {
            // FIXED: Use correct Prisma Role enum values
            const staleAdmins = await this.prisma.user.findMany({
                where: {
                    organizationId,
                    role: { in: ['ORG_ADMIN', 'SUPER_ADMIN'] }, // FIXED: Use correct enum values
                    lastLoginAt: {
                        lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days
                    },
                },
            });

            if (staleAdmins.length > 0) {
                findings.push({
                    id: crypto.randomUUID(),
                    title: 'Inactive Admin Accounts',
                    description: `${staleAdmins.length} admin accounts have not been used in over 30 days`,
                    severity: 'high',
                    category: 'Access Control',
                    resource: `organization:${organizationId}`,
                    remediation: 'Review and disable unused admin accounts or implement regular access reviews',
                    references: ['https://www.cisa.gov/publication/privileged-account-security'],
                });
            }
        } catch (error) {
            this.logger.error('Failed to check stale admin accounts', error.message, 'SecurityService');
        }

        return findings;
    }

    private async checkUnencryptedData(organizationId: string): Promise<SecurityFinding[]> {
        const findings: SecurityFinding[] = [];

        try {
            // Check if encryption is enabled in organization settings
            const org = await this.prisma.organization.findUnique({
                where: { id: organizationId },
                select: { settings: true },
            });

            if (org?.settings) {
                const settings = fromPrismaJson<OrganizationSettings>(org.settings);
                // FIXED: Proper type checking for nested properties
                const hasEncryption = settings &&
                    typeof settings === 'object' &&
                    'securityPolicy' in settings &&
                    settings.securityPolicy &&
                    typeof settings.securityPolicy === 'object' &&
                    'complianceSettings' in settings.securityPolicy &&
                    settings.securityPolicy.complianceSettings &&
                    typeof settings.securityPolicy.complianceSettings === 'object' &&
                    'enableEncryption' in settings.securityPolicy.complianceSettings &&
                    settings.securityPolicy.complianceSettings.enableEncryption;

                if (!hasEncryption) {
                    findings.push({
                        id: crypto.randomUUID(),
                        title: 'Encryption Not Enabled',
                        description: 'Data encryption is not enabled for this organization',
                        severity: 'high',
                        category: 'Data Protection',
                        resource: `organization:${organizationId}`,
                        remediation: 'Enable data encryption in security settings',
                        references: ['https://www.nist.gov/cybersecurity-framework'],
                    });
                }
            }
        } catch (error) {
            this.logger.error('Failed to check unencrypted data', error.message, 'SecurityService');
        }

        return findings;
    }

    private async checkOutdatedSecuritySettings(organizationId: string): Promise<SecurityFinding[]> {
        const findings: SecurityFinding[] = [];

        try {
            const policy = await this.getSecurityPolicy(organizationId);

            if (!policy.sessionPolicy.requireMFA) {
                findings.push({
                    id: crypto.randomUUID(),
                    title: 'MFA Not Required',
                    description: 'Multi-factor authentication is not required for users',
                    severity: 'medium',
                    category: 'Authentication',
                    resource: `organization:${organizationId}`,
                    remediation: 'Enable mandatory MFA for all users, especially admin accounts',
                    references: ['https://www.cisa.gov/mfa'],
                });
            }

            if (policy.passwordPolicy.minLength < 12) {
                findings.push({
                    id: crypto.randomUUID(),
                    title: 'Weak Password Policy',
                    description: 'Minimum password length is less than 12 characters',
                    severity: 'medium',
                    category: 'Password Security',
                    resource: `organization:${organizationId}`,
                    remediation: 'Increase minimum password length to 12 or more characters',
                    references: ['https://www.nist.gov/password-guidelines'],
                });
            }
        } catch (error) {
            this.logger.error('Failed to check outdated security settings', error.message, 'SecurityService');
        }

        return findings;
    }

    private async checkExposedAPIKeys(organizationId: string): Promise<SecurityFinding[]> {
        const findings: SecurityFinding[] = [];

        try {
            // Check for API keys that haven't been rotated
            const oldAPIKeys = await this.prisma.aPIKey.count({
                where: {
                    user: { organizationId },
                    createdAt: {
                        lt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 180 days
                    },
                    isActive: true,
                },
            });

            if (oldAPIKeys > 0) {
                findings.push({
                    id: crypto.randomUUID(),
                    title: 'Old API Keys Detected',
                    description: `${oldAPIKeys} API keys are older than 180 days and should be rotated`,
                    severity: 'medium',
                    category: 'API Security',
                    resource: `organization:${organizationId}`,
                    remediation: 'Implement API key rotation policy and update old keys',
                    references: ['https://owasp.org/www-project-api-security/'],
                });
            }
        } catch (error) {
            this.logger.error('Failed to check exposed API keys', error.message, 'SecurityService');
        }

        return findings;
    }

    async generateSecurityReport(organizationId: string): Promise<{
        summary: Record<string, any>;
        recommendations: string[];
        compliance: Record<string, boolean>;
    }> {
        const scanResult = await this.scanForVulnerabilities(organizationId);
        const policy = await this.getSecurityPolicy(organizationId);

        const summary = {
            totalFindings: scanResult.findings.length,
            criticalFindings: scanResult.summary.critical,
            highFindings: scanResult.summary.high,
            mediumFindings: scanResult.summary.medium,
            lowFindings: scanResult.summary.low,
            securityScore: this.calculateSecurityScore(scanResult.summary),
            lastScanDate: scanResult.completedAt,
        };

        const recommendations = this.generateRecommendations(scanResult.findings);

        const compliance = {
            'Strong Password Policy': this.checkPasswordPolicyCompliance(policy.passwordPolicy),
            'MFA Enabled': policy.sessionPolicy.requireMFA,
            'Audit Logging': policy.complianceSettings.enableAuditLog,
            'Data Encryption': policy.complianceSettings.enableEncryption,
            'TLS Required': policy.complianceSettings.requireTLS,
        };

        return { summary, recommendations, compliance };
    }

    private calculateSecurityScore(summary: SecurityScanResult['summary']): number {
        const totalFindings = summary.critical + summary.high + summary.medium + summary.low;

        if (totalFindings === 0) return 100;

        const criticalWeight = summary.critical * 10;
        const highWeight = summary.high * 7;
        const mediumWeight = summary.medium * 4;
        const lowWeight = summary.low * 1;

        const totalWeight = criticalWeight + highWeight + mediumWeight + lowWeight;
        const maxPossibleWeight = totalFindings * 10;

        return Math.max(0, Math.round(100 - (totalWeight / maxPossibleWeight) * 100));
    }

    private generateRecommendations(findings: SecurityFinding[]): string[] {
        const recommendations = new Set<string>();

        findings.forEach(finding => {
            if (finding.severity === 'critical' || finding.severity === 'high') {
                recommendations.add(finding.remediation);
            }
        });

        // Add general recommendations
        recommendations.add('Enable multi-factor authentication for all users');
        recommendations.add('Implement regular security training for staff');
        recommendations.add('Conduct quarterly security assessments');
        recommendations.add('Maintain an incident response plan');

        return Array.from(recommendations);
    }

    private checkPasswordPolicyCompliance(policy: SecurityPolicy['passwordPolicy']): boolean {
        return policy.minLength >= 8 &&
            policy.requireUppercase &&
            policy.requireLowercase &&
            policy.requireNumbers;
    }
}