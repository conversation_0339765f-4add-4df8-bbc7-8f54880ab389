/**
 * Logout API Route
 * Production-grade logout endpoint with MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { clearAuthCookies, getAuthenticatedSession } from '@/lib/auth/auth-service';
import { trackAuthEvent } from '@/lib/mcp-tools';

export async function POST(req: NextRequest): Promise<NextResponse> {
    try {
        // Get current user before clearing cookies
        const user = await getAuthenticatedSession(req);

        // Create response and clear auth cookies
        const response = NextResponse.json({
            success: true,
            message: 'Logged out successfully'
        });

        clearAuthCookies(response);

        // Track logout in MCP Memory if user was authenticated
        if (user) {
            trackAuthEvent(user.user.id, 'logout', {
                ip: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
                userAgent: req.headers.get('user-agent') || 'unknown',
                timestamp: new Date().toISOString()
            });
        }

        return response;
    } catch (error: any) {
        console.error('Logout error:', error);

        // Still clear cookies even if there's an error
        const response = NextResponse.json(
            {
                success: false,
                message: error.message || 'Logout failed',
                code: error.code || 'LOGOUT_ERROR'
            },
            { status: 500 }
        );

        clearAuthCookies(response);

        return response;
    }
}