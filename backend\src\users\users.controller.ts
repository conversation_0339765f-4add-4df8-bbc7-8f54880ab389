import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    Request,
    HttpCode,
    HttpStatus,
    ValidationPipe,
    ParseUUIDPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { TenantGuard } from '../auth/tenant.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import {
    CreateUserDto,
    UpdateUserDto,
    UpdateUserRoleDto,
    UserListQueryDto,
    UserListResponseDto,
    UserProfileDto,
    UserStatsDto,
    BulkUserActionDto,
    UserPreferencesDto,
    UserSecurityDto,
    ResetUserPasswordDto,
    UserAuditLogDto
} from './dto/users.dto';

@ApiTags('users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('users')
export class UsersController {
    constructor(private readonly usersService: UsersService) { }

    @Post()
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Create a new user' })
    @ApiResponse({ status: 201, description: 'User created successfully', type: UserProfileDto })
    @ApiResponse({ status: 400, description: 'Bad request - validation failed' })
    @ApiResponse({ status: 403, description: 'Forbidden - insufficient permissions' })
    async create(
        @Body(ValidationPipe) createUserDto: CreateUserDto,
        @Request() req: any
    ): Promise<UserProfileDto> {
        return this.usersService.create(createUserDto, req.user.id);
    }

    @Get()
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get all users with filtering and pagination' })
    @ApiResponse({ status: 200, description: 'Users retrieved successfully', type: UserListResponseDto })
    @ApiQuery({ name: 'page', required: false, type: Number })
    @ApiQuery({ name: 'limit', required: false, type: Number })
    @ApiQuery({ name: 'search', required: false, type: String })
    @ApiQuery({ name: 'role', required: false, enum: Role })
    @ApiQuery({ name: 'isActive', required: false, type: Boolean })
    @ApiQuery({ name: 'sortBy', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
    async findAll(
        @Query(ValidationPipe) query: UserListQueryDto,
        @Request() req: any
    ): Promise<UserListResponseDto> {
        return this.usersService.findAll(query, req.user.organizationId);
    }

    @Get('stats')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get user statistics for the organization' })
    @ApiResponse({ status: 200, description: 'User statistics retrieved successfully', type: UserStatsDto })
    async getStats(@Request() req: any): Promise<UserStatsDto> {
        return this.usersService.getUserStats(req.user.organizationId);
    }

    @Post('bulk-action')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Perform bulk actions on multiple users' })
    @ApiResponse({ status: 200, description: 'Bulk action completed' })
    @HttpCode(HttpStatus.OK)
    async bulkAction(
        @Body(ValidationPipe) bulkActionDto: BulkUserActionDto,
        @Request() req: any
    ): Promise<{ success: number; failed: number }> {
        return this.usersService.bulkAction(bulkActionDto, req.user.id, req.user.organizationId);
    }

    @Get(':id')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get user by ID' })
    @ApiResponse({ status: 200, description: 'User retrieved successfully', type: UserProfileDto })
    @ApiResponse({ status: 404, description: 'User not found' })
    async findOne(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ): Promise<UserProfileDto> {
        // Users can view their own profile, managers and above can view any user
        const canViewAnyUser = [Role.SUPER_ADMIN, Role.ORG_ADMIN].includes(req.user.role);
        if (!canViewAnyUser && req.user.id !== id) {
            throw new Error('Forbidden');
        }

        return this.usersService.findOne(id, req.user.organizationId);
    }

    @Patch(':id')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Update user profile' })
    @ApiResponse({ status: 200, description: 'User updated successfully', type: UserProfileDto })
    @ApiResponse({ status: 404, description: 'User not found' })
    async update(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(ValidationPipe) updateUserDto: UpdateUserDto,
        @Request() req: any
    ): Promise<UserProfileDto> {
        // Users can update their own profile, managers and above can update any user
        const canUpdateAnyUser = [Role.SUPER_ADMIN, Role.ORG_ADMIN].includes(req.user.role);
        if (!canUpdateAnyUser && req.user.id !== id) {
            throw new Error('Forbidden');
        }

        return this.usersService.update(id, updateUserDto, req.user.id, req.user.organizationId);
    }

    @Patch(':id/role')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Update user role' })
    @ApiResponse({ status: 200, description: 'User role updated successfully', type: UserProfileDto })
    @ApiResponse({ status: 404, description: 'User not found' })
    async updateRole(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(ValidationPipe) updateRoleDto: UpdateUserRoleDto,
        @Request() req: any
    ): Promise<UserProfileDto> {
        return this.usersService.updateRole(id, updateRoleDto, req.user.id, req.user.organizationId);
    }

    @Patch(':id/preferences')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Update user preferences' })
    @ApiResponse({ status: 200, description: 'User preferences updated successfully', type: UserProfileDto })
    async updatePreferences(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(ValidationPipe) preferences: UserPreferencesDto,
        @Request() req: any
    ): Promise<UserProfileDto> {
        // Users can only update their own preferences
            if (req.user.id !== id && ![Role.SUPER_ADMIN, Role.ORG_ADMIN].includes(req.user.role)) {
            throw new Error('Forbidden');
        }

        return this.usersService.updatePreferences(id, preferences, req.user.organizationId);
    }

    @Get(':id/security')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get user security information' })
    @ApiResponse({ status: 200, description: 'User security information retrieved', type: UserSecurityDto })
    async getSecurity(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ): Promise<UserSecurityDto> {
        // Users can view their own security, admins can view any user's security
        const canViewAnyUserSecurity = [Role.SUPER_ADMIN, Role.ORG_ADMIN].includes(req.user.role);
        if (!canViewAnyUserSecurity && req.user.id !== id) {
            throw new Error('Forbidden');
        }

        return this.usersService.getUserSecurity(id, req.user.organizationId);
    }

    @Post(':id/reset-password')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Reset user password (admin only)' })
    @ApiResponse({ status: 200, description: 'Password reset successfully' })
    @HttpCode(HttpStatus.OK)
    async resetPassword(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(ValidationPipe) resetPasswordDto: ResetUserPasswordDto,
        @Request() req: any
    ): Promise<{ message: string }> {
        await this.usersService.resetPassword(id, resetPasswordDto, req.user.id, req.user.organizationId);
        return { message: 'Password reset successfully' };
    }

    @Get(':id/audit-logs')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Get user audit logs' })
    @ApiResponse({ status: 200, description: 'Audit logs retrieved successfully' })
    @ApiQuery({ name: 'page', required: false, type: Number })
    @ApiQuery({ name: 'limit', required: false, type: Number })
    async getAuditLogs(
        @Param('id', ParseUUIDPipe) id: string,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 50,
        @Request() req: any
    ): Promise<{ logs: UserAuditLogDto[]; total: number; pagination: any }> {
        const result = await this.usersService.getUserAuditLogs(id, req.user.organizationId, page, limit);

        return {
            ...result,
            pagination: {
                page,
                limit,
                total: result.total,
                totalPages: Math.ceil(result.total / limit)
            }
        };
    }

    @Delete(':id')
    @Roles(Role.SUPER_ADMIN, Role.ORG_ADMIN)
    @ApiOperation({ summary: 'Deactivate user (soft delete)' })
    @ApiResponse({ status: 200, description: 'User deactivated successfully' })
    @HttpCode(HttpStatus.OK)
    async remove(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ): Promise<{ message: string }> {
        await this.usersService.remove(id, req.user.id, req.user.organizationId);
        return { message: 'User deactivated successfully' };
    }
}