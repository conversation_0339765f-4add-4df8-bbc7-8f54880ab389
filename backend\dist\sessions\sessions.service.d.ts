import { PrismaService } from '../prisma/prisma.service';
import { Cache } from 'cache-manager';
export interface SessionData {
    loginMethod: string;
    userAgent?: string;
    ipAddress?: string;
    mfaVerified?: boolean;
    rememberMe?: boolean;
    deviceFingerprint?: string;
    location?: {
        country?: string;
        city?: string;
        timezone?: string;
    };
}
export interface SessionContext {
    currentWorkflow?: string;
    currentAgent?: string;
    activeTools?: string[];
    breadcrumbs?: Array<{
        path: string;
        timestamp: number;
    }>;
    preferences?: any;
}
export interface SessionMemory {
    conversationHistory?: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: number;
    }>;
    workflowState?: any;
    agentMemory?: any;
    userInputs?: any;
    temporaryData?: any;
}
export interface HybridWorkflowContext {
    executionId: string;
    nodeId: string;
    agentContext: Record<string, any>;
    toolContexts: Record<string, any>;
    sharedVariables: Record<string, any>;
    executionHistory: Array<{
        timestamp: number;
        component: 'agent' | 'tool';
        componentId: string;
        action: string;
        data: any;
    }>;
    syncPoints: Record<string, any>;
    lastUpdated: Date;
}
export interface CrossComponentMemory {
    agentMemories: Record<string, any>;
    toolMemories: Record<string, any>;
    sharedContext: Record<string, any>;
    executionState: Record<string, any>;
    coordinationData: Record<string, any>;
}
export declare class SessionsService {
    private prisma;
    private cacheManager;
    constructor(prisma: PrismaService, cacheManager: Cache);
    createSession(userId: string, organizationId: string, sessionData: SessionData, expirationHours?: number): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getSession(sessionId: string): Promise<unknown>;
    getUserActiveSession(userId: string): Promise<unknown>;
    updateSessionContext(sessionId: string, context: Partial<SessionContext>): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    updateSessionMemory(sessionId: string, memory: Partial<SessionMemory>): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    addConversationMessage(sessionId: string, role: 'user' | 'assistant' | 'system', content: string, metadata?: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    setWorkflowState(sessionId: string, workflowId: string, state: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getWorkflowState(sessionId: string, workflowId: string): Promise<any>;
    setAgentMemory(sessionId: string, agentId: string, memory: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getAgentMemory(sessionId: string, agentId: string): Promise<any>;
    invalidateSession(sessionId: string): Promise<void>;
    invalidateUserSessions(userId: string): Promise<void>;
    getUserSessions(userId: string, includeInactive?: boolean): Promise<{
        id: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    getOrganizationSessions(organizationId: string, limit?: number): Promise<({
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    })[]>;
    cleanupExpiredSessions(): Promise<number>;
    getSessionAnalytics(organizationId: string, days?: number): Promise<{
        totalSessions: number;
        activeSessions: number;
        avgSessionDuration: number;
        dailyStats: Record<string, {
            count: number;
            active: number;
        }>;
    }>;
    private pruneMemory;
    initializeHybridContext(sessionId: string, executionId: string, nodeId: string, initialContext?: Record<string, any>): Promise<HybridWorkflowContext>;
    getHybridContext(sessionId: string, executionId: string, nodeId: string): Promise<HybridWorkflowContext | null>;
    updateHybridContext(sessionId: string, executionId: string, nodeId: string, updates: Partial<HybridWorkflowContext>): Promise<HybridWorkflowContext>;
    addHybridExecutionHistory(sessionId: string, executionId: string, nodeId: string, historyEntry: {
        component: 'agent' | 'tool';
        componentId: string;
        action: string;
        data: any;
    }): Promise<void>;
    shareContextBetweenComponents(sessionId: string, executionId: string, nodeId: string, fromComponent: string, toComponent: string, contextData: Record<string, any>, strategy?: 'full' | 'summary' | 'selective'): Promise<void>;
    setSyncPoint(sessionId: string, executionId: string, nodeId: string, syncPointName: string, data: any): Promise<void>;
    waitForSyncPoint(sessionId: string, executionId: string, nodeId: string, syncPointName: string, timeoutMs?: number): Promise<any>;
    getComponentContext(sessionId: string, executionId: string, nodeId: string, componentType: 'agent' | 'tool', componentId: string): Promise<Record<string, any>>;
    updateComponentContext(sessionId: string, executionId: string, nodeId: string, componentType: 'agent' | 'tool', componentId: string, contextUpdates: Record<string, any>): Promise<void>;
    getCrossComponentMemory(sessionId: string): Promise<CrossComponentMemory>;
    updateCrossComponentMemory(sessionId: string, updates: Partial<CrossComponentMemory>): Promise<void>;
    cleanupHybridContext(sessionId: string, executionId: string, nodeId: string): Promise<void>;
    getHybridExecutionAnalytics(sessionId: string, executionId: string, nodeId: string): Promise<{
        totalExecutionTime: number;
        componentBreakdown: Record<string, number>;
        contextSharingEvents: number;
        syncPointsReached: number;
        executionHistory: any[];
    }>;
    private createContextSummary;
    private selectiveContextShare;
}
