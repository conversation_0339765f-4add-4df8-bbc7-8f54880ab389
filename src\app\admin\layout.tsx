"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AuthProvider } from "@/components/providers/auth-provider";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from "@/components/ui/sheet";
import {
    Building2,
    ChevronDown,
    Home,
    LayoutDashboard,
    LogOut,
    Menu,
    Settings,
    Shield,
    User,
    Users,
    Workflow,
    Wrench,
    Bot,
    BarChart3,
    Bell,
    Search,
    HelpCircle
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";

interface NavItem {
    title: string;
    href: string;
    icon: React.ElementType;
    requiredRole?: string[];
}

const navItems: NavItem[] = [
    {
        title: "Dashboard",
        href: "/admin",
        icon: LayoutDashboard,
    },
    {
        title: "Organizations",
        href: "/admin/organizations",
        icon: Building2,
    },
    {
        title: "Users",
        href: "/admin/users",
        icon: Users,
    },
    {
        title: "Tools",
        href: "/admin/tools",
        icon: Wrench,
    },
    {
        title: "Agents",
        href: "/admin/agents",
        icon: Bot,
    },
    {
        title: "Workflows",
        href: "/admin/workflows",
        icon: Workflow,
    },
    {
        title: "Analytics",
        href: "/admin/analytics",
        icon: BarChart3,
    },
    {
        title: "Security",
        href: "/admin/security",
        icon: Shield,
        requiredRole: ["SUPER_ADMIN", "ORG_ADMIN"],
    },
    {
        title: "Settings",
        href: "/admin/settings",
        icon: Settings,
    },
];

export default function AdminLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const pathname = usePathname();
    const { user, logout } = useAuth();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Filter nav items based on user role
    const filteredNavItems = navItems.filter(item => {
        if (!item.requiredRole) return true;
        if (!user) return false;
        return item.requiredRole.includes(user.role);
    });

    // Close mobile menu when route changes
    useEffect(() => {
        setIsMobileMenuOpen(false);
    }, [pathname]);

    if (!isMounted) {
        return null; // Prevent hydration issues
    }

    return (
        <div className="flex min-h-screen flex-col">
            {/* Header */}
            <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="container flex h-14 max-w-screen-2xl items-center justify-between">
                    <div className="flex items-center gap-4">
                        {/* Mobile menu trigger */}
                        <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                            <SheetTrigger asChild className="lg:hidden">
                                <Button variant="ghost" size="icon" className="lg:hidden">
                                    <Menu className="h-5 w-5" />
                                    <span className="sr-only">Toggle menu</span>
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="left" className="w-64">
                                <SheetHeader>
                                    <SheetTitle>Admin Panel</SheetTitle>
                                </SheetHeader>
                                <nav className="mt-6 flex flex-col gap-2">
                                    {filteredNavItems.map((item) => {
                                        const isActive = pathname === item.href;
                                        return (
                                            <Link
                                                key={item.href}
                                                href={item.href}
                                                className={`flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors ${isActive
                                                    ? "bg-primary text-primary-foreground"
                                                    : "hover:bg-accent hover:text-accent-foreground"
                                                    }`}
                                            >
                                                <item.icon className="h-4 w-4" />
                                                {item.title}
                                            </Link>
                                        );
                                    })}
                                </nav>
                            </SheetContent>
                        </Sheet>

                        {/* Logo */}
                        <Link href="/admin" className="flex items-center gap-2">
                            <Shield className="h-6 w-6" />
                            <span className="font-bold hidden md:inline-block">Admin Panel</span>
                        </Link>

                        {/* Desktop navigation */}
                        <nav className="hidden lg:flex lg:items-center lg:gap-6">
                            {filteredNavItems.map((item) => {
                                const isActive = pathname === item.href;
                                return (
                                    <Link
                                        key={item.href}
                                        href={item.href}
                                        className={`flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors ${isActive
                                            ? "bg-accent text-accent-foreground"
                                            : "hover:bg-accent hover:text-accent-foreground"
                                            }`}
                                    >
                                        <item.icon className="h-4 w-4" />
                                        {item.title}
                                    </Link>
                                );
                            })}
                        </nav>
                    </div>

                    {/* Right side: search, notifications, theme switcher, user menu */}
                    <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" className="hidden md:flex">
                            <Search className="h-5 w-5" />
                            <span className="sr-only">Search</span>
                        </Button>

                        <Button variant="ghost" size="icon">
                            <Bell className="h-5 w-5" />
                            <span className="sr-only">Notifications</span>
                        </Button>

                        <ThemeSwitcher />

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="relative h-8 pl-2 pr-0">
                                    <Avatar className="h-8 w-8">
                                        <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user?.email || 'admin'}`} />
                                        <AvatarFallback>
                                            {user?.firstName?.[0]}{user?.lastName?.[0] || 'A'}
                                        </AvatarFallback>
                                    </Avatar>
                                    <ChevronDown className="ml-1 h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel>
                                    <div className="flex flex-col space-y-1">
                                        <p className="text-sm font-medium">{user?.firstName} {user?.lastName}</p>
                                        <p className="text-xs text-muted-foreground">{user?.email}</p>
                                        <p className="text-xs font-medium text-primary">{user?.role.replace('_', ' ')}</p>
                                    </div>
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuGroup>
                                    <DropdownMenuItem>
                                        <User className="mr-2 h-4 w-4" />
                                        <span>Profile</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                        <Settings className="mr-2 h-4 w-4" />
                                        <span>Settings</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                        <HelpCircle className="mr-2 h-4 w-4" />
                                        <span>Help</span>
                                    </DropdownMenuItem>
                                </DropdownMenuGroup>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => logout()}>
                                    <LogOut className="mr-2 h-4 w-4" />
                                    <span>Log out</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>
            </header>

            {/* Main content */}
            <main className="flex-1 container py-6 max-w-screen-2xl">
                {children}
            </main>

            {/* Footer */}
            <footer className="border-t border-border/40 bg-background py-4">
                <div className="container flex flex-col items-center justify-between gap-4 md:flex-row max-w-screen-2xl">
                    <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
                        &copy; {new Date().getFullYear()} AI Agent Platform. All rights reserved.
                    </p>
                    <div className="flex items-center gap-4">
                        <Link
                            href="/admin/help"
                            className="text-sm text-muted-foreground hover:underline"
                        >
                            Help
                        </Link>
                        <Link
                            href="/admin/documentation"
                            className="text-sm text-muted-foreground hover:underline"
                        >
                            Documentation
                        </Link>
                        <Link
                            href="/admin/changelog"
                            className="text-sm text-muted-foreground hover:underline"
                        >
                            Changelog
                        </Link>
                    </div>
                </div>
            </footer>
        </div>
    );
}