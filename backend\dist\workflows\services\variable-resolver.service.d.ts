import { PrismaService } from '../../prisma/prisma.service';
export declare class VariableResolverService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    resolveVariables(template: any, context: {
        executionId: string;
        workflowId: string;
        sessionId: string;
        organizationId: string;
        variables: Record<string, any>;
        nodeResults?: Map<string, any>;
    }): Promise<any>;
    private getSessionVariables;
    private getWorkflowVariables;
    private getSystemVariables;
    private processTemplate;
    private generateUUID;
    updateSessionVariables(sessionId: string, variables: Record<string, any>): Promise<void>;
}
