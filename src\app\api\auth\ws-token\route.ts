/**
 * WebSocket Token API Route
 * Production-grade WebSocket authentication token endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import { SignJWT } from 'jose';
import { getAuthenticatedSession } from '@/lib/auth/auth-service';
import { withAuth } from '@/lib/middleware/auth.middleware';

async function handler(req: NextRequest): Promise<NextResponse> {
    try {
        // Get authenticated session
        const user = await getAuthenticatedSession(req);

        if (!user) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Unauthorized',
                    code: 'UNAUTHORIZED'
                },
                { status: 401 }
            );
        }

        // Create WebSocket token
        const secret = new TextEncoder().encode(
            process.env.WS_SECRET || process.env.JWT_SECRET || 'fallback-secret-for-dev'
        );

        const now = Math.floor(Date.now() / 1000);
        const token = await new SignJWT({
            sub: user.user.id,  
            email: user.user.email,
            organizationId: user.user.organizationId,
            role: user.user.role,
            type: 'ws_token'
        })
            .setProtectedHeader({ alg: 'HS256' })
            .setIssuedAt(now)
            .setExpirationTime('1h') // 1 hour
            .sign(secret);

        return NextResponse.json({
            success: true,
            token,
            expiresAt: new Date(now * 1000 + 60 * 60 * 1000).toISOString() // 1 hour from now
        });
    } catch (error: any) {
        console.error('WebSocket token error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to generate WebSocket token',
                code: error.code || 'WS_TOKEN_ERROR'
            },
            { status: 500 }
        );
    }
}

// Export with auth middleware
export const GET = withAuth(handler);