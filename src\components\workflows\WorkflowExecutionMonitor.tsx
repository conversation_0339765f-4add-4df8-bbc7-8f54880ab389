"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Play,
    Pause,
    Square,
    RefreshCw,
    Eye,
    MoreHorizontal,
    Clock,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Activity,
    Download,
    Calendar,
} from "lucide-react";

interface WorkflowExecution {
    id: string;
    workflowId: string;
    workflowName: string;
    status: "pending" | "running" | "completed" | "failed" | "cancelled";
    startedAt: Date;
    completedAt?: Date;
    duration?: number;
    error?: string;
    progress?: number;
    steps: Array<{
        id: string;
        name: string;
        status: "pending" | "running" | "completed" | "failed" | "skipped";
        duration?: number;
        error?: string;
        startedAt?: Date;
        completedAt?: Date;
    }>;
    metadata?: Record<string, any>;
}

interface WorkflowExecutionMonitorProps {
    executions: WorkflowExecution[];
    onRefresh: () => void;
}

export default function WorkflowExecutionMonitor({
    executions,
    onRefresh
}: WorkflowExecutionMonitorProps) {
    const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
    const [autoRefresh, setAutoRefresh] = useState(true);

    useEffect(() => {
        if (!autoRefresh) return;

        const interval = setInterval(() => {
            // Only refresh if there are running executions
            const hasRunning = executions.some(exec =>
                exec.status === "running" || exec.status === "pending"
            );

            if (hasRunning) {
                onRefresh();
            }
        }, 5000);

        return () => clearInterval(interval);
    }, [executions, onRefresh, autoRefresh]);

    const getStatusColor = (status: string) => {
        switch (status) {
            case "completed": return "bg-green-500";
            case "running": return "bg-blue-500";
            case "failed": return "bg-red-500";
            case "cancelled": return "bg-gray-500";
            case "pending": return "bg-yellow-500";
            default: return "bg-gray-500";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "completed": return <CheckCircle className="h-4 w-4" />;
            case "running": return <Activity className="h-4 w-4" />;
            case "failed": return <XCircle className="h-4 w-4" />;
            case "cancelled": return <Square className="h-4 w-4" />;
            case "pending": return <Clock className="h-4 w-4" />;
            default: return <AlertTriangle className="h-4 w-4" />;
        }
    };

    const formatDuration = (ms?: number) => {
        if (!ms) return "N/A";
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    };

    const calculateProgress = (execution: WorkflowExecution) => {
        if (execution.status === "completed") return 100;
        if (execution.status === "failed" || execution.status === "cancelled") return 0;
        if (execution.progress) return execution.progress;

        const completedSteps = execution.steps.filter(step => step.status === "completed").length;
        const totalSteps = execution.steps.length;

        return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
    };

    const stopExecution = async (executionId: string) => {
        try {
            const response = await fetch(`/api/workflows/executions/${executionId}/stop`, {
                method: "POST",
            });

            if (response.ok) {
                onRefresh();
            }
        } catch (error) {
            console.error("Failed to stop execution:", error);
        }
    };

    const retryExecution = async (execution: WorkflowExecution) => {
        try {
            const response = await fetch(`/api/workflows/${execution.workflowId}/execute`, {
                method: "POST",
            });

            if (response.ok) {
                onRefresh();
            }
        } catch (error) {
            console.error("Failed to retry execution:", error);
        }
    };

    const runningExecutions = executions.filter(exec =>
        exec.status === "running" || exec.status === "pending"
    );

    const completedExecutions = executions.filter(exec =>
        exec.status === "completed" || exec.status === "failed" || exec.status === "cancelled"
    );

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">Workflow Executions</h3>
                    <p className="text-sm text-muted-foreground">
                        Monitor and manage workflow execution status
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onRefresh}
                    >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                    <Button
                        variant={autoRefresh ? "default" : "outline"}
                        size="sm"
                        onClick={() => setAutoRefresh(!autoRefresh)}
                    >
                        Auto Refresh {autoRefresh ? "On" : "Off"}
                    </Button>
                </div>
            </div>

            {/* Running Executions */}
            {runningExecutions.length > 0 && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Activity className="h-5 w-5 text-blue-500" />
                            Active Executions ({runningExecutions.length})
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {runningExecutions.map((execution) => (
                                <div
                                    key={execution.id}
                                    className="p-4 border rounded-lg bg-card/50"
                                >
                                    <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center gap-3">
                                            <Badge className={`${getStatusColor(execution.status)} text-white`}>
                                                {getStatusIcon(execution.status)}
                                                <span className="ml-1">{execution.status}</span>
                                            </Badge>
                                            <span className="font-medium">{execution.workflowName}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm text-muted-foreground">
                                                {formatDuration(Date.now() - new Date(execution.startedAt).getTime())}
                                            </span>
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" size="sm">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuItem onClick={() => setSelectedExecution(execution)}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View Details
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem
                                                        onClick={() => stopExecution(execution.id)}
                                                        className="text-destructive"
                                                    >
                                                        <Square className="h-4 w-4 mr-2" />
                                                        Stop Execution
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between text-sm">
                                            <span>Progress</span>
                                            <span>{calculateProgress(execution)}%</span>
                                        </div>
                                        <Progress value={calculateProgress(execution)} className="h-2" />
                                    </div>

                                    {execution.steps.length > 0 && (
                                        <div className="mt-3 flex flex-wrap gap-2">
                                            {execution.steps.map((step) => (
                                                <Badge
                                                    key={step.id}
                                                    variant="outline"
                                                    className={`${getStatusColor(step.status)} text-white border-none`}
                                                >
                                                    {step.name}
                                                </Badge>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Execution History */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Execution History
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {completedExecutions.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                            <Activity className="h-8 w-8 mx-auto mb-2" />
                            <p>No execution history available</p>
                        </div>
                    ) : (
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Workflow</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Started</TableHead>
                                        <TableHead>Duration</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {completedExecutions.map((execution) => (
                                        <TableRow key={execution.id}>
                                            <TableCell className="font-medium">
                                                {execution.workflowName}
                                            </TableCell>
                                            <TableCell>
                                                <Badge className={`${getStatusColor(execution.status)} text-white`}>
                                                    {getStatusIcon(execution.status)}
                                                    <span className="ml-1">{execution.status}</span>
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                {new Date(execution.startedAt).toLocaleString()}
                                            </TableCell>
                                            <TableCell>
                                                {formatDuration(execution.duration)}
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem onClick={() => setSelectedExecution(execution)}>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            View Details
                                                        </DropdownMenuItem>
                                                        {execution.status === "failed" && (
                                                            <DropdownMenuItem onClick={() => retryExecution(execution)}>
                                                                <Play className="h-4 w-4 mr-2" />
                                                                Retry
                                                            </DropdownMenuItem>
                                                        )}
                                                        <DropdownMenuItem>
                                                            <Download className="h-4 w-4 mr-2" />
                                                            Export Logs
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Execution Detail Dialog */}
            <Dialog open={!!selectedExecution} onOpenChange={() => setSelectedExecution(null)}>
                <DialogContent className="max-w-4xl max-h-[80vh]">
                    <DialogHeader>
                        <DialogTitle>
                            Execution Details: {selectedExecution?.workflowName}
                        </DialogTitle>
                    </DialogHeader>

                    {selectedExecution && (
                        <ScrollArea className="h-[60vh]">
                            <div className="space-y-6">
                                {/* Execution Summary */}
                                <div className="grid grid-cols-2 gap-4">
                                    <Card>
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-sm">Status</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <Badge className={`${getStatusColor(selectedExecution.status)} text-white`}>
                                                {getStatusIcon(selectedExecution.status)}
                                                <span className="ml-1">{selectedExecution.status}</span>
                                            </Badge>
                                        </CardContent>
                                    </Card>

                                    <Card>
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-sm">Duration</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <p className="text-lg font-semibold">
                                                {formatDuration(selectedExecution.duration)}
                                            </p>
                                        </CardContent>
                                    </Card>

                                    <Card>
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-sm">Started At</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <p>{new Date(selectedExecution.startedAt).toLocaleString()}</p>
                                        </CardContent>
                                    </Card>

                                    <Card>
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-sm">Completed At</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <p>
                                                {selectedExecution.completedAt
                                                    ? new Date(selectedExecution.completedAt).toLocaleString()
                                                    : "N/A"
                                                }
                                            </p>
                                        </CardContent>
                                    </Card>
                                </div>

                                {/* Error Message */}
                                {selectedExecution.error && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="text-sm text-destructive">Error Details</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <pre className="text-sm bg-destructive/10 p-3 rounded text-destructive whitespace-pre-wrap">
                                                {selectedExecution.error}
                                            </pre>
                                        </CardContent>
                                    </Card>
                                )}

                                {/* Steps */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-sm">Execution Steps</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {selectedExecution.steps.map((step, index) => (
                                                <div key={step.id} className="flex items-center gap-3 p-3 border rounded">
                                                    <div className="flex items-center gap-2 flex-1">
                                                        <span className="text-sm text-muted-foreground">
                                                            {index + 1}.
                                                        </span>
                                                        <Badge
                                                            variant="outline"
                                                            className={`${getStatusColor(step.status)} text-white border-none`}
                                                        >
                                                            {getStatusIcon(step.status)}
                                                        </Badge>
                                                        <span className="font-medium">{step.name}</span>
                                                    </div>
                                                    <div className="text-sm text-muted-foreground">
                                                        {formatDuration(step.duration)}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Metadata */}
                                {selectedExecution.metadata && Object.keys(selectedExecution.metadata).length > 0 && (
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="text-sm">Metadata</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <pre className="text-sm bg-muted/50 p-3 rounded overflow-auto">
                                                {JSON.stringify(selectedExecution.metadata, null, 2)}
                                            </pre>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        </ScrollArea>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}