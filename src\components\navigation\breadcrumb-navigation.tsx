"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import {
    Breadcrumb,
    BreadcrumbList,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Home, ChevronRight } from "lucide-react";

interface BreadcrumbNavigationProps {
    customBreadcrumbs?: Array<{
        label: string;
        href?: string;
    }>;
}

const routeLabels: Record<string, string> = {
    dashboard: "Dashboard",
    workflows: "Workflows",
    agents: "Agents",
    tools: "Tools",
    analytics: "Analytics",
    team: "Team",
    settings: "Settings",
    admin: "Admin Panel",
    organizations: "Organizations",
    users: "Users",
    security: "Security",
    system: "System Health",
    database: "Database",
    integrations: "Integrations",
    billing: "Billing",
    notifications: "Notifications",
    "api-keys": "API Keys",
    new: "Create New",
    edit: "Edit",
    view: "View",
    reports: "Reports",
};

export function BreadcrumbNavigation({ customBreadcrumbs }: BreadcrumbNavigationProps) {
    const pathname = usePathname();

    // Use custom breadcrumbs if provided
    if (customBreadcrumbs) {
        return (
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem>
                        <BreadcrumbLink asChild>
                            <Link href="/dashboard" className="flex items-center">
                                <Home className="h-4 w-4" />
                            </Link>
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    {customBreadcrumbs.map((crumb, index) => (
                        <div key={index} className="flex items-center">
                            <BreadcrumbItem>
                                {crumb.href ? (
                                    <BreadcrumbLink asChild>
                                        <Link href={crumb.href}>{crumb.label}</Link>
                                    </BreadcrumbLink>
                                ) : (
                                    <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                                )}
                            </BreadcrumbItem>
                            {index < customBreadcrumbs.length - 1 && <BreadcrumbSeparator />}
                        </div>
                    ))}
                </BreadcrumbList>
            </Breadcrumb>
        );
    }

    // Auto-generate breadcrumbs from pathname
    const pathSegments = pathname.split("/").filter(Boolean);

    // Don't show breadcrumbs for root or single-level paths
    if (pathSegments.length <= 1) {
        return null;
    }

    const breadcrumbs = pathSegments.map((segment, index) => {
        const href = "/" + pathSegments.slice(0, index + 1).join("/");
        const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
        const isLast = index === pathSegments.length - 1;

        return {
            label,
            href: isLast ? undefined : href,
            isLast,
        };
    });

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                        <Link href="/dashboard" className="flex items-center">
                            <Home className="h-4 w-4" />
                        </Link>
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                {breadcrumbs.map((crumb, index) => (
                    <div key={index} className="flex items-center">
                        <BreadcrumbItem>
                            {crumb.href ? (
                                <BreadcrumbLink asChild>
                                    <Link href={crumb.href}>{crumb.label}</Link>
                                </BreadcrumbLink>
                            ) : (
                                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                            )}
                        </BreadcrumbItem>
                        {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                    </div>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    );
}