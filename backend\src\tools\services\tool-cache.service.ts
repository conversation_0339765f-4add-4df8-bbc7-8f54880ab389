import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import * as crypto from 'crypto';

@Injectable()
export class ToolCacheService {
  constructor(private prisma: PrismaService) {}

  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================

  async getCachedResult(toolId: string, inputHash: string) {
    const cached = await this.prisma.toolCache.findFirst({
      where: {
        toolId,
        inputHash,
        isValid: true,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (cached) {
      // Update access statistics
      await this.prisma.toolCache.update({
        where: { id: cached.id },
        data: {
          hits: cached.hits + 1,
          lastAccessed: new Date(),
        },
      });

      return cached.output;
    }

    return null;
  }

  async setCachedResult(
    toolId: string,
    inputHash: string,
    input: any,
    output: any,
    strategy: string,
    ttl: number
  ) {
    const expiresAt = new Date(Date.now() + ttl * 1000);
    const size = this.calculateSize(output);

    await this.prisma.toolCache.upsert({
      where: {
        toolId_inputHash: {
          toolId,
          inputHash,
        },
      },
      update: {
        output,
        lastAccessed: new Date(),
        expiresAt,
        isValid: true,
        size,
      },
      create: {
        toolId,
        inputHash,
        input,
        output,
        strategy: strategy as any,
        ttl,
        expiresAt,
        size,
      },
    });
  }

  async invalidateCache(toolId: string, reason?: string) {
    await this.prisma.toolCache.updateMany({
      where: { toolId },
      data: {
        isValid: false,
        invalidatedBy: reason || 'Manual invalidation',
        invalidatedAt: new Date(),
      },
    });
  }

  async invalidateCacheByPattern(pattern: string, reason?: string) {
    // This would require a more sophisticated pattern matching
    // For now, we'll invalidate by tool name pattern
    const tools = await this.prisma.toolDefinition.findMany({
      where: {
        name: {
          contains: pattern,
          mode: 'insensitive',
        },
      },
      select: { id: true },
    });

    const toolIds = tools.map(t => t.id);

    await this.prisma.toolCache.updateMany({
      where: {
        toolId: { in: toolIds },
      },
      data: {
        isValid: false,
        invalidatedBy: reason || `Pattern invalidation: ${pattern}`,
        invalidatedAt: new Date(),
      },
    });
  }

  async cleanupExpiredCache() {
    const deleted = await this.prisma.toolCache.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { isValid: false },
        ],
      },
    });

    return deleted.count;
  }

  async getCacheStats(toolId?: string) {
    const where = toolId ? { toolId } : {};

    const [
      totalEntries,
      validEntries,
      expiredEntries,
      totalSize,
      avgHits,
      topCached,
    ] = await Promise.all([
      this.prisma.toolCache.count({ where }),
      this.prisma.toolCache.count({ where: { ...where, isValid: true } }),
      this.prisma.toolCache.count({
        where: { ...where, expiresAt: { lt: new Date() } },
      }),
      this.prisma.toolCache.aggregate({
        where,
        _sum: { size: true },
      }),
      this.prisma.toolCache.aggregate({
        where,
        _avg: { hits: true },
      }),
      this.prisma.toolCache.findMany({
        where,
        orderBy: { hits: 'desc' },
        take: 10,
        include: {
          tool: {
            select: { name: true },
          },
        },
      }),
    ]);

    return {
      totalEntries,
      validEntries,
      expiredEntries,
      totalSize: totalSize._sum.size || 0,
      avgHits: avgHits._avg.hits || 0,
      hitRate: totalEntries > 0 ? (validEntries / totalEntries) * 100 : 0,
      topCached: topCached.map(entry => ({
        toolId: entry.toolId,
        toolName: entry.tool.name,
        hits: entry.hits,
        size: entry.size,
        lastAccessed: entry.lastAccessed,
      })),
    };
  }

  // ============================================================================
  // CACHE OPTIMIZATION
  // ============================================================================

  async optimizeCache(toolId?: string) {
    const where = toolId ? { toolId } : {};

    // Remove expired entries
    const expiredDeleted = await this.prisma.toolCache.deleteMany({
      where: {
        ...where,
        expiresAt: { lt: new Date() },
      },
    });

    // Remove invalid entries
    const invalidDeleted = await this.prisma.toolCache.deleteMany({
      where: {
        ...where,
        isValid: false,
      },
    });

    // Remove least recently used entries if cache is too large
    const cacheSize = await this.prisma.toolCache.aggregate({
      where,
      _sum: { size: true },
    });

    const maxCacheSize = 1024 * 1024 * 1024; // 1GB per tool
    let lruDeleted = 0;

    if (cacheSize._sum.size && cacheSize._sum.size > maxCacheSize) {
      const lruEntries = await this.prisma.toolCache.findMany({
        where,
        orderBy: { lastAccessed: 'asc' },
        take: Math.ceil((cacheSize._sum.size - maxCacheSize) / 1024), // Rough estimation
      });

      const lruIds = lruEntries.map(entry => entry.id);
      const lruDeleteResult = await this.prisma.toolCache.deleteMany({
        where: { id: { in: lruIds } },
      });
      lruDeleted = lruDeleteResult.count;
    }

    return {
      expiredDeleted: expiredDeleted.count,
      invalidDeleted: invalidDeleted.count,
      lruDeleted,
      totalDeleted: expiredDeleted.count + invalidDeleted.count + lruDeleted,
    };
  }

  async preloadCache(toolId: string, commonInputs: any[]) {
    const tool = await this.prisma.toolDefinition.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new Error('Tool not found');
    }

    const results = [];

    for (const input of commonInputs) {
      const inputHash = this.generateInputHash(input);
      
      // Check if already cached
      const existing = await this.getCachedResult(toolId, inputHash);
      if (existing) {
        results.push({ input, cached: true, result: existing });
        continue;
      }

      try {
        // Execute tool and cache result
        // This would integrate with the ToolExecutionService
        const result = await this.executeAndCache(toolId, input);
        results.push({ input, cached: false, result });
      } catch (error) {
        results.push({ input, cached: false, error: error.message });
      }
    }

    return results;
  }

  // ============================================================================
  // CACHE ANALYTICS
  // ============================================================================

  async getCacheAnalytics(toolId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get cache entries created in the time period
    const cacheEntries = await this.prisma.toolCache.findMany({
      where: {
        toolId,
        createdAt: { gte: startDate },
      },
    });

    // Get tool analytics for the same period
    const toolAnalytics = await this.prisma.toolAnalytics.findMany({
      where: {
        toolId,
        date: { gte: startDate },
      },
    });

    const totalCacheHits = toolAnalytics.reduce((sum, a) => sum + a.cacheHits, 0);
    const totalCacheMisses = toolAnalytics.reduce((sum, a) => sum + a.cacheMisses, 0);
    const totalRequests = totalCacheHits + totalCacheMisses;

    // Calculate cache effectiveness
    const cacheHitRate = totalRequests > 0 ? (totalCacheHits / totalRequests) * 100 : 0;
    const avgCacheSize = cacheEntries.reduce((sum, entry) => sum + (entry.size || 0), 0) / cacheEntries.length;
    const avgHits = cacheEntries.reduce((sum, entry) => sum + entry.hits, 0) / cacheEntries.length;

    // Group by day for trend analysis
    const dailyStats = new Map();
    
    toolAnalytics.forEach(analytics => {
      const dateKey = analytics.date.toISOString().split('T')[0];
      if (!dailyStats.has(dateKey)) {
        dailyStats.set(dateKey, {
          date: dateKey,
          hits: 0,
          misses: 0,
          hitRate: 0,
        });
      }
      
      const dayStats = dailyStats.get(dateKey);
      dayStats.hits += analytics.cacheHits;
      dayStats.misses += analytics.cacheMisses;
      dayStats.hitRate = (dayStats.hits + dayStats.misses) > 0 
        ? (dayStats.hits / (dayStats.hits + dayStats.misses)) * 100 
        : 0;
    });

    return {
      overview: {
        totalCacheEntries: cacheEntries.length,
        totalCacheHits,
        totalCacheMisses,
        totalRequests,
        cacheHitRate,
        avgCacheSize,
        avgHits,
        totalCacheSize: cacheEntries.reduce((sum, entry) => sum + (entry.size || 0), 0),
      },
      trends: Array.from(dailyStats.values()).sort((a, b) => a.date.localeCompare(b.date)),
      topCachedInputs: cacheEntries
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10)
        .map(entry => ({
          inputHash: entry.inputHash,
          hits: entry.hits,
          size: entry.size,
          createdAt: entry.createdAt,
          lastAccessed: entry.lastAccessed,
        })),
    };
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private generateInputHash(input: any): string {
    const inputString = JSON.stringify(input, Object.keys(input).sort());
    return crypto.createHash('sha256').update(inputString).digest('hex');
  }

  private calculateSize(data: any): number {
    return Buffer.byteLength(JSON.stringify(data), 'utf8');
  }

  private async executeAndCache(toolId: string, input: any) {
    // This would integrate with ToolExecutionService
    // For now, return a mock result
    const mockResult = {
      success: true,
      data: `Cached result for tool ${toolId}`,
      timestamp: new Date().toISOString(),
    };

    const inputHash = this.generateInputHash(input);
    await this.setCachedResult(
      toolId,
      inputHash,
      input,
      mockResult,
      'INPUT_HASH',
      3600 // 1 hour TTL
    );

    return mockResult;
  }

  // ============================================================================
  // CACHE WARMING STRATEGIES
  // ============================================================================

  async warmCache(toolId: string, strategy: 'popular' | 'recent' | 'predicted' = 'popular') {
    const tool = await this.prisma.toolDefinition.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new Error('Tool not found');
    }

    let inputsToWarm: any[] = [];

    switch (strategy) {
      case 'popular':
        inputsToWarm = await this.getPopularInputs(toolId);
        break;
      case 'recent':
        inputsToWarm = await this.getRecentInputs(toolId);
        break;
      case 'predicted':
        inputsToWarm = await this.getPredictedInputs(toolId);
        break;
    }

    return this.preloadCache(toolId, inputsToWarm);
  }

  private async getPopularInputs(toolId: string): any[] {
    // Get most frequently used inputs from execution history
    const executions = await this.prisma.toolExecution.findMany({
      where: {
        toolId,
        status: 'COMPLETED',
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      select: { input: true },
      take: 100,
    });

    // Group by input hash and count frequency
    const inputFrequency = new Map();
    
    executions.forEach(execution => {
      const inputHash = this.generateInputHash(execution.input);
      if (!inputFrequency.has(inputHash)) {
        inputFrequency.set(inputHash, { input: execution.input, count: 0 });
      }
      inputFrequency.get(inputHash).count++;
    });

    // Return top 10 most frequent inputs
    return Array.from(inputFrequency.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(item => item.input);
  }

  private async getRecentInputs(toolId: string): any[] {
    const executions = await this.prisma.toolExecution.findMany({
      where: {
        toolId,
        status: 'COMPLETED',
      },
      orderBy: { createdAt: 'desc' },
      select: { input: true },
      take: 20,
    });

    // Remove duplicates
    const uniqueInputs = new Map();
    executions.forEach(execution => {
      const inputHash = this.generateInputHash(execution.input);
      if (!uniqueInputs.has(inputHash)) {
        uniqueInputs.set(inputHash, execution.input);
      }
    });

    return Array.from(uniqueInputs.values());
  }

  private async getPredictedInputs(toolId: string): any[] {
    // This would implement ML-based prediction of likely inputs
    // For now, return a combination of popular and recent
    const popular = await this.getPopularInputs(toolId);
    const recent = await this.getRecentInputs(toolId);
    
    // Merge and deduplicate
    const combined = new Map();
    [...popular, ...recent].forEach(input => {
      const inputHash = this.generateInputHash(input);
      combined.set(inputHash, input);
    });

    return Array.from(combined.values()).slice(0, 15);
  }
}