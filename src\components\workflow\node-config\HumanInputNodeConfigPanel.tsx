import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { CheckCircle, XCircle, AlertTriangle, User, MessageSquare, Clock } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const humanInputConfigSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required'),
  inputType: z.enum(['text', 'number', 'boolean', 'select', 'multiselect', 'file', 'json']),
  options: z.array(z.object({
    value: z.string(),
    label: z.string(),
  })),
  validation: z.object({
    required: z.boolean(),
    minLength: z.number().min(0).optional(),
    maxLength: z.number().min(1).optional(),
    pattern: z.string().optional(),
    min: z.number().optional(),
    max: z.number().optional(),
    customValidator: z.string().optional(),
  }),
  timeout: z.number().min(1000).max(3600000),
  defaultValue: z.any().optional(),
  placeholder: z.string().optional(),
  description: z.string().optional(),
  allowSkip: z.boolean(),
  skipValue: z.any().optional(),
  ui: z.object({
    title: z.string().optional(),
    subtitle: z.string().optional(),
    helpText: z.string().optional(),
    theme: z.enum(['default', 'minimal', 'card']),
    size: z.enum(['sm', 'md', 'lg']),
  }),
  notifications: z.object({
    email: z.boolean(),
    webhook: z.string().optional(),
    reminderInterval: z.number().min(60000).optional(),
  }),
  errorHandling: z.object({
    onTimeout: z.enum(['fail', 'use_default', 'skip']),
    onInvalid: z.enum(['retry', 'fail', 'use_default']),
    maxRetries: z.number().min(0).max(10),
  }),
});

type HumanInputConfigFormData = z.infer<typeof humanInputConfigSchema>;

interface HumanInputNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<HumanInputConfigFormData>;
  onConfigChange: (config: HumanInputConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function HumanInputNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
}: HumanInputNodeConfigPanelProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm<HumanInputConfigFormData>({
    resolver: zodResolver(humanInputConfigSchema),
    defaultValues: {
      prompt: 'Please provide input',
      inputType: 'text',
      options: [],
      validation: {
        required: true,
      },
      timeout: 300000,
      defaultValue: '',
      placeholder: '',
      description: '',
      allowSkip: false,
      skipValue: null,
      ui: {
        title: '',
        subtitle: '',
        helpText: '',
        theme: 'default',
        size: 'md',
      },
      notifications: {
        email: false,
        reminderInterval: 300000, // 5 minutes
      },
      errorHandling: {
        onTimeout: 'fail',
        onInvalid: 'retry',
        maxRetries: 3,
      },
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const inputType = watch('inputType');
  const options = watch('options');

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map((error: any) => error.message).filter(Boolean) as string[];
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, errors, onConfigChange, onValidationChange]);

  const addOption = () => {
    const newOption = {
      value: `option_${options.length + 1}`,
      label: `Option ${options.length + 1}`,
    };
    setValue('options', [...options, newOption]);
  };

  const removeOption = (index: number) => {
    setValue('options', options.filter((_: any, i: number) => i !== index));
  };

  const updateOption = (index: number, field: 'value' | 'label', value: string) => {
    const updated = options.map((option: any, i: number) =>
      i === index ? { ...option, [field]: value } : option
    );
    setValue('options', updated);
  };

  const getInputTypeDescription = (type: string) => {
    switch (type) {
      case 'text':
        return 'Single line text input';
      case 'number':
        return 'Numeric input with validation';
      case 'boolean':
        return 'Yes/No or True/False choice';
      case 'select':
        return 'Single selection from options';
      case 'multiselect':
        return 'Multiple selections from options';
      case 'file':
        return 'File upload input';
      case 'json':
        return 'JSON object input with validation';
      default:
        return '';
    }
  };

  const needsOptions = ['select', 'multiselect'].includes(inputType);

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <CardTitle>Human Input Node Configuration</CardTitle>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            {isValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="validation">Validation</TabsTrigger>
              <TabsTrigger value="ui">UI Settings</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt">Prompt Message *</Label>
                  <Textarea
                    {...register('prompt')}
                    rows={3}
                    placeholder="Enter the message to display to the user..."
                  />
                  {errors.prompt && (
                    <p className="text-sm text-red-600">{errors.prompt.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inputType">Input Type</Label>
                  <Select
                    value={watch('inputType')}
                    onValueChange={(value: any) => setValue('inputType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text">Text</SelectItem>
                      <SelectItem value="number">Number</SelectItem>
                      <SelectItem value="boolean">Boolean</SelectItem>
                      <SelectItem value="select">Select</SelectItem>
                      <SelectItem value="multiselect">Multi-Select</SelectItem>
                      <SelectItem value="file">File Upload</SelectItem>
                      <SelectItem value="json">JSON Object</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {getInputTypeDescription(inputType)}
                  </p>
                </div>

                {needsOptions && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Options</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addOption}
                      >
                        Add Option
                      </Button>
                    </div>

                    <div className="space-y-3">
                      {options.map((option: any, index: number  ) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            value={option.value}
                            onChange={(e) => updateOption(index, 'value', e.target.value)}
                            placeholder="Value"
                            className="flex-1"
                          />
                          <Input
                            value={option.label}
                            onChange={(e) => updateOption(index, 'label', e.target.value)}
                            placeholder="Label"
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeOption(index)}
                          >
                            ×
                          </Button>
                        </div>
                      ))}

                      {options.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No options defined. Add options for users to choose from.
                        </p>
                      )}
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="placeholder">Placeholder</Label>
                    <Input
                      {...register('placeholder')}
                      placeholder="Enter placeholder text..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultValue">Default Value</Label>
                    <Input
                      value={typeof watch('defaultValue') === 'string' ? watch('defaultValue') : JSON.stringify(watch('defaultValue') || '')}
                      onChange={(e) => {
                        let value: any = e.target.value;
                        if (inputType === 'number') {
                          value = parseFloat(value) || 0;
                        } else if (inputType === 'boolean') {
                          value = value === 'true';
                        } else if (inputType === 'json') {
                          try {
                            value = JSON.parse(value);
                          } catch {
                            value = e.target.value;
                          }
                        }
                        setValue('defaultValue', value);
                      }}
                      placeholder="Default value..."
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    {...register('description')}
                    rows={2}
                    placeholder="Additional description or instructions..."
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('allowSkip')}
                      onCheckedChange={(checked) => setValue('allowSkip', checked)}
                    />
                    <Label>Allow Skip</Label>
                  </div>

                  {watch('allowSkip') && (
                    <div className="space-y-2 pl-6 border-l-2 border-muted">
                      <Label htmlFor="skipValue">Skip Value</Label>
                      <Input
                        value={typeof watch('skipValue') === 'string' ? watch('skipValue') : JSON.stringify(watch('skipValue') || '')}
                        onChange={(e) => {
                          let value: any = e.target.value;
                          try {
                            value = JSON.parse(value);
                          } catch {
                            // Keep as string if not valid JSON
                          }
                          setValue('skipValue', value);
                        }}
                        placeholder="Value to use when skipped..."
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Timeout</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('timeout') || 300000]}
                      onValueChange={([value]) => setValue('timeout', value)}
                      min={1000}
                      max={3600000}
                      step={1000}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>1s</span>
                      <span>{Math.round((watch('timeout') || 300000) / 60000)}m</span>
                      <span>60m</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="validation" className="space-y-6">
              <div className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={watch('validation.required')}
                    onCheckedChange={(checked) => setValue('validation.required', checked)}
                  />
                  <Label>Required Input</Label>
                </div>

                {inputType === 'text' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Minimum Length</Label>
                        <Input
                          type="number"
                          value={watch('validation.minLength') || ''}
                          onChange={(e) => setValue('validation.minLength', parseInt(e.target.value) || undefined)}
                          placeholder="0"
                          min="0"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Maximum Length</Label>
                        <Input
                          type="number"
                          value={watch('validation.maxLength') || ''}
                          onChange={(e) => setValue('validation.maxLength', parseInt(e.target.value) || undefined)}
                          placeholder="No limit"
                          min="1"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Pattern (Regex)</Label>
                      <Input
                        value={watch('validation.pattern') || ''}
                        onChange={(e) => setValue('validation.pattern', e.target.value || undefined)}
                        placeholder="^[a-zA-Z0-9]+$"
                        className="font-mono text-sm"
                      />
                      <p className="text-xs text-muted-foreground">
                        Regular expression pattern for validation
                      </p>
                    </div>
                  </div>
                )}

                {inputType === 'number' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Minimum Value</Label>
                      <Input
                        type="number"
                        value={watch('validation.min') || ''}
                        onChange={(e) => setValue('validation.min', parseFloat(e.target.value) || undefined)}
                        placeholder="No minimum"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Maximum Value</Label>
                      <Input
                        type="number"
                        value={watch('validation.max') || ''}
                        onChange={(e) => setValue('validation.max', parseFloat(e.target.value) || undefined)}
                        placeholder="No maximum"
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Custom Validator (JavaScript)</Label>
                  <Textarea
                    value={watch('validation.customValidator') || ''}
                    onChange={(e) => setValue('validation.customValidator', e.target.value || undefined)}
                    rows={4}
                    className="font-mono text-sm"
                    placeholder={`// Custom validation function
// Return true if valid, string error message if invalid
function validate(value) {
  if (value.includes('test')) {
    return "Value cannot contain 'test'";
  }
  return true;
}`}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="ui" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Display Settings</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Title</Label>
                      <Input
                        value={watch('ui.title') || ''}
                        onChange={(e) => setValue('ui.title', e.target.value || undefined)}
                        placeholder="Input title..."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Subtitle</Label>
                      <Input
                        value={watch('ui.subtitle') || ''}
                        onChange={(e) => setValue('ui.subtitle', e.target.value || undefined)}
                        placeholder="Input subtitle..."
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Help Text</Label>
                    <Textarea
                      value={watch('ui.helpText') || ''}
                      onChange={(e) => setValue('ui.helpText', e.target.value || undefined)}
                      rows={2}
                      placeholder="Additional help text for users..."
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Appearance</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select
                        value={watch('ui.theme')}
                        onValueChange={(value: 'default' | 'minimal' | 'card') =>
                          setValue('ui.theme', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">Default</SelectItem>
                          <SelectItem value="minimal">Minimal</SelectItem>
                          <SelectItem value="card">Card</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Size</Label>
                      <Select
                        value={watch('ui.size')}
                        onValueChange={(value: 'sm' | 'md' | 'lg') =>
                          setValue('ui.size', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sm">Small</SelectItem>
                          <SelectItem value="md">Medium</SelectItem>
                          <SelectItem value="lg">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Preview</h4>

                  <div className="p-4 border rounded-lg bg-muted/20">
                    <div className="space-y-3">
                      {watch('ui.title') && (
                        <h3 className="text-lg font-semibold">{watch('ui.title')}</h3>
                      )}
                      {watch('ui.subtitle') && (
                        <p className="text-sm text-muted-foreground">{watch('ui.subtitle')}</p>
                      )}
                      <div className="space-y-2">
                        <Label>{watch('prompt')}</Label>
                        {inputType === 'text' && (
                          <Input placeholder={watch('placeholder') || 'Enter text...'} disabled />
                        )}
                        {inputType === 'select' && options.length > 0 && (
                          <Select disabled>
                            <SelectTrigger>
                              <SelectValue placeholder="Select an option..." />
                            </SelectTrigger>
                          </Select>
                        )}
                        {inputType === 'boolean' && (
                          <div className="flex items-center space-x-2">
                            <input type="checkbox" disabled />
                            <Label>Yes/No</Label>
                          </div>
                        )}
                      </div>
                      {watch('ui.helpText') && (
                        <p className="text-xs text-muted-foreground">{watch('ui.helpText')}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('notifications.email')}
                      onCheckedChange={(checked) => setValue('notifications.email', checked)}
                    />
                    <Label>Send Email Notification</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send email notification when human input is required
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Webhook URL</Label>
                  <Input
                    value={watch('notifications.webhook') || ''}
                    onChange={(e) => setValue('notifications.webhook', e.target.value || undefined)}
                    placeholder="https://your-webhook-url.com/notify"
                    type="url"
                  />
                  <p className="text-xs text-muted-foreground">
                    Optional webhook to call when input is required
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Reminder Interval</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('notifications.reminderInterval') || 300000]}
                      onValueChange={([value]) => setValue('notifications.reminderInterval', value)}
                      min={60000}
                      max={3600000}
                      step={60000}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>1m</span>
                      <span>{Math.round((watch('notifications.reminderInterval') || 300000) / 60000)}m</span>
                      <span>60m</span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    How often to send reminder notifications
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Error Handling</h4>

                  <div className="space-y-2">
                    <Label>On Timeout</Label>
                    <Select
                      value={watch('errorHandling.onTimeout')}
                      onValueChange={(value: 'fail' | 'use_default' | 'skip') =>
                        setValue('errorHandling.onTimeout', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fail">Fail workflow</SelectItem>
                        <SelectItem value="use_default">Use default value</SelectItem>
                        <SelectItem value="skip">Skip with skip value</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>On Invalid Input</Label>
                    <Select
                      value={watch('errorHandling.onInvalid')}
                      onValueChange={(value: 'retry' | 'fail' | 'use_default') =>
                        setValue('errorHandling.onInvalid', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="retry">Allow retry</SelectItem>
                        <SelectItem value="fail">Fail workflow</SelectItem>
                        <SelectItem value="use_default">Use default value</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Max Retries</Label>
                    <Slider
                      value={[watch('errorHandling.maxRetries') || 3]}
                      onValueChange={([value]) => setValue('errorHandling.maxRetries', value)}
                      min={0}
                      max={10}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {watch('errorHandling.maxRetries')} retry attempts allowed
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Configuration Summary</h4>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Input Type:</span>
                        <span className="ml-2">{inputType}</span>
                      </div>
                      <div>
                        <span className="font-medium">Required:</span>
                        <span className="ml-2">{watch('validation.required') ? 'Yes' : 'No'}</span>
                      </div>
                      <div>
                        <span className="font-medium">Timeout:</span>
                        <span className="ml-2">{Math.round((watch('timeout') || 300000) / 60000)}m</span>
                      </div>
                      <div>
                        <span className="font-medium">Allow Skip:</span>
                        <span className="ml-2">{watch('allowSkip') ? 'Yes' : 'No'}</span>
                      </div>
                      <div>
                        <span className="font-medium">Email Notifications:</span>
                        <span className="ml-2">{watch('notifications.email') ? 'Enabled' : 'Disabled'}</span>
                      </div>
                      <div>
                        <span className="font-medium">Options:</span>
                        <span className="ml-2">{needsOptions ? options.length : 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}