"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Settings,
    Shield,
    Bell,
    Globe,
    Database,
    Key,
    Users,
    CreditCard,
    Download,
    Upload,
    Save,
    RefreshCw,
    AlertTriangle,
    CheckCircle,
    Loader2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import apiClient from '@/lib/api-client';

interface OrganizationSettings {
    id: string;
    name: string;
    domain?: string;
    logo?: string;
    timezone: string;
    language: string;
    features: {
        workflows: boolean;
        agents: boolean;
        tools: boolean;
        analytics: boolean;
        teamManagement: boolean;
    };
    security: {
        twoFactorAuth: boolean;
        sessionTimeout: number;
        passwordPolicy: string;
        ipWhitelist: string[];
    };
    notifications: {
        email: boolean;
        push: boolean;
        slack: boolean;
        webhook: string;
    };
    billing: {
        plan: string;
        status: 'active' | 'past_due' | 'canceled';
        nextBilling: string;
        usage: {
            executions: number;
            storage: number;
            apiCalls: number;
        };
        limits: {
            executions: number;
            storage: number;
            apiCalls: number;
            teamMembers: number;
        };
    };
}

export default function SettingsPage() {
    const [settings, setSettings] = useState<OrganizationSettings | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const { toast } = useToast();

    // Load settings from real API
    const loadSettings = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get('/organization/settings');
            setSettings(response as OrganizationSettings);
        } catch (error: any) {
            console.error('Failed to load settings:', error);
            toast({
                title: "Error",
                description: "Failed to load organization settings.",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    // Save settings
    const saveSettings = async (updatedSettings: Partial<OrganizationSettings>) => {
        try {
            setSaving(true);
            await apiClient.put('/organization/settings', updatedSettings);
            setSettings(prev => prev ? { ...prev, ...updatedSettings } : null);
            toast({
                title: "Success",
                description: "Settings saved successfully.",
            });
        } catch (error: any) {
            toast({
                title: "Error",
                description: "Failed to save settings.",
                variant: "destructive"
            });
        } finally {
            setSaving(false);
        }
    };

    useEffect(() => {
        loadSettings();
    }, []);

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center gap-3">
                            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                            <span className="text-lg text-gray-600">Loading settings...</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!settings) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center py-12">
                        <Settings className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No settings available</h3>
                        <p className="text-gray-600">Organization settings will appear here once configured.</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
                        <p className="text-gray-600 mt-1">Manage your organization's configuration and preferences</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" onClick={loadSettings}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh
                        </Button>
                        <Button disabled={saving}>
                            {saving ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Changes
                                </>
                            )}
                        </Button>
                    </div>
                </div>

                {/* Settings Tabs */}
                <Tabs defaultValue="general" className="space-y-6">
                    <TabsList className="grid w-full grid-cols-5">
                        <TabsTrigger value="general">General</TabsTrigger>
                        <TabsTrigger value="security">Security</TabsTrigger>
                        <TabsTrigger value="notifications">Notifications</TabsTrigger>
                        <TabsTrigger value="billing">Billing</TabsTrigger>
                        <TabsTrigger value="integrations">Integrations</TabsTrigger>
                    </TabsList>

                    <TabsContent value="general">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>General Settings</CardTitle>
                                <CardDescription>Basic organization information and preferences</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <Label htmlFor="org-name">Organization Name</Label>
                                        <Input
                                            id="org-name"
                                            value={settings.name}
                                            onChange={(e) => setSettings(prev => prev ? { ...prev, name: e.target.value } : null)}
                                            className="mt-1"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="domain">Domain</Label>
                                        <Input
                                            id="domain"
                                            value={settings.domain || ''}
                                            onChange={(e) => setSettings(prev => prev ? { ...prev, domain: e.target.value } : null)}
                                            className="mt-1"
                                            placeholder="yourcompany.com"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="timezone">Timezone</Label>
                                        <select
                                            id="timezone"
                                            value={settings.timezone}
                                            onChange={(e) => setSettings(prev => prev ? { ...prev, timezone: e.target.value } : null)}
                                            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            <option value="UTC">UTC</option>
                                            <option value="America/New_York">Eastern Time</option>
                                            <option value="America/Chicago">Central Time</option>
                                            <option value="America/Denver">Mountain Time</option>
                                            <option value="America/Los_Angeles">Pacific Time</option>
                                        </select>
                                    </div>
                                    <div>
                                        <Label htmlFor="language">Language</Label>
                                        <select
                                            id="language"
                                            value={settings.language}
                                            onChange={(e) => setSettings(prev => prev ? { ...prev, language: e.target.value } : null)}
                                            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <Label className="text-base font-medium">Feature Toggles</Label>
                                    <div className="mt-4 space-y-4">
                                        {Object.entries(settings.features).map(([feature, enabled]) => (
                                            <div key={feature} className="flex items-center justify-between">
                                                <div>
                                                    <Label className="text-sm font-medium capitalize">
                                                        {feature.replace(/([A-Z])/g, ' $1').trim()}
                                                    </Label>
                                                    <p className="text-xs text-gray-500">
                                                        Enable or disable {feature.toLowerCase()} functionality
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={enabled}
                                                    onCheckedChange={(checked) =>
                                                        setSettings(prev => prev ? {
                                                            ...prev,
                                                            features: { ...prev.features, [feature]: checked }
                                                        } : null)
                                                    }
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="security">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Security Settings</CardTitle>
                                <CardDescription>Configure security policies and authentication</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label className="text-sm font-medium">Two-Factor Authentication</Label>
                                            <p className="text-xs text-gray-500">Require 2FA for all users</p>
                                        </div>
                                        <Switch
                                            checked={settings.security.twoFactorAuth}
                                            onCheckedChange={(checked) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    security: { ...prev.security, twoFactorAuth: checked }
                                                } : null)
                                            }
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                                        <Input
                                            id="session-timeout"
                                            type="number"
                                            value={settings.security.sessionTimeout}
                                            onChange={(e) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    security: { ...prev.security, sessionTimeout: parseInt(e.target.value) }
                                                } : null)
                                            }
                                            className="mt-1"
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="password-policy">Password Policy</Label>
                                        <select
                                            id="password-policy"
                                            value={settings.security.passwordPolicy}
                                            onChange={(e) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    security: { ...prev.security, passwordPolicy: e.target.value }
                                                } : null)
                                            }
                                            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            <option value="basic">Basic (8+ characters)</option>
                                            <option value="strong">Strong (12+ characters, mixed case, numbers)</option>
                                            <option value="very-strong">Very Strong (16+ characters, special chars)</option>
                                        </select>
                                    </div>

                                    <div>
                                        <Label htmlFor="ip-whitelist">IP Whitelist</Label>
                                        <Input
                                            id="ip-whitelist"
                                            value={settings.security.ipWhitelist.join(', ')}
                                            onChange={(e) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    security: { ...prev.security, ipWhitelist: e.target.value.split(',').map(ip => ip.trim()) }
                                                } : null)
                                            }
                                            className="mt-1"
                                            placeholder="***********, 10.0.0.0/8"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">Comma-separated IP addresses or ranges</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="notifications">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Notification Settings</CardTitle>
                                <CardDescription>Configure how you receive notifications</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label className="text-sm font-medium">Email Notifications</Label>
                                            <p className="text-xs text-gray-500">Receive notifications via email</p>
                                        </div>
                                        <Switch
                                            checked={settings.notifications.email}
                                            onCheckedChange={(checked) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    notifications: { ...prev.notifications, email: checked }
                                                } : null)
                                            }
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label className="text-sm font-medium">Push Notifications</Label>
                                            <p className="text-xs text-gray-500">Receive browser push notifications</p>
                                        </div>
                                        <Switch
                                            checked={settings.notifications.push}
                                            onCheckedChange={(checked) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    notifications: { ...prev.notifications, push: checked }
                                                } : null)
                                            }
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label className="text-sm font-medium">Slack Integration</Label>
                                            <p className="text-xs text-gray-500">Send notifications to Slack</p>
                                        </div>
                                        <Switch
                                            checked={settings.notifications.slack}
                                            onCheckedChange={(checked) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    notifications: { ...prev.notifications, slack: checked }
                                                } : null)
                                            }
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="webhook">Webhook URL</Label>
                                        <Input
                                            id="webhook"
                                            value={settings.notifications.webhook}
                                            onChange={(e) =>
                                                setSettings(prev => prev ? {
                                                    ...prev,
                                                    notifications: { ...prev.notifications, webhook: e.target.value }
                                                } : null)
                                            }
                                            className="mt-1"
                                            placeholder="https://your-webhook-url.com/notifications"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="billing">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Billing & Usage</CardTitle>
                                <CardDescription>Manage your subscription and monitor usage</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <Label className="text-sm font-medium">Current Plan</Label>
                                        <div className="mt-2 flex items-center gap-2">
                                            <Badge variant={settings.billing.status === 'active' ? 'default' : 'secondary'}>
                                                {settings.billing.plan}
                                            </Badge>
                                            <Badge variant={settings.billing.status === 'active' ? 'default' : 'destructive'}>
                                                {settings.billing.status}
                                            </Badge>
                                        </div>
                                        <p className="text-xs text-gray-500 mt-1">
                                            Next billing: {new Date(settings.billing.nextBilling).toLocaleDateString()}
                                        </p>
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium">Usage This Month</Label>
                                        <div className="mt-2 space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span>Executions</span>
                                                <span>{settings.billing.usage.executions.toLocaleString()} / {settings.billing.limits.executions.toLocaleString()}</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span>Storage</span>
                                                <span>{settings.billing.usage.storage}GB / {settings.billing.limits.storage}GB</span>
                                            </div>
                                            <div className="flex justify-between text-sm">
                                                <span>API Calls</span>
                                                <span>{settings.billing.usage.apiCalls.toLocaleString()} / {settings.billing.limits.apiCalls.toLocaleString()}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex gap-3">
                                    <Button variant="outline">
                                        <CreditCard className="h-4 w-4 mr-2" />
                                        Manage Billing
                                    </Button>
                                    <Button variant="outline">
                                        <Download className="h-4 w-4 mr-2" />
                                        Download Invoice
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="integrations">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Integrations</CardTitle>
                                <CardDescription>Connect with external services and APIs</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="text-center py-8 text-gray-500">
                                    <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>Integration settings will be available here</p>
                                    <p className="text-sm">Connect with Slack, GitHub, and other services</p>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}