"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const apix_gateway_1 = require("../apix/apix.gateway");
const sessions_service_1 = require("../sessions/sessions.service");
const client_1 = require("@prisma/client");
const flow_controller_service_1 = require("./services/flow-controller.service");
const node_registry_service_1 = require("./services/node-registry.service");
const variable_resolver_service_1 = require("./services/variable-resolver.service");
let WorkflowsService = class WorkflowsService {
    constructor(prisma, apixGateway, sessionsService, flowController, nodeRegistry, variableResolver) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.sessionsService = sessionsService;
        this.flowController = flowController;
        this.nodeRegistry = nodeRegistry;
        this.variableResolver = variableResolver;
    }
    async create(userId, organizationId, createWorkflowDto) {
        const { name, description, definition, tags } = createWorkflowDto;
        this.validateWorkflowDefinition(definition);
        const workflow = await this.prisma.workflow.create({
            data: {
                name,
                description,
                definition: definition,
                tags: tags || [],
                creatorId: userId,
                organizationId,
                version: 1,
            },
            include: {
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        await this.apixGateway.emitWorkflowEvent(organizationId, workflow.id, 'workflow_created', {
            name: workflow.name,
            createdBy: workflow.creator,
            version: workflow.version,
        });
        return workflow;
    }
    async findAll(organizationId, filters) {
        const where = { organizationId };
        if (filters?.isActive !== undefined) {
            where.isActive = filters.isActive;
        }
        if (filters?.tags && filters.tags.length > 0) {
            where.tags = {
                hasSome: filters.tags,
            };
        }
        if (filters?.creatorId) {
            where.creatorId = filters.creatorId;
        }
        if (filters?.search) {
            where.OR = [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { description: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        return this.prisma.workflow.findMany({
            where,
            include: {
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                _count: {
                    select: {
                        executions: true,
                    },
                },
            },
            orderBy: {
                updatedAt: 'desc',
            },
        });
    }
    async findOne(id, organizationId) {
        const workflow = await this.prisma.workflow.findFirst({
            where: { id, organizationId },
            include: {
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                executions: {
                    take: 10,
                    orderBy: {
                        startedAt: 'desc',
                    },
                    select: {
                        id: true,
                        status: true,
                        startedAt: true,
                        completedAt: true,
                        duration: true,
                        error: true,
                    },
                },
            },
        });
        if (!workflow) {
            throw new common_1.NotFoundException('Workflow not found');
        }
        return workflow;
    }
    async update(id, organizationId, userId, updateWorkflowDto) {
        const workflow = await this.prisma.workflow.findFirst({
            where: { id, organizationId },
        });
        if (!workflow) {
            throw new common_1.NotFoundException('Workflow not found');
        }
        const updateData = {};
        if (updateWorkflowDto.name)
            updateData.name = updateWorkflowDto.name;
        if (updateWorkflowDto.description !== undefined)
            updateData.description = updateWorkflowDto.description;
        if (updateWorkflowDto.tags)
            updateData.tags = updateWorkflowDto.tags;
        if (updateWorkflowDto.isActive !== undefined)
            updateData.isActive = updateWorkflowDto.isActive;
        if (updateWorkflowDto.definition) {
            this.validateWorkflowDefinition(updateWorkflowDto.definition);
            updateData.definition = updateWorkflowDto.definition;
            updateData.version = workflow.version + 1;
        }
        const updatedWorkflow = await this.prisma.workflow.update({
            where: { id },
            data: updateData,
            include: {
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        await this.apixGateway.emitWorkflowEvent(organizationId, workflow.id, 'workflow_updated', {
            name: updatedWorkflow.name,
            version: updatedWorkflow.version,
            changes: Object.keys(updateData),
        });
        return updatedWorkflow;
    }
    async remove(id, organizationId) {
        const workflow = await this.prisma.workflow.findFirst({
            where: { id, organizationId },
        });
        if (!workflow) {
            throw new common_1.NotFoundException('Workflow not found');
        }
        const activeExecutions = await this.prisma.workflowExecution.count({
            where: {
                workflowId: id,
                status: {
                    in: [client_1.ExecutionStatus.PENDING, client_1.ExecutionStatus.RUNNING],
                },
            },
        });
        if (activeExecutions > 0) {
            throw new common_1.BadRequestException('Cannot delete workflow with active executions');
        }
        await this.prisma.workflow.delete({
            where: { id },
        });
        await this.apixGateway.emitWorkflowEvent(organizationId, workflow.id, 'workflow_deleted', {
            name: workflow.name,
        });
    }
    async execute(id, organizationId, userId, executeWorkflowDto) {
        const workflow = await this.prisma.workflow.findFirst({
            where: { id, organizationId, isActive: true },
        });
        if (!workflow) {
            throw new common_1.NotFoundException('Workflow not found or inactive');
        }
        const executionId = await this.flowController.startExecution(id, userId, organizationId, executeWorkflowDto.input || {}, executeWorkflowDto.options || {});
        const execution = await this.prisma.workflowExecution.findUnique({
            where: { id: executionId },
        });
        return {
            executionId: execution.id,
            status: execution.status,
            startedAt: execution.startedAt,
        };
    }
    async getExecutions(workflowId, organizationId, limit = 50) {
        return this.prisma.workflowExecution.findMany({
            where: {
                workflowId,
                workflow: {
                    organizationId,
                },
            },
            include: {
                workflow: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                startedAt: 'desc',
            },
            take: limit,
        });
    }
    async getExecution(executionId, organizationId) {
        const execution = await this.prisma.workflowExecution.findFirst({
            where: {
                id: executionId,
                workflow: {
                    organizationId,
                },
            },
            include: {
                workflow: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                steps: {
                    orderBy: {
                        startedAt: 'asc',
                    },
                },
            },
        });
        if (!execution) {
            throw new common_1.NotFoundException('Execution not found');
        }
        return execution;
    }
    async cancelExecution(executionId, organizationId) {
        return this.flowController.cancelExecution(executionId, organizationId);
    }
    async pauseExecution(executionId, organizationId) {
        return this.flowController.pauseExecution(executionId, organizationId);
    }
    async resumeExecution(executionId, organizationId) {
        return this.flowController.resumeExecution(executionId, organizationId);
    }
    async getExecutionStatus(executionId, organizationId) {
        return this.flowController.getExecutionStatus(executionId, organizationId);
    }
    async getAvailableNodeTypes(organizationId) {
        const nodeTypes = this.nodeRegistry.getAllNodeDefinitions();
        const agents = await this.prisma.agent.findMany({
            where: { organizationId, isActive: true },
            select: { id: true, name: true, type: true },
        });
        const tools = await this.prisma.tool.findMany({
            where: { organizationId, isActive: true },
            select: { id: true, name: true, type: true },
        });
        return {
            nodeTypes,
            agents,
            tools,
        };
    }
    validateWorkflowDefinition(definition) {
        if (!definition.nodes || definition.nodes.length === 0) {
            throw new common_1.BadRequestException('Workflow must have at least one node');
        }
        if (!definition.edges) {
            throw new common_1.BadRequestException('Workflow must have edges array');
        }
        const nodeIds = definition.nodes.map(node => node.id);
        const uniqueNodeIds = new Set(nodeIds);
        if (nodeIds.length !== uniqueNodeIds.size) {
            throw new common_1.BadRequestException('Node IDs must be unique');
        }
        for (const edge of definition.edges) {
            if (!nodeIds.includes(edge.source)) {
                throw new common_1.BadRequestException(`Edge source node ${edge.source} not found`);
            }
            if (!nodeIds.includes(edge.target)) {
                throw new common_1.BadRequestException(`Edge target node ${edge.target} not found`);
            }
        }
        const startNodes = definition.nodes.filter(node => node.inputs.length === 0 ||
            !definition.edges.some(edge => edge.target === node.id));
        if (startNodes.length === 0) {
            throw new common_1.BadRequestException('Workflow must have at least one start node');
        }
        for (const node of definition.nodes) {
            const nodeDefinition = this.nodeRegistry.getNodeDefinition(node.type);
            if (!nodeDefinition) {
                throw new common_1.BadRequestException(`Unknown node type: ${node.type}`);
            }
        }
    }
};
exports.WorkflowsService = WorkflowsService;
exports.WorkflowsService = WorkflowsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        sessions_service_1.SessionsService,
        flow_controller_service_1.FlowControllerService,
        node_registry_service_1.NodeRegistryService,
        variable_resolver_service_1.VariableResolverService])
], WorkflowsService);
//# sourceMappingURL=workflows.service.js.map