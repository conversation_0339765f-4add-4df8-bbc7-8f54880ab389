"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
    Grid3X3,
    List,
    SlidersHorizontal,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Filter,
    X,
} from "lucide-react";
import { ViewMode, SortDirection, useViewPreferences } from "@/hooks/use-view-preferences";

interface ViewToggleProps {
    viewPreferences: ReturnType<typeof useViewPreferences>;
    sortOptions?: Array<{
        key: string;
        label: string;
    }>;
    showFilters?: boolean;
    activeFiltersCount?: number;
    onFiltersClick?: () => void;
    onClearFilters?: () => void;
}

export function ViewToggle({
    viewPreferences,
    sortOptions = [],
    showFilters = false,
    activeFiltersCount = 0,
    onFiltersClick,
    onClearFilters,
}: ViewToggleProps) {
    const { preferences, setViewMode, setSorting } = viewPreferences;

    const getSortIcon = (direction: SortDirection) => {
        switch (direction) {
            case "asc":
                return <ArrowUp className="h-3 w-3" />;
            case "desc":
                return <ArrowDown className="h-3 w-3" />;
            default:
                return <ArrowUpDown className="h-3 w-3" />;
        }
    };

    return (
        <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex items-center border rounded-md">
                <Button
                    variant={preferences.viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none border-r-0"
                >
                    <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                    variant={preferences.viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                >
                    <List className="h-4 w-4" />
                </Button>
            </div>

            {/* Sort Options */}
            {sortOptions.length > 0 && (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                            {getSortIcon(preferences.sortDirection)}
                            <span className="ml-2">Sort</span>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {sortOptions.map((option) => (
                            <DropdownMenuItem
                                key={option.key}
                                onClick={() => setSorting(option.key)}
                                className="flex items-center justify-between"
                            >
                                <span>{option.label}</span>
                                {preferences.sortBy === option.key && (
                                    <div className="flex items-center space-x-1">
                                        {getSortIcon(preferences.sortDirection)}
                                    </div>
                                )}
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}

            {/* Filters */}
            {showFilters && (
                <div className="flex items-center space-x-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onFiltersClick}
                        className="relative"
                    >
                        <Filter className="h-4 w-4" />
                        <span className="ml-2">Filters</span>
                        {activeFiltersCount > 0 && (
                            <Badge
                                variant="secondary"
                                className="ml-2 h-5 w-5 rounded-full p-0 text-xs"
                            >
                                {activeFiltersCount}
                            </Badge>
                        )}
                    </Button>

                    {activeFiltersCount > 0 && onClearFilters && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClearFilters}
                            className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    )}
                </div>
            )}

            {/* View Settings */}
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                        <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuLabel>Display Options</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => viewPreferences.setPageSize(10)}>
                        <span className="flex-1">10 per page</span>
                        {preferences.pageSize === 10 && <Badge variant="secondary">✓</Badge>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => viewPreferences.setPageSize(20)}>
                        <span className="flex-1">20 per page</span>
                        {preferences.pageSize === 20 && <Badge variant="secondary">✓</Badge>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => viewPreferences.setPageSize(50)}>
                        <span className="flex-1">50 per page</span>
                        {preferences.pageSize === 50 && <Badge variant="secondary">✓</Badge>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => viewPreferences.setPageSize(100)}>
                        <span className="flex-1">100 per page</span>
                        {preferences.pageSize === 100 && <Badge variant="secondary">✓</Badge>}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => viewPreferences.resetPreferences()}>
                        Reset to defaults
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
}