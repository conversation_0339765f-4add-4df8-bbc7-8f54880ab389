/**
 * 🔍 Centralized Validation Middleware
 * Production-grade validation system used across all API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError, ZodSchema } from 'zod';
import { AuthenticatedRequest } from './auth.middleware';

export interface ValidationOptions {
    body?: ZodSchema;
    query?: ZodSchema;
    params?: ZodSchema;
}

export interface ValidatedRequest extends AuthenticatedRequest {
    validatedBody?: any;
    validatedQuery?: any;
    validatedParams?: any;
}

/**
 * Main validation middleware - use this for all input validation
 */
export function withValidation(
    handler: (req: ValidatedRequest, context?: any) => Promise<NextResponse>,
    schemas: ValidationOptions
) {
    return async (request: ValidatedRequest, context?: any) => {
        try {
            // Validate request body
            if (schemas.body) {
                const body = await request.json().catch(() => ({}));
                const result = schemas.body.safeParse(body);
                if (!result.success) {
                    return validationErrorResponse(result.error, 'body');
                }
                request.validatedBody = result.data;
            }

            // Validate query parameters
            if (schemas.query) {
                const url = new URL(request.url);
                const query = Object.fromEntries(url.searchParams.entries());
                const result = schemas.query.safeParse(query);
                if (!result.success) {
                    return validationErrorResponse(result.error, 'query');
                }
                request.validatedQuery = result.data;
            }

            // Validate route parameters
            if (schemas.params && context?.params) {
                const result = schemas.params.safeParse(context.params);
                if (!result.success) {
                    return validationErrorResponse(result.error, 'params');
                }
                request.validatedParams = result.data;
            }

            return handler(request, context);
        } catch (error) {
            console.error('Validation middleware error:', error);
            return NextResponse.json(
                { error: 'Validation service error', code: 'VALIDATION_ERROR' },
                { status: 500 }
            );
        }
    };
}

/**
 * Create standardized validation error response
 */
function validationErrorResponse(error: ZodError, source: string) {
    const formattedErrors = error.issues.map(issue => ({
        field: issue.path.join('.'),
        message: issue.message,
        code: issue.code
    }));

    return NextResponse.json(
        {
            error: 'Validation failed',
            code: 'VALIDATION_FAILED',
            source,
            details: formattedErrors
        },
        { status: 400 }
    );
}

/**
 * Common validation schemas (reusable across routes)
 */
export const CommonSchemas = {
    // Pagination
    pagination: z.object({
        page: z.string().transform(val => parseInt(val) || 1),
        limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)),
        search: z.string().optional(),
        sort: z.string().optional(),
        order: z.enum(['asc', 'desc']).default('desc')
    }),

    // MongoDB-style ID
    mongoId: z.object({
        id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format')
    }),

    // UUID
    uuid: z.object({
        id: z.string().uuid('Invalid UUID format')
    }),

    // Common filters
    dateRange: z.object({
        startDate: z.string().datetime().optional(),
        endDate: z.string().datetime().optional()
    }),

    // Status filter
    statusFilter: z.object({
        status: z.enum(['active', 'inactive', 'all']).default('all')
    })
};

/**
 * Validation middleware shortcuts for common patterns
 */
export const withPagination = (handler: any) =>
    withValidation(handler, { query: CommonSchemas.pagination });

export const withIdValidation = (handler: any) =>
    withValidation(handler, { params: CommonSchemas.uuid });

export const withDateRange = (handler: any) =>
    withValidation(handler, { query: CommonSchemas.dateRange });

/**
 * Body validation helpers
 */
export function validateBody<T>(schema: ZodSchema<T>) {
    return (handler: (req: ValidatedRequest & { body: T }, context?: any) => Promise<NextResponse>) =>
        withValidation(handler as any, { body: schema });
}

export function validateQuery<T>(schema: ZodSchema<T>) {
    return (handler: (req: ValidatedRequest & { query: T }, context?: any) => Promise<NextResponse>) =>
        withValidation(handler as any, { query: schema });
}

export function validateParams<T>(schema: ZodSchema<T>) {
    return (handler: (req: ValidatedRequest & { params: T }, context?: any) => Promise<NextResponse>) =>
        withValidation(handler as any, { params: schema });
}