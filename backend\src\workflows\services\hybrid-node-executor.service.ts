import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { AgentsService } from '../../agents/agents.service';
import { ToolsService } from '../../tools/tools.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface HybridExecutionContext {
  executionId: string;
  sessionId: string;
  organizationId: string;
  userId: string;
  nodeId: string;
  workflowId: string;
}

export interface HybridNodeConfig {
  agentId: string;
  toolIds: string[];
  executionPattern: 'agent-first' | 'tool-first' | 'parallel' | 'multi-tool-orchestration';
  maxIterations: number;
  agentConfig: {
    systemPrompt?: string;
    maxTokens: number;
    temperature: number;
    contextWindow: number;
  };
  toolConfigs: Record<string, {
    timeout: number;
    retryPolicy: {
      enabled: boolean;
      maxRetries: number;
    };
    parameters: Record<string, any>;
  }>;
  coordination: {
    shareContext: boolean;
    contextStrategy: 'full' | 'summary' | 'selective';
    syncPoints: string[];
    errorPropagation: boolean;
  };
  performance: {
    parallelLimit: number;
    timeout: number;
    memoryLimit: number;
  };
  fallback: {
    enabled: boolean;
    fallbackAgent?: string;
    fallbackTools: string[];
    conditions: string[];
  };
}

@Injectable()
export class HybridNodeExecutorService {
  private readonly logger = new Logger(HybridNodeExecutorService.name);
  private activeExecutions = new Map<string, any>();

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private sessionsService: SessionsService,
    private agentsService: AgentsService,
    private toolsService: ToolsService,
    private eventEmitter: EventEmitter2,
  ) {}

  async executeHybridNode(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    const executionKey = `${context.executionId}_${context.nodeId}`;
    
    try {
      this.logger.log(`Starting hybrid node execution: ${executionKey}`);
      
      // Store execution context
      this.activeExecutions.set(executionKey, {
        config,
        variables,
        context,
        startTime: Date.now(),
        status: 'running',
      });

      // Emit hybrid execution started
      await this.emitHybridEvent('hybrid_execution_started', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        agentId: config.agentId,
        toolIds: config.toolIds,
        executionPattern: config.executionPattern,
        timestamp: Date.now(),
      }, context);

      // Initialize shared context if enabled
      if (config.coordination.shareContext) {
        await this.initializeSharedContext(context, variables);
      }

      // Execute based on pattern
      let result: any;
      switch (config.executionPattern) {
        case 'agent-first':
          result = await this.executeAgentFirstPattern(config, variables, context);
          break;
        case 'tool-first':
          result = await this.executeToolFirstPattern(config, variables, context);
          break;
        case 'parallel':
          result = await this.executeParallelPattern(config, variables, context);
          break;
        case 'multi-tool-orchestration':
          result = await this.executeOrchestrationPattern(config, variables, context);
          break;
        default:
          throw new Error(`Unknown execution pattern: ${config.executionPattern}`);
      }

      // Update execution status
      const execution = this.activeExecutions.get(executionKey);
      if (execution) {
        execution.status = 'completed';
        execution.result = result;
        execution.endTime = Date.now();
        execution.duration = execution.endTime - execution.startTime;
      }

      // Emit hybrid execution completed
      await this.emitHybridEvent('hybrid_execution_completed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        result,
        duration: execution?.duration,
        pattern: config.executionPattern,
        timestamp: Date.now(),
      }, context);

      // Store execution analytics
      await this.storeExecutionAnalytics(context, config, result, execution?.duration || 0);

      return result;

    } catch (error) {
      this.logger.error(`Hybrid node execution failed: ${error.message}`, error.stack);

      // Update execution status
      const execution = this.activeExecutions.get(executionKey);
      if (execution) {
        execution.status = 'failed';
        execution.error = error.message;
        execution.endTime = Date.now();
        execution.duration = execution.endTime - execution.startTime;
      }

      // Emit hybrid execution failed
      await this.emitHybridEvent('hybrid_execution_failed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        error: error.message,
        duration: execution?.duration,
        timestamp: Date.now(),
      }, context);

      // Try fallback if enabled
      if (config.fallback.enabled && this.shouldTriggerFallback(error, config.fallback.conditions)) {
        this.logger.log(`Triggering fallback for execution: ${executionKey}`);
        return await this.executeFallback(config, variables, context, error);
      }

      throw error;
    } finally {
      // Cleanup
      setTimeout(() => {
        this.activeExecutions.delete(executionKey);
      }, 300000); // Keep for 5 minutes for debugging
    }
  }

  private async executeAgentFirstPattern(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    this.logger.log(`Executing agent-first pattern for node: ${context.nodeId}`);

    // Emit pattern started
    await this.emitHybridEvent('agent_first_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      agentId: config.agentId,
      toolCount: config.toolIds.length,
    }, context);

    // Step 1: Execute agent
    const agentResult = await this.executeAgent(config, variables, context);

    // Step 2: Extract tool parameters from agent result
    const toolParameters = this.extractToolParameters(agentResult, config.toolIds);

    // Step 3: Execute tools with agent-derived parameters
    const toolResults = [];
    for (const toolId of config.toolIds) {
      try {
        const toolConfig = config.toolConfigs[toolId] || {};
        const parameters = { ...toolConfig.parameters, ...toolParameters[toolId] };

        const toolResult = await this.executeTool(toolId, parameters, toolConfig, context);
        toolResults.push({ toolId, result: toolResult, success: true });

        // Update shared context if enabled
        if (config.coordination.shareContext) {
          await this.updateSharedContext(context, { [`tool_${toolId}_result`]: toolResult });
        }

      } catch (error) {
        this.logger.error(`Tool execution failed: ${toolId}`, error);
        toolResults.push({ toolId, error: error.message, success: false });

        if (config.coordination.errorPropagation) {
          throw error;
        }
      }
    }

    const result = {
      pattern: 'agent-first',
      agentResult,
      toolResults,
      executionOrder: ['agent', ...config.toolIds],
      timestamp: Date.now(),
    };

    await this.emitHybridEvent('agent_first_completed', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      result,
    }, context);

    return result;
  }

  private async executeToolFirstPattern(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    this.logger.log(`Executing tool-first pattern for node: ${context.nodeId}`);

    await this.emitHybridEvent('tool_first_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      toolCount: config.toolIds.length,
      agentId: config.agentId,
    }, context);

    // Step 1: Execute all tools first
    const toolResults = [];
    const aggregatedData = {};

    for (const toolId of config.toolIds) {
      try {
        const toolConfig = config.toolConfigs[toolId] || {};
        const parameters = { ...toolConfig.parameters, ...variables };

        const toolResult = await this.executeTool(toolId, parameters, toolConfig, context);
        toolResults.push({ toolId, result: toolResult, success: true });

        // Aggregate tool data for agent
        Object.assign(aggregatedData, toolResult);

        // Update shared context
        if (config.coordination.shareContext) {
          await this.updateSharedContext(context, { [`tool_${toolId}_result`]: toolResult });
        }

      } catch (error) {
        this.logger.error(`Tool execution failed: ${toolId}`, error);
        toolResults.push({ toolId, error: error.message, success: false });

        if (config.coordination.errorPropagation) {
          throw error;
        }
      }
    }

    // Step 2: Execute agent with aggregated tool results
    const enrichedVariables = {
      ...variables,
      toolResults: aggregatedData,
      toolExecutionSummary: toolResults,
    };

    const agentResult = await this.executeAgent(config, enrichedVariables, context);

    const result = {
      pattern: 'tool-first',
      toolResults,
      agentResult,
      aggregatedData,
      executionOrder: [...config.toolIds, 'agent'],
      timestamp: Date.now(),
    };

    await this.emitHybridEvent('tool_first_completed', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      result,
    }, context);

    return result;
  }

  private async executeParallelPattern(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    this.logger.log(`Executing parallel pattern for node: ${context.nodeId}`);

    await this.emitHybridEvent('parallel_execution_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      agentId: config.agentId,
      toolCount: config.toolIds.length,
      parallelLimit: config.performance.parallelLimit,
    }, context);

    // Create execution promises
    const agentPromise = this.executeAgent(config, variables, context);
    const toolPromises = config.toolIds.map(async (toolId) => {
      try {
        const toolConfig = config.toolConfigs[toolId] || {};
        const parameters = { ...toolConfig.parameters, ...variables };
        const result = await this.executeTool(toolId, parameters, toolConfig, context);
        return { toolId, result, success: true };
      } catch (error) {
        return { toolId, error: error.message, success: false };
      }
    });

    // Execute with parallel limit
    const allPromises = [agentPromise, ...toolPromises];
    const results = await this.executeWithParallelLimit(allPromises, config.performance.parallelLimit);

    const agentResult = results[0];
    const toolResults = results.slice(1);

    // Synchronization points
    if (config.coordination.syncPoints.length > 0) {
      await this.processSyncPoints(config.coordination.syncPoints, context, {
        agentResult,
        toolResults,
      });
    }

    const result = {
      pattern: 'parallel',
      agentResult,
      toolResults,
      executionOrder: ['parallel'],
      synchronizationPoints: config.coordination.syncPoints,
      timestamp: Date.now(),
    };

    await this.emitHybridEvent('parallel_execution_completed', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      result,
    }, context);

    return result;
  }

  private async executeOrchestrationPattern(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    this.logger.log(`Executing orchestration pattern for node: ${context.nodeId}`);

    await this.emitHybridEvent('orchestration_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      agentId: config.agentId,
      toolIds: config.toolIds,
      maxIterations: config.maxIterations,
    }, context);

    let currentVariables = { ...variables };
    const executionHistory = [];
    let iteration = 0;

    while (iteration < config.maxIterations) {
      this.logger.log(`Orchestration iteration ${iteration + 1}/${config.maxIterations}`);

      // Agent decides next action
      const orchestrationPrompt = this.buildOrchestrationPrompt(
        config.toolIds,
        currentVariables,
        executionHistory,
        iteration
      );

      const agentConfig = {
        ...config.agentConfig,
        systemPrompt: orchestrationPrompt,
      };

      const agentResult = await this.executeAgent(
        { ...config, agentConfig },
        currentVariables,
        context
      );

      executionHistory.push({
        iteration: iteration + 1,
        type: 'agent_decision',
        result: agentResult,
        timestamp: Date.now(),
      });

      // Parse agent decision
      const decision = this.parseOrchestrationDecision(agentResult.response, config.toolIds);

      await this.emitHybridEvent('orchestration_decision', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        iteration: iteration + 1,
        decision,
        agentResult,
      }, context);

      if (decision.complete) {
        this.logger.log(`Orchestration completed at iteration ${iteration + 1}`);
        break;
      }

      // Execute decided tools
      if (decision.toolsToExecute.length > 0) {
        for (const toolExecution of decision.toolsToExecute) {
          try {
            const toolConfig = config.toolConfigs[toolExecution.toolId] || {};
            const parameters = {
              ...toolConfig.parameters,
              ...toolExecution.parameters,
              ...currentVariables,
            };

            const toolResult = await this.executeTool(
              toolExecution.toolId,
              parameters,
              toolConfig,
              context
            );

            executionHistory.push({
              iteration: iteration + 1,
              type: 'tool_execution',
              toolId: toolExecution.toolId,
              result: toolResult,
              timestamp: Date.now(),
            });

            // Update variables with tool result
            currentVariables = { ...currentVariables, ...toolResult };

            // Update shared context
            if (config.coordination.shareContext) {
              await this.updateSharedContext(context, {
                [`iteration_${iteration + 1}_tool_${toolExecution.toolId}`]: toolResult,
              });
            }

          } catch (error) {
            this.logger.error(`Tool execution failed in orchestration: ${toolExecution.toolId}`, error);
            
            executionHistory.push({
              iteration: iteration + 1,
              type: 'tool_error',
              toolId: toolExecution.toolId,
              error: error.message,
              timestamp: Date.now(),
            });

            if (config.coordination.errorPropagation) {
              throw error;
            }
          }
        }
      }

      iteration++;
    }

    // Final agent summary
    const summaryPrompt = this.buildSummaryPrompt(executionHistory, currentVariables);
    const finalAgentResult = await this.executeAgent(
      { ...config, agentConfig: { ...config.agentConfig, systemPrompt: summaryPrompt } },
      currentVariables,
      context
    );

    const result = {
      pattern: 'multi-tool-orchestration',
      executionHistory,
      finalResult: currentVariables,
      finalSummary: finalAgentResult,
      totalIterations: iteration,
      maxIterations: config.maxIterations,
      timestamp: Date.now(),
    };

    await this.emitHybridEvent('orchestration_completed', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      result,
    }, context);

    return result;
  }

  private async executeAgent(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
  ): Promise<any> {
    await this.emitHybridEvent('agent_execution_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      agentId: config.agentId,
    }, context);

    try {
      // Get shared context if enabled
      let contextData = {};
      if (config.coordination.shareContext) {
        contextData = await this.getSharedContext(context);
      }

      const input = {
        ...variables,
        ...contextData,
        hybridContext: {
          executionPattern: config.executionPattern,
          availableTools: config.toolIds,
          nodeId: context.nodeId,
        },
      };

      const result = await this.agentsService.executeAgent(
        config.agentId,
        {
          input: input.message || input.query || JSON.stringify(input),
          systemPrompt: config.agentConfig.systemPrompt,
          maxTokens: config.agentConfig.maxTokens,
          temperature: config.agentConfig.temperature,
          variables: input,
        },
        context.organizationId
      );

      await this.emitHybridEvent('agent_execution_completed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        agentId: config.agentId,
        result,
      }, context);

      return result;

    } catch (error) {
      await this.emitHybridEvent('agent_execution_failed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        agentId: config.agentId,
        error: error.message,
      }, context);
      throw error;
    }
  }

  private async executeTool(
    toolId: string,
    parameters: Record<string, any>,
    toolConfig: any,
    context: HybridExecutionContext,
  ): Promise<any> {
    await this.emitHybridEvent('tool_execution_started', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      toolId,
      parameters,
    }, context);

    try {
      const timeoutMs = toolConfig.timeout || 30000;
      let result: any;

      if (toolConfig.retryPolicy?.enabled) {
        result = await this.executeWithRetry(
          () => this.toolsService.executeTool(
            toolId,
            parameters,
            {
              organizationId: context.organizationId,
              executorType: 'hybrid_workflow',
              executorId: context.executionId,
              sessionId: context.sessionId,
            }
          ),
          toolConfig.retryPolicy.maxRetries || 2,
          timeoutMs
        );
      } else {
        result = await Promise.race([
          this.toolsService.executeTool(
            toolId,
            parameters,
            {
              organizationId: context.organizationId,
              executorType: 'hybrid_workflow',
              executorId: context.executionId,
              sessionId: context.sessionId,
            }
          ),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Tool execution timeout')), timeoutMs)
          ),
        ]);
      }

      await this.emitHybridEvent('tool_execution_completed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        toolId,
        result,
      }, context);

      return result;

    } catch (error) {
      await this.emitHybridEvent('tool_execution_failed', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        toolId,
        error: error.message,
      }, context);
      throw error;
    }
  }

  private async executeWithRetry(
    operation: () => Promise<any>,
    maxRetries: number,
    timeoutMs: number,
  ): Promise<any> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await Promise.race([
          operation(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Operation timeout')), timeoutMs)
          ),
        ]);
      } catch (error) {
        lastError = error as Error;
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  private async executeWithParallelLimit(promises: Promise<any>[], limit: number): Promise<any[]> {
    const results: any[] = [];
    const executing: Promise<any>[] = [];

    for (let i = 0; i < promises.length; i++) {
      const promise = promises[i].then(result => {
        executing.splice(executing.indexOf(promise), 1);
        return result;
      });

      results.push(promise);
      executing.push(promise);

      if (executing.length >= limit) {
        await Promise.race(executing);
      }
    }

    return Promise.all(results);
  }

  private extractToolParameters(agentResult: any, toolIds: string[]): Record<string, any> {
    const parameters: Record<string, any> = {};

    // Try to parse structured output from agent
    try {
      const response = agentResult.response || '';
      
      // Look for JSON blocks
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[1]);
        if (parsed.toolParameters) {
          return parsed.toolParameters;
        }
      }

      // Look for tool-specific parameters in the response
      for (const toolId of toolIds) {
        const toolMatch = response.match(new RegExp(`${toolId}[:\\s]*({[^}]+})`, 'i'));
        if (toolMatch) {
          try {
            parameters[toolId] = JSON.parse(toolMatch[1]);
          } catch {
            parameters[toolId] = { query: toolMatch[1] };
          }
        } else {
          // Default parameters based on agent response
          parameters[toolId] = {
            query: response.substring(0, 200),
            context: agentResult,
          };
        }
      }
    } catch (error) {
      this.logger.warn('Failed to extract tool parameters from agent result', error);
      
      // Fallback: use agent result as context for all tools
      for (const toolId of toolIds) {
        parameters[toolId] = {
          query: agentResult.response?.substring(0, 200) || '',
          context: agentResult,
        };
      }
    }

    return parameters;
  }

  private buildOrchestrationPrompt(
    toolIds: string[],
    variables: Record<string, any>,
    history: any[],
    iteration: number,
  ): string {
    return `You are orchestrating a multi-tool workflow. 

Available tools: ${toolIds.join(', ')}
Current iteration: ${iteration + 1}
Current data: ${JSON.stringify(variables, null, 2)}

Execution history:
${history.map(h => `- ${h.type}: ${JSON.stringify(h.result || h.error)}`).join('\n')}

Decide what to do next. Respond with JSON:
{
  "complete": boolean,
  "reasoning": "explanation of decision",
  "toolsToExecute": [
    {
      "toolId": "tool_name",
      "parameters": { "key": "value" }
    }
  ]
}

If the task is complete, set "complete": true and provide a summary.`;
  }

  private buildSummaryPrompt(history: any[], finalVariables: Record<string, any>): string {
    return `Summarize the multi-tool orchestration execution:

Execution History:
${history.map(h => `- ${h.type}: ${JSON.stringify(h.result || h.error)}`).join('\n')}

Final Data:
${JSON.stringify(finalVariables, null, 2)}

Provide a comprehensive summary of what was accomplished.`;
  }

  private parseOrchestrationDecision(response: string, availableTools: string[]): {
    complete: boolean;
    reasoning?: string;
    toolsToExecute: Array<{ toolId: string; parameters: any }>;
  } {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/({[\s\S]*})/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[1]);
        return {
          complete: parsed.complete || false,
          reasoning: parsed.reasoning,
          toolsToExecute: parsed.toolsToExecute || [],
        };
      }
    } catch (error) {
      this.logger.warn('Failed to parse orchestration decision JSON', error);
    }

    // Fallback parsing
    const lowerResponse = response.toLowerCase();
    
    if (lowerResponse.includes('complete') || lowerResponse.includes('done') || lowerResponse.includes('finished')) {
      return { complete: true, toolsToExecute: [] };
    }

    // Look for tool mentions
    const toolsToExecute = [];
    for (const toolId of availableTools) {
      if (lowerResponse.includes(toolId.toLowerCase())) {
        toolsToExecute.push({
          toolId,
          parameters: { query: response.substring(0, 100) },
        });
      }
    }

    return {
      complete: toolsToExecute.length === 0,
      toolsToExecute,
    };
  }

  private async initializeSharedContext(
    context: HybridExecutionContext,
    variables: Record<string, any>,
  ): Promise<void> {
    const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
    
    await this.sessionsService.updateSessionMemory(context.sessionId, {
      temporaryData: {
        [contextKey]: {
          initialized: true,
          variables,
          timestamp: Date.now(),
        },
      },
    });
  }

  private async updateSharedContext(
    context: HybridExecutionContext,
    updates: Record<string, any>,
  ): Promise<void> {
    const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
    
    const session = await this.sessionsService.getSession(context.sessionId);
    const currentContext = session?.memory?.temporaryData?.[contextKey] || {};
    
    await this.sessionsService.updateSessionMemory(context.sessionId, {
      temporaryData: {
        [contextKey]: {
          ...currentContext,
          ...updates,
          lastUpdated: Date.now(),
        },
      },
    });
  }

  private async getSharedContext(context: HybridExecutionContext): Promise<Record<string, any>> {
    const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
    
    const session = await this.sessionsService.getSession(context.sessionId);
    return session?.memory?.temporaryData?.[contextKey] || {};
  }

  private async processSyncPoints(
    syncPoints: string[],
    context: HybridExecutionContext,
    data: any,
  ): Promise<void> {
    for (const syncPoint of syncPoints) {
      await this.emitHybridEvent('sync_point_reached', {
        executionId: context.executionId,
        nodeId: context.nodeId,
        syncPoint,
        data,
      }, context);

      // Wait for sync point acknowledgment (simplified)
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private shouldTriggerFallback(error: Error, conditions: string[]): boolean {
    const errorMessage = error.message.toLowerCase();
    
    return conditions.some(condition => {
      switch (condition) {
        case 'timeout':
          return errorMessage.includes('timeout');
        case 'error':
          return true; // Any error
        case 'low_confidence':
          return errorMessage.includes('confidence') || errorMessage.includes('uncertain');
        case 'resource_limit':
          return errorMessage.includes('limit') || errorMessage.includes('quota');
        default:
          return false;
      }
    });
  }

  private async executeFallback(
    config: HybridNodeConfig,
    variables: Record<string, any>,
    context: HybridExecutionContext,
    originalError: Error,
  ): Promise<any> {
    await this.emitHybridEvent('fallback_triggered', {
      executionId: context.executionId,
      nodeId: context.nodeId,
      originalError: originalError.message,
      fallbackAgent: config.fallback.fallbackAgent,
      fallbackTools: config.fallback.fallbackTools,
    }, context);

    try {
      // Use fallback agent if specified
      if (config.fallback.fallbackAgent) {
        const fallbackConfig = {
          ...config,
          agentId: config.fallback.fallbackAgent,
          toolIds: config.fallback.fallbackTools,
        };
        
        return await this.executeAgentFirstPattern(fallbackConfig, variables, context);
      }

      // Use fallback tools only
      if (config.fallback.fallbackTools.length > 0) {
        const toolResults = [];
        
        for (const toolId of config.fallback.fallbackTools) {
          try {
            const result = await this.executeTool(toolId, variables, {}, context);
            toolResults.push({ toolId, result, success: true });
          } catch (error) {
            toolResults.push({ toolId, error: error.message, success: false });
          }
        }

        return {
          pattern: 'fallback',
          toolResults,
          originalError: originalError.message,
          timestamp: Date.now(),
        };
      }

      throw originalError;

    } catch (fallbackError) {
      this.logger.error('Fallback execution also failed', fallbackError);
      throw new Error(`Original error: ${originalError.message}. Fallback error: ${fallbackError.message}`);
    }
  }

  private async emitHybridEvent(
    eventType: string,
    data: any,
    context: HybridExecutionContext,
  ): Promise<void> {
    // Emit to workflow room
    await this.apixGateway.emitToRoom(
      `workflow:${context.workflowId}`,
      eventType,
      {
        ...data,
        organizationId: context.organizationId,
        timestamp: Date.now(),
      }
    );

    // Emit to organization
    await this.apixGateway.emitToOrganization(
      context.organizationId,
      eventType,
      {
        ...data,
        workflowId: context.workflowId,
        timestamp: Date.now(),
      }
    );

    // Emit internal event
    this.eventEmitter.emit(`hybrid.${eventType}`, {
      ...data,
      context,
    });
  }

  private async storeExecutionAnalytics(
    context: HybridExecutionContext,
    config: HybridNodeConfig,
    result: any,
    duration: number,
  ): Promise<void> {
    try {
      await this.prisma.hybridExecutionAnalytics.create({
        data: {
          executionId: context.executionId,
          nodeId: context.nodeId,
          workflowId: context.workflowId,
          organizationId: context.organizationId,
          agentId: config.agentId,
          toolIds: config.toolIds,
          executionPattern: config.executionPattern,
          duration,
          success: !!result,
          errorMessage: result?.error || null,
          resultSize: JSON.stringify(result).length,
          metadata: {
            maxIterations: config.maxIterations,
            toolCount: config.toolIds.length,
            contextSharing: config.coordination.shareContext,
            fallbackEnabled: config.fallback.enabled,
          },
        },
      });
    } catch (error) {
      this.logger.error('Failed to store execution analytics', error);
    }
  }

  // Public methods for monitoring and management

  getActiveExecutions(): Array<{
    executionKey: string;
    status: string;
    startTime: number;
    duration?: number;
  }> {
    return Array.from(this.activeExecutions.entries()).map(([key, execution]) => ({
      executionKey: key,
      status: execution.status,
      startTime: execution.startTime,
      duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime,
    }));
  }

  async cancelExecution(executionKey: string): Promise<boolean> {
    const execution = this.activeExecutions.get(executionKey);
    if (execution && execution.status === 'running') {
      execution.status = 'cancelled';
      execution.endTime = Date.now();
      
      // Emit cancellation event
      await this.emitHybridEvent('hybrid_execution_cancelled', {
        executionKey,
        reason: 'manual_cancellation',
      }, execution.context);
      
      return true;
    }
    return false;
  }

  async getExecutionAnalytics(organizationId: string, timeRange?: { start: Date; end: Date }) {
    const where: any = { organizationId };
    
    if (timeRange) {
      where.createdAt = {
        gte: timeRange.start,
        lte: timeRange.end,
      };
    }

    const analytics = await this.prisma.hybridExecutionAnalytics.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: 1000,
    });

    return {
      totalExecutions: analytics.length,
      successRate: analytics.filter(a => a.success).length / analytics.length,
      averageDuration: analytics.reduce((sum, a) => sum + a.duration, 0) / analytics.length,
      patternDistribution: this.calculatePatternDistribution(analytics),
      errorAnalysis: this.analyzeErrors(analytics),
      performanceMetrics: this.calculatePerformanceMetrics(analytics),
    };
  }

  private calculatePatternDistribution(analytics: any[]) {
    const distribution = {};
    analytics.forEach(a => {
      distribution[a.executionPattern] = (distribution[a.executionPattern] || 0) + 1;
    });
    return distribution;
  }

  private analyzeErrors(analytics: any[]) {
    const errors = analytics.filter(a => !a.success);
    const errorTypes = {};
    
    errors.forEach(error => {
      const errorType = this.categorizeError(error.errorMessage);
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });

    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: errors.length / analytics.length,
    };
  }

  private categorizeError(errorMessage: string): string {
    if (!errorMessage) return 'unknown';
    
    const message = errorMessage.toLowerCase();
    if (message.includes('timeout')) return 'timeout';
    if (message.includes('not found')) return 'not_found';
    if (message.includes('permission')) return 'permission';
    if (message.includes('rate limit')) return 'rate_limit';
    if (message.includes('network')) return 'network';
    
    return 'other';
  }

  private calculatePerformanceMetrics(analytics: any[]) {
    const durations = analytics.map(a => a.duration).sort((a, b) => a - b);
    
    return {
      min: durations[0] || 0,
      max: durations[durations.length - 1] || 0,
      median: durations[Math.floor(durations.length / 2)] || 0,
      p95: durations[Math.floor(durations.length * 0.95)] || 0,
      p99: durations[Math.floor(durations.length * 0.99)] || 0,
    };
  }
}