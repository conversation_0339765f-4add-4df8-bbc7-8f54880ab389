import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { WorkflowsService } from './workflows.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { TenantGuard } from '../auth/tenant.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  ExecuteWorkflowDto,
  WorkflowFilterDto,
  ScheduleWorkflowDto,
  WorkflowTemplateDto,
  WorkflowImportDto,
  WorkflowExportDto,
  WorkflowResponse,
  WorkflowExecutionResponse,
  WorkflowAnalyticsResponse,
} from './dto/workflow.dto';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FlowControllerService } from './services/flow-controller.service';
import { NodeRegistryService } from './services/node-registry.service';

@ApiTags('Workflows')
@Controller('workflows')
@UseGuards(JwtAuthGuard, TenantGuard)
@ApiBearerAuth()
export class WorkflowsController {
  constructor(
    private readonly workflowsService: WorkflowsService,
    private readonly flowController: FlowControllerService,
    private readonly nodeRegistry: NodeRegistryService,
  ) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Create a new workflow' })
  @ApiResponse({ status: 201, description: 'Workflow created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid workflow definition' })
  async create(
    @Request() req: any,
    @Body() createWorkflowDto: CreateWorkflowDto,
  ): Promise<WorkflowResponse> {
    return this.workflowsService.create(
      req.user.id,
      req.organizationId,
      createWorkflowDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all workflows' })
  @ApiResponse({ status: 200, description: 'Workflows retrieved successfully' })
  async findAll(
    @Request() req: any,
    @Query() filters: WorkflowFilterDto,
  ): Promise<WorkflowResponse[]> {
    return this.workflowsService.findAll(req.organizationId, filters);
  }

  @Get('templates')
  @ApiOperation({ summary: 'Get workflow templates' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async getTemplates(@Request() req: any) {
    // This would be implemented to return predefined workflow templates
    return {
      templates: [
        {
          id: 'data-processing',
          name: 'Data Processing Pipeline',
          description: 'Process and transform data using multiple tools',
          category: 'Data',
          definition: {
            nodes: [
              {
                id: 'start',
                type: 'tool',
                name: 'Data Ingestion',
                config: { toolType: 'data_fetch' },
                position: { x: 100, y: 100 },
                inputs: [],
                outputs: ['data'],
              },
              {
                id: 'process',
                type: 'tool',
                name: 'Data Processing',
                config: { toolType: 'data_transform' },
                position: { x: 300, y: 100 },
                inputs: ['data'],
                outputs: ['processed_data'],
              },
            ],
            edges: [
              {
                id: 'start-process',
                source: 'start',
                target: 'process',
              },
            ],
            triggers: [{ type: 'manual', config: {} }],
            settings: {},
          },
        },
        {
          id: 'hybrid-agent-tool',
          name: 'Hybrid Agent-Tool Workflow',
          description: 'Combine agent intelligence with tool capabilities',
          category: 'Hybrid',
          definition: {
            nodes: [
              {
                id: 'start',
                type: 'input',
                name: 'Start',
                config: {},
                position: { x: 100, y: 100 },
                inputs: [],
                outputs: ['input'],
              },
              {
                id: 'hybrid',
                type: 'hybrid',
                name: 'Hybrid Processing',
                config: { 
                  executionPattern: 'agent-first',
                  agentId: 'placeholder',
                  toolIds: ['placeholder'],
                },
                position: { x: 300, y: 100 },
                inputs: ['input'],
                outputs: ['result'],
              },
              {
                id: 'end',
                type: 'output',
                name: 'End',
                config: {},
                position: { x: 500, y: 100 },
                inputs: ['result'],
                outputs: [],
              },
            ],
            edges: [
              {
                id: 'start-hybrid',
                source: 'start',
                target: 'hybrid',
              },
              {
                id: 'hybrid-end',
                source: 'hybrid',
                target: 'end',
              },
            ],
            triggers: [{ type: 'manual', config: {} }],
            settings: {},
          },
        },
      ],
    };
  }

  @Get('analytics')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Get workflow analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getAnalytics(
    @Request() req: any,
    @Query('days') days = '30',
  ): Promise<WorkflowAnalyticsResponse> {
    // This would be implemented to return workflow analytics
    return {
      totalWorkflows: 0,
      activeWorkflows: 0,
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      avgExecutionTime: 0,
      executionsByStatus: {},
      executionsByDay: [],
      topWorkflows: [],
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get workflow by ID' })
  @ApiResponse({ status: 200, description: 'Workflow retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  async findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<WorkflowResponse> {
    return this.workflowsService.findOne(id, req.organizationId);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Update workflow' })
  @ApiResponse({ status: 200, description: 'Workflow updated successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  async update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
  ): Promise<WorkflowResponse> {
    return this.workflowsService.update(
      id,
      req.organizationId,
      req.user.id,
      updateWorkflowDto,
    );
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete workflow' })
  @ApiResponse({ status: 204, description: 'Workflow deleted successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  async remove(@Request() req: any, @Param('id') id: string): Promise<void> {
    return this.workflowsService.remove(id, req.organizationId);
  }

  @Post(':id/execute')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER, Role.VIEWER)
  @ApiOperation({ summary: 'Execute workflow' })
  @ApiResponse({ status: 200, description: 'Workflow execution started' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  async execute(
    @Request() req: any,
    @Param('id') id: string,
    @Body() executeWorkflowDto: ExecuteWorkflowDto,
  ) {
    return this.workflowsService.execute(
      id,
      req.organizationId,
      req.user.id,
      executeWorkflowDto,
    );
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get workflow executions' })
  @ApiResponse({ status: 200, description: 'Executions retrieved successfully' })
  async getExecutions(
    @Request() req: any,
    @Param('id') id: string,
    @Query('limit') limit = '50',
  ): Promise<WorkflowExecutionResponse[]> {
    return this.workflowsService.getExecutions(
      id,
      req.organizationId,
      parseInt(limit),
    );
  }

  @Get(':id/executions/:executionId')
  @ApiOperation({ summary: 'Get specific workflow execution' })
  @ApiResponse({ status: 200, description: 'Execution retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Execution not found' })
  async getExecution(
    @Request() req: any,
    @Param('id') id: string,
    @Param('executionId') executionId: string,
  ): Promise<WorkflowExecutionResponse> {
    return this.workflowsService.getExecution(executionId, req.organizationId);
  }

  @Post(':id/executions/:executionId/cancel')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Cancel workflow execution' })
  @ApiResponse({ status: 204, description: 'Execution cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Execution not found' })
  async cancelExecution(
    @Request() req: any,
    @Param('id') id: string,
    @Param('executionId') executionId: string,
  ): Promise<void> {
    return this.workflowsService.cancelExecution(executionId, req.organizationId);
  }

  @Post(':id/executions/:executionId/pause')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Pause workflow execution' })
  @ApiResponse({ status: 204, description: 'Execution paused successfully' })
  @ApiResponse({ status: 404, description: 'Execution not found' })
  async pauseExecution(
    @Request() req: any,
    @Param('id') id: string,
    @Param('executionId') executionId: string,
  ): Promise<void> {
    return this.workflowsService.pauseExecution(executionId, req.organizationId);
  }

  @Post(':id/executions/:executionId/resume')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Resume workflow execution' })
  @ApiResponse({ status: 204, description: 'Execution resumed successfully' })
  @ApiResponse({ status: 404, description: 'Execution not found' })
  async resumeExecution(
    @Request() req: any,
    @Param('id') id: string,
    @Param('executionId') executionId: string,
  ): Promise<void> {
    return this.workflowsService.resumeExecution(executionId, req.organizationId);
  }

  @Get(':id/executions/:executionId/status')
  @ApiOperation({ summary: 'Get workflow execution status' })
  @ApiResponse({ status: 200, description: 'Execution status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Execution not found' })
  async getExecutionStatus(
    @Request() req: any,
    @Param('id') id: string,
    @Param('executionId') executionId: string,
  ): Promise<any> {
    return this.workflowsService.getExecutionStatus(executionId, req.organizationId);
  }

  @Post(':id/schedule')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Schedule workflow execution' })
  @ApiResponse({ status: 201, description: 'Workflow scheduled successfully' })
  async scheduleWorkflow(
    @Request() req: any,
    @Param('id') id: string,
    @Body() scheduleDto: ScheduleWorkflowDto,
  ) {
    // This would be implemented to schedule workflow executions
    return {
      message: 'Workflow scheduled successfully',
      scheduleId: 'placeholder',
    };
  }

  @Get(':id/schedule')
  @ApiOperation({ summary: 'Get workflow schedule' })
  @ApiResponse({ status: 200, description: 'Schedule retrieved successfully' })
  async getSchedule(@Request() req: any, @Param('id') id: string) {
    // This would be implemented to return workflow schedule
    return {
      schedules: [],
    };
  }

  @Delete(':id/schedule/:scheduleId')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete workflow schedule' })
  @ApiResponse({ status: 204, description: 'Schedule deleted successfully' })
  async deleteSchedule(
    @Request() req: any,
    @Param('id') id: string,
    @Param('scheduleId') scheduleId: string,
  ): Promise<void> {
    // This would be implemented to delete workflow schedule
  }

  @Post(':id/duplicate')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Duplicate workflow' })
  @ApiResponse({ status: 201, description: 'Workflow duplicated successfully' })
  async duplicate(
    @Request() req: any,
    @Param('id') id: string,
    @Body() body: { name?: string },
  ): Promise<WorkflowResponse> {
    const originalWorkflow = await this.workflowsService.findOne(id, req.organizationId);
    
    const duplicateDto: CreateWorkflowDto = {
      name: body.name || `${originalWorkflow.name} (Copy)`,
      description: originalWorkflow.description,
      definition: originalWorkflow.definition,
      tags: originalWorkflow.tags,
    };

    return this.workflowsService.create(req.user.id, req.organizationId, duplicateDto);
  }

  @Post('import')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Import workflow' })
  @ApiResponse({ status: 201, description: 'Workflow imported successfully' })
  async import(
    @Request() req: any,
    @Body() importDto: WorkflowImportDto,
  ): Promise<WorkflowResponse> {
    // This would be implemented to import workflows from JSON/YAML
    let workflowData;
    
    try {
      if (importDto.format === 'json') {
        workflowData = JSON.parse(importDto.data);
      } else {
        // YAML parsing would be implemented here
        throw new Error('YAML import not yet implemented');
      }
    } catch (error) {
      throw new Error(`Invalid ${importDto.format} format: ${error.message}`);
    }

    return this.workflowsService.create(req.user.id, req.organizationId, workflowData);
  }

  @Post(':id/export')
  @ApiOperation({ summary: 'Export workflow' })
  @ApiResponse({ status: 200, description: 'Workflow exported successfully' })
  async export(
    @Request() req: any,
    @Param('id') id: string,
    @Body() exportDto: WorkflowExportDto,
  ) {
    const workflow = await this.workflowsService.findOne(id, req.organizationId);
    
    const exportData = {
      name: workflow.name,
      description: workflow.description,
      definition: workflow.definition,
      tags: workflow.tags,
      ...(exportDto.includeMetadata && {
        metadata: {
          version: workflow.version,
          createdAt: workflow.createdAt,
          updatedAt: workflow.updatedAt,
          creator: workflow.creator,
        },
      }),
    };

    if (exportDto.format === 'json') {
      return {
        format: 'json',
        data: JSON.stringify(exportData, null, 2),
        filename: `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`,
      };
    } else {
      // YAML export would be implemented here
      return {
        format: 'yaml',
        data: '# YAML export not yet implemented',
        filename: `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.yaml`,
      };
    }
  }

  @Get('node-types')
  @ApiOperation({ summary: 'Get available node types' })
  @ApiResponse({ status: 200, description: 'Node types retrieved successfully' })
  async getNodeTypes(@Request() req: any) {
    return this.workflowsService.getAvailableNodeTypes(req.organizationId);
  }

  @Post('templates')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Create workflow template' })
  @ApiResponse({ status: 201, description: 'Template created successfully' })
  async createTemplate(
    @Request() req: any,
    @Body() templateDto: WorkflowTemplateDto,
  ) {
    // This would be implemented to create workflow templates
    return {
      message: 'Template created successfully',
      templateId: 'placeholder',
    };
  }

  @Post('templates/:templateId/use')
  @UseGuards(RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiOperation({ summary: 'Create workflow from template' })
  @ApiResponse({ status: 201, description: 'Workflow created from template' })
  async useTemplate(
    @Request() req: any,
    @Param('templateId') templateId: string,
    @Body() body: { name: string; variables?: Record<string, any> },
  ): Promise<WorkflowResponse> {
    // This would be implemented to create workflows from templates
    // For now, return a placeholder
    const templateWorkflow: CreateWorkflowDto = {
      name: body.name,
      description: 'Created from template',
      definition: {
        nodes: [],
        edges: [],
        triggers: [],
        settings: {},
      },
    };

    return this.workflowsService.create(req.user.id, req.organizationId, templateWorkflow);
  }
}