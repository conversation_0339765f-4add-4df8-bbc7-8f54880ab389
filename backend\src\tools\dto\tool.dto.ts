import { IsString, IsOptional, IsEnum, IsObject, IsArray, IsBoolean, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ToolType {
  FUNCTION_CALL = 'FUNCTION_CALL',
  RAG = 'RAG',
  API_FETCH = 'API_FETCH',
  BROWSER_AUTOMATION = 'BROWSER_AUTOMATION',
  DATABASE = 'DATABASE',
  CUSTOM_LOGIC = 'CUSTOM_LOGIC',
}

export enum ToolStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DEPRECATED = 'DEPRECATED',
}

export class ToolParameterDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  defaultValue?: any;

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  validation?: Record<string, any>;
}

export class ToolConfigDto {
  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  apiConfig?: {
    baseUrl?: string;
    headers?: Record<string, string>;
    authentication?: {
      type: 'bearer' | 'basic' | 'api_key';
      credentials: Record<string, string>;
    };
  };

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  browserConfig?: {
    headless?: boolean;
    timeout?: number;
    viewport?: { width: number; height: number };
  };

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  databaseConfig?: {
    connectionString?: string;
    type: 'postgresql' | 'mysql' | 'mongodb';
    ssl?: boolean;
  };

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  ragConfig?: {
    embeddingModel?: string;
    chunkSize?: number;
    overlap?: number;
    vectorStore?: string;
  };
}

export class CreateToolDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ enum: ToolType })
  @IsEnum(ToolType)
  type: ToolType;

  @ApiPropertyOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ToolParameterDto)
  @IsOptional()
  parameters?: ToolParameterDto[];

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  config?: ToolConfigDto;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  version?: string;
}

export class UpdateToolDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ enum: ToolStatus })
  @IsEnum(ToolStatus)
  @IsOptional()
  status?: ToolStatus;

  @ApiPropertyOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ToolParameterDto)
  @IsOptional()
  parameters?: ToolParameterDto[];

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  config?: ToolConfigDto;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  version?: string;
}

export class ExecuteToolDto {
  @ApiProperty()
  @IsObject()
  input: Record<string, any>;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  sessionId?: string;

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  timeout?: number;
}

export class ToolSearchDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  query?: string;

  @ApiPropertyOptional({ enum: ToolType })
  @IsEnum(ToolType)
  @IsOptional()
  type?: ToolType;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  sortOrder?: 'asc' | 'desc';
}

export class ImportToolDto {
  @ApiProperty()
  @IsString()
  source: 'url' | 'file' | 'template';

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  url?: string;

  @ApiPropertyOptional()
  @IsObject()
  @IsOptional()
  toolData?: Record<string, any>;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  templateId?: string;
}

export class ToolExecutionResultDto {
  @ApiProperty()
  success: boolean;

  @ApiPropertyOptional()
  data?: any;

  @ApiPropertyOptional()
  error?: string;

  @ApiProperty()
  executionTime: number;

  @ApiProperty()
  timestamp: Date;

  @ApiPropertyOptional()
  metadata?: Record<string, any>;
}