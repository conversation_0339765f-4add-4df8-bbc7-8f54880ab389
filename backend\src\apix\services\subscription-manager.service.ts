import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject } from "@nestjs/common";
import { Cache } from "cache-manager";
import { ChannelType } from "../dto/apix.dto";
import { ConnectionManagerService } from "./connection-manager.service";

interface ChannelPermission {
  read: boolean;
  write: boolean;
  admin: boolean;
  roles?: string[];
  users?: string[];
  organizations?: string[];
}

interface SubscriptionFilter {
  eventTypes?: string[];
  priority?: string[];
  metadata?: Record<string, any>;
}

@Injectable()
export class SubscriptionManagerService {
  private readonly logger = new Logger(SubscriptionManagerService.name);
  private channelPermissions = new Map<string, ChannelPermission>();
  private subscriptions = new Map<string, Map<string, SubscriptionFilter>>();

  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private connectionManager: ConnectionManagerService,
  ) {
    this.initializeDefaultChannels();
  }

  async subscribeToChannel(
    socketId: string,
    channelName: string,
    filters: SubscriptionFilter = {},
  ): Promise<boolean> {
    const connection = this.connectionManager.getConnection(socketId);
    if (!connection) {
      this.logger.warn(
        `Subscription failed: Connection not found for ${socketId}`,
      );
      return false;
    }

    // Check permissions
    if (
      !(await this.hasChannelPermission(
        connection.userId,
        connection.organizationId,
        channelName,
        "read",
      ))
    ) {
      this.logger.warn(
        `Subscription denied: No read permission for ${channelName}`,
      );
      return false;
    }

    // Get or create channel
    const channel = await this.getOrCreateChannel(
      channelName,
      connection.organizationId,
    );
    if (!channel) {
      this.logger.warn(
        `Subscription failed: Channel ${channelName} not found or created`,
      );
      return false;
    }

    // Create subscription record
    await this.prisma.apiXSubscription.upsert({
      where: {
        connectionId_channelId: {
          connectionId: connection.id,
          channelId: channel.id,
        },
      },
      update: {
        filters,
        isActive: true,
      },
      create: {
        connectionId: connection.id,
        channelId: channel.id,
        filters,
        isActive: true,
      },
    });

    // Update in-memory subscription
    if (!this.subscriptions.has(socketId)) {
      this.subscriptions.set(socketId, new Map());
    }
    this.subscriptions.get(socketId)!.set(channelName, filters);

    // Update connection channels
    await this.connectionManager.addChannelToConnection(socketId, channelName);

    // Update channel subscriber count
    await this.prisma.apiXChannel.update({
      where: { id: channel.id },
      data: {
        subscribers: {
          increment: 1,
        },
      },
    });

    // Cache subscription
    await this.cacheSubscription(socketId, channelName, filters);

    this.logger.log(`Subscribed ${socketId} to channel ${channelName}`);
    return true;
  }

  async unsubscribeFromChannel(
    socketId: string,
    channelName: string,
  ): Promise<boolean> {
    const connection = this.connectionManager.getConnection(socketId);
    if (!connection) return false;

    const channel = await this.prisma.apiXChannel.findUnique({
      where: { name: channelName },
    });

    if (!channel) return false;

    // Remove subscription record
    await this.prisma.apiXSubscription.updateMany({
      where: {
        connectionId: connection.id,
        channelId: channel.id,
      },
      data: {
        isActive: false,
      },
    });

    // Update in-memory subscription
    const connectionSubs = this.subscriptions.get(socketId);
    if (connectionSubs) {
      connectionSubs.delete(channelName);
    }

    // Update connection channels
    await this.connectionManager.removeChannelFromConnection(
      socketId,
      channelName,
    );

    // Update channel subscriber count
    await this.prisma.apiXChannel.update({
      where: { id: channel.id },
      data: {
        subscribers: {
          decrement: 1,
        },
      },
    });

    // Remove from cache
    await this.cacheManager.del(`subscription:${socketId}:${channelName}`);

    this.logger.log(`Unsubscribed ${socketId} from channel ${channelName}`);
    return true;
  }

  async unsubscribeAll(socketId: string): Promise<void> {
    const connection = this.connectionManager.getConnection(socketId);
    if (!connection) return;

    // Get all subscriptions for this connection
    const subscriptions = await this.prisma.apiXSubscription.findMany({
      where: {
        connectionId: connection.id,
        isActive: true,
      },
      include: {
        channel: true,
      },
    });

    // Deactivate all subscriptions
    await this.prisma.apiXSubscription.updateMany({
      where: {
        connectionId: connection.id,
      },
      data: {
        isActive: false,
      },
    });

    // Update channel subscriber counts
    for (const sub of subscriptions) {
      await this.prisma.apiXChannel.update({
        where: { id: sub.channelId },
        data: {
          subscribers: {
            decrement: 1,
          },
        },
      });
    }

    // Clear in-memory subscriptions
    this.subscriptions.delete(socketId);

    // Clear cache
    for (const sub of subscriptions) {
      await this.cacheManager.del(
        `subscription:${socketId}:${sub.channel.name}`,
      );
    }

    this.logger.log(`Unsubscribed ${socketId} from all channels`);
  }

  getSubscriptions(socketId: string): Map<string, SubscriptionFilter> {
    return this.subscriptions.get(socketId) || new Map();
  }

  async getChannelSubscribers(channelName: string): Promise<string[]> {
    const channel = await this.prisma.apiXChannel.findUnique({
      where: { name: channelName },
      include: {
        subscriptions: {
          where: { isActive: true },
          include: {
            connection: true,
          },
        },
      },
    });

    if (!channel) return [];

    return channel.subscriptions
      .map((sub) => sub.connection.sessionId)
      .filter(Boolean);
  }

  async createChannel(
    name: string,
    type: ChannelType,
    organizationId?: string,
    permissions: ChannelPermission = { read: true, write: false, admin: false },
    metadata: Record<string, any> = {},
  ): Promise<boolean> {
    try {
      const channel = await this.prisma.apiXChannel.create({
        data: {
          name,
          type,
          organizationId,
          permissions,
          metadata,
        },
      });

      this.channelPermissions.set(name, permissions);
      await this.cacheManager.set(`channel:${name}`, channel, 3600);

      this.logger.log(`Channel created: ${name}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to create channel ${name}:`, error);
      return false;
    }
  }

  async updateChannelPermissions(
    channelName: string,
    permissions: ChannelPermission,
  ): Promise<boolean> {
    try {
      await this.prisma.apiXChannel.update({
        where: { name: channelName },
        data: { permissions },
      });

      this.channelPermissions.set(channelName, permissions);
      await this.cacheManager.del(`channel:${channelName}`);

      this.logger.log(`Channel permissions updated: ${channelName}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to update channel permissions ${channelName}:`,
        error,
      );
      return false;
    }
  }

  async deleteChannel(channelName: string): Promise<boolean> {
    try {
      // First unsubscribe all connections
      const channel = await this.prisma.apiXChannel.findUnique({
        where: { name: channelName },
        include: {
          subscriptions: {
            where: { isActive: true },
          },
        },
      });

      if (channel) {
        // Deactivate all subscriptions
        await this.prisma.apiXSubscription.updateMany({
          where: { channelId: channel.id },
          data: { isActive: false },
        });

        // Delete the channel
        await this.prisma.apiXChannel.update({
          where: { id: channel.id },
          data: { isActive: false },
        });
      }

      this.channelPermissions.delete(channelName);
      await this.cacheManager.del(`channel:${channelName}`);

      this.logger.log(`Channel deleted: ${channelName}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete channel ${channelName}:`, error);
      return false;
    }
  }

  async getChannelStats(organizationId?: string): Promise<{
    totalChannels: number;
    channelsByType: Record<ChannelType, number>;
    totalSubscriptions: number;
    activeSubscriptions: number;
    averageSubscribersPerChannel: number;
    topChannelsByActivity: Array<{ name: string; subscribers: number }>;
  }> {
    const where = organizationId ? { organizationId } : {};

    const [channels, subscriptions] = await Promise.all([
      this.prisma.apiXChannel.findMany({
        where: { ...where, isActive: true },
        orderBy: { subscribers: "desc" },
        take: 10,
      }),
      this.prisma.apiXSubscription.findMany({
        where: {
          channel: where,
        },
      }),
    ]);

    const allChannels = await this.prisma.apiXChannel.findMany({
      where: { ...where, isActive: true },
    });

    const stats = {
      totalChannels: allChannels.length,
      channelsByType: {} as Record<ChannelType, number>,
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter((s) => s.isActive).length,
      averageSubscribersPerChannel:
        allChannels.length > 0
          ? allChannels.reduce((sum, ch) => sum + ch.subscribers, 0) /
            allChannels.length
          : 0,
      topChannelsByActivity: channels.map((ch) => ({
        name: ch.name,
        subscribers: ch.subscribers,
      })),
    };

    Object.values(ChannelType).forEach((type) => {
      stats.channelsByType[type] = 0;
    });

    for (const channel of allChannels) {
      stats.channelsByType[channel.type as ChannelType]++;
    }

    return stats;
  }

  private async hasChannelPermission(
    userId: string | undefined,
    organizationId: string,
    channelName: string,
    permission: "read" | "write" | "admin",
  ): Promise<boolean> {
    // System channels are always accessible
    if (channelName.startsWith("system:")) {
      return true;
    }

    // Organization channels require organization membership
    if (channelName.startsWith("org:")) {
      const orgId = channelName.split(":")[1];
      return orgId === organizationId;
    }

    // User channels require user ownership
    if (channelName.startsWith("user:")) {
      const targetUserId = channelName.split(":")[1];
      return targetUserId === userId;
    }

    // Check stored permissions
    const permissions = this.channelPermissions.get(channelName);
    if (!permissions) {
      // Try to load from database
      const channel = await this.prisma.apiXChannel.findUnique({
        where: { name: channelName },
      });

      if (channel) {
        const perms = channel.permissions as ChannelPermission;
        this.channelPermissions.set(channelName, perms);
        return perms[permission] || false;
      }

      return false;
    }

    // Check basic permission
    if (permissions[permission]) {
      return true;
    }

    // Check role-based permissions
    if (permissions.roles && userId) {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (user && permissions.roles.includes(user.role)) {
        return true;
      }
    }

    // Check user-specific permissions
    if (permissions.users && userId && permissions.users.includes(userId)) {
      return true;
    }

    // Check organization-specific permissions
    if (
      permissions.organizations &&
      permissions.organizations.includes(organizationId)
    ) {
      return true;
    }

    return false;
  }

  private async getOrCreateChannel(
    channelName: string,
    organizationId?: string,
  ): Promise<any> {
    // Try cache first
    const cached = await this.cacheManager.get(`channel:${channelName}`);
    if (cached) return cached;

    // Try database
    let channel = await this.prisma.apiXChannel.findUnique({
      where: { name: channelName },
    });

    if (!channel) {
      // Auto-create certain channel types
      const type = this.inferChannelType(channelName);
      if (type) {
        channel = await this.prisma.apiXChannel.create({
          data: {
            name: channelName,
            type,
            organizationId:
              type === ChannelType.ORGANIZATION ? organizationId : undefined,
            permissions: this.getDefaultPermissions(type),
          },
        });
      }
    }

    if (channel) {
      await this.cacheManager.set(`channel:${channelName}`, channel, 3600);
    }

    return channel;
  }

  private inferChannelType(channelName: string): ChannelType | null {
    if (channelName.startsWith("agent:")) return ChannelType.AGENT_EVENTS;
    if (channelName.startsWith("tool:")) return ChannelType.TOOL_EVENTS;
    if (channelName.startsWith("workflow:")) return ChannelType.WORKFLOW_EVENTS;
    if (channelName.startsWith("provider:")) return ChannelType.PROVIDER_EVENTS;
    if (channelName.startsWith("system:")) return ChannelType.SYSTEM_EVENTS;
    if (channelName.startsWith("session:")) return ChannelType.SESSION_EVENTS;
    if (channelName.startsWith("user:")) return ChannelType.PRIVATE_USER;
    if (channelName.startsWith("org:")) return ChannelType.ORGANIZATION;
    return ChannelType.PUBLIC;
  }

  private getDefaultPermissions(type: ChannelType): ChannelPermission {
    switch (type) {
      case ChannelType.PRIVATE_USER:
        return { read: false, write: false, admin: false, users: [] };
      case ChannelType.ORGANIZATION:
        return { read: true, write: false, admin: false };
      case ChannelType.SYSTEM_EVENTS:
        return { read: true, write: false, admin: false };
      default:
        return { read: true, write: false, admin: false };
    }
  }

  private async cacheSubscription(
    socketId: string,
    channelName: string,
    filters: SubscriptionFilter,
  ): Promise<void> {
    await this.cacheManager.set(
      `subscription:${socketId}:${channelName}`,
      filters,
      3600,
    );
  }

  private async initializeDefaultChannels(): Promise<void> {
    const defaultChannels = [
      { name: "system:alerts", type: ChannelType.SYSTEM_EVENTS },
      { name: "system:maintenance", type: ChannelType.SYSTEM_EVENTS },
      { name: "system:health", type: ChannelType.SYSTEM_EVENTS },
    ];

    for (const channel of defaultChannels) {
      await this.createChannel(channel.name, channel.type, undefined, {
        read: true,
        write: false,
        admin: false,
      });
    }
  }
}
