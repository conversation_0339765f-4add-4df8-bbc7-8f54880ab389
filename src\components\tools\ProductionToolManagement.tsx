"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Search,
  Plus,
  Filter,
  Play,
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  Zap,
  Database,
  Globe,
  Code,
  Bot,
  Wrench,
  TrendingUp,
  Activity,
  Users,
  DollarSign,
  Timer,
  RefreshCw,
  Download,
  Upload,
  Eye,
  Edit,
  Trash2,
  Copy,
  Star,
  Tag,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import apiClient from '@/lib/api-client';

// Real types based on backend schema
interface ToolDefinition {
  id: string;
  name: string;
  description?: string;
  type: 'FUNCTION_CALL' | 'RAG' | 'API_FETCH' | 'BROWSER_AUTOMATION' | 'DATABASE' | 'CUSTOM_LOGIC';
  category: string;
  config: Record<string, any>;
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  timeout: number;
  retryPolicy: Record<string, any>;
  cacheStrategy: 'INPUT_HASH' | 'TIME_BASED' | 'CUSTOM';
  cacheTTL: number;
  version: string;
  tags: string[];
  documentation?: string;
  examples: any[];
  isPublic: boolean;
  isActive: boolean;
  dependencies: string[];
  requirements: Record<string, any>;
  usageCount: number;
  successRate: number;
  avgLatency: number;
  createdBy: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  organizationId?: string;
  organization?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  _count: {
    executions: number;
    versions: number;
  };
}

interface ToolExecution {
  id: string;
  toolId?: string;
  tool?: {
    name: string;
    type: string;
  };
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'TIMEOUT';
  input: Record<string, any>;
  output?: Record<string, any>;
  error?: string;
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  retryCount: number;
  tokensUsed: number;
  cost: number;
  memoryUsed?: number;
  cpuTime?: number;
  metadata: Record<string, any>;
  traceId?: string;
  cached: boolean;
  cacheKey?: string;
  executorType: string;
  executorId?: string;
  sessionId?: string;
  organizationId: string;
  createdAt: string;
}

interface ToolAnalytics {
  totalExecutions: number;
  successRate: number;
  errorRate: number;
  avgDuration: number;
  totalCost: number;
  cacheHitRate: number;
  executionsByDay: Array<{
    date: string;
    executions: number;
    averageTime: number;
  }>;
  errorDistribution: Record<string, number>;
  performanceMetrics: {
    p50: number;
    p95: number;
    p99: number;
  };
}

interface ToolSearchParams {
  query?: string;
  type?: string;
  category?: string;
  tags?: string[];
  isPublic?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

const TOOL_TYPES = [
  { value: 'FUNCTION_CALL', label: 'Function Call', icon: Code },
  { value: 'API_FETCH', label: 'API Fetch', icon: Globe },
  { value: 'RAG', label: 'RAG Query', icon: Database },
  { value: 'DATABASE', label: 'Database Query', icon: Database },
  { value: 'BROWSER_AUTOMATION', label: 'Browser Automation', icon: Bot },
  { value: 'CUSTOM_LOGIC', label: 'Custom Logic', icon: Wrench },
];

const CATEGORIES = [
  'DATA_PROCESSING',
  'COMMUNICATION',
  'ANALYSIS',
  'AUTOMATION',
  'INTEGRATION',
  'CUSTOM'
];

export default function ProductionToolManagement() {
  const [tools, setTools] = useState<ToolDefinition[]>([]);
  const [filteredTools, setFilteredTools] = useState<ToolDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [selectedTool, setSelectedTool] = useState<ToolDefinition | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [analytics, setAnalytics] = useState<ToolAnalytics | null>(null);
  const [executions, setExecutions] = useState<ToolExecution[]>([]);
  const [testInput, setTestInput] = useState('{}');
  const [testResult, setTestResult] = useState<any>(null);
  const [testLoading, setTestLoading] = useState(false);
  const [stats, setStats] = useState({
    totalTools: 0,
    activeTools: 0,
    totalExecutions: 0,
    avgSuccessRate: 0
  });
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Load tools from real API
  const loadTools = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const searchParams: ToolSearchParams = {
        page: 1,
        limit: 100,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      const response = await apiClient.getTools(1, 100);
      const toolsData = response.tools || [];

      setTools(toolsData);
      setFilteredTools(toolsData);

      // Calculate stats
      const totalTools = toolsData.length;
      const activeTools = toolsData.filter(t => t.isActive).length;
      const totalExecutions = toolsData.reduce((sum, tool) => sum + tool.usageCount, 0);
      const avgSuccessRate = totalTools > 0
        ? toolsData.reduce((sum, tool) => sum + tool.successRate, 0) / totalTools
        : 0;

      setStats({
        totalTools,
        activeTools,
        totalExecutions,
        avgSuccessRate
      });

    } catch (err: any) {
      console.error('Failed to load tools:', err);
      setError(err.message || 'Failed to load tools');
      toast({
        title: "Error",
        description: "Failed to load tools. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Load tool analytics
  const loadToolAnalytics = useCallback(async (toolId: string) => {
    try {
      const analyticsData = await apiClient.getToolAnalytics(toolId, '30d');
      // Transform API response to match ToolAnalytics interface
      const transformedAnalytics: ToolAnalytics = {
        totalExecutions: analyticsData.totalExecutions,
        successRate: analyticsData.successRate,
        errorRate: 1 - analyticsData.successRate,
        avgDuration: analyticsData.averageExecutionTime,
        totalCost: 0, // API doesn't provide this yet
        cacheHitRate: 0, // API doesn't provide this yet
        executionsByDay: analyticsData.executionsByDay,
        errorDistribution: analyticsData.errorDistribution,
        performanceMetrics: analyticsData.performanceMetrics
      };
      setAnalytics(transformedAnalytics);
    } catch (err: any) {
      console.error('Failed to load tool analytics:', err);
      toast({
        title: "Error",
        description: "Failed to load tool analytics.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Load execution history
  const loadExecutionHistory = useCallback(async (toolId: string) => {
    try {
      const executionsData = await apiClient.get(`/tools/${toolId}/executions?limit=50`);
      setExecutions(executionsData as ToolExecution[]);
    } catch (err: any) {
      console.error('Failed to load execution history:', err);
      toast({
        title: "Error",
        description: "Failed to load execution history.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Test tool execution
  const handleTestTool = async () => {
    if (!selectedTool) return;

    setTestLoading(true);
    try {
      const input = JSON.parse(testInput);

      const result = await apiClient.executeTool(selectedTool.id, input);

      setTestResult({
        status: 'COMPLETED',
        output: result,
        duration: result.executionTime || 0,
        tokensUsed: result.usage?.totalTokens || 0,
        cost: result.cost || 0
      });

      toast({
        title: "Success",
        description: "Tool test completed successfully.",
      });

    } catch (error: any) {
      console.error('Tool test failed:', error);

      setTestResult({
        status: 'FAILED',
        error: error.message || 'Tool execution failed',
        duration: 0
      });

      toast({
        title: "Error",
        description: error.message || "Tool test failed.",
        variant: "destructive"
      });
    } finally {
      setTestLoading(false);
    }
  };

  // Delete tool
  const handleDeleteTool = async (toolId: string) => {
    try {
      await apiClient.deleteTool(toolId);

      // Remove from local state
      setTools(prev => prev.filter(t => t.id !== toolId));
      setFilteredTools(prev => prev.filter(t => t.id !== toolId));

      if (selectedTool?.id === toolId) {
        setSelectedTool(null);
      }

      toast({
        title: "Success",
        description: "Tool deleted successfully.",
      });

    } catch (error: any) {
      console.error('Failed to delete tool:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete tool.",
        variant: "destructive"
      });
    }
  };

  // Duplicate tool
  const handleDuplicateTool = async (tool: ToolDefinition) => {
    try {
      const duplicatedTool = await apiClient.post(`/tools/${tool.id}/duplicate`, {
        name: `${tool.name} (Copy)`
      });

      setTools(prev => [duplicatedTool as ToolDefinition, ...prev]);
      setFilteredTools(prev => [duplicatedTool as ToolDefinition, ...prev]);

      toast({
        title: "Success",
        description: "Tool duplicated successfully.",
      });

    } catch (error: any) {
      console.error('Failed to duplicate tool:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to duplicate tool.",
        variant: "destructive"
      });
    }
  };

  // Export tool
  const handleExportTool = async (toolId: string) => {
    try {
      const exportData = await apiClient.get(`/tools/${toolId}/export?format=json&includeMetadata=true`);

      // Create download link
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tool-${toolId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Tool exported successfully.",
      });

    } catch (error: any) {
      console.error('Failed to export tool:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to export tool.",
        variant: "destructive"
      });
    }
  };

  // Filter tools based on search and filters
  useEffect(() => {
    let filtered = tools;

    if (searchQuery) {
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (selectedType !== 'all') {
      filtered = filtered.filter(tool => tool.type === selectedType);
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    setFilteredTools(filtered);
  }, [tools, searchQuery, selectedType, selectedCategory]);

  // Load data on mount
  useEffect(() => {
    loadTools();
  }, [loadTools]);

  // Load analytics and executions when tool is selected
  useEffect(() => {
    if (selectedTool) {
      loadToolAnalytics(selectedTool.id);
      loadExecutionHistory(selectedTool.id);
    }
  }, [selectedTool, loadToolAnalytics, loadExecutionHistory]);

  const getTypeIcon = (type: string) => {
    const typeConfig = TOOL_TYPES.find(t => t.value === type);
    return typeConfig?.icon || Code;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'text-green-600';
      case 'FAILED': return 'text-red-600';
      case 'PROCESSING': return 'text-blue-600';
      case 'TIMEOUT': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const ToolCard = ({ tool }: { tool: ToolDefinition }) => {
    const TypeIcon = getTypeIcon(tool.type);

    return (
      <Card className="bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:shadow-lg transition-all duration-200 cursor-pointer"
        onClick={() => setSelectedTool(tool)}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <TypeIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">{tool.name}</CardTitle>
                <CardDescription className="text-sm text-gray-600 mt-1">
                  {tool.description}
                </CardDescription>
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowTestDialog(true)}>
                  <Play className="h-4 w-4 mr-2" />
                  Test Tool
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDuplicateTool(tool)}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExportTool(tool.id)}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => handleDeleteTool(tool.id)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge variant="secondary" className="text-xs">
                {tool.type.replace('_', ' ')}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {tool.category}
              </Badge>
              {tool.isPublic && (
                <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                  Public
                </Badge>
              )}
              <Badge variant="outline" className="text-xs">
                v{tool.version}
              </Badge>
            </div>

            <div className="flex items-center gap-2 flex-wrap">
              {tool.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs bg-gray-50">
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
              {tool.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{tool.tags.length - 3} more
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-gray-900">{tool.usageCount.toLocaleString()}</div>
                <div className="text-gray-500">Executions</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-green-600">{(tool.successRate * 100).toFixed(1)}%</div>
                <div className="text-gray-500">Success Rate</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-blue-600">{Math.round(tool.avgLatency)}ms</div>
                <div className="text-gray-500">Avg Latency</div>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>By {tool.creator.firstName} {tool.creator.lastName}</span>
              <span>{new Date(tool.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const ToolDetailView = ({ tool }: { tool: ToolDefinition }) => (
    <div className="space-y-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-xl">
            {React.createElement(getTypeIcon(tool.type), { className: "h-8 w-8 text-blue-600" })}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{tool.name}</h2>
            <p className="text-gray-600 mt-1">{tool.description}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowTestDialog(true)}>
            <Play className="h-4 w-4 mr-2" />
            Test Tool
          </Button>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="executions">Executions</TabsTrigger>
          <TabsTrigger value="versions">Versions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold">{tool.usageCount.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">Total Executions</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold text-green-600">{(tool.successRate * 100).toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Timer className="h-5 w-5 text-orange-600" />
                  <div>
                    <div className="text-2xl font-bold">{Math.round(tool.avgLatency)}ms</div>
                    <div className="text-sm text-gray-600">Avg Latency</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                  <div>
                    <div className="text-2xl font-bold">v{tool.version}</div>
                    <div className="text-sm text-gray-600">Current Version</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Tool Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Type</Label>
                    <div className="mt-1 text-sm text-gray-900">{tool.type.replace('_', ' ')}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Category</Label>
                    <div className="mt-1 text-sm text-gray-900">{tool.category}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Status</Label>
                    <div className="mt-1">
                      <Badge variant={tool.isActive ? "default" : "secondary"}>
                        {tool.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Visibility</Label>
                    <div className="mt-1">
                      <Badge variant={tool.isPublic ? "default" : "outline"}>
                        {tool.isPublic ? "Public" : "Private"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Tags</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {tool.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Created</Label>
                  <div className="mt-1 text-sm text-gray-900">
                    {new Date(tool.createdAt).toLocaleDateString()} by {tool.creator.firstName} {tool.creator.lastName}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Success Rate</span>
                    <span>{(tool.successRate * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={tool.successRate * 100} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Performance Score</span>
                    <span>{Math.min(100, Math.max(0, 100 - (tool.avgLatency / 10))).toFixed(0)}%</span>
                  </div>
                  <Progress value={Math.min(100, Math.max(0, 100 - (tool.avgLatency / 10)))} className="h-2" />
                </div>
                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold">{tool._count.executions.toLocaleString()}</div>
                    <div className="text-xs text-gray-600">Total Runs</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold">{tool._count.versions}</div>
                    <div className="text-xs text-gray-600">Versions</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics Dashboard</CardTitle>
              <CardDescription>Performance metrics and usage analytics</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{analytics.totalExecutions.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">Total Executions</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{(analytics.successRate * 100).toFixed(1)}%</div>
                      <div className="text-sm text-gray-600">Success Rate</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">{Math.round(analytics.avgDuration)}ms</div>
                      <div className="text-sm text-gray-600">Avg Duration</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{(analytics.cacheHitRate * 100).toFixed(1)}%</div>
                      <div className="text-sm text-gray-600">Cache Hit Rate</div>
                    </div>
                  </div>

                  {analytics.executionsByDay.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3">Execution Trends</h4>
                      <div className="space-y-2">
                        {analytics.executionsByDay.slice(0, 7).map((day, index) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-sm">{new Date(day.date).toLocaleDateString()}</span>
                            <span className="text-sm font-medium">{day.executions} executions</span>
                            <span className="text-sm text-gray-500">{Math.round(day.averageTime)}ms avg</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Loading analytics...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="executions">
          <Card>
            <CardHeader>
              <CardTitle>Execution History</CardTitle>
              <CardDescription>Recent tool executions and their results</CardDescription>
            </CardHeader>
            <CardContent>
              {executions.length > 0 ? (
                <div className="space-y-4">
                  {executions.map((execution) => (
                    <div key={execution.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={execution.status === 'COMPLETED' ? 'default' : 'destructive'}>
                            {execution.status}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {new Date(execution.createdAt).toLocaleString()}
                          </span>
                        </div>
                        {execution.duration && (
                          <span className="text-sm text-gray-500">{execution.duration}ms</span>
                        )}
                      </div>
                      {execution.error && (
                        <div className="text-sm text-red-600 mb-2">{execution.error}</div>
                      )}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Tokens:</span> {execution.tokensUsed}
                        </div>
                        <div>
                          <span className="text-gray-500">Cost:</span> ${execution.cost.toFixed(4)}
                        </div>
                        <div>
                          <span className="text-gray-500">Cached:</span> {execution.cached ? 'Yes' : 'No'}
                        </div>
                        <div>
                          <span className="text-gray-500">Retries:</span> {execution.retryCount}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No execution history available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="versions">
          <Card>
            <CardHeader>
              <CardTitle>Version History</CardTitle>
              <CardDescription>Tool versions and change history</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Version history would be loaded here</p>
                <p className="text-sm">Version timeline with changes and rollback options</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-lg text-gray-600">Loading tools...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={loadTools}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tool Management</h1>
            <p className="text-gray-600 mt-1">Manage and monitor your production tools</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Tool
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Wrench className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.totalTools}</div>
                  <div className="text-sm text-gray-600">Total Tools</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <Activity className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.activeTools}</div>
                  <div className="text-sm text-gray-600">Active Tools</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{stats.totalExecutions.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Total Executions</div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{(stats.avgSuccessRate * 100).toFixed(1)}%</div>
                  <div className="text-sm text-gray-600">Avg Success Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search tools..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {TOOL_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {CATEGORIES.map(category => (
                    <SelectItem key={category} value={category}>
                      {category.replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        {selectedTool ? (
          <div>
            <Button
              variant="ghost"
              onClick={() => setSelectedTool(null)}
              className="mb-4"
            >
              ← Back to Tools
            </Button>
            <ToolDetailView tool={selectedTool} />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.length > 0 ? (
              filteredTools.map(tool => (
                <ToolCard key={tool.id} tool={tool} />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Wrench className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No tools found</h3>
                <p className="text-gray-600 mb-4">
                  {searchQuery || selectedType !== 'all' || selectedCategory !== 'all'
                    ? 'Try adjusting your search criteria'
                    : 'Get started by creating your first tool'
                  }
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Tool
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Test Tool Dialog */}
        <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Test Tool: {selectedTool?.name}</DialogTitle>
              <DialogDescription>
                Execute the tool with test input to verify functionality
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="test-input">Input JSON</Label>
                <Textarea
                  id="test-input"
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  placeholder='{"key": "value"}'
                  className="mt-1 font-mono text-sm"
                  rows={6}
                />
              </div>

              {testResult && (
                <div>
                  <Label>Result</Label>
                  <div className="mt-1 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant={testResult.status === 'COMPLETED' ? 'default' : 'destructive'}>
                        {testResult.status}
                      </Badge>
                      {testResult.duration && (
                        <Badge variant="outline">{testResult.duration}ms</Badge>
                      )}
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(testResult.output || testResult.error, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowTestDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleTestTool} disabled={testLoading}>
                  {testLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Run Test
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Create Tool Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Tool</DialogTitle>
              <DialogDescription>
                Create a new production-ready tool for your organization
              </DialogDescription>
            </DialogHeader>
            <div className="text-center py-8 text-gray-500">
              <Plus className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Tool creation form would be implemented here</p>
              <p className="text-sm">Complete tool configuration with schema validation</p>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}