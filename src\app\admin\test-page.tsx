"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestAdminPage() {
    const [loading, setLoading] = useState(false);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
                    <span>Loading admin dashboard...</span>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            <div>
                <h1 className="text-3xl font-bold">Test Admin Page</h1>
                <p className="text-muted-foreground">
                    Test page to check syntax
                </p>
            </div>
            <Card>
                <CardHeader>
                    <CardTitle>Test Card</CardTitle>
                </CardHeader>
                <CardContent>
                    <p>This is a test card.</p>
                </CardContent>
            </Card>
        </div>
    );
}