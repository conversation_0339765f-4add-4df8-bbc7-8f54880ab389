import { z } from 'zod';

export const ToolTypeEnum = z.enum([
  'FUNCTION_CALL',
  'RAG',
  'API_FETCH',
  'BROWSER_AUTOMATION',
  'DATABASE',
  'CUSTOM_LOGIC'
]);

export const ToolExecutionStatusEnum = z.enum([
  'PENDING',
  'RUNNING',
  'COMPLETED',
  'FAILED',
  'CANCELLED',
  'TIMEOUT'
]);

export const ToolCacheStrategyEnum = z.enum([
  'NONE',
  'INPUT_HASH',
  'TIME_BASED',
  'DEPENDENCY_BASED',
  'CUSTOM'
]);

export const SkillCategoryEnum = z.enum([
  'COMMUNICATION',
  'DATA_ANALYSIS',
  'AUTOMATION',
  'CONTENT_GENERATION',
  'DECISION_MAKING',
  'INTEGRATION',
  'CUSTOM'
]);

export const ToolDefinitionSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Tool name is required'),
  description: z.string().optional(),
  category: SkillCategoryEnum.default('CUSTOM'),
  type: ToolTypeEnum.default('FUNCTION_CALL'),
  config: z.record(z.string(), z.any()).default(() => ({})),
  inputSchema: z.record(z.string(), z.any()).default(() => ({})),
  outputSchema: z.record(z.string(), z.any()).default(() => ({})),
  timeout: z.number().int().min(1000).max(300000).default(30000),
  retryPolicy: z.record(z.string(), z.any()).default(() => ({})),
  cacheStrategy: ToolCacheStrategyEnum.default('INPUT_HASH'),
  cacheTTL: z.number().int().min(0).default(3600),
  version: z.string().default('1.0.0'),
  tags: z.array(z.string()).default(() => []),
  documentation: z.string().optional(),
  examples: z.array(z.record(z.string(), z.any())).default(() => []),
  isPublic: z.boolean().default(false),
  isActive: z.boolean().default(true),
  dependencies: z.array(z.string()).default(() => []),
  requirements: z.record(z.string(), z.any()).default(() => ({})),
  organizationId: z.string().optional(),
});

export const CreateToolDefinitionSchema = ToolDefinitionSchema.omit({ id: true });

export const UpdateToolDefinitionSchema = ToolDefinitionSchema.partial().omit({
  id: true,
  createdBy: true,
  organizationId: true
});

export const ToolVersionSchema = z.object({
  id: z.string().optional(),
  toolId: z.string(),
  version: z.string(),
  description: z.string().optional(),
  changes: z.array(z.string()).default(() => []),
  config: z.record(z.string(), z.any()),
  inputSchema: z.record(z.string(), z.any()),
  outputSchema: z.record(z.string(), z.any()),
  requirements: z.record(z.string(), z.any()).default(() => ({})),
  isStable: z.boolean().default(false),
  isDeprecated: z.boolean().default(false),
  releaseNotes: z.string().optional(),
  migrationPath: z.record(z.string(), z.any()).optional(),
  breakingChanges: z.array(z.string()).default(() => []),
});

export const CreateToolVersionSchema = ToolVersionSchema.omit({ id: true });

export const ToolCompositionSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Composition name is required'),
  description: z.string().optional(),
  flow: z.record(z.string(), z.any()).default(() => ({})),
  variables: z.record(z.string(), z.any()).default(() => ({})),
  conditions: z.record(z.string(), z.any()).default(() => ({})),
  parallel: z.boolean().default(false),
  timeout: z.number().int().min(1000).max(3600000).default(300000),
  errorHandling: z.record(z.string(), z.any()).default(() => ({})),
  tags: z.array(z.string()).default(() => []),
  isTemplate: z.boolean().default(false),
  isActive: z.boolean().default(true),
  organizationId: z.string(),
});

export const CreateToolCompositionSchema = ToolCompositionSchema.omit({ id: true });

export const UpdateToolCompositionSchema = ToolCompositionSchema.partial().omit({
  id: true,
  createdBy: true,
  organizationId: true
});

export const ToolCompositionStepSchema = z.object({
  id: z.string().optional(),
  compositionId: z.string(),
  toolId: z.string(),
  stepIndex: z.number().int().min(0),
  stepName: z.string().optional(),
  inputMapping: z.record(z.string(), z.any()).default(() => ({})),
  outputMapping: z.record(z.string(), z.any()).default(() => ({})),
  conditions: z.record(z.string(), z.any()).default(() => ({})),
  dependencies: z.array(z.string()).default(() => []),
  continueOnError: z.boolean().default(false),
  retryCount: z.number().int().min(0).max(10).default(0),
  fallbackTool: z.string().optional(),
});

export const CreateToolCompositionStepSchema = ToolCompositionStepSchema.omit({ id: true });

export const ToolExecutionSchema = z.object({
  id: z.string().optional(),
  toolId: z.string().optional(),
  compositionId: z.string().optional(),
  executorType: z.string().default('user'),
  executorId: z.string().optional(),
  sessionId: z.string().optional(),
  organizationId: z.string(),
  status: ToolExecutionStatusEnum.default('PENDING'),
  input: z.record(z.string(), z.any()),
  output: z.record(z.string(), z.any()).optional(),
  error: z.string().optional(),
  startedAt: z.date().optional(),
  completedAt: z.date().optional(),
  duration: z.number().int().optional(),
  retryCount: z.number().int().min(0).default(0),
  tokensUsed: z.number().int().min(0).default(0),
  cost: z.number().min(0).default(0),
  memoryUsed: z.number().int().optional(),
  cpuTime: z.number().int().optional(),
  metadata: z.record(z.string(), z.any()).default(() => ({})),
  traceId: z.string().optional(),
  parentId: z.string().optional(),
  cached: z.boolean().default(false),
  cacheKey: z.string().optional(),
});

export const CreateToolExecutionSchema = ToolExecutionSchema.omit({
  id: true,
  status: true,
  output: true,
  error: true,
  startedAt: true,
  completedAt: true,
  duration: true
});

export const ToolExecuteRequestSchema = z.object({
  toolId: z.string(),
  input: z.record(z.string(), z.any()),
  executionId: z.string().optional(),
  sessionId: z.string().optional(),
  metadata: z.record(z.string(), z.any()).default(() => ({})),
  options: z.object({
    timeout: z.number().int().min(1000).max(300000).optional(),
    retryCount: z.number().int().min(0).max(10).optional(),
    useCache: z.boolean().default(true),
    priority: z.enum(['low', 'normal', 'high']).default('normal'),
  }).default(() => ({
    useCache: true,
    priority: 'normal' as const
  })),
});

export const ToolCompositionExecuteRequestSchema = z.object({
  compositionId: z.string(),
  input: z.record(z.string(), z.any()),
  variables: z.record(z.string(), z.any()).default(() => ({})),
  executionId: z.string().optional(),
  sessionId: z.string().optional(),
  metadata: z.record(z.string(), z.any()).default(() => ({})),
  options: z.object({
    timeout: z.number().int().min(1000).max(3600000).optional(),
    parallel: z.boolean().optional(),
    continueOnError: z.boolean().default(false),
    priority: z.enum(['low', 'normal', 'high']).default('normal'),
  }).default(() => ({
    continueOnError: false,
    priority: 'normal' as const
  })),
});

export const ToolCacheSchema = z.object({
  id: z.string().optional(),
  toolId: z.string(),
  inputHash: z.string(),
  input: z.record(z.string(), z.any()),
  output: z.record(z.string(), z.any()),
  strategy: ToolCacheStrategyEnum,
  ttl: z.number().int().min(0),
  hits: z.number().int().min(0).default(0),
  lastAccessed: z.date().default(() => new Date()),
  isValid: z.boolean().default(true),
  invalidatedBy: z.string().optional(),
  invalidatedAt: z.date().optional(),
  dependencies: z.array(z.string()).default(() => []),
  dependencyHash: z.string().optional(),
  size: z.number().int().optional(),
  compressionRatio: z.number().optional(),
  expiresAt: z.date(),
});

export const ToolAnalyticsSchema = z.object({
  id: z.string().optional(),
  toolId: z.string(),
  date: z.date(),
  hour: z.number().int().min(0).max(23).optional(),
  executionCount: z.number().int().min(0).default(0),
  uniqueUsers: z.number().int().min(0).default(0),
  totalDuration: z.number().int().min(0).default(0),
  avgDuration: z.number().min(0).default(0),
  successCount: z.number().int().min(0).default(0),
  errorCount: z.number().int().min(0).default(0),
  timeoutCount: z.number().int().min(0).default(0),
  retryCount: z.number().int().min(0).default(0),
  minDuration: z.number().int().optional(),
  maxDuration: z.number().int().optional(),
  p95Duration: z.number().int().optional(),
  p99Duration: z.number().int().optional(),
  totalTokens: z.number().int().min(0).default(0),
  totalCost: z.number().min(0).default(0),
  avgMemoryUsed: z.number().min(0).default(0),
  avgCpuTime: z.number().min(0).default(0),
  cacheHits: z.number().int().min(0).default(0),
  cacheMisses: z.number().int().min(0).default(0),
  cacheHitRate: z.number().min(0).max(1).default(0),
  errorTypes: z.record(z.string(), z.any()).default(() => ({})),
  errorMessages: z.record(z.string(), z.any()).default(() => ({})),
});

export const ToolAPIKeySchema = z.object({
  id: z.string().optional(),
  toolId: z.string(),
  name: z.string().min(1, 'API key name is required'),
  description: z.string().optional(),
  keyType: z.string().default('external'),
  encryptedKey: z.string(),
  keyHash: z.string(),
  oauthConfig: z.record(z.string(), z.any()).optional(),
  refreshToken: z.string().optional(),
  tokenExpiry: z.date().optional(),
  permissions: z.array(z.string()).default(() => []),
  rateLimits: z.record(z.string(), z.any()).default(() => ({})),
  lastUsed: z.date().optional(),
  usageCount: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
  expiresAt: z.date().optional(),
  organizationId: z.string(),
});

export const CreateToolAPIKeySchema = ToolAPIKeySchema.omit({
  id: true,
  keyHash: true,
  lastUsed: true,
  usageCount: true
});

export const UpdateToolAPIKeySchema = ToolAPIKeySchema.partial().omit({
  id: true,
  toolId: true,
  keyHash: true,
  createdBy: true,
  organizationId: true
});

// Query schemas
export const ToolListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  search: z.string().optional(),
  category: SkillCategoryEnum.optional(),
  type: ToolTypeEnum.optional(),
  isActive: z.coerce.boolean().optional(),
  isPublic: z.coerce.boolean().optional(),
  tags: z.string().optional(), // Comma-separated tags
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'usageCount', 'successRate']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const ToolExecutionListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  toolId: z.string().optional(),
  status: ToolExecutionStatusEnum.optional(),
  executorType: z.string().optional(),
  executorId: z.string().optional(),
  sessionId: z.string().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  sortBy: z.enum(['createdAt', 'startedAt', 'completedAt', 'duration']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const ToolAnalyticsQuerySchema = z.object({
  toolId: z.string().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day'),
  metrics: z.string().optional(), // Comma-separated metrics
});

// Type exports
export type ToolDefinition = z.infer<typeof ToolDefinitionSchema>;
export type CreateToolDefinition = z.infer<typeof CreateToolDefinitionSchema>;
export type UpdateToolDefinition = z.infer<typeof UpdateToolDefinitionSchema>;

export type ToolVersion = z.infer<typeof ToolVersionSchema>;
export type CreateToolVersion = z.infer<typeof CreateToolVersionSchema>;

export type ToolComposition = z.infer<typeof ToolCompositionSchema>;
export type CreateToolComposition = z.infer<typeof CreateToolCompositionSchema>;
export type UpdateToolComposition = z.infer<typeof UpdateToolCompositionSchema>;

export type ToolCompositionStep = z.infer<typeof ToolCompositionStepSchema>;
export type CreateToolCompositionStep = z.infer<typeof CreateToolCompositionStepSchema>;

export type ToolExecution = z.infer<typeof ToolExecutionSchema>;
export type CreateToolExecution = z.infer<typeof CreateToolExecutionSchema>;
export type ToolExecuteRequest = z.infer<typeof ToolExecuteRequestSchema>;
export type ToolCompositionExecuteRequest = z.infer<typeof ToolCompositionExecuteRequestSchema>;

export type ToolCache = z.infer<typeof ToolCacheSchema>;
export type ToolAnalytics = z.infer<typeof ToolAnalyticsSchema>;

export type ToolAPIKey = z.infer<typeof ToolAPIKeySchema>;
export type CreateToolAPIKey = z.infer<typeof CreateToolAPIKeySchema>;
export type UpdateToolAPIKey = z.infer<typeof UpdateToolAPIKeySchema>;

export type ToolListQuery = z.infer<typeof ToolListQuerySchema>;
export type ToolExecutionListQuery = z.infer<typeof ToolExecutionListQuerySchema>;
export type ToolAnalyticsQuery = z.infer<typeof ToolAnalyticsQuerySchema>;

export type ToolType = z.infer<typeof ToolTypeEnum>;
export type ToolExecutionStatus = z.infer<typeof ToolExecutionStatusEnum>;
export type ToolCacheStrategy = z.infer<typeof ToolCacheStrategyEnum>;
export type SkillCategory = z.infer<typeof SkillCategoryEnum>;