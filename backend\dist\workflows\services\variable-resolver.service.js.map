{"version": 3, "file": "variable-resolver.service.js", "sourceRoot": "", "sources": ["../../../src/workflows/services/variable-resolver.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gEAA4D;AAGrD,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAGlC,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;QAFxB,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;IAEvB,CAAC;IAE7C,KAAK,CAAC,gBAAgB,CACpB,QAAa,EACb,OAOC;QAED,IAAI,CAAC;YAEH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC3E,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC9E,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAElD,MAAM,YAAY,GAAG;gBACnB,GAAG,eAAe;gBAClB,GAAG,iBAAiB;gBACpB,GAAG,gBAAgB;gBACnB,GAAG,OAAO,CAAC,SAAS;aACrB,CAAC;YAGF,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAiB;QACjD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,MAAM,cAAc,GAAG,OAAO,CAAC,OAA8B,CAAC;YAC9D,OAAO,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACpF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QACnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACrD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAiC,CAAC;YAC9D,OAAO,UAAU,CAAC,SAAS,IAAI,EAAE,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,kBAAkB;QAExB,OAAO;YACL,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;SAC1B,CAAC;IACJ,CAAC;IAEO,eAAe,CACrB,QAAa,EACb,SAA8B,EAC9B,WAA8B;QAE9B,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAEjC,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBACvD,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC,CAAC;oBACvC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAEnC,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;QAClF,CAAC;aAAM,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAEpD,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAC5E,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,QAAQ,KAAK,cAAc,IAAI,WAAW,EAAE,CAAC;YAEtD,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,WAAW,EAAE,CAAC;YAE9F,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QACzC,CAAC;QAGD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,YAAY;QAElB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAS,CAAC;YACvE,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAGD,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,SAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACnD,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,cAAc,GAAG,OAAO,CAAC,OAA8B,CAAC;YAC9D,MAAM,cAAc,GAAG;gBACrB,GAAG,cAAc;gBACjB,SAAS,EAAE;oBACT,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC;oBACnC,GAAG,SAAS;iBACb;aACF,CAAC;YAEF,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBACxB,IAAI,EAAE;oBACJ,OAAO,EAAE,cAAc;iBACxB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAnKY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAIiB,8BAAa;GAH9B,uBAAuB,CAmKnC"}