import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Layers, CheckCircle, XCircle, Activity, AlertTriangle, <PERSON><PERSON>, Wrench } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface HybridNodeData {
  label: string;
  description?: string;
  config?: {
    agentId?: string;
    toolIds?: string[];
    executionPattern?: 'agent-first' | 'tool-first' | 'parallel' | 'multi-tool-orchestration';
    maxIterations?: number;
  };
  status?: 'idle' | 'running' | 'success' | 'error' | 'thinking';
  isConfigValid?: boolean;
  lastOutput?: string;
  errorMessage?: string;
  progress?: number;
}

const HybridNode: React.FC<NodeProps<HybridNodeData>> = ({ data, selected }) => {
  const getStatusIcon = () => {
    switch (data.status) {
      case 'running':
        return <Activity className="h-3 w-3 text-blue-600 animate-pulse" />;
      case 'thinking':
        return <Activity className="h-3 w-3 text-purple-600 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'thinking':
        return 'border-purple-500 bg-purple-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getPatternDescription = (pattern?: string) => {
    switch (pattern) {
      case 'agent-first':
        return 'Agent → Tools';
      case 'tool-first':
        return 'Tools → Agent';
      case 'parallel':
        return 'Agent ∥ Tools';
      case 'multi-tool-orchestration':
        return 'Agent ↔ Tools';
      default:
        return 'Not configured';
    }
  };

  return (
    <Card
      className={cn(
        'min-w-[200px] max-w-[300px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor(),
        !data.isConfigValid && 'border-red-300 bg-red-50'
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      
      <CardContent className="p-4">
        {/* Progress bar for running status */}
        {(data.status === 'running' || data.status === 'thinking') && data.progress !== undefined && (
          <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-t-lg overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300"
              style={{ width: `${data.progress}%` }}
            />
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Layers className="h-4 w-4 text-purple-600" />
            {getStatusIcon()}
          </div>
          <div className="flex items-center space-x-1">
            {!data.isConfigValid && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            {data.config?.toolIds && (
              <Badge variant="outline" className="text-xs">
                {data.config.toolIds.length} tools
              </Badge>
            )}
          </div>
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1 truncate">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs mb-2">
          <Layers className="h-2 w-2 mr-1" />
          Hybrid
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
            {data.description}
          </p>
        )}

        {/* Execution pattern */}
        <div className="mb-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
          <div className="font-medium text-gray-800 mb-1">Execution Pattern:</div>
          <div className="flex items-center space-x-2">
            <Bot className="h-3 w-3 text-blue-600" />
            <span className="text-gray-700 font-mono">
              {getPatternDescription(data.config?.executionPattern)}
            </span>
            <Wrench className="h-3 w-3 text-orange-600" />
          </div>
        </div>

        {/* Status-specific content */}
        {data.status === 'error' && data.errorMessage && (
          <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <XCircle className="h-3 w-3 text-red-600" />
              <span className="font-medium text-red-800">Error</span>
            </div>
            <p className="text-red-700 line-clamp-2">{data.errorMessage}</p>
          </div>
        )}

        {/* Thinking/Running status */}
        {(data.status === 'thinking' || data.status === 'running') && (
          <div className="mb-2 p-2 bg-purple-100 border border-purple-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <Activity className="h-3 w-3 text-purple-600 animate-pulse" />
              <span className="font-medium text-purple-800">
                {data.status === 'thinking' ? 'Agent Thinking' : 'Executing'}
              </span>
            </div>
            <p className="text-purple-700">
              {data.status === 'thinking' 
                ? 'Agent is processing and coordinating tools...'
                : 'Hybrid execution in progress...'
              }
            </p>
          </div>
        )}

        {/* Success output */}
        {data.status === 'success' && data.lastOutput && (
          <div className="mb-2 p-2 bg-green-100 border border-green-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="font-medium text-green-800">Result</span>
            </div>
            <p className="text-green-700 line-clamp-3 font-mono">
              {data.lastOutput}
            </p>
          </div>
        )}

        {/* Configuration summary */}
        {data.config && (
          <div className="text-xs text-muted-foreground space-y-1">
            {data.config.maxIterations && (
              <div>Max iterations: {data.config.maxIterations}</div>
            )}
            {data.config.agentId && (
              <div>Agent: {data.config.agentId}</div>
            )}
          </div>
        )}
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(HybridNode);