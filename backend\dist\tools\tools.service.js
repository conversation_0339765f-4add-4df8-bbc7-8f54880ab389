"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const tool_manager_service_1 = require("./services/tool-manager.service");
const tool_execution_service_1 = require("./services/tool-execution.service");
let ToolsService = class ToolsService {
    constructor(prisma, toolManager, toolExecution) {
        this.prisma = prisma;
        this.toolManager = toolManager;
        this.toolExecution = toolExecution;
    }
    async findAll(organizationId) {
        return this.toolManager.searchTools({
            organizationId,
            limit: 100,
        });
    }
    async findOne(id, organizationId) {
        return this.toolManager.getTool(id, organizationId);
    }
    async create(createToolDto, creatorId, organizationId) {
        return this.toolManager.createTool(createToolDto, creatorId, organizationId);
    }
    async update(id, updateToolDto, userId, organizationId) {
        return this.toolManager.updateTool(id, updateToolDto, userId, organizationId);
    }
    async remove(id, userId, organizationId) {
        return this.toolManager.deleteTool(id, userId, organizationId);
    }
    async executeTool(toolId, input, context) {
        const executionContext = {
            toolId,
            input,
            executorType: context.executorType || 'user',
            executorId: context.executorId,
            sessionId: context.sessionId,
            organizationId: context.organizationId || '',
            metadata: context.metadata,
            timeout: context.timeout,
            retryPolicy: context.retryPolicy,
        };
        return this.toolExecution.executeTool(executionContext);
    }
    async getToolsByType(type, organizationId) {
        return this.toolManager.searchTools({
            type: type,
            organizationId,
        });
    }
    async getPopularTools(organizationId, limit = 10) {
        return this.toolManager.getPopularTools(organizationId, limit);
    }
    async getToolStats(organizationId) {
        return this.toolManager.getToolStats(organizationId);
    }
    async importTool(importOptions, creatorId, organizationId) {
        return this.toolManager.importTool(importOptions, creatorId, organizationId);
    }
    async exportTool(toolId, format, organizationId) {
        return this.toolManager.exportTool(toolId, format, organizationId);
    }
};
exports.ToolsService = ToolsService;
exports.ToolsService = ToolsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        tool_manager_service_1.ToolManagerService,
        tool_execution_service_1.ToolExecutionService])
], ToolsService);
//# sourceMappingURL=tools.service.js.map