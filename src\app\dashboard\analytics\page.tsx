"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
    BarChart3,
    TrendingUp,
    TrendingDown,
    Activity,
    Users,
    Clock,
    DollarSign,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Calendar,
    Download,
    RefreshCw,
    Loader2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import apiClient from '@/lib/api-client';

interface AnalyticsData {
    overview: {
        totalExecutions: number;
        successRate: number;
        avgResponseTime: number;
        activeUsers: number;
        totalCost: number;
        errorRate: number;
    };
    trends: {
        date: string;
        executions: number;
        successRate: number;
        avgResponseTime: number;
        cost: number;
    }[];
    topTools: {
        id: string;
        name: string;
        executions: number;
        successRate: number;
        avgResponseTime: number;
        cost: number;
    }[];
    topAgents: {
        id: string;
        name: string;
        tasks: number;
        successRate: number;
        avgResponseTime: number;
    }[];
    errors: {
        type: string;
        count: number;
        percentage: number;
    }[];
}

export default function AnalyticsPage() {
    const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [timeRange, setTimeRange] = useState('30d');
    const { toast } = useToast();

    // Load analytics data from real API
    const loadAnalytics = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get(`/analytics?range=${timeRange}`);
            setAnalytics(response as AnalyticsData);
        } catch (error: any) {
            console.error('Failed to load analytics:', error);
            toast({
                title: "Error",
                description: "Failed to load analytics data.",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAnalytics();
    }, [timeRange]);

    const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
        switch (trend) {
            case 'up': return TrendingUp;
            case 'down': return TrendingDown;
            default: return Activity;
        }
    };

    const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
        switch (trend) {
            case 'up': return 'text-green-600';
            case 'down': return 'text-red-600';
            default: return 'text-gray-600';
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center gap-3">
                            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                            <span className="text-lg text-gray-600">Loading analytics...</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!analytics) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center py-12">
                        <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No analytics data available</h3>
                        <p className="text-gray-600">Analytics data will appear here once you start using the platform.</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
                        <p className="text-gray-600 mt-1">Monitor platform performance and usage metrics</p>
                    </div>
                    <div className="flex gap-3">
                        <select
                            value={timeRange}
                            onChange={(e) => setTimeRange(e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="7d">Last 7 days</option>
                            <option value="30d">Last 30 days</option>
                            <option value="90d">Last 90 days</option>
                            <option value="1y">Last year</option>
                        </select>
                        <Button variant="outline" onClick={loadAnalytics}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Refresh
                        </Button>
                        <Button variant="outline">
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {/* Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-50 rounded-lg">
                                    <Activity className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{analytics.overview.totalExecutions.toLocaleString()}</div>
                                    <div className="text-sm text-gray-600">Total Executions</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-50 rounded-lg">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{(analytics.overview.successRate * 100).toFixed(1)}%</div>
                                    <div className="text-sm text-gray-600">Success Rate</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-purple-50 rounded-lg">
                                    <Clock className="h-5 w-5 text-purple-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{Math.round(analytics.overview.avgResponseTime)}ms</div>
                                    <div className="text-sm text-gray-600">Avg Response Time</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-orange-50 rounded-lg">
                                    <Users className="h-5 w-5 text-orange-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{analytics.overview.activeUsers}</div>
                                    <div className="text-sm text-gray-600">Active Users</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-red-50 rounded-lg">
                                    <DollarSign className="h-5 w-5 text-red-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">${analytics.overview.totalCost.toFixed(2)}</div>
                                    <div className="text-sm text-gray-600">Total Cost</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-yellow-50 rounded-lg">
                                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{(analytics.overview.errorRate * 100).toFixed(1)}%</div>
                                    <div className="text-sm text-gray-600">Error Rate</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Detailed Analytics */}
                <Tabs defaultValue="trends" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="trends">Trends</TabsTrigger>
                        <TabsTrigger value="tools">Top Tools</TabsTrigger>
                        <TabsTrigger value="agents">Top Agents</TabsTrigger>
                        <TabsTrigger value="errors">Error Analysis</TabsTrigger>
                    </TabsList>

                    <TabsContent value="trends">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Execution Trends</CardTitle>
                                <CardDescription>Performance metrics over time</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {analytics.trends.map((trend, index) => (
                                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center gap-4">
                                                <Calendar className="h-4 w-4 text-gray-400" />
                                                <span className="font-medium">{new Date(trend.date).toLocaleDateString()}</span>
                                            </div>
                                            <div className="flex items-center gap-6">
                                                <div className="text-center">
                                                    <div className="font-semibold">{trend.executions}</div>
                                                    <div className="text-xs text-gray-500">Executions</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold text-green-600">{(trend.successRate * 100).toFixed(1)}%</div>
                                                    <div className="text-xs text-gray-500">Success Rate</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold">{Math.round(trend.avgResponseTime)}ms</div>
                                                    <div className="text-xs text-gray-500">Avg Time</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold">${trend.cost.toFixed(2)}</div>
                                                    <div className="text-xs text-gray-500">Cost</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="tools">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Top Performing Tools</CardTitle>
                                <CardDescription>Most used and reliable tools</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {analytics.topTools.map((tool, index) => (
                                        <div key={tool.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center gap-4">
                                                <Badge variant="secondary">{index + 1}</Badge>
                                                <div>
                                                    <div className="font-medium">{tool.name}</div>
                                                    <div className="text-sm text-gray-500">ID: {tool.id}</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-6">
                                                <div className="text-center">
                                                    <div className="font-semibold">{tool.executions.toLocaleString()}</div>
                                                    <div className="text-xs text-gray-500">Executions</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold text-green-600">{(tool.successRate * 100).toFixed(1)}%</div>
                                                    <div className="text-xs text-gray-500">Success Rate</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold">{Math.round(tool.avgResponseTime)}ms</div>
                                                    <div className="text-xs text-gray-500">Avg Time</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold">${tool.cost.toFixed(2)}</div>
                                                    <div className="text-xs text-gray-500">Cost</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="agents">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Top Performing Agents</CardTitle>
                                <CardDescription>Most active and successful agents</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {analytics.topAgents.map((agent, index) => (
                                        <div key={agent.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center gap-4">
                                                <Badge variant="secondary">{index + 1}</Badge>
                                                <div>
                                                    <div className="font-medium">{agent.name}</div>
                                                    <div className="text-sm text-gray-500">ID: {agent.id}</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-6">
                                                <div className="text-center">
                                                    <div className="font-semibold">{agent.tasks.toLocaleString()}</div>
                                                    <div className="text-xs text-gray-500">Tasks</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold text-green-600">{(agent.successRate * 100).toFixed(1)}%</div>
                                                    <div className="text-xs text-gray-500">Success Rate</div>
                                                </div>
                                                <div className="text-center">
                                                    <div className="font-semibold">{Math.round(agent.avgResponseTime)}ms</div>
                                                    <div className="text-xs text-gray-500">Avg Time</div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="errors">
                        <Card className="bg-white/80 backdrop-blur-sm">
                            <CardHeader>
                                <CardTitle>Error Analysis</CardTitle>
                                <CardDescription>Most common error types and their frequency</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {analytics.errors.map((error, index) => (
                                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex items-center gap-4">
                                                <Badge variant="destructive">{index + 1}</Badge>
                                                <div>
                                                    <div className="font-medium">{error.type}</div>
                                                    <div className="text-sm text-gray-500">{error.count} occurrences</div>
                                                </div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-semibold text-red-600">{error.percentage.toFixed(1)}%</div>
                                                <div className="text-xs text-gray-500">of total errors</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
}