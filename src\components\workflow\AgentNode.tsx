import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ContextMenu, 
  ContextMenuContent, 
  ContextMenuItem, 
  ContextMenuTrigger,
  ContextMenuSeparator 
} from '@/components/ui/context-menu';
import { 
  Toolt<PERSON>, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip';
import { 
  Bo<PERSON>, 
  Settings, 
  Copy, 
  Trash2, 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  MessageSquare,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

export type AgentNodeStatus = 'idle' | 'running' | 'success' | 'error' | 'paused' | 'thinking';

export interface AgentNodeData {
  id: string;
  name: string;
  type: 'STANDALONE' | 'TOOL_DRIVEN' | 'HYBRID' | 'MULTI_TASKING';
  description?: string;
  config?: {
    agentId?: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
  };
  status?: AgentNodeStatus;
  progress?: number;
  lastOutput?: string;
  executionTime?: number;
  errorMessage?: string;
  isConfigValid?: boolean;
}

export interface AgentNodeProps {
  data: AgentNodeData;
  position: { x: number; y: number };
  selected?: boolean;
  dragging?: boolean;
  onSelect?: (nodeId: string) => void;
  onConfigure?: (nodeId: string) => void;
  onDuplicate?: (nodeId: string) => void;
  onDelete?: (nodeId: string) => void;
  onExecute?: (nodeId: string) => void;
  onPause?: (nodeId: string) => void;
  onResume?: (nodeId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function AgentNode({
  data,
  position,
  selected = false,
  dragging = false,
  onSelect,
  onConfigure,
  onDuplicate,
  onDelete,
  onExecute,
  onPause,
  onResume,
  className,
  style,
}: AgentNodeProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [streamingOutput, setStreamingOutput] = useState('');

  // Simulate streaming output for demo
  useEffect(() => {
    if (data.status === 'thinking' && data.lastOutput) {
      let index = 0;
      const text = data.lastOutput;
      setStreamingOutput('');
      
      const interval = setInterval(() => {
        if (index < text.length) {
          setStreamingOutput(text.slice(0, index + 1));
          index++;
        } else {
          clearInterval(interval);
        }
      }, 50);

      return () => clearInterval(interval);
    } else {
      setStreamingOutput(data.lastOutput || '');
    }
  }, [data.status, data.lastOutput]);

  const getStatusIcon = (status: AgentNodeStatus) => {
    switch (status) {
      case 'running':
        return <Activity className="h-4 w-4 text-blue-600 animate-pulse" />;
      case 'thinking':
        return <MessageSquare className="h-4 w-4 text-purple-600 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: AgentNodeStatus) => {
    switch (status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'thinking':
        return 'border-purple-500 bg-purple-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      case 'paused':
        return 'border-yellow-500 bg-yellow-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'HYBRID':
        return <Zap className="h-3 w-3" />;
      case 'TOOL_DRIVEN':
        return <Settings className="h-3 w-3" />;
      case 'MULTI_TASKING':
        return <Activity className="h-3 w-3" />;
      default:
        return <Bot className="h-3 w-3" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'HYBRID':
        return 'bg-purple-100 text-purple-800';
      case 'TOOL_DRIVEN':
        return 'bg-blue-100 text-blue-800';
      case 'MULTI_TASKING':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect?.(data.id);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onConfigure?.(data.id);
  };

  return (
    <TooltipProvider>
      <ContextMenu>
        <ContextMenuTrigger>
          <Card
            className={cn(
              'relative min-w-[200px] max-w-[300px] cursor-pointer transition-all duration-200',
              'hover:shadow-lg',
              selected && 'ring-2 ring-primary ring-offset-2',
              dragging && 'opacity-60 rotate-2',
              getStatusColor(data.status || 'idle'),
              !data.isConfigValid && 'border-red-300 bg-red-50',
              className
            )}
            style={{
              position: 'absolute',
              left: position.x,
              top: position.y,
              ...style,
            }}
            onClick={handleClick}
            onDoubleClick={handleDoubleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Progress bar for running status */}
            {(data.status === 'running' || data.status === 'thinking') && data.progress !== undefined && (
              <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-t-lg overflow-hidden">
                <div 
                  className="h-full bg-blue-500 transition-all duration-300"
                  style={{ width: `${data.progress}%` }}
                />
              </div>
            )}

            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <div className="flex items-center space-x-1">
                    <Bot className="h-5 w-5 text-blue-600 flex-shrink-0" />
                    {getStatusIcon(data.status || 'idle')}
                  </div>
                  <div className="min-w-0 flex-1">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <h3 className="font-medium text-sm truncate">
                          {data.name}
                        </h3>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{data.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>

                {/* Configuration status indicator */}
                <div className="flex items-center space-x-1 flex-shrink-0">
                  {data.isConfigValid === false && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Configuration incomplete</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                  
                  {isHovered && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onConfigure?.(data.id);
                      }}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Type badge */}
              <div className="flex items-center justify-between mb-3">
                <Badge variant="outline" className={cn('text-xs', getTypeColor(data.type))}>
                  {getTypeIcon(data.type)}
                  <span className="ml-1">{data.type.replace('_', ' ')}</span>
                </Badge>

                {/* Execution time */}
                {data.executionTime && (
                  <span className="text-xs text-muted-foreground">
                    {data.executionTime}ms
                  </span>
                )}
              </div>

              {/* Description */}
              {data.description && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                      {data.description}
                    </p>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p>{data.description}</p>
                  </TooltipContent>
                </Tooltip>
              )}

              {/* Status-specific content */}
              {data.status === 'error' && data.errorMessage && (
                <div className="mb-3 p-2 bg-red-100 border border-red-200 rounded text-xs">
                  <div className="flex items-center space-x-1 mb-1">
                    <XCircle className="h-3 w-3 text-red-600" />
                    <span className="font-medium text-red-800">Error</span>
                  </div>
                  <p className="text-red-700 line-clamp-2">{data.errorMessage}</p>
                </div>
              )}

              {/* Streaming output */}
              {(data.status === 'thinking' || data.status === 'running') && streamingOutput && (
                <div className="mb-3 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
                  <div className="flex items-center space-x-1 mb-1">
                    <MessageSquare className="h-3 w-3 text-gray-600" />
                    <span className="font-medium text-gray-800">Output</span>
                  </div>
                  <p className="text-gray-700 line-clamp-3 font-mono">
                    {streamingOutput}
                    {data.status === 'thinking' && (
                      <span className="animate-pulse">|</span>
                    )}
                  </p>
                </div>
              )}

              {/* Success output */}
              {data.status === 'success' && data.lastOutput && (
                <div className="mb-3 p-2 bg-green-100 border border-green-200 rounded text-xs">
                  <div className="flex items-center space-x-1 mb-1">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span className="font-medium text-green-800">Result</span>
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <p className="text-green-700 line-clamp-3 cursor-help">
                        {data.lastOutput}
                      </p>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-md">
                      <p className="whitespace-pre-wrap">{data.lastOutput}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              )}

              {/* Configuration summary */}
              {data.config && (
                <div className="text-xs text-muted-foreground space-y-1">
                  {data.config.maxTokens && (
                    <div>Max tokens: {data.config.maxTokens}</div>
                  )}
                  {data.config.temperature !== undefined && (
                    <div>Temperature: {data.config.temperature}</div>
                  )}
                </div>
              )}

              {/* Input/Output pins */}
              <div className="absolute -left-2 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-sm" />
              </div>
              <div className="absolute -right-2 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm" />
              </div>
            </CardContent>
          </Card>
        </ContextMenuTrigger>

        <ContextMenuContent>
          <ContextMenuItem onClick={() => onConfigure?.(data.id)}>
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </ContextMenuItem>
          
          <ContextMenuSeparator />
          
          {data.status === 'idle' || data.status === 'paused' ? (
            <ContextMenuItem onClick={() => onExecute?.(data.id)}>
              <Play className="mr-2 h-4 w-4" />
              Execute
            </ContextMenuItem>
          ) : data.status === 'running' || data.status === 'thinking' ? (
            <ContextMenuItem onClick={() => onPause?.(data.id)}>
              <Pause className="mr-2 h-4 w-4" />
              Pause
            </ContextMenuItem>
          ) : null}

          {data.status === 'paused' && (
            <ContextMenuItem onClick={() => onResume?.(data.id)}>
              <Play className="mr-2 h-4 w-4" />
              Resume
            </ContextMenuItem>
          )}
          
          <ContextMenuSeparator />
          
          <ContextMenuItem onClick={() => onDuplicate?.(data.id)}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </ContextMenuItem>
          
          <ContextMenuItem 
            onClick={() => onDelete?.(data.id)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </TooltipProvider>
  );
}