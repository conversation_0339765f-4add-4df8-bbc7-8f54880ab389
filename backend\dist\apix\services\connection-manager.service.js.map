{"version": 3, "file": "connection-manager.service.js", "sourceRoot": "", "sources": ["../../../src/apix/services/connection-manager.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,gEAA4D;AAC5D,yDAAsD;AACtD,2CAAwC;AAExC,8CAA+D;AAkBxD,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAMnC,YACU,MAAqB,EACN,YAA2B;QAD1C,WAAM,GAAN,MAAM,CAAe;QACE,iBAAY,GAAZ,YAAY,CAAO;QAPnC,WAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;QAC5D,gBAAW,GAAG,IAAI,GAAG,EAA6B,CAAC;QACnD,oBAAe,GAAG,IAAI,GAAG,EAAuB,CAAC;QACjD,4BAAuB,GAAG,IAAI,GAAG,EAAuB,CAAC;QAM/D,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,QAAgB,EAChB,SAAiB,EACjB,MAA0B,EAC1B,cAAsB,EACtB,UAAsB,EACtB,WAAgC,EAAE,EAClC,SAAkB,EAClB,SAAkB;QAElB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACzD,IAAI,EAAE;gBACJ,SAAS;gBACT,cAAc;gBACd,MAAM;gBACN,UAAU;gBACV,QAAQ,EAAE,EAAE;gBACZ,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,MAAM,EAAE,2BAAgB,CAAC,SAAS;aACnC;SACF,CAAC,CAAC;QAEH,MAAM,OAAO,GAAsB;YACjC,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,SAAS;YACT,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ,EAAE,IAAI,GAAG,EAAE;YACnB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,cAAc,EAAE,IAAI,GAAG,EAAE;YACzB,QAAQ;YACR,cAAc,EAAE,CAAC;YACjB,SAAS;YACT,SAAS;SACV,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAExC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,CAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhE,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,uBAAuB,QAAQ,aAAa,MAAM,WAAW,cAAc,EAAE,CAC9E,CAAC;QACF,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,QAAgB,EAChB,OAME;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACzC,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAClD,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,OAAO,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,IAAI,EAAE;gBACJ,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,aAAa,EAAE,OAAO,CAAC,YAAY;oBACjC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;oBAChC,CAAC,CAAC,SAAS;gBACb,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,IAAI,EAAE;gBACJ,MAAM,EAAE,2BAAgB,CAAC,YAAY;gBACrC,QAAQ,EAAE;oBACR,GAAG,OAAO,CAAC,QAAQ;oBACnB,cAAc,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACxC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,YAAY;iBAC5C;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAElC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACjE,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACjC,IAAI,eAAe,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC/B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC9C,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CACrD,OAAO,CAAC,cAAc,CACvB,CAAC;QACF,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;QAEtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,aAAa,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,QAAgB;QAEhB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACxC,cAAc,QAAQ,EAAE,CACzB,CAAC;QACF,OAAO,MAAM,IAAI,IAAI,CAAC;IACxB,CAAC;IAED,kBAAkB,CAAC,MAAc;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrD,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpD,CAAC;IAED,0BAA0B,CAAC,cAAsB;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACrE,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACpD,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED,kBAAkB;QAUhB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAG,MAAM,CAAC;QAChC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAC5B,YAAY,EAAE,EAAgC;YAC9C,cAAc,EAAE,EAA4B;YAC5C,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;YACtC,cAAc,EAAE,CAAC;YACjB,kBAAkB,EAAE,CAAC;YACrB,kBAAkB,EAAE,CAAC;YACrB,uBAAuB,EAAE,CAAC;SAC3B,CAAC;QAEF,MAAM,CAAC,MAAM,CAAC,qBAAU,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACzC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC;gBAC1C,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAG1D,IAAI,GAAG,GAAG,OAAO,CAAC,YAAY,GAAG,gBAAgB,EAAE,CAAC;gBAClD,YAAY,EAAE,CAAC;YACjB,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;gBAC/B,iBAAiB,EAAE,CAAC;YACtB,CAAC;YAGD,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;gBACtD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACvB,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;oBAC3D,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,cAAc,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,KAAK,CAAC,kBAAkB;YACtB,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,KAAK,CAAC,kBAAkB,GAAG,YAAY,CAAC;QACxC,KAAK,CAAC,uBAAuB,GAAG,iBAAiB,CAAC;QAElD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,QAAgB,EAChB,OAAe;QAEf,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE9B,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,IAAI,EAAE;gBACJ,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;aACvC;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,2BAA2B,CAC/B,QAAgB,EAChB,OAAe;QAEf,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEjC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;YACzB,IAAI,EAAE;gBACJ,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;aACvC;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAED,YAAY,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3C,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtB,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,iBAAiB;aAC1B,MAAM,CAAC;YACN,IAAI,EAAE;gBACJ,YAAY,EAAE,OAAO,CAAC,EAAE;gBACxB,SAAS;gBACT,SAAS,EAAE,OAAO;gBAClB,cAAc,EAAE,OAAO,CAAC,cAAc;aACvC;SACF,CAAC;aACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACP,CAAC;IAED,eAAe,CACb,QAAgB,EAChB,SAAkB;QAOlB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC;QAE1B,MAAM,OAAO,GAAG,SAAS;YACvB,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC;YACvC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QAEvD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAElD,OAAO;YACL,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;YACxD,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;YACzB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;YACzB,KAAK,EAAE,OAAO,CAAC,MAAM;SACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,OAAgB;QACtD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YACpC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,OAAO;SACnB,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,QAAgB,EAChB,OAA0B;QAE1B,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAEO,iBAAiB;QACvB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACpC,CAAC,EAAE,MAAM,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,OAAO,CAAC;QACxB,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,GAAG,GAAG,OAAO,CAAC,YAAY,GAAG,OAAO,EAAE,CAAC;gBACzC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,MAAM,uBAAuB,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;CACF,CAAA;AArYY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;IASR,WAAA,IAAA,eAAM,EAAC,6BAAa,CAAC,CAAA;qCADN,8BAAa;GAPpB,wBAAwB,CAqYpC"}