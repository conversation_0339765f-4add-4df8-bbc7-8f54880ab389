import { Injectable, LoggerService, ConsoleLogger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

export enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3,
    VERBOSE = 4,
}

export interface LogEntry {
    timestamp: string;
    level: string;
    message: string;
    context?: string;
    trace?: string;
    userId?: string;
    organizationId?: string;
    requestId?: string;
    sessionId?: string;
    metadata?: Record<string, any>;
}

export interface LogQuery {
    level?: string;
    context?: string;
    userId?: string;
    organizationId?: string;
    requestId?: string;
    sessionId?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    page?: number;
    limit?: number;
}

@Injectable()
export class LoggingService extends ConsoleLogger implements LoggerService {
    private logLevel: LogLevel;
    private logDirectory: string;
    private enableFileLogging: boolean;
    private enableConsoleLogging: boolean;
    private logRotationEnabled: boolean;
    private maxLogFiles: number;
    private maxLogSize: number;

    constructor(private configService: ConfigService) {
        super('SynapseAI');

        this.logLevel = this.getLogLevel(configService.get('LOG_LEVEL', 'info'));
        this.logDirectory = configService.get('LOG_DIRECTORY', './logs');
        this.enableFileLogging = configService.get('ENABLE_FILE_LOGGING', 'true') === 'true';
        this.enableConsoleLogging = configService.get('ENABLE_CONSOLE_LOGGING', 'true') === 'true';
        this.logRotationEnabled = configService.get('LOG_ROTATION_ENABLED', 'true') === 'true';
        this.maxLogFiles = parseInt(configService.get('MAX_LOG_FILES', '10'));
        this.maxLogSize = parseInt(configService.get('MAX_LOG_SIZE', '10485760')); // 10MB

        this.initializeLogDirectory();
    }

    private getLogLevel(level: string): LogLevel {
        switch (level.toLowerCase()) {
            case 'error': return LogLevel.ERROR;
            case 'warn': return LogLevel.WARN;
            case 'info': return LogLevel.INFO;
            case 'debug': return LogLevel.DEBUG;
            case 'verbose': return LogLevel.VERBOSE;
            default: return LogLevel.INFO;
        }
    }

    private initializeLogDirectory(): void {
        if (this.enableFileLogging && !fs.existsSync(this.logDirectory)) {
            fs.mkdirSync(this.logDirectory, { recursive: true });
        }
    }

    private createLogEntry(
        level: string,
        message: string,
        context?: string,
        trace?: string,
        metadata?: Record<string, any>
    ): LogEntry {
        return {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            context,
            trace,
            userId: metadata?.userId,
            organizationId: metadata?.organizationId,
            requestId: metadata?.requestId,
            sessionId: metadata?.sessionId,
            metadata: metadata ? { ...metadata } : undefined,
        };
    }

    private formatLogEntry(entry: LogEntry): string {
        const parts = [
            entry.timestamp,
            `[${entry.level}]`,
            entry.context ? `[${entry.context}]` : '',
            entry.message
        ].filter(Boolean);

        let formatted = parts.join(' ');

        if (entry.userId) formatted += ` | User: ${entry.userId}`;
        if (entry.organizationId) formatted += ` | Org: ${entry.organizationId}`;
        if (entry.requestId) formatted += ` | Request: ${entry.requestId}`;
        if (entry.sessionId) formatted += ` | Session: ${entry.sessionId}`;

        if (entry.metadata && Object.keys(entry.metadata).length > 0) {
            formatted += ` | Metadata: ${JSON.stringify(entry.metadata)}`;
        }

        if (entry.trace) {
            formatted += `\n${entry.trace}`;
        }

        return formatted;
    }

    private async writeToFile(entry: LogEntry): Promise<void> {
        if (!this.enableFileLogging) return;

        try {
            const filename = `app-${new Date().toISOString().split('T')[0]}.log`;
            const filepath = path.join(this.logDirectory, filename);

            // Check if log rotation is needed
            if (this.logRotationEnabled && fs.existsSync(filepath)) {
                const stats = fs.statSync(filepath);
                if (stats.size > this.maxLogSize) {
                    await this.rotateLogFile(filepath);
                }
            }

            const logLine = this.formatLogEntry(entry) + '\n';
            fs.appendFileSync(filepath, logLine, 'utf8');
        } catch (error) {
            console.error('Failed to write log to file:', error);
        }
    }

    private async rotateLogFile(filepath: string): Promise<void> {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const rotatedPath = filepath.replace('.log', `-${timestamp}.log`);

            fs.renameSync(filepath, rotatedPath);

            // Clean up old log files
            await this.cleanupOldLogFiles();
        } catch (error) {
            console.error('Failed to rotate log file:', error);
        }
    }

    private async cleanupOldLogFiles(): Promise<void> {
        try {
            const files = fs.readdirSync(this.logDirectory)
                .filter(file => file.endsWith('.log'))
                .map(file => ({
                    name: file,
                    path: path.join(this.logDirectory, file),
                    stats: fs.statSync(path.join(this.logDirectory, file))
                }))
                .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

            if (files.length > this.maxLogFiles) {
                const filesToDelete = files.slice(this.maxLogFiles);
                for (const file of filesToDelete) {
                    fs.unlinkSync(file.path);
                }
            }
        } catch (error) {
            console.error('Failed to cleanup old log files:', error);
        }
    }

    private shouldLog(level: LogLevel): boolean {
        return level <= this.logLevel;
    }

    private async writeLog(
        level: LogLevel,
        levelName: string,
        message: string,
        context?: string,
        trace?: string,
        metadata?: Record<string, any>
    ): Promise<void> {
        if (!this.shouldLog(level)) return;

        const entry = this.createLogEntry(levelName, message, context, trace, metadata);

        // Console logging
        if (this.enableConsoleLogging) {
            const formatted = this.formatLogEntry(entry);
            console.log(formatted);
        }

        // File logging
        await this.writeToFile(entry);
    }

    // Override ConsoleLogger methods
    error(message: any, trace?: string, context?: string, metadata?: Record<string, any>): void {
        this.writeLog(LogLevel.ERROR, 'ERROR', message, context, trace, metadata);
    }

    warn(message: any, context?: string, metadata?: Record<string, any>): void {
        this.writeLog(LogLevel.WARN, 'WARN', message, context, undefined, metadata);
    }

    log(message: any, context?: string, metadata?: Record<string, any>): void {
        this.writeLog(LogLevel.INFO, 'INFO', message, context, undefined, metadata);
    }

    debug(message: any, context?: string, metadata?: Record<string, any>): void {
        this.writeLog(LogLevel.DEBUG, 'DEBUG', message, context, undefined, metadata);
    }

    verbose(message: any, context?: string, metadata?: Record<string, any>): void {
        this.writeLog(LogLevel.VERBOSE, 'VERBOSE', message, context, undefined, metadata);
    }

    // Custom methods for structured logging
    async logUserAction(
        userId: string,
        organizationId: string,
        action: string,
        resource: string,
        details?: Record<string, any>,
        requestId?: string
    ): Promise<void> {
        await this.writeLog(LogLevel.INFO, 'INFO', `User action: ${action} on ${resource}`, 'USER_ACTION', undefined, {
            userId,
            organizationId,
            action,
            resource,
            details,
            requestId,
        });
    }

    async logSystemEvent(
        event: string,
        details?: Record<string, any>,
        level: 'info' | 'warn' | 'error' = 'info'
    ): Promise<void> {
        const logLevel = level === 'error' ? LogLevel.ERROR : level === 'warn' ? LogLevel.WARN : LogLevel.INFO;
        await this.writeLog(logLevel, level.toUpperCase(), `System event: ${event}`, 'SYSTEM', undefined, {
            event,
            details,
        });
    }

    async logApiRequest(
        method: string,
        path: string,
        statusCode: number,
        responseTime: number,
        userId?: string,
        organizationId?: string,
        requestId?: string
    ): Promise<void> {
        await this.writeLog(LogLevel.INFO, 'INFO', `${method} ${path} - ${statusCode} (${responseTime}ms)`, 'API', undefined, {
            method,
            path,
            statusCode,
            responseTime,
            userId,
            organizationId,
            requestId,
        });
    }

    async logSecurityEvent(
        event: string,
        severity: 'low' | 'medium' | 'high' | 'critical',
        details?: Record<string, any>,
        userId?: string,
        organizationId?: string
    ): Promise<void> {
        const logLevel = severity === 'critical' || severity === 'high' ? LogLevel.ERROR : LogLevel.WARN;
        await this.writeLog(logLevel, severity === 'critical' ? 'ERROR' : 'WARN', `Security event: ${event}`, 'SECURITY', undefined, {
            event,
            severity,
            details,
            userId,
            organizationId,
        });
    }

    async logPerformanceMetric(
        metric: string,
        value: number,
        unit: string,
        context?: string,
        metadata?: Record<string, any>
    ): Promise<void> {
        await this.writeLog(LogLevel.INFO, 'INFO', `Performance metric: ${metric} = ${value} ${unit}`, context || 'PERFORMANCE', undefined, {
            metric,
            value,
            unit,
            ...metadata,
        });
    }

    async searchLogs(query: LogQuery): Promise<{ logs: LogEntry[]; total: number }> {
        // This is a basic implementation for file-based logs
        // In production, you might want to use a proper log aggregation service like ELK stack

        const logs: LogEntry[] = [];
        const { page = 1, limit = 100 } = query;

        try {
            const logFiles = fs.readdirSync(this.logDirectory)
                .filter(file => file.endsWith('.log'))
                .map(file => path.join(this.logDirectory, file));

            for (const logFile of logFiles) {
                const content = fs.readFileSync(logFile, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    try {
                        // Parse log line back to LogEntry
                        const entry = this.parseLogLine(line);
                        if (entry && this.matchesQuery(entry, query)) {
                            logs.push(entry);
                        }
                    } catch (error) {
                        // Skip malformed log lines
                    }
                }
            }

            // Sort by timestamp (newest first)
            logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const paginatedLogs = logs.slice(startIndex, startIndex + limit);

            return {
                logs: paginatedLogs,
                total: logs.length,
            };
        } catch (error) {
            console.error('Failed to search logs:', error);
            return { logs: [], total: 0 };
        }
    }

    private parseLogLine(line: string): LogEntry | null {
        try {
            // Basic parsing - in production, you might want more sophisticated parsing
            const timestampMatch = line.match(/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)/);
            const levelMatch = line.match(/\[([A-Z]+)\]/);
            const contextMatch = line.match(/\[([A-Z_]+)\]/g);

            if (!timestampMatch || !levelMatch) return null;

            const timestamp = timestampMatch[1];
            const level = levelMatch[1];
            const context = contextMatch && contextMatch.length > 1 ? contextMatch[1].slice(1, -1) : undefined;

            // Extract message and metadata
            let message = line.substring(line.indexOf(']') + 1);
            if (context) {
                message = message.substring(message.indexOf(']') + 1);
            }
            message = message.trim();

            // Extract metadata if present
            const metadataMatch = message.match(/\| Metadata: ({.*})$/);
            let metadata: Record<string, any> | undefined;
            if (metadataMatch) {
                try {
                    metadata = JSON.parse(metadataMatch[1]);
                    message = message.replace(metadataMatch[0], '').trim();
                } catch (error) {
                    // Failed to parse metadata
                }
            }

            return {
                timestamp,
                level,
                message,
                context,
                metadata,
            };
        } catch (error) {
            return null;
        }
    }

    private matchesQuery(entry: LogEntry, query: LogQuery): boolean {
        if (query.level && entry.level !== query.level.toUpperCase()) return false;
        if (query.context && entry.context !== query.context) return false;
        if (query.userId && entry.userId !== query.userId) return false;
        if (query.organizationId && entry.organizationId !== query.organizationId) return false;
        if (query.requestId && entry.requestId !== query.requestId) return false;
        if (query.sessionId && entry.sessionId !== query.sessionId) return false;

        if (query.startDate && new Date(entry.timestamp) < query.startDate) return false;
        if (query.endDate && new Date(entry.timestamp) > query.endDate) return false;

        if (query.search) {
            const searchLower = query.search.toLowerCase();
            const messageMatch = entry.message.toLowerCase().includes(searchLower);
            const contextMatch = entry.context?.toLowerCase().includes(searchLower);
            const metadataMatch = entry.metadata ? JSON.stringify(entry.metadata).toLowerCase().includes(searchLower) : false;

            if (!messageMatch && !contextMatch && !metadataMatch) return false;
        }

        return true;
    }

    async getLogStats(): Promise<{
        totalLogs: number;
        logsByLevel: Record<string, number>;
        logsByContext: Record<string, number>;
        recentErrors: number;
        diskUsage: number;
    }> {
        try {
            const stats = {
                totalLogs: 0,
                logsByLevel: {} as Record<string, number>,
                logsByContext: {} as Record<string, number>,
                recentErrors: 0,
                diskUsage: 0,
            };

            const logFiles = fs.readdirSync(this.logDirectory)
                .filter(file => file.endsWith('.log'))
                .map(file => path.join(this.logDirectory, file));

            const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours

            for (const logFile of logFiles) {
                const fileStats = fs.statSync(logFile);
                stats.diskUsage += fileStats.size;

                const content = fs.readFileSync(logFile, 'utf8');
                const lines = content.split('\n').filter(line => line.trim());

                for (const line of lines) {
                    const entry = this.parseLogLine(line);
                    if (entry) {
                        stats.totalLogs++;

                        // Count by level
                        stats.logsByLevel[entry.level] = (stats.logsByLevel[entry.level] || 0) + 1;

                        // Count by context
                        if (entry.context) {
                            stats.logsByContext[entry.context] = (stats.logsByContext[entry.context] || 0) + 1;
                        }

                        // Count recent errors
                        if (entry.level === 'ERROR' && new Date(entry.timestamp) > recentDate) {
                            stats.recentErrors++;
                        }
                    }
                }
            }

            return stats;
        } catch (error) {
            console.error('Failed to get log stats:', error);
            return {
                totalLogs: 0,
                logsByLevel: {},
                logsByContext: {},
                recentErrors: 0,
                diskUsage: 0,
            };
        }
    }
}