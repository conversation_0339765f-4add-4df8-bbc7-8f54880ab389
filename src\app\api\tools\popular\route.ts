import { NextRequest, NextResponse } from 'next/server';
import { create<PERSON>etHandler } from '@/lib/utils/api-handler';
import { z } from 'zod';
import { ValidatedRequest, AuthenticatedRequest, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { Permissions, Role } from '@/lib/types/auth';
import { Tool } from '@prisma/client';
import apiClient from '@/lib/api-client';
import { successResponse } from '@/lib/utils/api-responses';
// Validation schema for params
const paramsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(10),
});

// GET /api/tools/popular - Get popular tools
export const getPopularTools = withAuthAndValidation(
    async (req: AuthenticatedRequest & ValidatedRequest) => {
    const { limit } = req.validatedParams;
        const tools = await apiClient.get('/tools/popular', {   query: { limit } } ) as unknown as Tool[];
    return successResponse(tools, "Popular tools fetched successfully");
  },
  {
    requiredPermissions: [Permissions.TOOL_READ as unknown as string],
    requiredRoles: [Role.SUPER_ADMIN as unknown as string],
    paramsSchema,
  } 
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '10';

    const response = await  fetch(`${API_BASE_URL}/api/tools/popular?limit=${limit}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
      },
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Popular Tools API Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}