"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import apiClient from "@/lib/api-client";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  Zap,
  BarChart3,
  Users,
  RefreshCw,
} from "lucide-react";

interface AgentStatus {
  id: string;
  name: string;
  status: "online" | "offline" | "error" | "busy";
  lastActive: string;
  tasks: number;
  completedTasks: number;
}

interface WorkflowStatus {
  id: string;
  name: string;
  status: "running" | "completed" | "failed" | "pending";
  progress: number;
  startTime: string;
  executionTime: number;
}

interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  trend: "up" | "down" | "stable";
}

const MonitoringWidgets = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [agentStatuses, setAgentStatuses] = useState<AgentStatus[]>([]);
  const [workflowStatuses, setWorkflowStatuses] = useState<WorkflowStatus[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Load real monitoring data
  const loadMonitoringData = useCallback(async () => {
    try {
      setLoading(true);

      // Load agent statuses
      const agentsResponse = await apiClient.get('/agents/status');
      setAgentStatuses(agentsResponse as AgentStatus[]);

      // Load workflow statuses
      const workflowsResponse = await apiClient.get('/workflows/status');
      setWorkflowStatuses(workflowsResponse as WorkflowStatus[]);

      // Load system metrics
      const metricsResponse = await apiClient.get('/system/metrics');
      setSystemMetrics(metricsResponse as SystemMetric[]);

    } catch (error: any) {
      console.error('Failed to load monitoring data:', error);
      toast({
        title: "Error",
        description: "Failed to load monitoring data.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadMonitoringData();

    // Set up real-time updates
    const interval = setInterval(loadMonitoringData, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [loadMonitoringData]);


// Simulate real-time updates
useEffect(() => {
  const interval = setInterval(() => {
    // Update agent statuses
    setAgentStatuses((prev) =>
      prev.map((agent) => {
        if (Math.random() > 0.8) {
          const statuses = ["online", "offline", "error", "busy"] as const;
          return {
            ...agent,
            status: statuses[Math.floor(Math.random() * statuses.length)],
            lastActive: "Just now",
          };
        }
        return agent;
      }),
    );

    // Update workflow progress
    setWorkflowStatuses((prev) =>
      prev.map((workflow) => {
        if (workflow.status === "running" && workflow.progress < 100) {
          const newProgress = Math.min(
            workflow.progress + Math.floor(Math.random() * 5),
            100,
          );
          return {
            ...workflow,
            progress: newProgress,
            executionTime: workflow.executionTime + 5,
            status: newProgress === 100 ? "completed" : "running",
          };
        }
        return workflow;
      }),
    );

    // Update system metrics
    setSystemMetrics((prev) =>
      prev.map((metric) => {
        if (Math.random() > 0.7) {
          const trends = ["up", "down", "stable"] as const;
          let newValue = metric.value;

          if (metric.name === "API Response Time") {
            newValue = Math.max(
              150,
              Math.min(250, metric.value + (Math.random() > 0.5 ? 5 : -5)),
            );
          } else if (metric.name === "Active Users") {
            newValue = Math.max(
              1000,
              Math.min(1500, metric.value + (Math.random() > 0.5 ? 15 : -10)),
            );
          } else if (metric.name === "System Load") {
            newValue = Math.max(
              30,
              Math.min(60, metric.value + (Math.random() > 0.5 ? 2 : -2)),
            );
          } else if (metric.name === "Error Rate") {
            newValue = Math.max(
              0.05,
              Math.min(
                0.2,
                metric.value + (Math.random() > 0.5 ? 0.01 : -0.01),
              ),
            );
          }

          return {
            ...metric,
            value: newValue,
            trend: trends[Math.floor(Math.random() * trends.length)],
          };
        }
        return metric;
      }),
    );
  }, 3000);

  return () => clearInterval(interval);
}, []);

const getStatusBadge = (status: string) => {
  switch (status) {
    case "online":
      return <Badge className="bg-green-500">Online</Badge>;
    case "offline":
      return <Badge variant="outline">Offline</Badge>;
    case "error":
      return <Badge variant="destructive">Error</Badge>;
    case "busy":
      return <Badge className="bg-yellow-500">Busy</Badge>;
    case "running":
      return <Badge className="bg-blue-500">Running</Badge>;
    case "completed":
      return <Badge className="bg-green-500">Completed</Badge>;
    case "failed":
      return <Badge variant="destructive">Failed</Badge>;
    case "pending":
      return <Badge variant="outline">Pending</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case "up":
      return <span className="text-green-500">↑</span>;
    case "down":
      return <span className="text-red-500">↓</span>;
    case "stable":
      return <span className="text-gray-500">→</span>;
    default:
      return null;
  }
};

return (
  <div className="w-full bg-background/60 backdrop-blur-lg rounded-lg border border-border/50 p-4 space-y-4">
    <div className="flex items-center justify-between">
      <h2 className="text-2xl font-semibold">System Monitoring</h2>
      <Button size="sm" variant="outline" className="flex items-center gap-1">
        <RefreshCw className="h-4 w-4" />
        Refresh
      </Button>
    </div>

    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-3 w-full max-w-md mx-auto mb-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="agents">Agents</TabsTrigger>
        <TabsTrigger value="workflows">Workflows</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {systemMetrics.map((metric) => (
            <Card
              key={metric.name}
              className="bg-background/80 backdrop-blur-sm"
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {metric.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">
                    {metric.name === "Error Rate"
                      ? metric.value.toFixed(2)
                      : Math.round(metric.value)}
                    {metric.unit}
                  </div>
                  <div className="flex items-center">
                    {getTrendIcon(metric.trend)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="bg-background/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Agent Status Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-green-500"></span>
                    Online
                  </span>
                  <span>
                    {
                      agentStatuses.filter((a) => a.status === "online")
                        .length
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-yellow-500"></span>
                    Busy
                  </span>
                  <span>
                    {agentStatuses.filter((a) => a.status === "busy").length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-gray-300"></span>
                    Offline
                  </span>
                  <span>
                    {
                      agentStatuses.filter((a) => a.status === "offline")
                        .length
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-red-500"></span>
                    Error
                  </span>
                  <span>
                    {agentStatuses.filter((a) => a.status === "error").length}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-background/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Workflow Status Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-blue-500"></span>
                    Running
                  </span>
                  <span>
                    {
                      workflowStatuses.filter((w) => w.status === "running")
                        .length
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-green-500"></span>
                    Completed
                  </span>
                  <span>
                    {
                      workflowStatuses.filter((w) => w.status === "completed")
                        .length
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-red-500"></span>
                    Failed
                  </span>
                  <span>
                    {
                      workflowStatuses.filter((w) => w.status === "failed")
                        .length
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-2">
                    <span className="h-3 w-3 rounded-full bg-gray-300"></span>
                    Pending
                  </span>
                  <span>
                    {
                      workflowStatuses.filter((w) => w.status === "pending")
                        .length
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="agents" className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          {agentStatuses.map((agent) => (
            <Card
              key={agent.id}
              className="bg-background/80 backdrop-blur-sm"
            >
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                      {agent.status === "online" && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {agent.status === "offline" && (
                        <Clock className="h-4 w-4 text-gray-500" />
                      )}
                      {agent.status === "error" && (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                      {agent.status === "busy" && (
                        <Activity className="h-4 w-4 text-yellow-500" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{agent.name}</h3>
                      <p className="text-xs text-muted-foreground">
                        Last active: {agent.lastActive}
                      </p>
                    </div>
                  </div>
                  <div>{getStatusBadge(agent.status)}</div>
                </div>

                <div className="mt-4">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>
                      Tasks: {agent.completedTasks}/{agent.tasks}
                    </span>
                    <span>
                      {Math.round(
                        (agent.completedTasks / Math.max(1, agent.tasks)) *
                        100,
                      )}
                      %
                    </span>
                  </div>
                  <Progress
                    value={
                      (agent.completedTasks / Math.max(1, agent.tasks)) * 100
                    }
                    className="h-1"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="workflows" className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          {workflowStatuses.map((workflow) => (
            <Card
              key={workflow.id}
              className="bg-background/80 backdrop-blur-sm"
            >
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                      {workflow.status === "running" && (
                        <Zap className="h-4 w-4 text-blue-500" />
                      )}
                      {workflow.status === "completed" && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {workflow.status === "failed" && (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                      {workflow.status === "pending" && (
                        <Clock className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{workflow.name}</h3>
                      <p className="text-xs text-muted-foreground">
                        Started: {workflow.startTime}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {workflow.executionTime > 0 && (
                      <span className="text-xs text-muted-foreground">
                        {workflow.executionTime}s
                      </span>
                    )}
                    {getStatusBadge(workflow.status)}
                  </div>
                </div>

                <div className="mt-4">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{workflow.progress}%</span>
                  </div>
                  <Progress value={workflow.progress} className="h-1" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>
    </Tabs>
  </div>
);
};

export default MonitoringWidgets;
