import { Injectable, UnauthorizedException, BadRequestException, ConflictException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { SessionsService } from '../sessions/sessions.service';
import {
  LoginDto,
  RegisterDto,
  RefreshTokenDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  ChangePasswordDto,
  EnableMFADto,
  VerifyMFADto,
  UpdateProfileDto,
  InviteUserDto,
  AcceptInviteDto,
  UpdateUserRoleDto,
  CreateAPIKeyDto,
  AuthResponse,
  MFASetupResponse,
  APIKeyResponse
} from './dto/auth.dto';
import * as bcrypt from 'bcryptjs';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import * as crypto from 'crypto';
import { Role } from '@prisma/client';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

@Injectable()
export class AuthService {
  private readonly jwtSecret: string;
  private readonly jwtRefreshSecret: string;
  private readonly jwtExpiresIn: string;
  private readonly jwtRefreshExpiresIn: string;

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private sessionsService: SessionsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.jwtSecret = this.configService.get<string>('JWT_SECRET') || 'your-secret-key';
    this.jwtRefreshSecret = this.configService.get<string>('JWT_REFRESH_SECRET') || 'your-refresh-secret-key';
    this.jwtExpiresIn = this.configService.get<string>('JWT_EXPIRES_IN') || '15m';
    this.jwtRefreshExpiresIn = this.configService.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d';
  }

  async register(registerDto: RegisterDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse> {
    const { email, password, firstName, lastName, organizationName, organizationSlug } = registerDto;

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Check if organization slug is available
    const existingOrg = await this.prisma.organization.findUnique({
      where: { slug: organizationSlug },
    });

    if (existingOrg) {
      throw new ConflictException('Organization slug is already taken');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create organization and user in transaction
    const result = await this.prisma.$transaction(async (tx) => {
      // Create organization with default settings
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          slug: organizationSlug,
          settings: {
            theme: 'light',
            language: 'en',
            timezone: 'UTC',
            dateFormat: 'MM/dd/yyyy',
            timeFormat: '12h',
            currency: 'USD',
            features: {
              workflows: true,
              agents: true,
              tools: true,
              analytics: true,
              apiAccess: true,
            },
            security: {
              mfaRequired: false,
              sessionTimeout: 24,
              passwordPolicy: {
                minLength: 8,
                requireUppercase: true,
                requireLowercase: true,
                requireNumbers: true,
                requireSpecialChars: true,
              },
            },
          },
          branding: {
            primaryColor: '#3b82f6',
            secondaryColor: '#1e40af',
            logo: null,
            favicon: null,
            customCSS: null,
          },
        },
      });

      // Create user as ORG_ADMIN
      const user = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          role: Role.ORG_ADMIN,
          organizationId: organization.id,
          preferences: {
            theme: 'system',
            language: 'en',
            timezone: 'UTC',
            notifications: {
              email: true,
              push: true,
              workflow: true,
              agent: true,
              system: true,
            },
            viewMode: 'grid',
            sidebarCollapsed: false,
          },
        },
        include: {
          organization: true,
        },
      });

      // Log audit event
      await tx.auditLog.create({
        data: {
          userId: user.id,
          organizationId: organization.id,
          action: 'USER_REGISTERED',
          resource: 'user',
          resourceId: user.id,
          details: {
            method: 'email',
            userAgent,
            ipAddress,
            organizationCreated: true,
          },
        },
      });

      return { user, organization };
    });

    // Generate tokens
    const tokens = await this.generateTokens(result.user, false, ipAddress, userAgent);

    // Create session
    const session = await this.sessionsService.createSession(
      result.user.id,
      result.organization.id,
      {
        loginMethod: 'email',
        userAgent,
        ipAddress,
        mfaVerified: false,
      }
    );

    return {
      user: {
        id: result.user.id,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        role: result.user.role,
        avatar: result.user.avatar,
        organization: {
          id: result.organization.id,
          name: result.organization.name,
          slug: result.organization.slug,
          settings: result.organization.settings,
          branding: result.organization.branding,
        },
        permissions: this.getRolePermissions(result.user.role),
        preferences: result.user.preferences,
      },
      tokens,
      session: {
        id: session.id,
        expiresAt: session.expiresAt.toISOString(),
      },
    };
  }

  async login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse> {
    const { email, password, organizationSlug, mfaCode, rememberMe } = loginDto;

    // Find user with organization
    const user = await this.prisma.user.findFirst({
      where: {
        email,
        isActive: true,
        ...(organizationSlug && {
          organization: {
            slug: organizationSlug,
          },
        }),
      },
      include: {
        organization: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if MFA is required
    if (user.mfaEnabled && !mfaCode) {
      throw new UnauthorizedException('MFA code required');
    }

    // Verify MFA if provided
    if (user.mfaEnabled && mfaCode) {
      const isValidMFA = speakeasy.totp.verify({
        secret: user.mfaSecret!,
        encoding: 'base32',
        token: mfaCode,
        window: 2,
      });

      if (!isValidMFA) {
        throw new UnauthorizedException('Invalid MFA code');
      }
    }

    // Update last login
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Generate tokens
    const tokens = await this.generateTokens(user, rememberMe, ipAddress, userAgent);

    // Create session
    const session = await this.sessionsService.createSession(
      user.id,
      user.organizationId,
      {
        loginMethod: 'email',
        userAgent,
        ipAddress,
        mfaVerified: user.mfaEnabled,
        rememberMe,
      }
    );

    // Log audit event
    await this.logAuditEvent(user.id, user.organizationId, 'USER_LOGIN', 'user', user.id, {
      method: 'email',
      userAgent,
      ipAddress,
      mfaUsed: user.mfaEnabled,
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        avatar: user.avatar,
        organization: {
          id: user.organization.id,
          name: user.organization.name,
          slug: user.organization.slug,
          settings: user.organization.settings,
          branding: user.organization.branding,
        },
        permissions: this.getRolePermissions(user.role),
        preferences: user.preferences,
      },
      tokens,
      session: {
        id: session.id,
        expiresAt: session.expiresAt.toISOString(),
      },
    };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto, ipAddress?: string, userAgent?: string): Promise<AuthResponse> {
    const { refreshToken } = refreshTokenDto;

    // Find and validate refresh token
    const storedToken = await this.prisma.refreshToken.findUnique({
      where: { token: refreshToken },
      include: {
        user: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!storedToken || !storedToken.isActive || storedToken.expiresAt < new Date()) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    // Check if user is still active
    if (!storedToken.user.isActive) {
      throw new UnauthorizedException('User account is deactivated');
    }

    // Revoke the current refresh token (token rotation)
    await this.prisma.refreshToken.update({
      where: { id: storedToken.id },
      data: {
        isActive: false,
        revokedAt: new Date(),
      },
    });

    // Generate new tokens
    const tokens = await this.generateTokens(storedToken.user, false, ipAddress, userAgent);

    // Update session if it exists
    const sessions = await this.prisma.session.findMany({
      where: {
        userId: storedToken.user.id,
        isActive: true,
        expiresAt: { gt: new Date() }
      },
      orderBy: { updatedAt: 'desc' },
      take: 1,
    });

    const session = sessions[0];

    if (session) {
      await this.prisma.session.update({
        where: { id: session.id },
        data: { updatedAt: new Date() },
      });
    }

    // Log audit event
    await this.logAuditEvent(
      storedToken.user.id,
      storedToken.user.organizationId,
      'TOKEN_REFRESHED',
      'auth',
      storedToken.user.id,
      { ipAddress, userAgent }
    );

    return {
      user: {
        id: storedToken.user.id,
        email: storedToken.user.email,
        firstName: storedToken.user.firstName,
        lastName: storedToken.user.lastName,
        role: storedToken.user.role,
        organization: storedToken.user.organization,
      },
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresAt: tokens.expiresAt,
      session: session ? {
        id: session.id,
        expiresAt: session.expiresAt.toISOString(),
      } : undefined,
    };
  }

  async logout(userId: string): Promise<void> {
    // Invalidate all sessions for the user
    await this.sessionsService.invalidateUserSessions(userId);

    // Log audit event
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (user) {
      await this.logAuditEvent(userId, user.organizationId, 'USER_LOGOUT', 'user', userId, {
        method: 'manual',
      });
    }
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const { email, organizationSlug } = forgotPasswordDto;

    const user = await this.prisma.user.findFirst({
      where: {
        email,
        isActive: true,
        ...(organizationSlug && {
          organization: {
            slug: organizationSlug,
          },
        }),
      },
    });

    if (!user) {
      // Don't reveal if user exists or not
      return;
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour

    // Store reset token in cache
    await this.cacheManager.set(
      `password_reset:${resetToken}`,
      { userId: user.id, email: user.email },
      3600000 // 1 hour
    );

    // TODO: Send email with reset link
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);

    // Log audit event
    await this.logAuditEvent(user.id, user.organizationId, 'PASSWORD_RESET_REQUESTED', 'user', user.id, {
      email: user.email,
    });
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, password } = resetPasswordDto;

    // Get reset token from cache
    const resetData = await this.cacheManager.get(`password_reset:${token}`);
    if (!resetData) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    const { userId } = resetData as any;

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update password
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    // Remove reset token from cache
    await this.cacheManager.del(`password_reset:${token}`);

    // Invalidate all sessions
    await this.sessionsService.invalidateUserSessions(userId);

    // Log audit event
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (user) {
      await this.logAuditEvent(userId, user.organizationId, 'PASSWORD_RESET_COMPLETED', 'user', userId, {
        email: user.email,
      });
    }
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'PASSWORD_CHANGED', 'user', userId, {
      email: user.email,
    });
  }

  async setupMFA(userId: string): Promise<MFASetupResponse> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Generate MFA secret
    const secret = speakeasy.generateSecret({
      name: `${user.organization.name} (${user.email})`,
      issuer: 'SynapseAI',
    });

    // Generate QR code
    const qrCode = await qrcode.toDataURL(secret.otpauth_url!);

    // Generate backup codes
    const backupCodes = Array.from({ length: 10 }, () =>
      crypto.randomBytes(4).toString('hex').toUpperCase()
    );

    // Store secret temporarily (not enabled until verified)
    await this.cacheManager.set(
      `mfa_setup:${userId}`,
      { secret: secret.base32, backupCodes },
      1800000 // 30 minutes
    );

    return {
      secret: secret.base32!,
      qrCode,
      backupCodes,
    };
  }

  async enableMFA(userId: string, enableMFADto: EnableMFADto): Promise<void> {
    const { secret, code } = enableMFADto;

    // Get setup data from cache
    const setupData = await this.cacheManager.get(`mfa_setup:${userId}`);
    if (!setupData || (setupData as any).secret !== secret) {
      throw new BadRequestException('Invalid MFA setup session');
    }

    // Verify the code
    const isValid = speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token: code,
      window: 2,
    });

    if (!isValid) {
      throw new BadRequestException('Invalid MFA code');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Enable MFA
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        mfaEnabled: true,
        mfaSecret: secret,
      },
    });

    // Remove setup data from cache
    await this.cacheManager.del(`mfa_setup:${userId}`);

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'MFA_ENABLED', 'user', userId, {
      email: user.email,
    });
  }

  async disableMFA(userId: string, verifyMFADto: VerifyMFADto): Promise<void> {
    const { code } = verifyMFADto;

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.mfaEnabled) {
      throw new BadRequestException('MFA is not enabled for this user');
    }

    // Verify the code
    const isValid = speakeasy.totp.verify({
      secret: user.mfaSecret!,
      encoding: 'base32',
      token: code,
      window: 2,
    });

    if (!isValid) {
      throw new BadRequestException('Invalid MFA code');
    }

    // Disable MFA
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        mfaEnabled: false,
        mfaSecret: null,
      },
    });

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'MFA_DISABLED', 'user', userId, {
      email: user.email,
    });
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updateData: any = {};

    if (updateProfileDto.firstName) updateData.firstName = updateProfileDto.firstName;
    if (updateProfileDto.lastName) updateData.lastName = updateProfileDto.lastName;
    if (updateProfileDto.avatar) updateData.avatar = updateProfileDto.avatar;

    if (updateProfileDto.preferences) {
      updateData.preferences = {
        ...user.preferences,
        ...updateProfileDto.preferences,
      };
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
    });

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'PROFILE_UPDATED', 'user', userId, {
      changes: Object.keys(updateData),
    });
  }

  async validateUser(payload: any) {
    const user = await this.prisma.user.findUnique({
      where: { id: payload.sub },
      include: { organization: true },
    });

    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    return user;
  }

  async checkPermission(userId: string, resource: string, action: string, scope?: any): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.isActive) {
      return false;
    }

    const permissions = this.getRolePermissions(user.role);
    const requiredPermission = `${resource}:${action}`;

    return permissions.includes(requiredPermission) || permissions.includes('*:*');
  }

  async createAPIKey(userId: string, createAPIKeyDto: CreateAPIKeyDto): Promise<APIKeyResponse> {
    const { name, description, permissions, expiresAt } = createAPIKeyDto;

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Generate API key
    const apiKey = `sk_${crypto.randomBytes(32).toString('hex')}`;
    const hashedKey = await bcrypt.hash(apiKey, 12);

    // Store API key info in cache (in production, use a proper API key table)
    const keyData = {
      id: crypto.randomUUID(),
      name,
      description,
      permissions,
      userId,
      organizationId: user.organizationId,
      hashedKey,
      createdAt: new Date(),
      expiresAt,
    };

    await this.cacheManager.set(
      `api_key:${keyData.id}`,
      keyData,
      expiresAt ? expiresAt.getTime() - Date.now() : 0 // No expiry if not set
    );

    // Log audit event
    await this.logAuditEvent(userId, user.organizationId, 'API_KEY_CREATED', 'api_key', keyData.id, {
      name,
      permissions,
    });

    return {
      id: keyData.id,
      name,
      key: apiKey, // Only return the actual key once
      permissions,
      createdAt: keyData.createdAt.toISOString(),
      expiresAt: expiresAt?.toISOString(),
    };
  }

  private async generateTokens(user: any, rememberMe = false, ipAddress?: string, userAgent?: string): Promise<{ accessToken: string; refreshToken: string; expiresAt: number }> {
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      organizationId: user.organizationId,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.jwtSecret,
      expiresIn: this.jwtExpiresIn,
    });

    // Generate secure refresh token
    const refreshTokenValue = crypto.randomBytes(40).toString('hex');
    const refreshTokenExpiresAt = new Date();
    refreshTokenExpiresAt.setDate(refreshTokenExpiresAt.getDate() + (rememberMe ? 30 : 7));

    // Store refresh token in database
    await this.prisma.refreshToken.create({
      data: {
        token: refreshTokenValue,
        userId: user.id,
        expiresAt: refreshTokenExpiresAt,
        ipAddress,
        userAgent,
      },
    });

    const decoded = this.jwtService.decode(accessToken) as any;
    const expiresAt = decoded.exp * 1000;

    return { accessToken, refreshToken: refreshTokenValue, expiresAt };
  }

  private getRolePermissions(role: Role): string[] {
    const permissions = {
      [Role.SUPER_ADMIN]: ['*:*'],
      [Role.ORG_ADMIN]: [
        'organization:*',
        'user:*',
        'workflow:*',
        'agent:*',
        'tool:*',
        'provider:*',
        'analytics:read',
        'audit:read',
      ],
      [Role.DEVELOPER]: [
        'workflow:*',
        'agent:*',
        'tool:*',
        'provider:read',
        'analytics:read',
      ],
      [Role.VIEWER]: [
        'workflow:read',
        'agent:read',
        'tool:read',
        'provider:read',
        'analytics:read',
      ],
    };

    return permissions[role] || [];
  }

  private async logAuditEvent(
    userId: string,
    organizationId: string,
    action: string,
    resource: string,
    resourceId: string,
    details: any,
  ) {
    return this.prisma.auditLog.create({
      data: {
        userId,
        organizationId,
        action,
        resource,
        resourceId,
        details,
      },
    });
  }
}