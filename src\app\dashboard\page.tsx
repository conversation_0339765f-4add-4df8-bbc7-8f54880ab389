"use client";

import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  Zap,
  Database,
  Workflow,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Play,
  Pause,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import Link from "next/link";

interface MetricCardProps {
  title: string;
  value: string | number;
  change: string;
  trend: "up" | "down";
  icon: React.ReactNode;
}

function MetricCard({ title, value, change, trend, icon }: MetricCardProps) {
  return (
    <Card className="bg-card/80 backdrop-blur-sm hover:bg-card/90 transition-colors">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg text-primary">
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
            </div>
          </div>
          <div className={`flex items-center space-x-1 text-sm ${trend === "up" ? "text-green-600" : "text-red-600"
            }`}>
            {trend === "up" ? (
              <ArrowUpRight className="h-4 w-4" />
            ) : (
              <ArrowDownRight className="h-4 w-4" />
            )}
            <span>{change}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface WorkflowItemProps {
  id: string;
  name: string;
  status: "active" | "idle" | "error";
  lastRun: string;
  success: number;
  executions: number;
}

function WorkflowItem({ id, name, status, lastRun, success, executions }: WorkflowItemProps) {
  const statusConfig = {
    active: { color: "bg-green-500", label: "Active" },
    idle: { color: "bg-yellow-500", label: "Idle" },
    error: { color: "bg-red-500", label: "Error" }
  };

  return (
    <div className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-3">
        <div className={`w-3 h-3 rounded-full ${statusConfig[status].color}`} />
        <div>
          <p className="font-medium">{name}</p>
          <p className="text-sm text-muted-foreground">
            {executions} executions • Last run {lastRun}
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="text-right">
          <p className="text-sm font-medium">{success}% success</p>
          <Progress value={success} className="w-20 h-2" />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Play className="mr-2 h-4 w-4" />
              Run now
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Pause className="mr-2 h-4 w-4" />
              Pause
            </DropdownMenuItem>
            <DropdownMenuItem>
              <BarChart3 className="mr-2 h-4 w-4" />
              View analytics
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

interface AgentItemProps {
  id: string;
  name: string;
  status: "online" | "offline" | "busy";
  provider: string;
  usage: number;
  requests: number;
}

function AgentItem({ id, name, status, provider, usage, requests }: AgentItemProps) {
  const statusConfig = {
    online: { color: "bg-green-500", label: "Online" },
    offline: { color: "bg-gray-500", label: "Offline" },
    busy: { color: "bg-yellow-500", label: "Busy" }
  };

  return (
    <div className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-3">
        <div className={`w-3 h-3 rounded-full ${statusConfig[status].color}`} />
        <div>
          <p className="font-medium">{name}</p>
          <p className="text-sm text-muted-foreground">
            {provider} • {requests} requests today
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="text-right">
          <p className="text-sm font-medium">{usage}% usage</p>
          <Progress value={usage} className="w-20 h-2" />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Activity className="mr-2 h-4 w-4" />
              View logs
            </DropdownMenuItem>
            <DropdownMenuItem>
              <BarChart3 className="mr-2 h-4 w-4" />
              Performance
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Pause className="mr-2 h-4 w-4" />
              Stop agent
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");
  const [realTimeData, setRealTimeData] = useState({
    activeWorkflows: 0,
    activeAgents: 0,
    totalExecutions: 0,
    errorRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load real dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.get('/dashboard/metrics') as any;
        setRealTimeData(response.data || {});
      } catch (error: any) {
        const errorMessage = error?.response?.data?.message || 'Failed to load dashboard data';
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [toast]);

  // Set up real-time updates via APIX
  useEffect(() => {
    const unsubscribe = apixClient.on('system_alert', (event) => {
      if (event.data?.type === 'dashboard_metrics') {
        setRealTimeData(event.data?.metrics || {});
      }
    });

    return unsubscribe;
  }, []);

  const metrics = [
    {
      title: "Active Workflows",
      value: realTimeData.activeWorkflows,
      change: "+12%",
      trend: "up" as const,
      icon: <Workflow className="h-5 w-5" />
    },
    {
      title: "Active Agents",
      value: realTimeData.activeAgents,
      change: "+5%",
      trend: "up" as const,
      icon: <Zap className="h-5 w-5" />
    },
    {
      title: "Total Executions",
      value: realTimeData.totalExecutions.toLocaleString(),
      change: "+22%",
      trend: "up" as const,
      icon: <Activity className="h-5 w-5" />
    },
    {
      title: "Error Rate",
      value: `${realTimeData.errorRate.toFixed(2)}%`,
      change: "-0.02%",
      trend: "down" as const,
      icon: <AlertTriangle className="h-5 w-5" />
    }
  ];

  const recentWorkflows = [
    {
      id: "wf-1",
      name: "Customer Support Bot",
      status: "active" as const,
      lastRun: "2 mins ago",
      success: 98,
      executions: 156
    },
    {
      id: "wf-2",
      name: "Data Processing Pipeline",
      status: "active" as const,
      lastRun: "5 mins ago",
      success: 100,
      executions: 89
    },
    {
      id: "wf-3",
      name: "Content Generation",
      status: "idle" as const,
      lastRun: "1 hour ago",
      success: 95,
      executions: 234
    },
    {
      id: "wf-4",
      name: "Email Classification",
      status: "active" as const,
      lastRun: "3 mins ago",
      success: 99,
      executions: 67
    }
  ];

  const recentAgents = [
    {
      id: "ag-1",
      name: "Support Assistant",
      status: "online" as const,
      provider: "OpenAI GPT-4",
      usage: 78,
      requests: 234
    },
    {
      id: "ag-2",
      name: "Data Analyst",
      status: "busy" as const,
      provider: "Claude 3",
      usage: 45,
      requests: 156
    },
    {
      id: "ag-3",
      name: "Content Writer",
      status: "offline" as const,
      provider: "Gemini Pro",
      usage: 0,
      requests: 0
    },
    {
      id: "ag-4",
      name: "Code Helper",
      status: "online" as const,
      provider: "OpenAI GPT-4",
      usage: 92,
      requests: 345
    }
  ];

  const recentActivity = [
    {
      id: "1",
      type: "workflow",
      message: "Customer Support Bot completed successfully",
      time: "2 minutes ago",
      status: "success"
    },
    {
      id: "2",
      type: "agent",
      message: "Support Assistant deployed to production",
      time: "5 minutes ago",
      status: "success"
    },
    {
      id: "3",
      type: "error",
      message: "Data Processing Pipeline failed with timeout",
      time: "12 minutes ago",
      status: "error"
    },
    {
      id: "4",
      type: "workflow",
      message: "New workflow 'Email Classifier' created",
      time: "1 hour ago",
      status: "info"
    }
  ];

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span>Loading dashboard...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Dashboard</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Monitor and manage your AI workflows and agents
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Reports
            </Button>
            <Button>
              <Workflow className="mr-2 h-4 w-4" />
              Create Workflow
            </Button>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <MetricCard key={index} {...metric} />
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="agents">Agents</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Workflows */}
              <Card className="bg-card/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-lg">Recent Workflows</CardTitle>
                  <Link href="/dashboard/workflows">
                    <Button variant="ghost" size="sm">
                      View all
                      <ArrowUpRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentWorkflows.slice(0, 3).map((workflow) => (
                    <WorkflowItem key={workflow.id} {...workflow} />
                  ))}
                </CardContent>
              </Card>

              {/* Recent Agents */}
              <Card className="bg-card/80 backdrop-blur-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-lg">Active Agents</CardTitle>
                  <Link href="/dashboard/agents">
                    <Button variant="ghost" size="sm">
                      View all
                      <ArrowUpRight className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentAgents.slice(0, 3).map((agent) => (
                    <AgentItem key={agent.id} {...agent} />
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${activity.status === "success" ? "bg-green-500" :
                        activity.status === "error" ? "bg-red-500" : "bg-blue-500"
                        }`} />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{activity.message}</p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                      <Badge variant={
                        activity.status === "success" ? "secondary" :
                          activity.status === "error" ? "destructive" : "default"
                      } className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="workflows" className="space-y-6">
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">All Workflows</CardTitle>
                <Button>
                  <Workflow className="mr-2 h-4 w-4" />
                  Create New
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentWorkflows.map((workflow) => (
                  <WorkflowItem key={workflow.id} {...workflow} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents" className="space-y-6">
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">All Agents</CardTitle>
                <Button>
                  <Zap className="mr-2 h-4 w-4" />
                  Deploy New
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentAgents.map((agent) => (
                  <AgentItem key={agent.id} {...agent} />
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Quick Actions */}
        <Card className="bg-card/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/dashboard/workflows/new">
                <Button className="w-full h-auto py-6 flex flex-col items-center justify-center space-y-2 hover:bg-primary/90 transition-colors">
                  <Workflow className="h-8 w-8" />
                  <span className="font-medium">Create Workflow</span>
                  <span className="text-xs opacity-80">Build new AI workflow</span>
                </Button>
              </Link>
              <Link href="/dashboard/agents/new">
                <Button variant="outline" className="w-full h-auto py-6 flex flex-col items-center justify-center space-y-2 hover:bg-muted transition-colors">
                  <Zap className="h-8 w-8" />
                  <span className="font-medium">Deploy Agent</span>
                  <span className="text-xs opacity-80">Launch AI agent</span>
                </Button>
              </Link>
              <Link href="/dashboard/tools/new">
                <Button variant="outline" className="w-full h-auto py-6 flex flex-col items-center justify-center space-y-2 hover:bg-muted transition-colors">
                  <Database className="h-8 w-8" />
                  <span className="font-medium">Add Tool</span>
                  <span className="text-xs opacity-80">Configure new tool</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}