import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

export interface HybridAnalyticsMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  patternDistribution: Record<string, number>;
  agentUtilization: Record<string, number>;
  toolUtilization: Record<string, number>;
  errorAnalysis: {
    totalErrors: number;
    errorTypes: Record<string, number>;
    errorRate: number;
    commonErrors: Array<{ message: string; count: number }>;
  };
  performanceMetrics: {
    p50: number;
    p95: number;
    p99: number;
    minDuration: number;
    maxDuration: number;
  };
  contextSharingStats: {
    totalShares: number;
    averageShareSize: number;
    sharesByStrategy: Record<string, number>;
  };
  humanInteractionStats: {
    totalRequests: number;
    completionRate: number;
    averageResponseTime: number;
    skipRate: number;
    timeoutRate: number;
  };
  resourceUtilization: {
    averageMemoryUsage: number;
    averageTokenUsage: number;
    costAnalysis: {
      totalCost: number;
      costByComponent: Record<string, number>;
      costTrends: Array<{ date: string; cost: number }>;
    };
  };
}

export interface ExecutionPattern {
  pattern: string;
  count: number;
  successRate: number;
  averageDuration: number;
  commonAgents: string[];
  commonTools: string[];
  trends: Array<{ date: string; count: number; successRate: number }>;
}

export interface ComponentAnalytics {
  componentId: string;
  componentType: 'agent' | 'tool';
  name: string;
  totalExecutions: number;
  successfulExecutions: number;
  averageExecutionTime: number;
  errorRate: number;
  utilizationRate: number;
  performanceScore: number;
  trends: Array<{ date: string; executions: number; successRate: number }>;
  commonPartners: Array<{ id: string; name: string; count: number }>;
}

@Injectable()
export class HybridWorkflowAnalyticsService {
  private readonly logger = new Logger(HybridWorkflowAnalyticsService.name);
  private metricsCache = new Map<string, any>();
  private realTimeMetrics = new Map<string, any>();

  constructor(
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    // Initialize real-time metrics collection
    this.initializeRealTimeMetrics();
  }

  async getHybridAnalytics(
    organizationId: string,
    timeRange: { start: Date; end: Date },
    filters?: {
      agentIds?: string[];
      toolIds?: string[];
      executionPatterns?: string[];
      nodeIds?: string[];
    },
  ): Promise<HybridAnalyticsMetrics> {
    try {
      this.logger.log(`Generating hybrid analytics for organization: ${organizationId}`);

      const cacheKey = `hybrid_analytics:${organizationId}:${timeRange.start.getTime()}:${timeRange.end.getTime()}`;
      
      // Try cache first
      let metrics = await this.cacheManager.get(cacheKey) as HybridAnalyticsMetrics;
      
      if (!metrics) {
        // Generate fresh metrics
        metrics = await this.generateAnalytics(organizationId, timeRange, filters);
        
        // Cache for 5 minutes
        await this.cacheManager.set(cacheKey, metrics, 300000);
      }

      return metrics;

    } catch (error) {
      this.logger.error(`Failed to get hybrid analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getExecutionPatternAnalysis(
    organizationId: string,
    timeRange: { start: Date; end: Date },
  ): Promise<ExecutionPattern[]> {
    try {
      const executions = await this.prisma.hybridExecutionAnalytics.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end,
          },
        },
        include: {
          workflow: {
            select: { name: true },
          },
        },
      });

      const patternMap = new Map<string, any>();

      executions.forEach(execution => {
        const pattern = execution.executionPattern;
        
        if (!patternMap.has(pattern)) {
          patternMap.set(pattern, {
            pattern,
            executions: [],
            agents: new Set(),
            tools: new Set(),
          });
        }

        const patternData = patternMap.get(pattern);
        patternData.executions.push(execution);
        patternData.agents.add(execution.agentId);
        execution.toolIds.forEach((toolId: string) => patternData.tools.add(toolId));
      });

      const patterns: ExecutionPattern[] = [];

      for (const [pattern, data] of patternMap.entries()) {
        const successfulExecutions = data.executions.filter((e: any) => e.success).length;
        const totalDuration = data.executions.reduce((sum: number, e: any) => sum + e.duration, 0);

        patterns.push({
          pattern,
          count: data.executions.length,
          successRate: data.executions.length > 0 ? successfulExecutions / data.executions.length : 0,
          averageDuration: data.executions.length > 0 ? totalDuration / data.executions.length : 0,
          commonAgents: Array.from(data.agents).slice(0, 5),
          commonTools: Array.from(data.tools).slice(0, 10),
          trends: this.calculatePatternTrends(data.executions, timeRange),
        });
      }

      return patterns.sort((a, b) => b.count - a.count);

    } catch (error) {
      this.logger.error(`Failed to get execution pattern analysis: ${error.message}`, error.stack);
      return [];
    }
  }

  async getComponentAnalytics(
    organizationId: string,
    componentType: 'agent' | 'tool',
    timeRange: { start: Date; end: Date },
  ): Promise<ComponentAnalytics[]> {
    try {
      const executions = await this.prisma.hybridExecutionAnalytics.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end,
          },
        },
      });

      const componentMap = new Map<string, any>();

      executions.forEach(execution => {
        if (componentType === 'agent') {
          const agentId = execution.agentId;
          if (!componentMap.has(agentId)) {
            componentMap.set(agentId, {
              componentId: agentId,
              executions: [],
              partners: new Map(),
            });
          }
          
          const agentData = componentMap.get(agentId);
          agentData.executions.push(execution);
          
          // Track tool partners
          execution.toolIds.forEach((toolId: string) => {
            agentData.partners.set(toolId, (agentData.partners.get(toolId) || 0) + 1);
          });
        } else {
          // For tools, we need to iterate through toolIds in each execution
          execution.toolIds.forEach((toolId: string) => {
            if (!componentMap.has(toolId)) {
              componentMap.set(toolId, {
                componentId: toolId,
                executions: [],
                partners: new Map(),
              });
            }
            
            const toolData = componentMap.get(toolId);
            toolData.executions.push(execution);
            
            // Track agent partners
            toolData.partners.set(execution.agentId, (toolData.partners.get(execution.agentId) || 0) + 1);
          });
        }
      });

      const analytics: ComponentAnalytics[] = [];

      for (const [componentId, data] of componentMap.entries()) {
        const successfulExecutions = data.executions.filter((e: any) => e.success).length;
        const totalDuration = data.executions.reduce((sum: number, e: any) => sum + e.duration, 0);
        const totalExecutions = data.executions.length;

        // Get component name
        const componentName = await this.getComponentName(componentId, componentType, organizationId);

        // Calculate performance score (0-100)
        const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;
        const avgDuration = totalExecutions > 0 ? totalDuration / totalExecutions : 0;
        const performanceScore = this.calculatePerformanceScore(successRate, avgDuration, totalExecutions);

        // Get common partners
        const commonPartners = Array.from(data.partners.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5)
          .map(([partnerId, count]) => ({
            id: partnerId,
            name: partnerId, // Would need to fetch actual names
            count,
          }));

        analytics.push({
          componentId,
          componentType,
          name: componentName,
          totalExecutions,
          successfulExecutions,
          averageExecutionTime: avgDuration,
          errorRate: 1 - successRate,
          utilizationRate: this.calculateUtilizationRate(totalExecutions, timeRange),
          performanceScore,
          trends: this.calculateComponentTrends(data.executions, timeRange),
          commonPartners,
        });
      }

      return analytics.sort((a, b) => b.totalExecutions - a.totalExecutions);

    } catch (error) {
      this.logger.error(`Failed to get component analytics: ${error.message}`, error.stack);
      return [];
    }
  }

  async getPerformanceInsights(
    organizationId: string,
    timeRange: { start: Date; end: Date },
  ): Promise<{
    bottlenecks: Array<{ component: string; type: string; impact: number; recommendation: string }>;
    optimizations: Array<{ area: string; potential: number; description: string }>;
    trends: Array<{ metric: string; trend: 'improving' | 'declining' | 'stable'; change: number }>;
  }> {
    try {
      const executions = await this.prisma.hybridExecutionAnalytics.findMany({
        where: {
          organizationId,
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end,
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      const bottlenecks = this.identifyBottlenecks(executions);
      const optimizations = this.identifyOptimizations(executions);
      const trends = this.analyzeTrends(executions, timeRange);

      return { bottlenecks, optimizations, trends };

    } catch (error) {
      this.logger.error(`Failed to get performance insights: ${error.message}`, error.stack);
      return { bottlenecks: [], optimizations: [], trends: [] };
    }
  }

  async getRealTimeMetrics(organizationId: string): Promise<{
    activeExecutions: number;
    executionsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    topPatterns: Array<{ pattern: string; count: number }>;
  }> {
    try {
      const realTimeKey = `realtime:${organizationId}`;
      const metrics = this.realTimeMetrics.get(realTimeKey) || {
        activeExecutions: 0,
        executionsPerMinute: 0,
        averageResponseTime: 0,
        errorRate: 0,
        topPatterns: [],
      };

      return metrics;

    } catch (error) {
      this.logger.error(`Failed to get real-time metrics: ${error.message}`, error.stack);
      return {
        activeExecutions: 0,
        executionsPerMinute: 0,
        averageResponseTime: 0,
        errorRate: 0,
        topPatterns: [],
      };
    }
  }

  private async generateAnalytics(
    organizationId: string,
    timeRange: { start: Date; end: Date },
    filters?: any,
  ): Promise<HybridAnalyticsMetrics> {
    // Build where clause
    const where: any = {
      organizationId,
      createdAt: {
        gte: timeRange.start,
        lte: timeRange.end,
      },
    };

    if (filters?.agentIds?.length) {
      where.agentId = { in: filters.agentIds };
    }

    if (filters?.executionPatterns?.length) {
      where.executionPattern = { in: filters.executionPatterns };
    }

    // Get execution data
    const executions = await this.prisma.hybridExecutionAnalytics.findMany({
      where,
      include: {
        workflow: {
          select: { name: true },
        },
      },
    });

    // Get human input data
    const humanInputs = await this.prisma.workflowHumanInput.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end,
        },
      },
    });

    // Calculate basic metrics
    const totalExecutions = executions.length;
    const successfulExecutions = executions.filter(e => e.success).length;
    const failedExecutions = totalExecutions - successfulExecutions;

    const durations = executions.map(e => e.duration).sort((a, b) => a - b);
    const averageExecutionTime = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;

    // Pattern distribution
    const patternDistribution = executions.reduce((acc, e) => {
      acc[e.executionPattern] = (acc[e.executionPattern] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Agent utilization
    const agentUtilization = executions.reduce((acc, e) => {
      acc[e.agentId] = (acc[e.agentId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Tool utilization
    const toolUtilization = executions.reduce((acc, e) => {
      e.toolIds.forEach((toolId: string) => {
        acc[toolId] = (acc[toolId] || 0) + 1;
      });
      return acc;
    }, {} as Record<string, number>);

    // Error analysis
    const errors = executions.filter(e => !e.success);
    const errorTypes = errors.reduce((acc, e) => {
      const errorType = this.categorizeError(e.errorMessage);
      acc[errorType] = (acc[errorType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonErrors = Object.entries(errorTypes)
      .map(([message, count]) => ({ message, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Performance metrics
    const performanceMetrics = {
      p50: durations[Math.floor(durations.length * 0.5)] || 0,
      p95: durations[Math.floor(durations.length * 0.95)] || 0,
      p99: durations[Math.floor(durations.length * 0.99)] || 0,
      minDuration: durations[0] || 0,
      maxDuration: durations[durations.length - 1] || 0,
    };

    // Context sharing stats (would need additional data collection)
    const contextSharingStats = {
      totalShares: 0, // Would be tracked in real-time
      averageShareSize: 0,
      sharesByStrategy: {} as Record<string, number>,
    };

    // Human interaction stats
    const totalHumanRequests = humanInputs.length;
    const completedRequests = humanInputs.filter(h => h.status === 'COMPLETED').length;
    const skippedRequests = humanInputs.filter(h => h.status === 'SKIPPED').length;
    const timedOutRequests = humanInputs.filter(h => h.status === 'EXPIRED').length;

    const completedWithTimes = humanInputs.filter(h => h.completedAt && h.status === 'COMPLETED');
    const averageResponseTime = completedWithTimes.length > 0
      ? completedWithTimes.reduce((sum, h) => 
          sum + (h.completedAt!.getTime() - h.createdAt.getTime()), 0) / completedWithTimes.length
      : 0;

    const humanInteractionStats = {
      totalRequests: totalHumanRequests,
      completionRate: totalHumanRequests > 0 ? completedRequests / totalHumanRequests : 0,
      averageResponseTime: averageResponseTime / 1000, // Convert to seconds
      skipRate: totalHumanRequests > 0 ? skippedRequests / totalHumanRequests : 0,
      timeoutRate: totalHumanRequests > 0 ? timedOutRequests / totalHumanRequests : 0,
    };

    // Resource utilization (simplified)
    const resourceUtilization = {
      averageMemoryUsage: 0, // Would need additional tracking
      averageTokenUsage: 0, // Would need additional tracking
      costAnalysis: {
        totalCost: 0,
        costByComponent: {} as Record<string, number>,
        costTrends: [] as Array<{ date: string; cost: number }>,
      },
    };

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageExecutionTime,
      patternDistribution,
      agentUtilization,
      toolUtilization,
      errorAnalysis: {
        totalErrors: errors.length,
        errorTypes,
        errorRate: totalExecutions > 0 ? errors.length / totalExecutions : 0,
        commonErrors,
      },
      performanceMetrics,
      contextSharingStats,
      humanInteractionStats,
      resourceUtilization,
    };
  }

  private calculatePatternTrends(executions: any[], timeRange: { start: Date; end: Date }): Array<{ date: string; count: number; successRate: number }> {
    const days = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const trends: Array<{ date: string; count: number; successRate: number }> = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(timeRange.start.getTime() + i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayExecutions = executions.filter(e => 
        e.createdAt >= dayStart && e.createdAt < dayEnd
      );

      const successfulCount = dayExecutions.filter(e => e.success).length;

      trends.push({
        date: dayStart.toISOString().split('T')[0],
        count: dayExecutions.length,
        successRate: dayExecutions.length > 0 ? successfulCount / dayExecutions.length : 0,
      });
    }

    return trends;
  }

  private calculateComponentTrends(executions: any[], timeRange: { start: Date; end: Date }): Array<{ date: string; executions: number; successRate: number }> {
    const days = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const trends: Array<{ date: string; executions: number; successRate: number }> = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(timeRange.start.getTime() + i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayExecutions = executions.filter(e => 
        e.createdAt >= dayStart && e.createdAt < dayEnd
      );

      const successfulCount = dayExecutions.filter(e => e.success).length;

      trends.push({
        date: dayStart.toISOString().split('T')[0],
        executions: dayExecutions.length,
        successRate: dayExecutions.length > 0 ? successfulCount / dayExecutions.length : 0,
      });
    }

    return trends;
  }

  private async getComponentName(componentId: string, componentType: 'agent' | 'tool', organizationId: string): Promise<string> {
    try {
      if (componentType === 'agent') {
        const agent = await this.prisma.agent.findFirst({
          where: { id: componentId, organizationId },
          select: { name: true },
        });
        return agent?.name || componentId;
      } else {
        const tool = await this.prisma.tool.findFirst({
          where: { id: componentId, organizationId },
          select: { name: true },
        });
        return tool?.name || componentId;
      }
    } catch {
      return componentId;
    }
  }

  private calculatePerformanceScore(successRate: number, avgDuration: number, totalExecutions: number): number {
    // Performance score calculation (0-100)
    const successScore = successRate * 40; // 40% weight
    const speedScore = Math.max(0, 40 - (avgDuration / 1000) * 2); // 40% weight, penalize slow executions
    const volumeScore = Math.min(20, totalExecutions / 10); // 20% weight, up to 20 points

    return Math.round(successScore + speedScore + volumeScore);
  }

  private calculateUtilizationRate(totalExecutions: number, timeRange: { start: Date; end: Date }): number {
    const hours = (timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60);
    return totalExecutions / Math.max(1, hours); // Executions per hour
  }

  private identifyBottlenecks(executions: any[]): Array<{ component: string; type: string; impact: number; recommendation: string }> {
    const bottlenecks: Array<{ component: string; type: string; impact: number; recommendation: string }> = [];

    // Analyze execution times by component
    const componentTimes = new Map<string, number[]>();
    
    executions.forEach(execution => {
      // Agent times
      if (!componentTimes.has(execution.agentId)) {
        componentTimes.set(execution.agentId, []);
      }
      componentTimes.get(execution.agentId)!.push(execution.duration);

      // Tool times (simplified - would need more detailed tracking)
      execution.toolIds.forEach((toolId: string) => {
        if (!componentTimes.has(toolId)) {
          componentTimes.set(toolId, []);
        }
        componentTimes.get(toolId)!.push(execution.duration / execution.toolIds.length);
      });
    });

    // Identify slow components
    for (const [componentId, times] of componentTimes.entries()) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const p95Time = times.sort((a, b) => a - b)[Math.floor(times.length * 0.95)] || 0;

      if (avgTime > 30000) { // 30 seconds
        bottlenecks.push({
          component: componentId,
          type: 'slow_execution',
          impact: Math.min(100, avgTime / 1000), // Impact score based on seconds
          recommendation: `Consider optimizing ${componentId} - average execution time is ${Math.round(avgTime / 1000)}s`,
        });
      }

      if (p95Time > 60000) { // 60 seconds
        bottlenecks.push({
          component: componentId,
          type: 'high_latency_variance',
          impact: Math.min(100, p95Time / 1000),
          recommendation: `${componentId} has high latency variance - investigate timeout issues`,
        });
      }
    }

    return bottlenecks.sort((a, b) => b.impact - a.impact).slice(0, 10);
  }

  private identifyOptimizations(executions: any[]): Array<{ area: string; potential: number; description: string }> {
    const optimizations: Array<{ area: string; potential: number; description: string }> = [];

    // Pattern analysis
    const patternStats = executions.reduce((acc, e) => {
      if (!acc[e.executionPattern]) {
        acc[e.executionPattern] = { total: 0, successful: 0, avgDuration: 0 };
      }
      acc[e.executionPattern].total++;
      if (e.success) acc[e.executionPattern].successful++;
      acc[e.executionPattern].avgDuration += e.duration;
      return acc;
    }, {} as Record<string, any>);

    Object.entries(patternStats).forEach(([pattern, stats]: [string, any]) => {
      stats.avgDuration /= stats.total;
      const successRate = stats.successful / stats.total;

      if (successRate < 0.8) {
        optimizations.push({
          area: `${pattern}_pattern_reliability`,
          potential: Math.round((0.8 - successRate) * 100),
          description: `${pattern} pattern has ${Math.round(successRate * 100)}% success rate - consider improving error handling`,
        });
      }

      if (stats.avgDuration > 20000) {
        optimizations.push({
          area: `${pattern}_pattern_performance`,
          potential: Math.round(stats.avgDuration / 1000),
          description: `${pattern} pattern averages ${Math.round(stats.avgDuration / 1000)}s - consider parallel execution or caching`,
        });
      }
    });

    return optimizations.sort((a, b) => b.potential - a.potential).slice(0, 10);
  }

  private analyzeTrends(executions: any[], timeRange: { start: Date; end: Date }): Array<{ metric: string; trend: 'improving' | 'declining' | 'stable'; change: number }> {
    const trends: Array<{ metric: string; trend: 'improving' | 'declining' | 'stable'; change: number }> = [];

    // Split executions into two halves for comparison
    const midpoint = new Date((timeRange.start.getTime() + timeRange.end.getTime()) / 2);
    const firstHalf = executions.filter(e => e.createdAt < midpoint);
    const secondHalf = executions.filter(e => e.createdAt >= midpoint);

    if (firstHalf.length > 0 && secondHalf.length > 0) {
      // Success rate trend
      const firstSuccessRate = firstHalf.filter(e => e.success).length / firstHalf.length;
      const secondSuccessRate = secondHalf.filter(e => e.success).length / secondHalf.length;
      const successRateChange = ((secondSuccessRate - firstSuccessRate) / firstSuccessRate) * 100;

      trends.push({
        metric: 'success_rate',
        trend: successRateChange > 5 ? 'improving' : successRateChange < -5 ? 'declining' : 'stable',
        change: Math.round(successRateChange),
      });

      // Average duration trend
      const firstAvgDuration = firstHalf.reduce((sum, e) => sum + e.duration, 0) / firstHalf.length;
      const secondAvgDuration = secondHalf.reduce((sum, e) => sum + e.duration, 0) / secondHalf.length;
      const durationChange = ((secondAvgDuration - firstAvgDuration) / firstAvgDuration) * 100;

      trends.push({
        metric: 'execution_time',
        trend: durationChange < -5 ? 'improving' : durationChange > 5 ? 'declining' : 'stable',
        change: Math.round(durationChange),
      });

      // Volume trend
      const volumeChange = ((secondHalf.length - firstHalf.length) / firstHalf.length) * 100;

      trends.push({
        metric: 'execution_volume',
        trend: volumeChange > 10 ? 'improving' : volumeChange < -10 ? 'declining' : 'stable',
        change: Math.round(volumeChange),
      });
    }

    return trends;
  }

  private categorizeError(errorMessage: string | null): string {
    if (!errorMessage) return 'unknown';
    
    const message = errorMessage.toLowerCase();
    if (message.includes('timeout')) return 'timeout';
    if (message.includes('not found')) return 'not_found';
    if (message.includes('permission') || message.includes('unauthorized')) return 'permission';
    if (message.includes('rate limit')) return 'rate_limit';
    if (message.includes('network') || message.includes('connection')) return 'network';
    if (message.includes('validation')) return 'validation';
    if (message.includes('memory') || message.includes('resource')) return 'resource';
    
    return 'other';
  }

  private initializeRealTimeMetrics(): void {
    // Initialize real-time metrics collection
    setInterval(() => {
      this.updateRealTimeMetrics();
    }, 60000); // Update every minute
  }

  private async updateRealTimeMetrics(): void {
    try {
      // Get organizations with recent activity
      const recentExecutions = await this.prisma.hybridExecutionAnalytics.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
          },
        },
        select: {
          organizationId: true,
          executionPattern: true,
          duration: true,
          success: true,
        },
      });

      // Group by organization
      const orgMetrics = new Map<string, any>();

      recentExecutions.forEach(execution => {
        const orgId = execution.organizationId;
        
        if (!orgMetrics.has(orgId)) {
          orgMetrics.set(orgId, {
            executions: [],
            patterns: new Map(),
          });
        }

        const metrics = orgMetrics.get(orgId);
        metrics.executions.push(execution);
        
        const pattern = execution.executionPattern;
        metrics.patterns.set(pattern, (metrics.patterns.get(pattern) || 0) + 1);
      });

      // Update real-time metrics for each organization
      for (const [orgId, data] of orgMetrics.entries()) {
        const executions = data.executions;
        const successfulExecutions = executions.filter((e: any) => e.success).length;
        const totalDuration = executions.reduce((sum: number, e: any) => sum + e.duration, 0);

        const topPatterns = Array.from(data.patterns.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5)
          .map(([pattern, count]) => ({ pattern, count }));

        this.realTimeMetrics.set(`realtime:${orgId}`, {
          activeExecutions: executions.length,
          executionsPerMinute: executions.length,
          averageResponseTime: executions.length > 0 ? totalDuration / executions.length : 0,
          errorRate: executions.length > 0 ? (executions.length - successfulExecutions) / executions.length : 0,
          topPatterns,
        });
      }

    } catch (error) {
      this.logger.error(`Failed to update real-time metrics: ${error.message}`, error.stack);
    }
  }

  // Event listeners for real-time updates

  @OnEvent('hybrid.hybrid_execution_started')
  handleExecutionStarted(payload: any) {
    const orgId = payload.context.organizationId;
    const realTimeKey = `realtime:${orgId}`;
    const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
    metrics.activeExecutions = (metrics.activeExecutions || 0) + 1;
    this.realTimeMetrics.set(realTimeKey, metrics);
  }

  @OnEvent('hybrid.hybrid_execution_completed')
  handleExecutionCompleted(payload: any) {
    const orgId = payload.context.organizationId;
    const realTimeKey = `realtime:${orgId}`;
    const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
    metrics.activeExecutions = Math.max(0, (metrics.activeExecutions || 0) - 1);
    this.realTimeMetrics.set(realTimeKey, metrics);
  }

  @OnEvent('hybrid.hybrid_execution_failed')
  handleExecutionFailed(payload: any) {
    const orgId = payload.context.organizationId;
    const realTimeKey = `realtime:${orgId}`;
    const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
    metrics.activeExecutions = Math.max(0, (metrics.activeExecutions || 0) - 1);
    this.realTimeMetrics.set(realTimeKey, metrics);
  }

  // Public API methods

  async exportAnalytics(
    organizationId: string,
    timeRange: { start: Date; end: Date },
    format: 'json' | 'csv' | 'pdf' = 'json',
  ): Promise<any> {
    const analytics = await this.getHybridAnalytics(organizationId, timeRange);
    
    switch (format) {
      case 'json':
        return analytics;
      
      case 'csv':
        return this.convertToCSV(analytics);
      
      case 'pdf':
        return this.generatePDFReport(analytics);
      
      default:
        return analytics;
    }
  }

  private convertToCSV(analytics: HybridAnalyticsMetrics): string {
    // Simplified CSV conversion
    const lines = [
      'Metric,Value',
      `Total Executions,${analytics.totalExecutions}`,
      `Successful Executions,${analytics.successfulExecutions}`,
      `Failed Executions,${analytics.failedExecutions}`,
      `Average Execution Time,${analytics.averageExecutionTime}`,
      `Error Rate,${analytics.errorAnalysis.errorRate}`,
    ];

    return lines.join('\n');
  }

  private generatePDFReport(analytics: HybridAnalyticsMetrics): any {
    // Would use a PDF generation library like puppeteer or jsPDF
    return {
      message: 'PDF generation not implemented in this example',
      data: analytics,
    };
  }

  clearCache(): void {
    this.metricsCache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.metricsCache.size,
      keys: Array.from(this.metricsCache.keys()),
    };
  }
}