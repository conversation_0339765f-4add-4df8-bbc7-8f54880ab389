#!/usr/bin/env tsx

/**
 * 🔒 SynapseAI Production Database Seeder
 * 
 * This script sets up production-ready initial data including:
 * - Default organization with all roles
 * - Comprehensive permission system
 * - Security configurations
 * - APIX channels and configurations
 */

import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

async function main() {
    console.log('🚀 Starting SynapseAI Database Seeding...\n');

    // ============================================================================
    // 🏢 CREATE DEFAULT ORGANIZATION
    // ============================================================================
    console.log('📊 Creating default organization...');

    const defaultOrg = await prisma.organization.upsert({
        where: { slug: 'synapseai-default' },
        update: {},
        create: {
            name: 'SynapseAI Platform',
            slug: 'synapseai-default',
            domain: 'synapseai.com',
            isActive: true,
            features: [
                'workflows',
                'agents',
                'tools',
                'analytics',
                'api_access',
                'websockets',
                'multi_tenant'
            ],
            plan: 'enterprise',
            settings: {
                security: {
                    passwordPolicy: {
                        minLength: 8,
                        requireSpecialChars: true,
                        requireNumbers: true,
                        requireUppercase: true,
                    },
                    mfa: {
                        required: false,
                        allowedMethods: ['totp', 'backup_codes'],
                    },
                    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
                    maxLoginAttempts: 5,
                    lockoutDuration: 15 * 60 * 1000, // 15 minutes
                },
                features: {
                    userRegistration: true,
                    emailVerification: true,
                    passwordReset: true,
                    apiKeys: true,
                },
            },
            branding: {
                primaryColor: '#3B82F6',
                logo: '/assets/logo.png',
                favicon: '/assets/favicon.ico',
            },
        },
    });

    console.log(`✅ Organization created: ${defaultOrg.name} (${defaultOrg.slug})\n`);

    // ============================================================================
    // 🔐 CREATE COMPREHENSIVE PERMISSION SYSTEM
    // ============================================================================
    console.log('🛡️ Setting up permission system...');

    const permissions = [
        // Organization permissions
        { resource: 'organization', action: 'read', scope: 'organization' },
        { resource: 'organization', action: 'update', scope: 'organization' },
        { resource: 'organization', action: 'delete', scope: 'global' },

        // User management
        { resource: 'user', action: 'create', scope: 'organization' },
        { resource: 'user', action: 'read', scope: 'organization' },
        { resource: 'user', action: 'update', scope: 'organization' },
        { resource: 'user', action: 'delete', scope: 'organization' },
        { resource: 'user', action: 'invite', scope: 'organization' },
        { resource: 'user', action: 'manage_roles', scope: 'organization' },

        // Workflow permissions
        { resource: 'workflow', action: 'create', scope: 'organization' },
        { resource: 'workflow', action: 'read', scope: 'organization' },
        { resource: 'workflow', action: 'update', scope: 'own' },
        { resource: 'workflow', action: 'delete', scope: 'own' },
        { resource: 'workflow', action: 'execute', scope: 'organization' },
        { resource: 'workflow', action: 'share', scope: 'organization' },

        // Agent permissions
        { resource: 'agent', action: 'create', scope: 'organization' },
        { resource: 'agent', action: 'read', scope: 'organization' },
        { resource: 'agent', action: 'update', scope: 'own' },
        { resource: 'agent', action: 'delete', scope: 'own' },
        { resource: 'agent', action: 'deploy', scope: 'organization' },

        // Tool permissions
        { resource: 'tool', action: 'create', scope: 'organization' },
        { resource: 'tool', action: 'read', scope: 'organization' },
        { resource: 'tool', action: 'update', scope: 'own' },
        { resource: 'tool', action: 'delete', scope: 'own' },
        { resource: 'tool', action: 'configure', scope: 'organization' },

        // Provider permissions
        { resource: 'provider', action: 'read', scope: 'organization' },
        { resource: 'provider', action: 'configure', scope: 'organization' },
        { resource: 'provider', action: 'manage', scope: 'organization' },

        // Analytics permissions
        { resource: 'analytics', action: 'read', scope: 'organization' },
        { resource: 'analytics', action: 'export', scope: 'organization' },

        // Audit permissions
        { resource: 'audit', action: 'read', scope: 'organization' },
        { resource: 'audit', action: 'export', scope: 'organization' },

        // API Key permissions
        { resource: 'api_key', action: 'create', scope: 'own' },
        { resource: 'api_key', action: 'read', scope: 'own' },
        { resource: 'api_key', action: 'revoke', scope: 'own' },
        { resource: 'api_key', action: 'manage', scope: 'organization' },

        // Session permissions
        { resource: 'session', action: 'read', scope: 'own' },
        { resource: 'session', action: 'manage', scope: 'own' },
        { resource: 'session', action: 'admin', scope: 'organization' },
    ];

    for (const permission of permissions) {
        await prisma.permission.upsert({
            where: {
                resource_action_scope_organizationId: {
                    resource: permission.resource,
                    action: permission.action,
                    scope: permission.scope,
                    organizationId: defaultOrg.id,
                },
            },
            update: {},
            create: {
                ...permission,
                organizationId: defaultOrg.id,
            },
        });
    }

    console.log(`✅ Created ${permissions.length} permissions\n`);

    // ============================================================================
    // 👥 CREATE USERS FOR ALL ROLES
    // ============================================================================
    console.log('👤 Creating default users...');

    const defaultPassword = 'Admin123!@#';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const users = [
        {
            email: '<EMAIL>',
            firstName: 'Super',
            lastName: 'Admin',
            role: Role.SUPER_ADMIN,
            emailVerified: true,
        },
        {
            email: '<EMAIL>',
            firstName: 'Org',
            lastName: 'Admin',
            role: Role.ORG_ADMIN,
            emailVerified: true,
        },
        {
            email: '<EMAIL>',
            firstName: 'Dev',
            lastName: 'User',
            role: Role.DEVELOPER,
            emailVerified: true,
        },
        {
            email: '<EMAIL>',
            firstName: 'Viewer',
            lastName: 'User',
            role: Role.VIEWER,
            emailVerified: true,
        },
    ];

    for (const userData of users) {
        const user = await prisma.user.upsert({
            where: { email: userData.email },
            update: {},
            create: {
                ...userData,
                password: hashedPassword,
                organizationId: defaultOrg.id,
                preferences: {
                    theme: 'dark',
                    notifications: {
                        email: true,
                        push: true,
                        workflow: true,
                        security: true,
                    },
                    dashboard: {
                        layout: 'grid',
                        widgets: ['recent_workflows', 'agent_status', 'system_health'],
                    },
                },
            },
        });

        console.log(`   ✅ ${userData.role}: ${user.email}`);
    }

    console.log(`\n💡 Default password for all users: ${defaultPassword}\n`);

    // ============================================================================
    // 🔌 SETUP APIX CHANNELS
    // ============================================================================
    console.log('📡 Setting up APIX WebSocket channels...');

    const channels = [
        {
            name: 'system_global',
            type: 'SYSTEM_EVENTS',
            organizationId: null, // Global channel
            permissions: {},
            metadata: {
                description: 'Global system events and announcements',
                priority: 'HIGH',
            },
        },
        {
            name: `org_${defaultOrg.slug}`,
            type: 'ORGANIZATION',
            organizationId: defaultOrg.id,
            permissions: {
                subscribe: ['ORG_ADMIN', 'DEVELOPER', 'VIEWER'],
                publish: ['ORG_ADMIN', 'DEVELOPER'],
            },
            metadata: {
                description: 'Organization-wide events and notifications',
                priority: 'NORMAL',
            },
        },
        {
            name: `workflows_${defaultOrg.slug}`,
            type: 'WORKFLOW_EVENTS',
            organizationId: defaultOrg.id,
            permissions: {
                subscribe: ['ORG_ADMIN', 'DEVELOPER', 'VIEWER'],
                publish: ['SYSTEM'],
            },
            metadata: {
                description: 'Workflow execution events and status updates',
                priority: 'NORMAL',
            },
        },
        {
            name: `agents_${defaultOrg.slug}`,
            type: 'AGENT_EVENTS',
            organizationId: defaultOrg.id,
            permissions: {
                subscribe: ['ORG_ADMIN', 'DEVELOPER'],
                publish: ['SYSTEM'],
            },
            metadata: {
                description: 'Agent status and execution events',
                priority: 'NORMAL',
            },
        },
        {
            name: `tools_${defaultOrg.slug}`,
            type: 'TOOL_EVENTS',
            organizationId: defaultOrg.id,
            permissions: {
                subscribe: ['ORG_ADMIN', 'DEVELOPER'],
                publish: ['SYSTEM'],
            },
            metadata: {
                description: 'Tool execution and configuration events',
                priority: 'LOW',
            },
        },
    ];

    for (const channelData of channels) {
        await prisma.apiXChannel.upsert({
            where: { name: channelData.name },
            update: {},
            create: channelData,
        });
        console.log(`   ✅ Channel: ${channelData.name} (${channelData.type})`);
    }

    console.log();

    // ============================================================================
    // 📊 CREATE SAMPLE WORKFLOWS & AGENTS (Optional)
    // ============================================================================
    console.log('🔧 Creating sample workflows and agents...');

    const superAdmin = await prisma.user.findFirst({
        where: { role: Role.SUPER_ADMIN, organizationId: defaultOrg.id },
    });

    if (superAdmin) {
        // Sample Workflow
        await prisma.workflow.upsert({
            where: { id: 'sample-welcome-workflow' },
            update: {},
            create: {
                id: 'sample-welcome-workflow',
                name: 'Welcome New User',
                description: 'Automated workflow to onboard new users',
                definition: {
                    nodes: [
                        {
                            id: 'start',
                            type: 'start',
                            position: { x: 100, y: 100 },
                        },
                        {
                            id: 'send-welcome-email',
                            type: 'tool',
                            position: { x: 300, y: 100 },
                            data: {
                                toolId: 'email-sender',
                                parameters: {
                                    template: 'welcome',
                                    subject: 'Welcome to SynapseAI!',
                                },
                            },
                        },
                        {
                            id: 'end',
                            type: 'end',
                            position: { x: 500, y: 100 },
                        },
                    ],
                    edges: [
                        { id: 'e1', source: 'start', target: 'send-welcome-email' },
                        { id: 'e2', source: 'send-welcome-email', target: 'end' },
                    ],
                },
                version: 1,
                isActive: true,
                tags: ['onboarding', 'email', 'automation'],
                creatorId: superAdmin.id,
                organizationId: defaultOrg.id,
            },
        });

        // Sample Agent
        await prisma.agent.upsert({
            where: { id: 'sample-assistant-agent' },
            update: {},
            create: {
                id: 'sample-assistant-agent',
                name: 'General Assistant',
                description: 'A helpful AI assistant for general tasks',
                type: 'HYBRID',
                config: {
                    model: 'gpt-4',
                    temperature: 0.7,
                    maxTokens: 2000,
                    systemPrompt: 'You are a helpful AI assistant for SynapseAI platform users.',
                    tools: ['web-search', 'calculator', 'file-reader'],
                },
                skills: [
                    'general_qa',
                    'task_planning',
                    'data_analysis',
                    'workflow_assistance',
                ],
                version: 1,
                isActive: true,
                creatorId: superAdmin.id,
                organizationId: defaultOrg.id,
            },
        });

        // Sample Tool
        await prisma.tool.upsert({
            where: { id: 'sample-email-tool' },
            update: {},
            create: {
                id: 'sample-email-tool',
                name: 'Email Sender',
                description: 'Send emails using various templates',
                type: 'API_FETCH',
                schema: {
                    type: 'object',
                    properties: {
                        to: { type: 'string', format: 'email' },
                        subject: { type: 'string' },
                        template: { type: 'string' },
                        variables: { type: 'object' },
                    },
                    required: ['to', 'subject', 'template'],
                },
                config: {
                    endpoint: '/api/email/send',
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                },
                version: 1,
                isActive: true,
                creatorId: superAdmin.id,
                organizationId: defaultOrg.id,
            },
        });
    }

    console.log('   ✅ Sample workflow: Welcome New User');
    console.log('   ✅ Sample agent: General Assistant');
    console.log('   ✅ Sample tool: Email Sender\n');

    // ============================================================================
    // 📋 SETUP SUMMARY
    // ============================================================================
    console.log('🎉 Database seeding completed successfully!\n');
    console.log('📋 Setup Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`🏢 Organization: ${defaultOrg.name} (${defaultOrg.slug})`);
    console.log(`🔐 Permissions: ${permissions.length} permission rules created`);
    console.log(`👥 Users: ${users.length} users created (all roles)`);
    console.log(`📡 Channels: ${channels.length} APIX WebSocket channels`);
    console.log(`🔧 Samples: 1 workflow, 1 agent, 1 tool`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    console.log('🔑 Login Credentials:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    users.forEach(user => {
        console.log(`${user.role.padEnd(12)} | ${user.email.padEnd(30)} | ${defaultPassword}`);
    });
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    console.log('🚀 Next Steps:');
    console.log('1. Run: npm run dev (for backend)');
    console.log('2. Run: npm run dev (for frontend)');
    console.log('3. Login with any of the above credentials');
    console.log('4. Configure your environment variables');
    console.log('5. Set up your AI providers (OpenAI, Claude, etc.)\n');

    console.log('⚠️  IMPORTANT SECURITY NOTES:');
    console.log('• Change default passwords immediately in production');
    console.log('• Update JWT secrets in your .env file');
    console.log('• Configure proper CORS origins');
    console.log('• Enable HTTPS in production');
    console.log('• Set up proper database backups\n');
}

main()
    .catch((e) => {
        console.error('❌ Seeding failed:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });