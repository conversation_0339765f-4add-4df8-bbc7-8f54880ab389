import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ApixGateway } from '../../apix/apix.gateway';
export interface ProviderConfig {
    id: string;
    name: string;
    type: 'openai' | 'anthropic' | 'google' | 'azure' | 'local' | 'custom';
    endpoint?: string;
    apiKey: string;
    organization?: string;
    project?: string;
    models: string[];
    features: {
        chat: boolean;
        completion: boolean;
        embedding: boolean;
        image: boolean;
        audio: boolean;
        functions: boolean;
        streaming: boolean;
    };
    limits: {
        requestsPerMinute: number;
        requestsPerHour: number;
        tokensPerMinute: number;
        contextWindow: number;
    };
    pricing: {
        inputTokenPrice: number;
        outputTokenPrice: number;
        imagePrice?: number;
        audioPrice?: number;
    };
    quality: {
        accuracy: number;
        speed: number;
        reliability: number;
    };
    isActive: boolean;
    priority: number;
    healthCheckUrl?: string;
    tags: string[];
    description?: string;
    version: string;
}
export interface ProviderMetrics {
    providerId: string;
    timestamp: Date;
    responseTime: number;
    successRate: number;
    errorRate: number;
    throughput: number;
    requestCount: number;
    tokenCount: number;
    cost: number;
    averageQuality?: number;
    userSatisfaction?: number;
}
export interface RoutingRequest {
    task: any;
    requirements: {
        model?: string;
        features?: string[];
        maxCost?: number;
        maxLatency?: number;
        minQuality?: number;
        preferredProviders?: string[];
        excludedProviders?: string[];
    };
    context?: {
        agentId?: string;
        userId?: string;
        organizationId?: string;
        priority?: number;
    };
}
export interface ProviderClient {
    id: string;
    config: ProviderConfig;
    chat(params: any): Promise<any>;
    completion?(params: any): Promise<any>;
    embedding?(params: any): Promise<any>;
    healthCheck(): Promise<boolean>;
    getMetrics(): Promise<any>;
}
export declare class ProviderRouterService {
    private prisma;
    private cacheManager;
    private configService;
    private apixGateway;
    private readonly logger;
    private readonly providers;
    private readonly routingCache;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, apixGateway: ApixGateway);
    registerProvider(config: ProviderConfig): Promise<void>;
    unregisterProvider(providerId: string): Promise<void>;
    getProvider(providerId: string, organizationId?: string): Promise<ProviderClient>;
    getAvailableProviders(organizationId: string): Promise<ProviderClient[]>;
    selectBestProvider(providers: ProviderClient[], request: RoutingRequest): Promise<ProviderClient>;
    private scoreProvider;
    private estimateCost;
    private generateRoutingCacheKey;
    private createProviderClient;
    private createOpenAIClient;
    private createAnthropicClient;
    private createGoogleClient;
    private createAzureClient;
    private createLocalClient;
    private createCustomClient;
    private calculateOpenAICost;
    private calculateAnthropicCost;
    private calculateAzureCost;
    private validateProviderConfig;
    private checkProviderAccess;
    private getProviderMetrics;
    private getCurrentLoad;
    private initializeProviders;
    getProviderConfigs(): Promise<ProviderConfig[]>;
    getProviderStatus(providerId: string): Promise<any>;
    getRoutingStats(): Promise<any>;
}
