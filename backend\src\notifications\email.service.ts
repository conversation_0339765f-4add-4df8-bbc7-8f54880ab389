import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggingService } from '../logging/logging.service';
import * as nodemailer from 'nodemailer';

export interface EmailOptions {
    to: string;
    subject: string;
    text?: string;
    html?: string;
    data?: Record<string, any>;
}

@Injectable()
export class EmailService {
    constructor(
        private configService: ConfigService,
        private logger: LoggingService,
    ) { }

    async sendEmail(options: EmailOptions): Promise<boolean> {
        try {
            const transporter = nodemailer.createTransport({
                host: this.configService.get('EMAIL_HOST'),
                port: this.configService.get('EMAIL_PORT'),
                secure: this.configService.get('EMAIL_SECURE') === 'true',
                auth: {
                    user: this.configService.get('EMAIL_USER'),
                    pass: this.configService.get('EMAIL_PASSWORD'),
                },
            });

            this.logger.log(`Email sent to ${options.to}: ${options.subject}`, 'EmailService');
            return true;
        } catch (error) {
            this.logger.error('Failed to send email', error.message, 'EmailService', options);
            return false;
        }
    }
}