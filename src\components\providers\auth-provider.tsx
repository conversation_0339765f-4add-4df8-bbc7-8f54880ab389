"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
    AuthContext as AuthContextType,
    LoginCredentials,
    RegisterData,
    SessionUser,
    AuthResponse,
    Role
} from "@/lib/types/auth";
import { useToast } from "@/components/ui/use-toast";
import { trackAuthEvent } from "@/lib/mcp-tools";

// Create auth context
const AuthContext = createContext<AuthContextType | null>(null);

// Auth provider props
interface AuthProviderProps {
    children: React.ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
    const [user, setUser] = useState<SessionUser | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const router = useRouter();
    const { toast } = useToast();

    // Check if user is authenticated on mount
    useEffect(() => {
        const checkAuth = async () => {
            try {
                setIsLoading(true);
                const response = await fetch("/api/auth/session");

                if (response.ok) {
                    const data = await response.json();
                    if (data.user) {
                        setUser(data.user);
                        setIsAuthenticated(true);
                    } else {
                        setUser(null);
                        setIsAuthenticated(false);
                    }
                } else {
                    setUser(null);
                    setIsAuthenticated(false);
                }
            } catch (error) {
                console.error("Failed to check authentication:", error);
                setUser(null);
                setIsAuthenticated(false);
            } finally {
                setIsLoading(false);
            }
        };

        checkAuth();
    }, []);

    // Set up token refresh interval
    useEffect(() => {
        if (!isAuthenticated) return;

        // Refresh token every 10 minutes
        const refreshInterval = setInterval(async () => {
            try {
                await refreshAuth();
            } catch (error) {
                console.error("Failed to refresh authentication:", error);
            }
        }, 10 * 60 * 1000); // 10 minutes

        return () => clearInterval(refreshInterval);
    }, [isAuthenticated]);

    // Login function
    const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
        setIsLoading(true);

        try {
            const response = await fetch("/api/auth/login", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(credentials),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || "Login failed");
            }

            const data = await response.json();

            // Handle MFA if required
            if (data.mfaRequired) {
                return data;
            }

            // Set user and auth state
            setUser(data.user);
            setIsAuthenticated(true);

            // Track login event
            if (data.user) {
                trackAuthEvent(data.user.id, 'login', {
                    timestamp: new Date().toISOString()
                }).catch(console.error);
            }

            return data;
        } catch (error: any) {
            toast({
                title: "Login Failed",
                description: error.message || "Failed to authenticate",
                variant: "destructive",
            });
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Logout function
    const logout = async (): Promise<void> => {
        setIsLoading(true);

        try {
            // Track logout event before clearing user
            if (user) {
                trackAuthEvent(user.id, 'logout', {
                    timestamp: new Date().toISOString()
                }).catch(console.error);
            }

            await fetch("/api/auth/logout", {
                method: "POST",
            });

            // Clear user state
            setUser(null);
            setIsAuthenticated(false);

            // Redirect to login page
            router.push("/auth/login");
        } catch (error) {
            console.error("Logout failed:", error);

            // Still clear user state even if API call fails
            setUser(null);
            setIsAuthenticated(false);
        } finally {
            setIsLoading(false);
        }
    };

    // Register function
    const register = async (data: RegisterData): Promise<AuthResponse> => {
        setIsLoading(true);

        try {
            const response = await fetch("/api/auth/register", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || "Registration failed");
            }

            const responseData = await response.json();

            // Set user and auth state
            setUser(responseData.user);
            setIsAuthenticated(true);

            // Track registration event
            if (responseData.user) {
                trackAuthEvent(responseData.user.id, 'register', {
                    email: data.email,
                    company: data.company,
                    timestamp: new Date().toISOString()
                }).catch(console.error);
            }

            return responseData;
        } catch (error: any) {
            toast({
                title: "Registration Failed",
                description: error.message || "Failed to register",
                variant: "destructive",
            });
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Refresh authentication
    const refreshAuth = async (): Promise<void> => {
        try {
            const response = await fetch("/api/auth/refresh", {
                method: "POST",
            });

            if (!response.ok) {
                // If refresh fails, clear authentication
                setUser(null);
                setIsAuthenticated(false);
                return;
            }

            // Check session after refresh
            const sessionResponse = await fetch("/api/auth/session");
            if (sessionResponse.ok) {
                const data = await sessionResponse.json();
                if (data.user) {
                    setUser(data.user);
                    setIsAuthenticated(true);
                } else {
                    setUser(null);
                    setIsAuthenticated(false);
                }
            }
        } catch (error) {
            console.error("Failed to refresh authentication:", error);
            setUser(null);
            setIsAuthenticated(false);
        }
    };

    // Check if user has required role
    const hasRole = (roles: Role[]): boolean => {
        if (!user) return false;

        // Role hierarchy for permission checking
        const ROLE_HIERARCHY: Record<Role, number> = {
            'SUPER_ADMIN': 100,
            'ORG_ADMIN': 80,
            'DEVELOPER': 60,
            'VIEWER': 40,
            'USER': 20,
            'ADMIN': 10
        };

        // Super admin always has access
        if (user.role === 'SUPER_ADMIN') return true;

        // Get user's role level
        const userLevel = ROLE_HIERARCHY[user.role];

        // Check if user's role level meets any of the required role levels
        return roles.some(role => userLevel >= ROLE_HIERARCHY[role]);
    };

    // Check if user has specific permission
    const hasPermission = (permission: string): boolean => {
        if (!user) return false;

        // Super admin and org admin always have all permissions
        if (['SUPER_ADMIN', 'ORG_ADMIN'].includes(user.role)) {
            return true;
        }

        // Check user's explicit permissions
        return user.permissions.includes(permission);
    };

    // Check if user has all specified permissions
    const hasPermissions = (permissions: string[]): boolean => {
        if (!user) return false;

        // Super admin and org admin always have all permissions
        if (['SUPER_ADMIN', 'ORG_ADMIN'].includes(user.role)) {
            return true;
        }

        // Check if user has all specified permissions
        return permissions.every(permission => user.permissions.includes(permission));
    };

    // Auth context value
    const contextValue: AuthContextType = {
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        register,
        refreshAuth,
        hasRole,
        hasPermission,
        hasPermissions,
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {children}
        </AuthContext.Provider>
    );
}

// Auth hook
export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);

    if (!context) {
        throw new Error("useAuth must be used within an AuthProvider");
    }

    return context;
};