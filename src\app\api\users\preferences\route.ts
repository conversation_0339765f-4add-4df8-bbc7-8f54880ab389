/**
 * User Preferences API Route
 * Production-grade user preferences endpoint with MCP integration
 */

import { NextResponse } from 'next/server';
import { z } from 'zod';
import { withAuth, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { mcp_Memory_add_observations } from '@/lib/mcp-tools';
import { AuthenticatedRequest, ValidatedRequest } from '@/lib/middleware/auth.middleware';
import apiClient from '@/lib/api-client';
// Define UserPreferences type inline since it matches our schema
interface UserPreferences {
    theme?: 'light' | 'dark' | 'system';
    sidebarCollapsed?: boolean;
    dashboardLayout?: 'grid' | 'list';
    notifications?: {
        email?: boolean;
        push?: boolean;
        inApp?: boolean;
    };
    timezone?: string;
    language?: string;
    dateFormat?: string;
}

// User preferences schema
const preferencesSchema = z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    sidebarCollapsed: z.boolean().optional(),
    dashboardLayout: z.enum(['grid', 'list']).optional(),
    notifications: z.object({
        email: z.boolean().optional(),
        push: z.boolean().optional(),
        inApp: z.boolean().optional()
    }).optional(),
    timezone: z.string().optional(),
    language: z.string().optional(),
    dateFormat: z.string().optional()
});

// GET handler - Retrieve user preferences
async function getHandler(req: AuthenticatedRequest): Promise<NextResponse> {
    try {
        if (!req.user) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Unauthorized',
                    code: 'UNAUTHORIZED'
                },
                { status: 401 }
            );
        }

        // Call backend API to get user preferences
        const response = await apiClient.get(`/users/${req.user.id}/preferences`) as { status: number, data: UserPreferences | { message?: string } };

        if (response.status !== 200) {
            const errorData = response.data as { error?: string, message?: string };
            return NextResponse.json(
                {
                    success: false,
                    message: errorData.error || errorData.message || 'Failed to retrieve preferences',
                    code: 'API_ERROR'
                },
                { status: response.status }
            );
        }
        const data = response.data as UserPreferences;
        return NextResponse.json(data || {});
    } catch (error: any) {
        console.error('Get user preferences error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to retrieve preferences',
                code: error.code || 'API_ERROR'
            },
            { status: 500 }
        );
    }
}

// PUT handler - Update user preferences
async function putHandler(req: AuthenticatedRequest & ValidatedRequest): Promise<NextResponse> {
    try {
        if (!req.user) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Unauthorized',
                    code: 'UNAUTHORIZED'
                },
                { status: 401 }
            );
        }

        // Call backend API to update user preferences
        const response = await apiClient.put(`/users/${req.user.id}/preferences`, req.validatedBody) as { status: number, data: UserPreferences | { message?: string } };

        if (response.status === 200) {
            return NextResponse.json({
                success: true,
                preferences: response.data as UserPreferences
            });
        }
        if (response.status !== 200) {
            const errorData = response.data as { error?: string, message?: string };
            return NextResponse.json(
                {
                    success: false,
                    message: errorData.error || errorData.message || 'Failed to update preferences',
                    code: 'API_ERROR'
                },
                { status: response.status }
            );
        }

        // Track preference update in MCP Memory
        await mcp_Memory_add_observations({
            observations: [
                {
                    entityName: `user:${req.user.id}`,
                    contents: [
                        `User updated preferences at ${new Date().toISOString()}`,
                        `Updated preferences: ${JSON.stringify(req.validatedBody)}`
                    ]
                }
            ]
        });

        const data = response.data as UserPreferences;
        return NextResponse.json({
            success: true,
            preferences: data
        });
    } catch (error: any) {
        console.error('Update user preferences error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to update preferences',
                code: error.code || 'API_ERROR'
            },
            { status: 500 }
        );
    }
}

// Export with auth middleware
export const GET = withAuth(getHandler);
export const PUT = withAuthAndValidation(
    putHandler as any,
    {},
    { body: preferencesSchema }
);