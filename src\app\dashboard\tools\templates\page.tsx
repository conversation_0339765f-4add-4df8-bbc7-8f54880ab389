'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Search,
  Star,
  Download,
  Eye,
  Plus,
  Filter,
  ArrowLeft,
  Code,
  FileText,
  Database,
  Globe,
  Bot,
  Zap,
  Users,
  Clock,
  CheckCircle,
  TrendingUp
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { ViewToggle } from '@/components/ui/view-toggle';
import { useViewPreferences } from '@/hooks/use-view-preferences';

interface ToolTemplate {
  id: string;
  name: string;
  description: string;
  type: 'FUNCTION_CALL' | 'RAG' | 'API_FETCH' | 'BROWSER_AUTOMATION' | 'DATABASE' | 'CUSTOM_LOGIC';
  category: string;
  tags: string[];
  isPublic: boolean;
  popularity: number;
  usageCount: number;
  rating: number;
  author: {
    name: string;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
  parameters: any[];
  config: any;
  code?: string;
  preview?: string;
  documentation?: string;
}

const TOOL_TYPES = [
  {
    value: 'FUNCTION_CALL',
    label: 'Function Call',
    icon: Code,
    color: 'bg-blue-500'
  },
  {
    value: 'RAG',
    label: 'RAG',
    icon: FileText,
    color: 'bg-green-500'
  },
  {
    value: 'API_FETCH',
    label: 'API Fetch',
    icon: Globe,
    color: 'bg-purple-500'
  },
  {
    value: 'BROWSER_AUTOMATION',
    label: 'Browser Automation',
    icon: Bot,
    color: 'bg-orange-500'
  },
  {
    value: 'DATABASE',
    label: 'Database',
    icon: Database,
    color: 'bg-red-500'
  },
  {
    value: 'CUSTOM_LOGIC',
    label: 'Custom Logic',
    icon: Zap,
    color: 'bg-gray-500'
  },
];

const TEMPLATE_CATEGORIES = [
  'Popular',
  'Data Processing',
  'Web Scraping',
  'API Integration',
  'Database Operations',
  'File Operations',
  'Communication',
  'Analytics',
  'Automation',
  'AI/ML',
  'Utilities'
];

export default function ToolTemplatesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const viewPreferences = useViewPreferences({ key: 'tool-templates' });

  const [templates, setTemplates] = useState<ToolTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('popularity');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ToolTemplate | null>(null);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    loadTemplates();
  }, [searchQuery, selectedType, selectedCategory, sortBy]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (searchQuery) params.append('query', searchQuery);
      if (selectedType !== 'all') params.append('type', selectedType);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      params.append('sortBy', sortBy);

      const response = await fetch(`/api/tools/templates/gallery?${params}`);
      const data = await response.json();

      if (data.success) {
        setTemplates(data.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load templates',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load templates',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFromTemplate = async (templateId: string, customization?: any) => {
    try {
      setCreating(true);
      const response = await fetch(`/api/tools/templates/${templateId}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customization || {}),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Tool created from template successfully',
        });
        router.push(`/dashboard/tools/${data.data.id}/edit`);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to create tool from template',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create tool from template',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = TOOL_TYPES.find(t => t.value === type);
    return typeConfig?.icon || Code;
  };

  const getTypeColor = (type: string) => {
    const typeConfig = TOOL_TYPES.find(t => t.value === type);
    return typeConfig?.color || 'bg-gray-500';
  };

  const filteredTemplates = templates.filter(template => {
    if (searchQuery && !template.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !template.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (selectedType !== 'all' && template.type !== selectedType) return false;
    if (selectedCategory !== 'all' && template.category !== selectedCategory) return false;
    return true;
  });

  const renderTemplateCard = (template: ToolTemplate) => {
    const Icon = getTypeIcon(template.type);

    return (
      <Card key={template.id} className="bg-white/80 backdrop-blur-sm border border-white/20 hover:bg-white/90 transition-all duration-200 group">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className={`p-1.5 rounded-md ${getTypeColor(template.type)} text-white`}>
                  <Icon className="h-4 w-4" />
                </div>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  {template.name}
                </CardTitle>
              </div>
              <CardDescription className="text-sm text-gray-600 line-clamp-2">
                {template.description}
              </CardDescription>
            </div>
            <div className="flex items-center gap-1 ml-4">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="text-sm font-medium">{template.rating}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Category: {template.category}</span>
              <Badge className={`${getTypeColor(template.type)} text-white text-xs`}>
                {TOOL_TYPES.find(t => t.value === template.type)?.label}
              </Badge>
            </div>

            <div className="flex flex-wrap gap-1">
              {template.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-gray-900">{template.usageCount}</div>
                <div className="text-gray-500">Uses</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">{template.popularity}</div>
                <div className="text-gray-500">Popularity</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">{template.parameters.length}</div>
                <div className="text-gray-500">Params</div>
              </div>
            </div>

            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                  {template.author.name.charAt(0).toUpperCase()}
                </div>
                <span>by {template.author.name}</span>
              </div>
              <div className="flex items-center gap-1">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setSelectedTemplate(template)}
                      className="h-8 w-8 p-0"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Icon className="h-5 w-5" />
                        {template.name}
                      </DialogTitle>
                      <DialogDescription>
                        {template.description}
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                      <div className="flex flex-wrap gap-2">
                        <Badge className={`${getTypeColor(template.type)} text-white`}>
                          {TOOL_TYPES.find(t => t.value === template.type)?.label}
                        </Badge>
                        <Badge variant="outline">{template.category}</Badge>
                        {template.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      {template.parameters.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">Parameters</h4>
                          <div className="space-y-2">
                            {template.parameters.map((param, index) => (
                              <div key={index} className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium">{param.name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {param.type}
                                  </Badge>
                                </div>
                                {param.description && (
                                  <p className="text-sm text-gray-600">{param.description}</p>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {template.code && (
                        <div>
                          <h4 className="font-medium mb-2">Code Preview</h4>
                          <pre className="bg-gray-50 p-3 rounded-lg text-sm overflow-x-auto">
                            <code>{template.code.slice(0, 500)}...</code>
                          </pre>
                        </div>
                      )}

                      <div className="flex items-center justify-between pt-4 border-t">
                        <div className="text-sm text-gray-500">
                          Created {new Date(template.createdAt).toLocaleDateString()}
                        </div>
                        <Button
                          onClick={() => handleCreateFromTemplate(template.id)}
                          disabled={creating}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          {creating ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Creating...
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-2" />
                              Use Template
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleCreateFromTemplate(template.id)}
                  disabled={creating}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderTemplateList = (template: ToolTemplate) => {
    const Icon = getTypeIcon(template.type);

    return (
      <Card key={template.id} className="bg-white/80 backdrop-blur-sm border border-white/20 hover:bg-white/90 transition-all duration-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 flex-1">
              <div className={`p-2 rounded-lg ${getTypeColor(template.type)} text-white`}>
                <Icon className="h-5 w-5" />
              </div>

              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-gray-900">{template.name}</h3>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="text-xs font-medium">{template.rating}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 line-clamp-1 mb-2">{template.description}</p>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span>{template.category}</span>
                  <span>{template.usageCount} uses</span>
                  <span>{template.parameters.length} parameters</span>
                  <span>by {template.author.name}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge className={`${getTypeColor(template.type)} text-white text-xs`}>
                {TOOL_TYPES.find(t => t.value === template.type)?.label}
              </Badge>

              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  {/* Same dialog content as card view */}
                </DialogContent>
              </Dialog>

              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleCreateFromTemplate(template.id)}
                disabled={creating}
                className="h-8 w-8 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/dashboard/tools')}
              className="bg-white/80 backdrop-blur-sm border border-white/20"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Tool Templates</h1>
              <p className="text-gray-600">Browse and use pre-built tool templates</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard/tools/new')}
              className="bg-white/80 backdrop-blur-sm border-white/20"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Custom
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Templates</p>
                  <p className="text-2xl font-bold text-gray-900">{templates.length}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Popular</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {templates.filter(t => t.popularity > 80).length}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Uses</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {templates.reduce((sum, t) => sum + t.usageCount, 0)}
                  </p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(templates.reduce((sum, t) => sum + t.rating, 0) / templates.length || 0).toFixed(1)}
                  </p>
                </div>
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="bg-white/80 backdrop-blur-sm border border-white/20 mb-6">
          <CardContent className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/50"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="bg-white/50"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <ViewToggle
                viewPreferences={viewPreferences}
              />
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger className="bg-white/50">
                    <SelectValue placeholder="Tool Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {TOOL_TYPES.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="bg-white/50">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {TEMPLATE_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="bg-white/50">
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popularity">Most Popular</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                    <SelectItem value="usageCount">Most Used</SelectItem>
                    <SelectItem value="createdAt">Newest</SelectItem>
                    <SelectItem value="name">Name A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Templates Grid/List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="bg-white/80 backdrop-blur-sm border border-white/20">
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredTemplates.length === 0 ? (
          <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
            <CardContent className="p-12 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No templates found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || selectedType !== 'all' || selectedCategory !== 'all'
                  ? 'Try adjusting your search criteria or filters.'
                  : 'No templates are available at the moment.'}
              </p>
              <Button
                onClick={() => router.push('/dashboard/tools/new')}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Custom Tool
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className={viewPreferences.preferences.viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
          }>
            {filteredTemplates.map(template =>
              viewPreferences.preferences.viewMode === 'grid' ? renderTemplateCard(template) : renderTemplateList(template)
            )}
          </div>
        )}
      </div>
    </div>
  );
}