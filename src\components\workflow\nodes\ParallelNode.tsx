import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Split, CheckCircle, XCircle, Activity, AlertTriangle, Play, Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ParallelNodeData {
  label: string;
  description?: string;
  config?: {
    nodes?: Array<{ id: string; nodeId: string; label: string; enabled: boolean }>;
    executionMode?: 'all' | 'race' | 'some';
    aggregateResults?: boolean;
  };
  status?: 'idle' | 'running' | 'success' | 'error';
  isConfigValid?: boolean;
  lastOutput?: string;
  errorMessage?: string;
  progress?: number;
}

const ParallelNode: React.FC<NodeProps<ParallelNodeData>> = ({ data, selected }) => {
  const getStatusIcon = () => {
    switch (data.status) {
      case 'running':
        return <Activity className="h-3 w-3 text-blue-600 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const activeNodes = data.config?.nodes?.filter(n => n.enabled).length || 0;
  const totalNodes = data.config?.nodes?.length || 0;

  return (
    <Card
      className={cn(
        'min-w-[200px] max-w-[300px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor(),
        !data.isConfigValid && 'border-red-300 bg-red-50'
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      
      <CardContent className="p-4">
        {/* Progress bar for running status */}
        {data.status === 'running' && data.progress !== undefined && (
          <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-t-lg overflow-hidden">
            <div 
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${data.progress}%` }}
            />
          </div>
        )}

        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Split className="h-4 w-4 text-orange-600" />
            {getStatusIcon()}
          </div>
          <div className="flex items-center space-x-1">
            {!data.isConfigValid && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <Badge variant="outline" className="text-xs">
              {activeNodes}/{totalNodes}
            </Badge>
          </div>
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1 truncate">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs mb-2">
          <Split className="h-2 w-2 mr-1" />
          Parallel
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
            {data.description}
          </p>
        )}

        {/* Parallel nodes preview */}
        {data.config?.nodes && data.config.nodes.length > 0 && (
          <div className="mb-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
            <div className="font-medium text-gray-800 mb-1">Parallel Nodes:</div>
            <div className="space-y-1">
              {data.config.nodes.slice(0, 3).map((node, index) => (
                <div key={index} className="flex items-center space-x-1">
                  {node.enabled ? (
                    <Play className="h-2 w-2 text-green-600" />
                  ) : (
                    <Pause className="h-2 w-2 text-gray-400" />
                  )}
                  <span className="text-gray-700 truncate">{node.label}</span>
                </div>
              ))}
              {data.config.nodes.length > 3 && (
                <div className="text-gray-500">
                  +{data.config.nodes.length - 3} more...
                </div>
              )}
            </div>
          </div>
        )}

        {/* Status-specific content */}
        {data.status === 'error' && data.errorMessage && (
          <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <XCircle className="h-3 w-3 text-red-600" />
              <span className="font-medium text-red-800">Error</span>
            </div>
            <p className="text-red-700 line-clamp-2">{data.errorMessage}</p>
          </div>
        )}

        {/* Configuration summary */}
        {data.config && (
          <div className="text-xs text-muted-foreground space-y-1">
            {data.config.executionMode && (
              <div>Mode: {data.config.executionMode}</div>
            )}
            {data.config.aggregateResults !== undefined && (
              <div>Aggregate: {data.config.aggregateResults ? 'Yes' : 'No'}</div>
            )}
          </div>
        )}
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(ParallelNode);