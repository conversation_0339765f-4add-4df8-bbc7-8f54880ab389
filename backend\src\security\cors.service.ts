import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CorsService {
    constructor(private configService: ConfigService) { }

    getCorsOptions() {
        const allowedOrigins = this.configService.get<string>('ALLOWED_ORIGINS', '*').split(',');

        return {
            origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => {
                if (!origin || allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
                    callback(null, true);
                } else {
                    callback(new Error('Not allowed by CORS'));
                }
            },
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            maxAge: 86400, // 24 hours
        };
    }
}