import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseGuards,
    Request,
    HttpCode,
    HttpStatus,
    ValidationPipe,
    ParseUUIDPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { OrganizationsService } from './organizations.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { TenantGuard } from '../auth/tenant.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import {
    CreateOrganizationDto,
    UpdateOrganizationDto,
    OrganizationDto,
    OrganizationListQueryDto,
    OrganizationListResponseDto,
    OrganizationStatsDto,
    OrganizationSettingsDto,
    OrganizationBrandingDto,
    OrganizationUsageDto,
    OrganizationMemberDto,
    BulkMemberActionDto
} from './dto/organizations.dto';

@ApiTags('organizations')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('organizations')
export class OrganizationsController {
    constructor(private readonly organizationsService: OrganizationsService) { }

    @Post()
    @Roles(Role.ADMIN, Role.OWNER) // Only system admins can create organizations
    @ApiOperation({ summary: 'Create a new organization' })
    @ApiResponse({ status: 201, description: 'Organization created successfully', type: OrganizationDto })
    @ApiResponse({ status: 400, description: 'Bad request - validation failed' })
    @ApiResponse({ status: 409, description: 'Conflict - slug or domain already exists' })
    async create(
        @Body(ValidationPipe) createOrganizationDto: CreateOrganizationDto,
        @Request() req: any
    ): Promise<OrganizationDto> {
        return this.organizationsService.create(createOrganizationDto, req.user.id);
    }

    @Get()
    @Roles(Role.ADMIN) // Only system admins can list all organizations
    @ApiOperation({ summary: 'Get all organizations with filtering and pagination' })
    @ApiResponse({ status: 200, description: 'Organizations retrieved successfully', type: OrganizationListResponseDto })
    @ApiQuery({ name: 'page', required: false, type: Number })
    @ApiQuery({ name: 'limit', required: false, type: Number })
    @ApiQuery({ name: 'search', required: false, type: String })
    @ApiQuery({ name: 'plan', required: false, type: String })
    @ApiQuery({ name: 'isActive', required: false, type: Boolean })
    @ApiQuery({ name: 'sortBy', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
    async findAll(
        @Query(ValidationPipe) query: OrganizationListQueryDto
    ): Promise<OrganizationListResponseDto> {
        return this.organizationsService.findAll(query);
    }

    @Get('current')
    @UseGuards(TenantGuard)
    @ApiOperation({ summary: 'Get current user organization' })
    @ApiResponse({ status: 200, description: 'Current organization retrieved successfully', type: OrganizationDto })
    async getCurrent(@Request() req: any): Promise<OrganizationDto> {
        return this.organizationsService.findOne(req.user.organizationId);
    }

    @Get('current/stats')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER, Role.MANAGER)
    @ApiOperation({ summary: 'Get current organization statistics' })
    @ApiResponse({ status: 200, description: 'Organization statistics retrieved successfully', type: OrganizationStatsDto })
    async getCurrentStats(@Request() req: any): Promise<OrganizationStatsDto> {
        return this.organizationsService.getStats(req.user.organizationId);
    }

    @Get('current/usage')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER, Role.MANAGER)
    @ApiOperation({ summary: 'Get current organization usage metrics' })
    @ApiResponse({ status: 200, description: 'Organization usage retrieved successfully', type: OrganizationUsageDto })
    @ApiQuery({ name: 'period', required: false, enum: ['day', 'week', 'month'] })
    async getCurrentUsage(
        @Request() req: any,
        @Query('period') period: 'day' | 'week' | 'month' = 'month'
    ): Promise<OrganizationUsageDto> {
        return this.organizationsService.getUsage(req.user.organizationId, period);
    }

    @Get('current/members')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER, Role.MANAGER)
    @ApiOperation({ summary: 'Get current organization members' })
    @ApiResponse({ status: 200, description: 'Organization members retrieved successfully', type: [OrganizationMemberDto] })
    async getCurrentMembers(@Request() req: any): Promise<OrganizationMemberDto[]> {
        return this.organizationsService.getMembers(req.user.organizationId);
    }

    @Post('current/members/bulk-action')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER)
    @ApiOperation({ summary: 'Perform bulk actions on organization members' })
    @ApiResponse({ status: 200, description: 'Bulk action completed' })
    @HttpCode(HttpStatus.OK)
    async bulkMemberAction(
        @Body(ValidationPipe) bulkActionDto: BulkMemberActionDto,
        @Request() req: any
    ): Promise<{ success: number; failed: number }> {
        return this.organizationsService.bulkMemberAction(req.user.organizationId, bulkActionDto, req.user.id);
    }

    @Get(':id')
    @Roles(Role.ADMIN) // Only system admins can view any organization
    @ApiOperation({ summary: 'Get organization by ID' })
    @ApiResponse({ status: 200, description: 'Organization retrieved successfully', type: OrganizationDto })
    @ApiResponse({ status: 404, description: 'Organization not found' })
    async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<OrganizationDto> {
        return this.organizationsService.findOne(id);
    }

    @Patch('current')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER)
    @ApiOperation({ summary: 'Update current organization' })
    @ApiResponse({ status: 200, description: 'Organization updated successfully', type: OrganizationDto })
    @ApiResponse({ status: 404, description: 'Organization not found' })
    @ApiResponse({ status: 409, description: 'Conflict - slug or domain already exists' })
    async updateCurrent(
        @Body(ValidationPipe) updateOrganizationDto: UpdateOrganizationDto,
        @Request() req: any
    ): Promise<OrganizationDto> {
        return this.organizationsService.update(req.user.organizationId, updateOrganizationDto, req.user.id);
    }

    @Patch(':id')
    @Roles(Role.ADMIN) // Only system admins can update any organization
    @ApiOperation({ summary: 'Update organization by ID' })
    @ApiResponse({ status: 200, description: 'Organization updated successfully', type: OrganizationDto })
    @ApiResponse({ status: 404, description: 'Organization not found' })
    @ApiResponse({ status: 409, description: 'Conflict - slug or domain already exists' })
    async update(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(ValidationPipe) updateOrganizationDto: UpdateOrganizationDto,
        @Request() req: any
    ): Promise<OrganizationDto> {
        return this.organizationsService.update(id, updateOrganizationDto, req.user.id);
    }

    @Patch('current/settings')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER)
    @ApiOperation({ summary: 'Update current organization settings' })
    @ApiResponse({ status: 200, description: 'Organization settings updated successfully', type: OrganizationDto })
    async updateCurrentSettings(
        @Body(ValidationPipe) settings: OrganizationSettingsDto,
        @Request() req: any
    ): Promise<OrganizationDto> {
        return this.organizationsService.updateSettings(req.user.organizationId, settings, req.user.id);
    }

    @Patch('current/branding')
    @UseGuards(TenantGuard)
    @Roles(Role.ADMIN, Role.OWNER)
    @ApiOperation({ summary: 'Update current organization branding' })
    @ApiResponse({ status: 200, description: 'Organization branding updated successfully', type: OrganizationDto })
    async updateCurrentBranding(
        @Body(ValidationPipe) branding: OrganizationBrandingDto,
        @Request() req: any
    ): Promise<OrganizationDto> {
        return this.organizationsService.updateBranding(req.user.organizationId, branding, req.user.id);
    }

    @Delete('current')
    @UseGuards(TenantGuard)
    @Roles(Role.OWNER) // Only organization owners can delete their organization
    @ApiOperation({ summary: 'Deactivate current organization (soft delete)' })
    @ApiResponse({ status: 200, description: 'Organization deactivated successfully' })
    @HttpCode(HttpStatus.OK)
    async removeCurrent(@Request() req: any): Promise<{ message: string }> {
        await this.organizationsService.remove(req.user.organizationId, req.user.id);
        return { message: 'Organization deactivated successfully' };
    }

    @Delete(':id')
    @Roles(Role.ADMIN) // Only system admins can delete any organization
    @ApiOperation({ summary: 'Deactivate organization by ID (soft delete)' })
    @ApiResponse({ status: 200, description: 'Organization deactivated successfully' })
    @HttpCode(HttpStatus.OK)
    async remove(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ): Promise<{ message: string }> {
        await this.organizationsService.remove(id, req.user.id);
        return { message: 'Organization deactivated successfully' };
    }
}