import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Send, 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  DollarSign, 
  Zap,
  Copy,
  Download,
  RefreshCw,
  Settings,
  BarChart3
} from 'lucide-react';

interface AiRequest {
  requestType: string;
  input: Record<string, any>;
  sessionId?: string;
  executorType?: string;
  executorId?: string;
  preferredProviderId?: string;
  preferredModelId?: string;
  maxLatencyMs?: number;
  maxCost?: number;
  requiredCapabilities?: string[];
  streaming?: boolean;
  metadata?: Record<string, any>;
}

interface AiResponse {
  requestId: string;
  output: any;
  usage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  provider: {
    id: string;
    name: string;
    type: string;
  };
  model: {
    id: string;
    modelId: string;
    displayName: string;
  } | null;
  duration: number;
  cost: number;
}

interface Provider {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  models: ProviderModel[];
}

interface ProviderModel {
  id: string;
  modelId: string;
  displayName: string;
  capabilities: Record<string, any>;
  contextWindow: number;
  inputCostPer1k?: number;
  outputCostPer1k?: number;
  isActive: boolean;
  isDefault: boolean;
}

const REQUEST_TYPES = [
  { value: 'chat', label: 'Chat Completion', description: 'Generate conversational responses' },
  { value: 'completion', label: 'Text Completion', description: 'Complete text prompts' },
  { value: 'embedding', label: 'Text Embedding', description: 'Generate text embeddings' },
  { value: 'function_call', label: 'Function Calling', description: 'Execute function calls' },
  { value: 'code_generation', label: 'Code Generation', description: 'Generate code' },
  { value: 'analysis', label: 'Text Analysis', description: 'Analyze and process text' },
];

const CAPABILITIES = [
  'chat',
  'completion',
  'embedding',
  'vision',
  'function_calling',
  'code_generation',
  'analysis',
  'reasoning',
];

export default function AiRequestExecutor() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<AiResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const streamingRef = useRef<EventSource | null>(null);

  // Form state
  const [request, setRequest] = useState<AiRequest>({
    requestType: 'chat',
    input: {
      messages: [
        { role: 'user', content: 'Hello! Can you help me with a task?' }
      ],
      parameters: {
        temperature: 0.7,
        max_tokens: 1000,
      }
    },
    streaming: false,
    metadata: {},
  });

  // Advanced settings
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [requestHistory, setRequestHistory] = useState<AiResponse[]>([]);

  useEffect(() => {
    loadProviders();
    loadRequestHistory();
  }, []);

  const loadProviders = async () => {
    try {
      const response = await fetch('/api/providers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      const data = await response.json();
      setProviders(data.filter((p: Provider) => p.isActive));
    } catch (error) {
      console.error('Failed to load providers:', error);
    }
  };

  const loadRequestHistory = async () => {
    try {
      const response = await fetch('/api/providers/usage/logs?limit=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      const data = await response.json();
      setRequestHistory(data.logs || []);
    } catch (error) {
      console.error('Failed to load request history:', error);
    }
  };

  const executeRequest = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);
    setStreamingContent('');

    try {
      if (request.streaming) {
        await executeStreamingRequest();
      } else {
        await executeNormalRequest();
      }
    } catch (err: any) {
      setError(err.message || 'Request failed');
    } finally {
      setLoading(false);
      setIsStreaming(false);
    }
  };

  const executeNormalRequest = async () => {
    const response = await fetch('/api/providers/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Request failed');
    }

    const data = await response.json();
    setResponse(data);
    loadRequestHistory(); // Refresh history
  };

  const executeStreamingRequest = async () => {
    setIsStreaming(true);
    
    // For streaming, we'll simulate the streaming response
    // In a real implementation, you'd use Server-Sent Events or WebSocket
    const response = await fetch('/api/providers/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify({ ...request, streaming: true }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Streaming request failed');
    }

    const data = await response.json();
    
    // Simulate streaming by gradually revealing the content
    const content = data.output.content || '';
    const words = content.split(' ');
    
    for (let i = 0; i < words.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setStreamingContent(words.slice(0, i + 1).join(' '));
    }

    setResponse(data);
    loadRequestHistory();
  };

  const stopStreaming = () => {
    if (streamingRef.current) {
      streamingRef.current.close();
      streamingRef.current = null;
    }
    setIsStreaming(false);
    setLoading(false);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadResponse = () => {
    if (!response) return;

    const data = {
      request,
      response,
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-request-${response.requestId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const updateInputMessages = (messages: any[]) => {
    setRequest({
      ...request,
      input: {
        ...request.input,
        messages,
      },
    });
  };

  const addMessage = () => {
    const currentMessages = request.input.messages || [];
    updateInputMessages([
      ...currentMessages,
      { role: 'user', content: '' },
    ]);
  };

  const updateMessage = (index: number, field: string, value: string) => {
    const currentMessages = [...(request.input.messages || [])];
    currentMessages[index] = {
      ...currentMessages[index],
      [field]: value,
    };
    updateInputMessages(currentMessages);
  };

  const removeMessage = (index: number) => {
    const currentMessages = request.input.messages || [];
    updateInputMessages(currentMessages.filter((_: any, i: number) => i !== index));
  };

  const selectedProvider = providers.find(p => p.id === request.preferredProviderId);
  const availableModels = selectedProvider?.models.filter(m => m.isActive) || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Request Executor</h1>
            <p className="text-gray-600 mt-1">Test and execute AI requests with smart routing</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={loadRequestHistory}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh History
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              <Settings className="h-4 w-4 mr-2" />
              {showAdvanced ? 'Hide' : 'Show'} Advanced
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Request Configuration */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Request Configuration</CardTitle>
                <CardDescription>Configure your AI request parameters</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Request Type */}
                <div>
                  <Label htmlFor="requestType">Request Type</Label>
                  <Select
                    value={request.requestType}
                    onValueChange={(value) => setRequest({ ...request, requestType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {REQUEST_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-sm text-gray-500">{type.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Messages (for chat/completion) */}
                {(request.requestType === 'chat' || request.requestType === 'completion') && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <Label>Messages</Label>
                      <Button size="sm" variant="outline" onClick={addMessage}>
                        <Send className="h-4 w-4 mr-2" />
                        Add Message
                      </Button>
                    </div>
                    <div className="space-y-3">
                      {(request.input.messages || []).map((message: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <Select
                              value={message.role}
                              onValueChange={(value) => updateMessage(index, 'role', value)}
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="user">User</SelectItem>
                                <SelectItem value="assistant">Assistant</SelectItem>
                                <SelectItem value="system">System</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => removeMessage(index)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                          <Textarea
                            placeholder="Enter message content..."
                            value={message.content}
                            onChange={(e) => updateMessage(index, 'content', e.target.value)}
                            rows={3}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Parameters */}
                <div>
                  <Label>Parameters</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <Label htmlFor="temperature" className="text-sm">Temperature</Label>
                      <Input
                        id="temperature"
                        type="number"
                        min="0"
                        max="2"
                        step="0.1"
                        value={request.input.parameters?.temperature || 0.7}
                        onChange={(e) => setRequest({
                          ...request,
                          input: {
                            ...request.input,
                            parameters: {
                              ...request.input.parameters,
                              temperature: parseFloat(e.target.value),
                            },
                          },
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="maxTokens" className="text-sm">Max Tokens</Label>
                      <Input
                        id="maxTokens"
                        type="number"
                        min="1"
                        max="8000"
                        value={request.input.parameters?.max_tokens || 1000}
                        onChange={(e) => setRequest({
                          ...request,
                          input: {
                            ...request.input,
                            parameters: {
                              ...request.input.parameters,
                              max_tokens: parseInt(e.target.value),
                            },
                          },
                        })}
                      />
                    </div>
                  </div>
                </div>

                {/* Advanced Settings */}
                {showAdvanced && (
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="font-medium">Advanced Settings</h4>
                    
                    {/* Provider Selection */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="provider">Preferred Provider</Label>
                        <Select
                          value={request.preferredProviderId || ''}
                          onValueChange={(value) => setRequest({
                            ...request,
                            preferredProviderId: value || undefined,
                            preferredModelId: undefined, // Reset model when provider changes
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Auto-select" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">Auto-select</SelectItem>
                            {providers.map((provider) => (
                              <SelectItem key={provider.id} value={provider.id}>
                                {provider.name} ({provider.type})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="model">Preferred Model</Label>
                        <Select
                          value={request.preferredModelId || ''}
                          onValueChange={(value) => setRequest({
                            ...request,
                            preferredModelId: value || undefined,
                          })}
                          disabled={!selectedProvider}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Auto-select" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">Auto-select</SelectItem>
                            {availableModels.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.displayName}
                                {model.isDefault && <Badge className="ml-2">Default</Badge>}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Constraints */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="maxLatency">Max Latency (ms)</Label>
                        <Input
                          id="maxLatency"
                          type="number"
                          min="100"
                          value={request.maxLatencyMs || ''}
                          onChange={(e) => setRequest({
                            ...request,
                            maxLatencyMs: e.target.value ? parseInt(e.target.value) : undefined,
                          })}
                          placeholder="No limit"
                        />
                      </div>

                      <div>
                        <Label htmlFor="maxCost">Max Cost ($)</Label>
                        <Input
                          id="maxCost"
                          type="number"
                          min="0"
                          step="0.01"
                          value={request.maxCost || ''}
                          onChange={(e) => setRequest({
                            ...request,
                            maxCost: e.target.value ? parseFloat(e.target.value) : undefined,
                          })}
                          placeholder="No limit"
                        />
                      </div>
                    </div>

                    {/* Required Capabilities */}
                    <div>
                      <Label>Required Capabilities</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {CAPABILITIES.map((capability) => (
                          <Badge
                            key={capability}
                            variant={request.requiredCapabilities?.includes(capability) ? "default" : "outline"}
                            className="cursor-pointer"
                            onClick={() => {
                              const current = request.requiredCapabilities || [];
                              const updated = current.includes(capability)
                                ? current.filter(c => c !== capability)
                                : [...current, capability];
                              setRequest({ ...request, requiredCapabilities: updated });
                            }}
                          >
                            {capability}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Streaming */}
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="streaming"
                        checked={request.streaming || false}
                        onCheckedChange={(checked) => setRequest({ ...request, streaming: checked })}
                      />
                      <Label htmlFor="streaming">Enable Streaming</Label>
                    </div>
                  </div>
                )}

                {/* Execute Button */}
                <div className="flex gap-3">
                  <Button 
                    onClick={executeRequest} 
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {isStreaming ? 'Streaming...' : 'Executing...'}
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Execute Request
                      </>
                    )}
                  </Button>
                  {isStreaming && (
                    <Button variant="outline" onClick={stopStreaming}>
                      Stop
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Response */}
            {(response || error || isStreaming) && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Response</CardTitle>
                    {response && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(JSON.stringify(response, null, 2))}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={downloadResponse}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {error && (
                    <Alert className="border-red-200 bg-red-50">
                      <XCircle className="h-4 w-4 text-red-500" />
                      <AlertDescription className="text-red-700">
                        {error}
                      </AlertDescription>
                    </Alert>
                  )}

                  {isStreaming && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-gray-600">Streaming response...</span>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <pre className="whitespace-pre-wrap text-sm">
                          {streamingContent}
                          <span className="animate-pulse">|</span>
                        </pre>
                      </div>
                    </div>
                  )}

                  {response && !isStreaming && (
                    <div className="space-y-4">
                      {/* Response Metadata */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-sm font-medium">Status</p>
                            <p className="text-sm text-gray-600">Success</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="text-sm font-medium">Duration</p>
                            <p className="text-sm text-gray-600">{response.duration}ms</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-sm font-medium">Cost</p>
                            <p className="text-sm text-gray-600">${response.cost.toFixed(4)}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-purple-500" />
                          <div>
                            <p className="text-sm font-medium">Tokens</p>
                            <p className="text-sm text-gray-600">{response.usage.totalTokens}</p>
                          </div>
                        </div>
                      </div>

                      {/* Provider Info */}
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h4 className="font-medium mb-2">Selected Provider</h4>
                        <div className="flex items-center gap-4">
                          <div>
                            <p className="text-sm font-medium">{response.provider.name}</p>
                            <p className="text-sm text-gray-600">{response.provider.type}</p>
                          </div>
                          {response.model && (
                            <div>
                              <p className="text-sm font-medium">{response.model.displayName}</p>
                              <p className="text-sm text-gray-600">{response.model.modelId}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Response Content */}
                      <div>
                        <h4 className="font-medium mb-2">Response Content</h4>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <pre className="whitespace-pre-wrap text-sm">
                            {typeof response.output === 'string' 
                              ? response.output 
                              : JSON.stringify(response.output, null, 2)
                            }
                          </pre>
                        </div>
                      </div>

                      {/* Usage Details */}
                      <div>
                        <h4 className="font-medium mb-2">Token Usage</h4>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-center">
                            <p className="text-2xl font-bold text-blue-600">{response.usage.inputTokens}</p>
                            <p className="text-sm text-gray-600">Input Tokens</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold text-green-600">{response.usage.outputTokens}</p>
                            <p className="text-sm text-gray-600">Output Tokens</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold text-purple-600">{response.usage.totalTokens}</p>
                            <p className="text-sm text-gray-600">Total Tokens</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Active Providers</span>
                  <span className="font-medium">{providers.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Recent Requests</span>
                  <span className="font-medium">{requestHistory.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg Response Time</span>
                  <span className="font-medium">
                    {requestHistory.length > 0 
                      ? Math.round(requestHistory.reduce((sum, r) => sum + (r.duration || 0), 0) / requestHistory.length)
                      : 0
                    }ms
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Request History */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Requests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {requestHistory.slice(0, 5).map((req: any) => (
                    <div key={req.id} className="border rounded-lg p-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {req.requestType}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(req.createdAt).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">{req.provider?.name}</span>
                        <span className="font-medium">{req.durationMs}ms</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">{req.totalTokens} tokens</span>
                        <span className="font-medium">${req.totalCost.toFixed(4)}</span>
                      </div>
                    </div>
                  ))}
                  
                  {requestHistory.length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      No recent requests
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Provider Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Provider Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {providers.slice(0, 5).map((provider) => (
                    <div key={provider.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium">{provider.name}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {provider.models.length} models
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}