"use client";

import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  MessageS<PERSON>re,
  Zap,
  Users,
  Brain,
  Wrench,
  AlertCircle,
  Info,
  Plus,
  X,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface AgentNodeConfigPanelProps {
  nodeId: string;
  config: {
    agentId?: string;
    agentType?: "STANDALONE" | "TOOL_DRIVEN" | "HYBRID" | "MULTI_TASKING" | "MULTI_PROVIDER";
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: {
      canCollaborate?: boolean;
      shareContext?: boolean;
      maxConcurrentTasks?: number;
      memoryWindow?: number;
      providerRequirements?: any;
    };
    communication?: {
      enableAgentToAgent?: boolean;
      allowBroadcast?: boolean;
      subscribeToEvents?: string[];
      priority?: "low" | "normal" | "high" | "urgent";
    };
    fallback?: {
      enabled?: boolean;
      fallbackAgentId?: string;
      maxRetries?: number;
      retryDelay?: number;
    };
    monitoring?: {
      trackMetrics?: boolean;
      logConversations?: boolean;
      alertOnErrors?: boolean;
    };
  };
  onConfigChange: (config: any) => void;
  readOnly?: boolean;
}

const AgentNodeConfigPanel = ({
  nodeId,
  config,
  onConfigChange,
  readOnly = false,
}: AgentNodeConfigPanelProps) => {
  const { toast } = useToast();
  const [agents, setAgents] = useState<any[]>([]);
  const [tools, setTools] = useState<any[]>([]);
  const [skills, setSkills] = useState<any[]>([]);
  const [providers, setProviders] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [agentsRes, toolsRes, skillsRes, providersRes] = await Promise.all([
        fetch('/api/agents').then(r => r.json()),
        fetch('/api/tools').then(r => r.json()),
        fetch('/api/skills').then(r => r.json()),
        fetch('/api/providers').then(r => r.json()),
      ]);

      setAgents(agentsRes.agents || []);
      setTools(toolsRes.tools || []);
      setSkills(skillsRes.skills || []);
      setProviders(providersRes.providers || []);

      // Set models based on selected provider
      const selectedProvider = providersRes.providers?.find((p: any) => p.id === config.provider);
      setModels(selectedProvider?.models || []);

    } catch (error: any) {
      const errorMessage = error?.message || 'Failed to load configuration data';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      setAgents([]);
      setTools([]);
      setSkills([]);
      setProviders([]);
      setModels([]);
    } finally {
      setLoading(false);
    }
  };

  const handleConfigUpdate = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    onConfigChange(newConfig);
  };

  const handleNestedConfigUpdate = (section: string, key: string, value: any) => {
    const newConfig = {
      ...config,
      [section]: {
        ...(config[section as keyof typeof config] as any || {}),
        [key]: value,
      },
    };
    onConfigChange(newConfig);
  };

  const handleProviderChange = (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    setModels(provider?.models || []);
    handleConfigUpdate('provider', providerId);

    // Reset model if it's not available in the new provider
    if (config.model && !provider?.models.includes(config.model)) {
      handleConfigUpdate('model', provider?.models[0] || '');
    }
  };

  const addSkill = (skillId: string) => {
    const currentSkills = config.skills || [];
    if (!currentSkills.includes(skillId)) {
      handleConfigUpdate('skills', [...currentSkills, skillId]);
    }
  };

  const removeSkill = (skillId: string) => {
    const currentSkills = config.skills || [];
    handleConfigUpdate('skills', currentSkills.filter(id => id !== skillId));
  };

  const addTool = (toolId: string) => {
    const currentTools = config.tools || [];
    if (!currentTools.includes(toolId)) {
      handleConfigUpdate('tools', [...currentTools, toolId]);
    }
  };

  const removeTool = (toolId: string) => {
    const currentTools = config.tools || [];
    handleConfigUpdate('tools', currentTools.filter(id => id !== toolId));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading agent configuration...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-destructive mb-2">Configuration Error</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadData} variant="outline">
                Retry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Bot className="mr-2 h-5 w-5" />
          Agent Node Configuration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px] pr-4">
          <Accordion type="multiple" defaultValue={["basic", "ai-model"]} className="w-full">

            {/* Basic Configuration */}
            <AccordionItem value="basic">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Basic Configuration
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="agentId">Agent Instance</Label>
                  <Select
                    value={config.agentId || ""}
                    onValueChange={(value) => handleConfigUpdate('agentId', value)}
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an agent" />
                    </SelectTrigger>
                    <SelectContent>
                      {agents.map((agent) => (
                        <SelectItem key={agent.id} value={agent.id}>
                          <div className="flex items-center">
                            <span>{agent.name}</span>
                            <Badge variant="outline" className="ml-2">
                              {agent.type}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="agentType">Agent Type</Label>
                  <Select
                    value={config.agentType || "STANDALONE"}
                    onValueChange={(value) => handleConfigUpdate('agentType', value)}
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STANDALONE">Standalone</SelectItem>
                      <SelectItem value="TOOL_DRIVEN">Tool-Driven</SelectItem>
                      <SelectItem value="HYBRID">Hybrid</SelectItem>
                      <SelectItem value="MULTI_TASKING">Multi-Tasking</SelectItem>
                      <SelectItem value="MULTI_PROVIDER">Multi-Provider</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {config.agentType === "STANDALONE" && "Pure conversational agent without tools"}
                    {config.agentType === "TOOL_DRIVEN" && "Agent that primarily uses tools to complete tasks"}
                    {config.agentType === "HYBRID" && "Agent that combines conversation and tool usage"}
                    {config.agentType === "MULTI_TASKING" && "Agent that can handle multiple concurrent tasks"}
                    {config.agentType === "MULTI_PROVIDER" && "Agent that can switch between AI providers"}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="systemPrompt">System Prompt</Label>
                  <Textarea
                    id="systemPrompt"
                    value={config.systemPrompt || ""}
                    onChange={(e) => handleConfigUpdate('systemPrompt', e.target.value)}
                    placeholder="Enter system prompt for the agent..."
                    rows={4}
                    disabled={readOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea
                    id="instructions"
                    value={config.instructions || ""}
                    onChange={(e) => handleConfigUpdate('instructions', e.target.value)}
                    placeholder="Enter specific instructions for the agent..."
                    rows={3}
                    disabled={readOnly}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* AI Model Configuration */}
            <AccordionItem value="ai-model">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Brain className="mr-2 h-4 w-4" />
                  AI Model Configuration
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">AI Provider</Label>
                  <Select
                    value={config.provider || "openai"}
                    onValueChange={handleProviderChange}
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {providers.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          <div className="flex items-center">
                            <span>{provider.name}</span>
                            <Badge
                              variant={provider.status === "ACTIVE" ? "default" : "secondary"}
                              className="ml-2"
                            >
                              {provider.status}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={config.model || ""}
                    onValueChange={(value) => handleConfigUpdate('model', value)}
                    disabled={readOnly || !config.provider}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model} value={model}>
                          {model}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="temperature">
                    Temperature: {config.temperature || 0.7}
                  </Label>
                  <Slider
                    id="temperature"
                    min={0}
                    max={2}
                    step={0.1}
                    value={[config.temperature || 0.7]}
                    onValueChange={(value) => handleConfigUpdate('temperature', value[0])}
                    disabled={readOnly}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Controls randomness in responses (0 = deterministic, 2 = very creative)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxTokens">Max Tokens</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    value={config.maxTokens || 2000}
                    onChange={(e) => handleConfigUpdate('maxTokens', parseInt(e.target.value))}
                    min={1}
                    max={32000}
                    disabled={readOnly}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="topP">
                      Top P: {config.topP || 1.0}
                    </Label>
                    <Slider
                      id="topP"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[config.topP || 1.0]}
                      onValueChange={(value) => handleConfigUpdate('topP', value[0])}
                      disabled={readOnly}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="frequencyPenalty">
                      Frequency Penalty: {config.frequencyPenalty || 0}
                    </Label>
                    <Slider
                      id="frequencyPenalty"
                      min={-2}
                      max={2}
                      step={0.1}
                      value={[config.frequencyPenalty || 0]}
                      onValueChange={(value) => handleConfigUpdate('frequencyPenalty', value[0])}
                      disabled={readOnly}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Skills and Tools */}
            <AccordionItem value="skills-tools">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Wrench className="mr-2 h-4 w-4" />
                  Skills and Tools
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Skills</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {(config.skills || []).map((skillId) => {
                      const skill = skills.find(s => s.id === skillId);
                      return skill ? (
                        <Badge key={skillId} variant="secondary" className="flex items-center">
                          {skill.name}
                          {!readOnly && (
                            <X
                              className="ml-1 h-3 w-3 cursor-pointer"
                              onClick={() => removeSkill(skillId)}
                            />
                          )}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                  {!readOnly && (
                    <Select onValueChange={addSkill}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add a skill" />
                      </SelectTrigger>
                      <SelectContent>
                        {skills
                          .filter(skill => !(config.skills || []).includes(skill.id))
                          .map((skill) => (
                            <SelectItem key={skill.id} value={skill.id}>
                              <div className="flex items-center">
                                <span>{skill.name}</span>
                                <Badge variant="outline" className="ml-2">
                                  {skill.category}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Tools</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {(config.tools || []).map((toolId) => {
                      const tool = tools.find(t => t.id === toolId);
                      return tool ? (
                        <Badge key={toolId} variant="secondary" className="flex items-center">
                          {tool.name}
                          {!readOnly && (
                            <X
                              className="ml-1 h-3 w-3 cursor-pointer"
                              onClick={() => removeTool(toolId)}
                            />
                          )}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                  {!readOnly && (
                    <Select onValueChange={addTool}>
                      <SelectTrigger>
                        <SelectValue placeholder="Add a tool" />
                      </SelectTrigger>
                      <SelectContent>
                        {tools
                          .filter(tool => !(config.tools || []).includes(tool.id))
                          .map((tool) => (
                            <SelectItem key={tool.id} value={tool.id}>
                              <div className="flex items-center">
                                <span>{tool.name}</span>
                                <Badge variant="outline" className="ml-2">
                                  {tool.type}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Capabilities */}
            <AccordionItem value="capabilities">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Zap className="mr-2 h-4 w-4" />
                  Capabilities
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="canCollaborate"
                    checked={config.capabilities?.canCollaborate || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('capabilities', 'canCollaborate', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="canCollaborate">Enable Agent Collaboration</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="shareContext"
                    checked={config.capabilities?.shareContext || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('capabilities', 'shareContext', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="shareContext">Share Context Between Agents</Label>
                </div>

                {config.agentType === "MULTI_TASKING" && (
                  <div className="space-y-2">
                    <Label htmlFor="maxConcurrentTasks">
                      Max Concurrent Tasks: {config.capabilities?.maxConcurrentTasks || 3}
                    </Label>
                    <Slider
                      id="maxConcurrentTasks"
                      min={1}
                      max={10}
                      step={1}
                      value={[config.capabilities?.maxConcurrentTasks || 3]}
                      onValueChange={(value) =>
                        handleNestedConfigUpdate('capabilities', 'maxConcurrentTasks', value[0])
                      }
                      disabled={readOnly}
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="memoryWindow">
                    Memory Window: {config.capabilities?.memoryWindow || 10} messages
                  </Label>
                  <Slider
                    id="memoryWindow"
                    min={1}
                    max={50}
                    step={1}
                    value={[config.capabilities?.memoryWindow || 10]}
                    onValueChange={(value) =>
                      handleNestedConfigUpdate('capabilities', 'memoryWindow', value[0])
                    }
                    disabled={readOnly}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Communication */}
            <AccordionItem value="communication">
              <AccordionTrigger>
                <div className="flex items-center">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Agent Communication
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enableAgentToAgent"
                    checked={config.communication?.enableAgentToAgent || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('communication', 'enableAgentToAgent', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="enableAgentToAgent">Enable Agent-to-Agent Communication</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="allowBroadcast"
                    checked={config.communication?.allowBroadcast || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('communication', 'allowBroadcast', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="allowBroadcast">Allow Broadcasting Messages</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Message Priority</Label>
                  <Select
                    value={config.communication?.priority || "normal"}
                    onValueChange={(value) =>
                      handleNestedConfigUpdate('communication', 'priority', value)
                    }
                    disabled={readOnly}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Fallback and Error Handling */}
            <AccordionItem value="fallback">
              <AccordionTrigger>
                <div className="flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  Fallback & Error Handling
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="fallbackEnabled"
                    checked={config.fallback?.enabled || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('fallback', 'enabled', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="fallbackEnabled">Enable Fallback Agent</Label>
                </div>

                {config.fallback?.enabled && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="fallbackAgentId">Fallback Agent</Label>
                      <Select
                        value={config.fallback?.fallbackAgentId || ""}
                        onValueChange={(value) =>
                          handleNestedConfigUpdate('fallback', 'fallbackAgentId', value)
                        }
                        disabled={readOnly}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select fallback agent" />
                        </SelectTrigger>
                        <SelectContent>
                          {agents
                            .filter(agent => agent.id !== config.agentId)
                            .map((agent) => (
                              <SelectItem key={agent.id} value={agent.id}>
                                {agent.name}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxRetries">
                        Max Retries: {config.fallback?.maxRetries || 3}
                      </Label>
                      <Slider
                        id="maxRetries"
                        min={1}
                        max={10}
                        step={1}
                        value={[config.fallback?.maxRetries || 3]}
                        onValueChange={(value) =>
                          handleNestedConfigUpdate('fallback', 'maxRetries', value[0])
                        }
                        disabled={readOnly}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="retryDelay">Retry Delay (ms)</Label>
                      <Input
                        id="retryDelay"
                        type="number"
                        value={config.fallback?.retryDelay || 1000}
                        onChange={(e) =>
                          handleNestedConfigUpdate('fallback', 'retryDelay', parseInt(e.target.value))
                        }
                        min={100}
                        max={30000}
                        disabled={readOnly}
                      />
                    </div>
                  </>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* Monitoring */}
            <AccordionItem value="monitoring">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Info className="mr-2 h-4 w-4" />
                  Monitoring & Logging
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="trackMetrics"
                    checked={config.monitoring?.trackMetrics !== false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('monitoring', 'trackMetrics', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="trackMetrics">Track Performance Metrics</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="logConversations"
                    checked={config.monitoring?.logConversations !== false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('monitoring', 'logConversations', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="logConversations">Log Conversations</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="alertOnErrors"
                    checked={config.monitoring?.alertOnErrors || false}
                    onCheckedChange={(checked) =>
                      handleNestedConfigUpdate('monitoring', 'alertOnErrors', checked)
                    }
                    disabled={readOnly}
                  />
                  <Label htmlFor="alertOnErrors">Alert on Errors</Label>
                </div>
              </AccordionContent>
            </AccordionItem>

          </Accordion>
        </ScrollArea>

        <Separator className="my-4" />

        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Node ID: {nodeId}
          </div>
          {!readOnly && (
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                Reset to Defaults
              </Button>
              <Button size="sm">
                Save Configuration
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AgentNodeConfigPanel;