import { OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
interface AuthenticatedSocket extends Socket {
    userId?: string;
    organizationId?: string;
    user?: any;
}
interface APXEvent {
    type: string;
    channel?: string;
    data: any;
    timestamp: string;
    organizationId?: string;
    compressed?: boolean;
    retryCount?: number;
    priority?: 'low' | 'normal' | 'high';
    metadata?: Record<string, any>;
}
export declare class ApixGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
    private jwtService;
    private prisma;
    private eventEmitter;
    server: Server;
    private readonly logger;
    private connectedClients;
    private userSockets;
    private organizationSockets;
    private roomSubscriptions;
    private compressionThreshold;
    private eventHistory;
    private maxHistorySize;
    constructor(jwtService: JwtService, prisma: PrismaService, eventEmitter: EventEmitter2);
    afterInit(server: Server): void;
    handleConnection(client: AuthenticatedSocket): Promise<void>;
    handleDisconnect(client: AuthenticatedSocket): void;
    handleEvent(client: AuthenticatedSocket, event: APXEvent): Promise<void>;
    handleJoinRoom(client: AuthenticatedSocket, roomId: string): void;
    handleLeaveRoom(client: AuthenticatedSocket, roomId: string): void;
    handleSubscribe(client: AuthenticatedSocket, data: {
        channels: string[];
        filters?: Record<string, any>;
    }): void;
    handleUnsubscribe(client: AuthenticatedSocket, data: {
        channels: string[];
    }): void;
    handleRequestReplay(client: AuthenticatedSocket, data: {
        since?: string;
        limit?: number;
    }): void;
    handleHeartbeat(client: AuthenticatedSocket, data: {
        timestamp: number;
    }): void;
    emitToUser(userId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'low' | 'normal' | 'high';
    }): Promise<void>;
    emitToOrganization(organizationId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'low' | 'normal' | 'high';
    }): Promise<void>;
    emitToRoom(roomId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'low' | 'normal' | 'high';
    }): Promise<void>;
    emitWorkflowEvent(organizationId: string, workflowId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'high' | 'normal' | 'low';
    }): Promise<void>;
    emitAgentEvent(organizationId: string, agentId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'high' | 'normal' | 'low';
    }): Promise<void>;
    emitToolEvent(organizationId: string, toolId: string, eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'high' | 'normal' | 'low';
    }): Promise<void>;
    emitSystemEvent(eventType: string, data: any, options?: {
        compress?: boolean;
        priority?: 'high' | 'normal' | 'low';
    }): Promise<void>;
    private createEvent;
    private shouldCompress;
    private emitToSocket;
    private storeEventInHistory;
    handleWorkflowStarted(payload: any): Promise<void>;
    handleWorkflowCompleted(payload: any): Promise<void>;
    handleWorkflowFailed(payload: any): Promise<void>;
    handleAgentThinking(payload: any): Promise<void>;
    handleAgentResponse(payload: any): Promise<void>;
    handleToolStarted(payload: any): Promise<void>;
    handleToolCompleted(payload: any): Promise<void>;
    handleToolFailed(payload: any): Promise<void>;
    handleSystemAlert(payload: any): Promise<void>;
    handleSystemMaintenance(payload: any): Promise<void>;
    getConnectionStats(): {
        totalConnections: number;
        userConnections: number;
        organizationConnections: number;
        activeRooms: number;
        eventHistorySize: number;
    };
    getOrganizationConnections(organizationId: string): {
        socketId: string;
        userId: string;
        connectedAt: string;
    }[];
    broadcastToOrganization(organizationId: string, message: string, data?: any): Promise<void>;
    cleanup(): Promise<void>;
}
export {};
