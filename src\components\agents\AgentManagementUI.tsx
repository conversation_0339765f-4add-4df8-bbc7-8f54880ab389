"use client";

import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Bot,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Play,
  Pause,
  Copy,
  Settings,
  MessageSquare,
  Activity,
  Users,
  Zap,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { useAgents, useAgentTemplates } from "@/hooks/useAgents";
import { AgentInstance, AgentTemplate } from "@/lib/agent-api";

const AgentManagementUI = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [selectedAgent, setSelectedAgent] = useState<AgentInstance | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("agents");

  // Use our custom hooks
  const {
    agents,
    loading: agentsLoading,
    error: agentsError,
    total: agentsTotal,
    createAgent,
    updateAgent,
    deleteAgent,
    updateParams,
  } = useAgents();

  const {
    templates,
    loading: templatesLoading,
    error: templatesError,
  } = useAgentTemplates();

  // Form state for creating/editing agents
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "STANDALONE" as AgentInstance["type"],
    templateId: "",
    systemPrompt: "",
    instructions: "",
    provider: "openai",
    model: "gpt-4",
    temperature: 0.7,
    maxTokens: 2000,
    skills: [] as string[],
    tools: [] as string[],
    capabilities: {
      canCollaborate: false,
      shareContext: false,
      maxConcurrentTasks: 3,
      memoryWindow: 10,
    },
    communication: {
      enableAgentToAgent: false,
      allowBroadcast: false,
      priority: "normal" as const,
    },
  });

  // Apply filters
  useEffect(() => {
    const filters: any = {};
    
    if (statusFilter !== "all") {
      filters.status = statusFilter;
    }
    
    if (typeFilter !== "all") {
      filters.type = typeFilter;
    }
    
    if (searchTerm) {
      filters.search = searchTerm;
    }
    
    updateParams(filters);
  }, [searchTerm, statusFilter, typeFilter, updateParams]);

  const handleCreateAgent = async () => {
    try {
      await createAgent({
        name: formData.name,
        description: formData.description,
        type: formData.type,
        templateId: formData.templateId || undefined,
        systemPrompt: formData.systemPrompt,
        instructions: formData.instructions,
        provider: formData.provider,
        model: formData.model,
        temperature: formData.temperature,
        maxTokens: formData.maxTokens,
        skills: formData.skills,
        tools: formData.tools,
        capabilities: formData.capabilities,
        canCollaborate: formData.capabilities.canCollaborate,
        shareContext: formData.capabilities.shareContext,
      });
      
      setIsCreateDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };

  const handleUpdateAgent = async () => {
    if (!selectedAgent) return;

    try {
      await updateAgent(selectedAgent.id, {
        name: formData.name,
        description: formData.description,
        systemPrompt: formData.systemPrompt,
        instructions: formData.instructions,
        provider: formData.provider,
        model: formData.model,
        temperature: formData.temperature,
        maxTokens: formData.maxTokens,
        skills: formData.skills,
        tools: formData.tools,
        capabilities: formData.capabilities,
        canCollaborate: formData.capabilities.canCollaborate,
        shareContext: formData.capabilities.shareContext,
      });
      
      setIsEditDialogOpen(false);
      setSelectedAgent(null);
      resetForm();
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    try {
      await deleteAgent(agentId);
    } catch (error) {
      console.error('Failed to delete agent:', error);
    }
  };

  const handleToggleAgentStatus = async (agent: AgentInstance) => {
    try {
      await updateAgent(agent.id, {
        status: agent.status === "ACTIVE" ? "INACTIVE" : "ACTIVE",
      });
    } catch (error) {
      console.error('Failed to toggle agent status:', error);
    }
  };

  const handleDuplicateAgent = async (agent: AgentInstance) => {
    try {
      await createAgent({
        name: `${agent.name} (Copy)`,
        description: agent.description,
        type: agent.type,
        systemPrompt: agent.systemPrompt,
        instructions: agent.instructions,
        provider: agent.provider,
        model: agent.model,
        temperature: agent.temperature,
        maxTokens: agent.maxTokens,
        skills: [...agent.skills],
        tools: [...agent.tools],
        capabilities: { ...agent.capabilities },
      });
    } catch (error) {
      console.error('Failed to duplicate agent:', error);
    }
  };

  const handleUseTemplate = (template: AgentTemplate) => {
    setFormData({
      name: `${template.name} Instance`,
      description: template.description,
      type: template.type,
      templateId: template.id,
      systemPrompt: template.systemPrompt || "",
      instructions: template.instructions || "",
      provider: template.provider,
      model: template.model,
      temperature: template.temperature,
      maxTokens: template.maxTokens,
      skills: [...template.skills],
      tools: [...template.tools],
      capabilities: { ...template.capabilities, maxConcurrentTasks: template.capabilities.maxConcurrentTasks || 3 },
      communication: {
        enableAgentToAgent: false,
        allowBroadcast: false,
        priority: "normal",
      },
    });
    setIsCreateDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      type: "STANDALONE",
      templateId: "",
      systemPrompt: "",
      instructions: "",
      provider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      maxTokens: 2000,
      skills: [],
      tools: [],
      capabilities: {
        canCollaborate: false,
        shareContext: false,
        maxConcurrentTasks: 3,
        memoryWindow: 10,
      },
      communication: {
        enableAgentToAgent: false,
        allowBroadcast: false,
        priority: "normal",
      },
    });
  };

  const openEditDialog = (agent: AgentInstance) => {
    setSelectedAgent(agent);
    setFormData({
      name: agent.name,
      description: agent.description || "",
      type: agent.type,
      templateId: agent.templateId || "",
      systemPrompt: agent.systemPrompt || "",
      instructions: agent.instructions || "",
      provider: agent.provider,
      model: agent.model,
      temperature: agent.temperature,
      maxTokens: agent.maxTokens,
      skills: [...agent.skills],
      tools: [...agent.tools],
      capabilities: { ...agent.capabilities, maxConcurrentTasks: agent.capabilities.maxConcurrentTasks || 3 },
      communication: {
        enableAgentToAgent: false,
        allowBroadcast: false,
        priority: "normal",
      },
    });
    setIsEditDialogOpen(true);
  };

  const getStatusIcon = (status: AgentInstance["status"]) => {
    switch (status) {
      case "ACTIVE":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "INACTIVE":
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case "ARCHIVED":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: AgentInstance["type"]) => {
    switch (type) {
      case "STANDALONE":
        return "bg-blue-100 text-blue-800";
      case "TOOL_DRIVEN":
        return "bg-green-100 text-green-800";
      case "HYBRID":
        return "bg-purple-100 text-purple-800";
      case "MULTI_TASKING":
        return "bg-orange-100 text-orange-800";
      case "MULTI_PROVIDER":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (agentsLoading && !agents.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading agents...</span>
      </div>
    );
  }

  if (agentsError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Agents</h3>
          <p className="text-muted-foreground mb-4">{agentsError}</p>
          <Button onClick={() => updateParams({})} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Agent Management</h1>
          <p className="text-muted-foreground">
            Create, configure, and manage AI agents for your workflows
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Agent
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="agents">
            <Bot className="mr-2 h-4 w-4" />
            Agents ({agentsTotal})
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Settings className="mr-2 h-4 w-4" />
            Templates ({templates.length})
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <TrendingUp className="mr-2 h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Agents Tab */}
        <TabsContent value="agents" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search agents..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="INACTIVE">Inactive</SelectItem>
                    <SelectItem value="ARCHIVED">Archived</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="STANDALONE">Standalone</SelectItem>
                    <SelectItem value="TOOL_DRIVEN">Tool-Driven</SelectItem>
                    <SelectItem value="HYBRID">Hybrid</SelectItem>
                    <SelectItem value="MULTI_TASKING">Multi-Tasking</SelectItem>
                    <SelectItem value="MULTI_PROVIDER">Multi-Provider</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Agents Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Sessions</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {agents.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{agent.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {agent.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTypeColor(agent.type)}>
                          {agent.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getStatusIcon(agent.status)}
                          <span className="ml-2">{agent.status}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{agent.provider}</div>
                          <div className="text-sm text-muted-foreground">
                            {agent.model}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{agent._count?.sessions || 0}</TableCell>
                      <TableCell>
                        {new Date(agent.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openEditDialog(agent)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateAgent(agent)}>
                              <Copy className="mr-2 h-4 w-4" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleAgentStatus(agent)}>
                              {agent.status === "ACTIVE" ? (
                                <>
                                  <Pause className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Play className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteAgent(agent.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                  {agents.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center space-y-2">
                          <Bot className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">No agents found</p>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => setIsCreateDialogOpen(true)}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            Create Agent
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-4">
          {templatesLoading && !templates.length ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Loading templates...</span>
            </div>
          ) : templatesError ? (
            <div className="flex flex-col items-center justify-center h-64 space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Templates</h3>
                <p className="text-muted-foreground mb-4">{templatesError}</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <Badge className={getTypeColor(template.type)}>
                        {template.type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {template.description}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Category:</span>
                        <Badge variant="outline">{template.category}</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Provider:</span>
                        <span>{template.provider}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Used:</span>
                        <span>{template.usageCount} times</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Skills:</span>
                        <span>{template.skills.length}</span>
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <Button
                      className="w-full"
                      onClick={() => handleUseTemplate(template)}
                    >
                      Use Template
                    </Button>
                  </CardContent>
                </Card>
              ))}
              {templates.length === 0 && (
                <div className="col-span-full flex flex-col items-center justify-center h-64 space-y-4">
                  <Settings className="h-12 w-12 text-muted-foreground" />
                  <p className="text-muted-foreground">No templates found</p>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
                <Bot className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{agentsTotal}</div>
                <p className="text-xs text-muted-foreground">
                  {agents.filter(a => a.status === "ACTIVE").length} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {agents.reduce((sum, agent) => sum + (agent._count?.sessions || 0), 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all agents
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Templates</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {templates.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Available templates
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Collaborative Agents</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {agents.filter(a => a.capabilities?.canCollaborate).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Can communicate with others
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create Agent Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Agent</DialogTitle>
            <DialogDescription>
              Configure a new AI agent for your workflows
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter agent name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value as AgentInstance["type"] })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="STANDALONE">Standalone</SelectItem>
                    <SelectItem value="TOOL_DRIVEN">Tool-Driven</SelectItem>
                    <SelectItem value="HYBRID">Hybrid</SelectItem>
                    <SelectItem value="MULTI_TASKING">Multi-Tasking</SelectItem>
                    <SelectItem value="MULTI_PROVIDER">Multi-Provider</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe what this agent does"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="provider">Provider</Label>
                <Select
                  value={formData.provider}
                  onValueChange={(value) => setFormData({ ...formData, provider: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="claude">Anthropic Claude</SelectItem>
                    <SelectItem value="gemini">Google Gemini</SelectItem>
                    <SelectItem value="local">Local Models</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Select
                  value={formData.model}
                  onValueChange={(value) => setFormData({ ...formData, model: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={formData.systemPrompt}
                onChange={(e) => setFormData({ ...formData, systemPrompt: e.target.value })}
                placeholder="Enter system prompt for the agent"
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="temperature">
                Temperature: {formData.temperature}
              </Label>
              <Slider
                id="temperature"
                min={0}
                max={2}
                step={0.1}
                value={[formData.temperature]}
                onValueChange={(value) => setFormData({ ...formData, temperature: value[0] })}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTokens">Max Tokens</Label>
              <Input
                id="maxTokens"
                type="number"
                value={formData.maxTokens}
                onChange={(e) => setFormData({ ...formData, maxTokens: parseInt(e.target.value) })}
                min={1}
                max={32000}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="canCollaborate"
                  checked={formData.capabilities.canCollaborate}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      capabilities: { ...formData.capabilities, canCollaborate: checked }
                    })
                  }
                />
                <Label htmlFor="canCollaborate">Enable Agent Collaboration</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="enableAgentToAgent"
                  checked={formData.communication.enableAgentToAgent}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      communication: { ...formData.communication, enableAgentToAgent: checked }
                    })
                  }
                />
                <Label htmlFor="enableAgentToAgent">Enable Agent-to-Agent Communication</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAgent}>
              Create Agent
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Agent Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Agent</DialogTitle>
            <DialogDescription>
              Update agent configuration
            </DialogDescription>
          </DialogHeader>

          {/* Similar form content as create dialog */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter agent name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value as AgentInstance["type"] })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="STANDALONE">Standalone</SelectItem>
                    <SelectItem value="TOOL_DRIVEN">Tool-Driven</SelectItem>
                    <SelectItem value="HYBRID">Hybrid</SelectItem>
                    <SelectItem value="MULTI_TASKING">Multi-Tasking</SelectItem>
                    <SelectItem value="MULTI_PROVIDER">Multi-Provider</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe what this agent does"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-systemPrompt">System Prompt</Label>
              <Textarea
                id="edit-systemPrompt"
                value={formData.systemPrompt}
                onChange={(e) => setFormData({ ...formData, systemPrompt: e.target.value })}
                placeholder="Enter system prompt for the agent"
                rows={4}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-provider">Provider</Label>
                <Select
                  value={formData.provider}
                  onValueChange={(value) => setFormData({ ...formData, provider: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="claude">Anthropic Claude</SelectItem>
                    <SelectItem value="gemini">Google Gemini</SelectItem>
                    <SelectItem value="local">Local Models</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-model">Model</Label>
                <Select
                  value={formData.model}
                  onValueChange={(value) => setFormData({ ...formData, model: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateAgent}>
              Update Agent
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentManagementUI;