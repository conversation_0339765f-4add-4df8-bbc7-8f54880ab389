import { PrismaService } from "../../prisma/prisma.service";
import { Cache } from "cache-manager";
import { ClientType, ConnectionStatus } from "../dto/apix.dto";
interface ConnectionContext {
    id: string;
    sessionId: string;
    userId?: string;
    organizationId: string;
    clientType: ClientType;
    channels: Set<string>;
    lastActivity: number;
    latencyMetrics: Map<string, number[]>;
    metadata: Record<string, any>;
    reconnectCount: number;
    ipAddress?: string;
    userAgent?: string;
}
export declare class ConnectionManagerService {
    private prisma;
    private cacheManager;
    private readonly logger;
    private connections;
    private userConnections;
    private organizationConnections;
    constructor(prisma: PrismaService, cacheManager: Cache);
    createConnection(socketId: string, sessionId: string, userId: string | undefined, organizationId: string, clientType: ClientType, metadata?: Record<string, any>, ipAddress?: string, userAgent?: string): Promise<ConnectionContext>;
    updateConnection(socketId: string, updates: Partial<{
        status: ConnectionStatus;
        lastActivity: number;
        reconnectCount: number;
        latencyMs: number;
        metadata: Record<string, any>;
    }>): Promise<void>;
    removeConnection(socketId: string): Promise<void>;
    getConnection(socketId: string): ConnectionContext | undefined;
    getConnectionFromCache(socketId: string): Promise<ConnectionContext | null>;
    getUserConnections(userId: string): string[];
    getOrganizationConnections(organizationId: string): string[];
    getAllConnections(): Map<string, ConnectionContext>;
    getConnectionStats(): {
        total: number;
        byClientType: Record<ClientType, number>;
        byOrganization: Record<string, number>;
        activeUsers: number;
        averageLatency: number;
        connectionsPerUser: number;
        healthyConnections: number;
        reconnectingConnections: number;
    };
    addChannelToConnection(socketId: string, channel: string): Promise<void>;
    removeChannelFromConnection(socketId: string, channel: string): Promise<void>;
    trackLatency(socketId: string, eventType: string, latency: number): void;
    getLatencyStats(socketId: string, eventType?: string): {
        avg: number;
        min: number;
        max: number;
        count: number;
    } | null;
    updateHeartbeat(socketId: string, latency?: number): Promise<void>;
    private cacheConnection;
    private startCleanupTimer;
    private cleanupInactiveConnections;
}
export {};
