"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsModule = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const config_1 = require("@nestjs/config");
const tools_controller_1 = require("./tools.controller");
const tools_service_1 = require("./tools.service");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
const tool_manager_service_1 = require("./services/tool-manager.service");
const tool_execution_service_1 = require("./services/tool-execution.service");
const tool_cache_service_1 = require("./services/tool-cache.service");
const apix_module_1 = require("../apix/apix.module");
let ToolsModule = class ToolsModule {
};
exports.ToolsModule = ToolsModule;
exports.ToolsModule = ToolsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            cache_manager_1.CacheModule.register(),
            config_1.ConfigModule,
            apix_module_1.ApixModule,
        ],
        controllers: [tools_controller_1.ToolsController],
        providers: [
            tools_service_1.ToolsService,
            tool_manager_service_1.ToolManagerService,
            tool_execution_service_1.ToolExecutionService,
            tool_cache_service_1.ToolCacheService,
        ],
        exports: [
            tools_service_1.ToolsService,
            tool_manager_service_1.ToolManagerService,
            tool_execution_service_1.ToolExecutionService,
            tool_cache_service_1.ToolCacheService,
        ],
    })
], ToolsModule);
//# sourceMappingURL=tools.module.js.map