import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class IPWhitelistGuard implements CanActivate {
    private readonly whitelist: string[];

    constructor(private configService: ConfigService) {
        const whitelistStr = this.configService.get<string>('IP_WHITELIST', '');
        this.whitelist = whitelistStr ? whitelistStr.split(',').map(ip => ip.trim()) : [];
    }

    canActivate(context: ExecutionContext): boolean {
        if (this.whitelist.length === 0) {
            return true; // No whitelist configured, allow all
        }

        const request = context.switchToHttp().getRequest();
        const clientIP = this.getClientIP(request);

        if (!this.isIPWhitelisted(clientIP)) {
            throw new ForbiddenException(`Access denied for IP: ${clientIP}`);
        }

        return true;
    }

    private getClientIP(request: any): string {
        return request.headers['x-forwarded-for']?.split(',')[0] ||
            request.headers['x-real-ip'] ||
            request.connection.remoteAddress ||
            request.socket.remoteAddress ||
            request.ip;
    }

    private isIPWhitelisted(ip: string): boolean {
        return this.whitelist.some(whitelistedIP => {
            if (whitelistedIP.includes('/')) {
                // CIDR notation
                return this.isIPInCIDR(ip, whitelistedIP);
            }
            return ip === whitelistedIP;
        });
    }

    private isIPInCIDR(ip: string, cidr: string): boolean {
        // Simple CIDR check implementation
        // In production, use a proper IP library like 'ip' or 'ipaddr.js'
        const [range, bits] = cidr.split('/');
        const mask = -1 << (32 - parseInt(bits));
        const ipNum = this.ipToNumber(ip);
        const rangeNum = this.ipToNumber(range);
        return (ipNum & mask) === (rangeNum & mask);
    }

    private ipToNumber(ip: string): number {
        return ip.split('.').reduce((num, octet) => (num << 8) + parseInt(octet), 0);
    }
}