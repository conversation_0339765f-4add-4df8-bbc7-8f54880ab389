"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeRegistryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let NodeRegistryService = class NodeRegistryService {
    constructor(prisma) {
        this.prisma = prisma;
        this.nodeTypes = new Map();
        this.registerCoreNodes();
    }
    async initialize() {
        await this.loadCustomNodes();
    }
    registerNode(nodeDefinition) {
        this.nodeTypes.set(nodeDefinition.type, nodeDefinition);
    }
    getNodeDefinition(type) {
        return this.nodeTypes.get(type);
    }
    getAllNodeDefinitions() {
        return Array.from(this.nodeTypes.values());
    }
    getNodesByCategory(category) {
        return this.getAllNodeDefinitions().filter(node => node.category === category);
    }
    registerCoreNodes() {
        this.registerNode({
            type: 'agent',
            category: 'agents',
            name: 'Agent',
            description: 'Execute an AI agent with specific instructions',
            icon: 'bot',
            color: '#4f46e5',
            inputs: [
                {
                    name: 'input',
                    type: 'object',
                    description: 'Input data for the agent',
                    required: false,
                }
            ],
            outputs: [
                {
                    name: 'response',
                    type: 'string',
                    description: 'Agent response text',
                },
                {
                    name: 'data',
                    type: 'object',
                    description: 'Structured data from agent',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['agentId'],
                    properties: {
                        agentId: {
                            type: 'string',
                            title: 'Agent',
                        },
                        systemPrompt: {
                            type: 'string',
                            title: 'System Prompt',
                        },
                        maxTokens: {
                            type: 'number',
                            title: 'Max Tokens',
                            default: 1000,
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'tool',
            category: 'tools',
            name: 'Tool',
            description: 'Execute a tool with specific parameters',
            icon: 'tool',
            color: '#0ea5e9',
            inputs: [
                {
                    name: 'parameters',
                    type: 'object',
                    description: 'Parameters for the tool',
                    required: false,
                }
            ],
            outputs: [
                {
                    name: 'output',
                    type: 'string',
                    description: 'Tool output text',
                },
                {
                    name: 'data',
                    type: 'object',
                    description: 'Structured data from tool',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['toolId'],
                    properties: {
                        toolId: {
                            type: 'string',
                            title: 'Tool',
                        },
                        parameters: {
                            type: 'object',
                            title: 'Parameters',
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'condition',
            category: 'flow',
            name: 'Condition',
            description: 'Branch workflow based on a condition',
            icon: 'git-branch',
            color: '#f59e0b',
            inputs: [
                {
                    name: 'condition',
                    type: 'string',
                    description: 'Condition to evaluate',
                    required: true,
                }
            ],
            outputs: [
                {
                    name: 'result',
                    type: 'boolean',
                    description: 'Condition result',
                },
                {
                    name: 'evaluatedPath',
                    type: 'string',
                    description: 'Path taken (true/false)',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['condition'],
                    properties: {
                        condition: {
                            type: 'string',
                            title: 'Condition',
                        },
                        paths: {
                            type: 'object',
                            title: 'Paths',
                            properties: {
                                true: {
                                    type: 'string',
                                    title: 'True Path',
                                },
                                false: {
                                    type: 'string',
                                    title: 'False Path',
                                }
                            }
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'parallel',
            category: 'flow',
            name: 'Parallel',
            description: 'Execute multiple nodes in parallel',
            icon: 'split',
            color: '#10b981',
            inputs: [],
            outputs: [
                {
                    name: 'parallelResults',
                    type: 'array',
                    description: 'Results from all parallel executions',
                },
                {
                    name: 'aggregatedResult',
                    type: 'object',
                    description: 'Aggregated result',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['nodes'],
                    properties: {
                        nodes: {
                            type: 'array',
                            title: 'Nodes to Execute',
                            items: {
                                type: 'string'
                            }
                        },
                        aggregateResults: {
                            type: 'boolean',
                            title: 'Aggregate Results',
                            default: false
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'human_input',
            category: 'interaction',
            name: 'Human Input',
            description: 'Request input from a human',
            icon: 'user',
            color: '#ec4899',
            inputs: [],
            outputs: [
                {
                    name: 'userInput',
                    type: 'string',
                    description: 'Input provided by user',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['prompt'],
                    properties: {
                        prompt: {
                            type: 'string',
                            title: 'Prompt',
                        },
                        inputType: {
                            type: 'string',
                            title: 'Input Type',
                            enum: ['text', 'number', 'boolean', 'select', 'file'],
                            default: 'text'
                        },
                        timeout: {
                            type: 'number',
                            title: 'Timeout (ms)',
                            default: 300000
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'delay',
            category: 'flow',
            name: 'Delay',
            description: 'Pause workflow execution for a specified time',
            icon: 'clock',
            color: '#6366f1',
            inputs: [],
            outputs: [
                {
                    name: 'delayed',
                    type: 'number',
                    description: 'Delay duration in ms',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    properties: {
                        delay: {
                            type: 'number',
                            title: 'Delay (ms)',
                            default: 1000
                        }
                    }
                }
            }
        });
        this.registerNode({
            type: 'hybrid',
            category: 'advanced',
            name: 'Hybrid Agent-Tool',
            description: 'Combine agent and tools in various execution patterns',
            icon: 'layers',
            color: '#8b5cf6',
            inputs: [],
            outputs: [
                {
                    name: 'pattern',
                    type: 'string',
                    description: 'Execution pattern used',
                },
                {
                    name: 'agentResult',
                    type: 'object',
                    description: 'Result from agent execution',
                },
                {
                    name: 'toolResults',
                    type: 'array',
                    description: 'Results from tool executions',
                }
            ],
            config: {
                schema: {
                    type: 'object',
                    required: ['agentId', 'toolIds', 'executionPattern'],
                    properties: {
                        agentId: {
                            type: 'string',
                            title: 'Agent',
                        },
                        toolIds: {
                            type: 'array',
                            title: 'Tools',
                            items: {
                                type: 'string'
                            }
                        },
                        executionPattern: {
                            type: 'string',
                            title: 'Execution Pattern',
                            enum: [
                                'agent-first',
                                'tool-first',
                                'parallel',
                                'multi-tool-orchestration'
                            ],
                            default: 'agent-first'
                        },
                        agentConfig: {
                            type: 'object',
                            title: 'Agent Configuration',
                        },
                        toolConfigs: {
                            type: 'object',
                            title: 'Tool Configurations',
                        },
                        maxIterations: {
                            type: 'number',
                            title: 'Max Iterations',
                            default: 5
                        }
                    }
                }
            }
        });
    }
    async loadCustomNodes() {
        try {
        }
        catch (error) {
            console.error('Failed to load custom nodes:', error);
        }
    }
};
exports.NodeRegistryService = NodeRegistryService;
exports.NodeRegistryService = NodeRegistryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], NodeRegistryService);
//# sourceMappingURL=node-registry.service.js.map