import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class BranchEvaluatorService {
  private readonly logger = new Logger(BranchEvaluatorService.name);

  evaluateCondition(
    condition: string,
    variables: Record<string, any>,
    nodeResult?: any,
  ): boolean {
    try {
      // Create a safe evaluation context with variables
      return this.safeEval(condition, variables, nodeResult);
    } catch (error) {
      this.logger.error(`Condition evaluation failed: ${error.message}`, error.stack);
      throw new Error(`Condition evaluation failed: ${error.message}`);
    }
  }

  evaluateExpression(
    expression: string,
    variables: Record<string, any>,
    nodeResult?: any,
  ): any {
    try {
      // Create a safe evaluation context with variables
      return this.safeEval(expression, variables, nodeResult);
    } catch (error) {
      this.logger.error(`Expression evaluation failed: ${error.message}`, error.stack);
      throw new Error(`Expression evaluation failed: ${error.message}`);
    }
  }

  evaluateSwitch(
    value: any,
    cases: Record<string, any>,
    defaultCase: any,
  ): any {
    try {
      // Convert value to string for comparison
      const stringValue = String(value);
      
      // Check if case exists
      if (cases[stringValue] !== undefined) {
        return cases[stringValue];
      }
      
      // Return default case if provided
      return defaultCase;
    } catch (error) {
      this.logger.error(`Switch evaluation failed: ${error.message}`, error.stack);
      throw new Error(`Switch evaluation failed: ${error.message}`);
    }
  }

  evaluateLoop(
    items: any[],
    iterationLimit: number = 100,
  ): { valid: boolean; count: number } {
    try {
      // Validate items is an array
      if (!Array.isArray(items)) {
        throw new Error('Loop items must be an array');
      }
      
      // Check if loop is within iteration limit
      if (items.length > iterationLimit) {
        return { valid: false, count: items.length };
      }
      
      return { valid: true, count: items.length };
    } catch (error) {
      this.logger.error(`Loop evaluation failed: ${error.message}`, error.stack);
      throw new Error(`Loop evaluation failed: ${error.message}`);
    }
  }

  private safeEval(
    expression: string,
    variables: Record<string, any>,
    nodeResult?: any,
  ): any {
    // Replace $result with the actual result if provided
    let processedExpression = expression;
    if (nodeResult !== undefined) {
      processedExpression = expression.replace(/\$result/g, JSON.stringify(nodeResult));
    }
    
    // Replace variable references
    processedExpression = processedExpression.replace(/\${(\w+)}/g, (match, varName) => {
      return variables[varName] !== undefined ? 
        JSON.stringify(variables[varName]) : 'undefined';
    });
    
    // Create a function with variables as parameters
    const keys = Object.keys(variables);
    const values = Object.values(variables);
    
    // Add $result to context if provided
    if (nodeResult !== undefined) {
      keys.push('$result');
      values.push(nodeResult);
    }
    
    // Create a safe evaluation context
    const evalFunction = new Function(...keys, `return ${processedExpression};`);
    return evalFunction(...values);
  }

  // Helper methods for common operations
  isEqual(a: any, b: any): boolean {
    return a === b;
  }

  isNotEqual(a: any, b: any): boolean {
    return a !== b;
  }

  isGreaterThan(a: any, b: any): boolean {
    return a > b;
  }

  isLessThan(a: any, b: any): boolean {
    return a < b;
  }

  isGreaterThanOrEqual(a: any, b: any): boolean {
    return a >= b;
  }

  isLessThanOrEqual(a: any, b: any): boolean {
    return a <= b;
  }

  contains(a: any, b: any): boolean {
    if (typeof a === 'string') {
      return a.includes(b);
    }
    if (Array.isArray(a)) {
      return a.includes(b);
    }
    return false;
  }

  startsWith(a: string, b: string): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
      return a.startsWith(b);
    }
    return false;
  }

  endsWith(a: string, b: string): boolean {
    if (typeof a === 'string' && typeof b === 'string') {
      return a.endsWith(b);
    }
    return false;
  }

  and(a: boolean, b: boolean): boolean {
    return a && b;
  }

  or(a: boolean, b: boolean): boolean {
    return a || b;
  }

  not(a: boolean): boolean {
    return !a;
  }
}