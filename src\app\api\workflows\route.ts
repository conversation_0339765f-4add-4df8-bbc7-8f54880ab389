import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getAuthenticatedSession, unauthorizedResponse } from "@/lib/auth";
import { z } from "zod";
import { ZodError } from "zod";


const prisma = new PrismaClient();

// Validation schemas
const createWorkflowSchema = z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    definition: z.object({
        nodes: z.array(z.any()),
        edges: z.array(z.any()),
    }),
    tags: z.array(z.string()).optional(),
    isActive: z.boolean().optional(),
});

const updateWorkflowSchema = createWorkflowSchema.partial();

// GET /api/workflows - List workflows with stats
export async function GET(request: NextRequest) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get("page") || "1");
        const limit = parseInt(url.searchParams.get("limit") || "20");
        const search = url.searchParams.get("search");
        const status = url.searchParams.get("status");
        const tags = url.searchParams.get("tags")?.split(",");

        const skip = (page - 1) * limit;

        // Build where clause
        const where: any = {
            organizationId: session.user.organizationId,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: "insensitive" } },
                    { description: { contains: search, mode: "insensitive" } },
                ],
            }),
            ...(status && status !== "all" && {
                isActive: status === "active",
            }),
            ...(tags && tags.length > 0 && {
                tags: {
                    hasSome: tags,
                },
            }),
        };

        // Get workflows
        const [workflows, total] = await Promise.all([
            prisma.workflow.findMany({
                where,
                skip,
                take: limit,
                orderBy: { updatedAt: "desc" },
                include: {
                    creator: {
                        select: {
                            firstName: true,
                            lastName: true,
                            avatar: true,
                        },
                    },
                    executions: {
                        select: {
                            status: true,
                            duration: true,
                            completedAt: true,
                        },
                        orderBy: { startedAt: "desc" },
                        take: 10,
                    },
                    _count: {
                        select: {
                            executions: true,
                        },
                    },
                },
            }),
            prisma.workflow.count({ where }),
        ]);

        // Calculate stats for each workflow
        const workflowsWithStats = workflows.map((workflow) => {
            const completedExecutions = workflow.executions.filter(
                (exec) => exec.status === "COMPLETED"
            );
            const successRate = workflow.executions.length > 0
                ? Math.round((completedExecutions.length / workflow.executions.length) * 100)
                : 0;

            const avgDuration = completedExecutions.length > 0
                ? Math.round(
                    completedExecutions.reduce((sum, exec) => sum + (exec.duration || 0), 0) /
                    completedExecutions.length
                )
                : 0;

            const lastRun = workflow.executions[0]?.completedAt;

            return {
                id: workflow.id,
                name: workflow.name,
                description: workflow.description,
                status: workflow.isActive ? "active" : "inactive",
                version: workflow.version,
                tags: workflow.tags,
                lastRun,
                successRate,
                totalRuns: workflow._count.executions,
                avgDuration,
                creator: {
                    name: `${workflow.creator.firstName} ${workflow.creator.lastName}`,
                    avatar: workflow.creator.avatar,
                },
                createdAt: workflow.createdAt,
                updatedAt: workflow.updatedAt,
            };
        });

        // Get overall stats
        const [totalWorkflows, activeWorkflows, totalExecutions] = await Promise.all([
            prisma.workflow.count({
                where: { organizationId: session.user.organizationId },
            }),
            prisma.workflow.count({
                where: { organizationId: session.user.organizationId, isActive: true },
            }),
            prisma.workflowExecution.count({
                where: {
                    workflow: { organizationId: session.user.organizationId },
                    startedAt: {
                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
                    },
                },
            }),
        ]);

        const allExecutions = await prisma.workflowExecution.findMany({
            where: {
                workflow: { organizationId: session.user.organizationId },
                status: "COMPLETED",
                startedAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                },
            },
            select: { duration: true },
        });

        const overallSuccessRate = totalExecutions > 0
            ? Math.round((allExecutions.length / totalExecutions) * 100)
            : 0;

        const overallAvgDuration = allExecutions.length > 0
            ? Math.round(
                allExecutions.reduce((sum, exec) => sum + (exec.duration || 0), 0) /
                allExecutions.length
            )
            : 0;

        const stats = {
            total: totalWorkflows,
            active: activeWorkflows,
            draft: totalWorkflows - activeWorkflows,
            totalExecutions,
            successRate: overallSuccessRate,
            avgDuration: overallAvgDuration,
        };

        return NextResponse.json({
            workflows: workflowsWithStats,
            stats,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        console.error("Failed to fetch workflows:", error);
        return NextResponse.json(
            { error: "Failed to fetch workflows" },
            { status: 500 }
        );
    }
}

// POST /api/workflows - Create workflow
export async function POST(request: NextRequest) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const body = await request.json();
        const validatedData = createWorkflowSchema.parse(body);

        // Create workflow
        const workflow = await prisma.workflow.create({
            data: {
                name: validatedData.name,
                description: validatedData.description,
                definition: validatedData.definition,
                tags: validatedData.tags || [],
                isActive: validatedData.isActive ?? true,
                creatorId: session.user.id,
                organizationId: session.user.organizationId,
            },
            include: {
                creator: {
                    select: {
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        executions: true,
                    },
                },
            },
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.user.id,
                action: "WORKFLOW_CREATED",
                resource: "WORKFLOW",
                resourceId: workflow.id,
                details: {
                    name: workflow.name,
                    isActive: workflow.isActive,
                },
                organizationId: session.user.organizationId,
            },
        });

        const response = {
            id: workflow.id,
            name: workflow.name,
            description: workflow.description,
            status: workflow.isActive ? "active" : "inactive",
            version: workflow.version,
            tags: workflow.tags,
            successRate: 0,
            totalRuns: 0,
            avgDuration: 0,
            creator: {
                name: `${workflow.creator.firstName} ${workflow.creator.lastName}`,
                avatar: workflow.creator.avatar,
            },
            createdAt: workflow.createdAt,
            updatedAt: workflow.updatedAt,
        };

        return NextResponse.json(response, { status: 201 });
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { error: "Validation failed", details: error.issues.map((err: any) => err.message) },
                { status: 400 }
            );
        }

        console.error("Failed to create workflow:", error);
        return NextResponse.json(
            { error: "Failed to create workflow" },
            { status: 500 }
        );
    }
}