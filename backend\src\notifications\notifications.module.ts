import { Modu<PERSON> } from '@nestjs/common';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { EmailService } from './email.service';
import { PushService } from './push.service';
import { WebsocketService } from './websocket.service';
import { NotificationTemplateService } from './notification-template.service';

@Module({
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    EmailService,
    PushService,
    WebsocketService,
    NotificationTemplateService,
  ],
  exports: [
    NotificationsService,
    EmailService,
    PushService,
    WebsocketService,
  ],
})
export class NotificationsModule {}