import { z } from 'zod';
export declare const LoginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    organizationSlug: z.ZodOptional<z.ZodString>;
    mfaCode: z.ZodOptional<z.ZodString>;
    rememberMe: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
export declare const RegisterSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    organizationName: z.ZodString;
    organizationSlug: z.ZodString;
    acceptTerms: z.ZodBoolean;
}, z.core.$strip>;
export declare const RefreshTokenSchema: z.ZodObject<{
    refreshToken: z.ZodString;
}, z.core.$strip>;
export declare const ForgotPasswordSchema: z.ZodObject<{
    email: z.ZodString;
    organizationSlug: z.ZodOptional<z.ZodString>;
}, z.core.$strip>;
export declare const ResetPasswordSchema: z.ZodObject<{
    token: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
}, z.core.$strip>;
export declare const ChangePasswordSchema: z.ZodObject<{
    currentPassword: z.ZodString;
    newPassword: z.ZodString;
    confirmPassword: z.ZodString;
}, z.core.$strip>;
export declare const EnableMFASchema: z.ZodObject<{
    secret: z.ZodString;
    code: z.ZodString;
}, z.core.$strip>;
export declare const VerifyMFASchema: z.ZodObject<{
    code: z.ZodString;
}, z.core.$strip>;
export declare const UpdateProfileSchema: z.ZodObject<{
    firstName: z.ZodOptional<z.ZodString>;
    lastName: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    preferences: z.ZodOptional<z.ZodObject<{
        theme: z.ZodOptional<z.ZodEnum<{
            system: "system";
            light: "light";
            dark: "dark";
        }>>;
        language: z.ZodOptional<z.ZodString>;
        timezone: z.ZodOptional<z.ZodString>;
        notifications: z.ZodOptional<z.ZodBoolean>;
        viewMode: z.ZodOptional<z.ZodEnum<{
            grid: "grid";
            list: "list";
        }>>;
    }, z.core.$strip>>;
}, z.core.$strip>;
export declare const InviteUserSchema: z.ZodObject<{
    email: z.ZodString;
    role: z.ZodEnum<{
        SUPER_ADMIN: "SUPER_ADMIN";
        ORG_ADMIN: "ORG_ADMIN";
        DEVELOPER: "DEVELOPER";
        VIEWER: "VIEWER";
    }>;
    firstName: z.ZodString;
    lastName: z.ZodString;
    message: z.ZodOptional<z.ZodString>;
}, z.core.$strip>;
export declare const AcceptInviteSchema: z.ZodObject<{
    token: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
}, z.core.$strip>;
export declare const UpdateUserRoleSchema: z.ZodObject<{
    userId: z.ZodString;
    role: z.ZodEnum<{
        SUPER_ADMIN: "SUPER_ADMIN";
        ORG_ADMIN: "ORG_ADMIN";
        DEVELOPER: "DEVELOPER";
        VIEWER: "VIEWER";
    }>;
}, z.core.$strip>;
export declare const SSOConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<{
        SAML: "SAML";
        OAUTH: "OAUTH";
        ACTIVE_DIRECTORY: "ACTIVE_DIRECTORY";
    }>;
    config: z.ZodObject<{
        entityId: z.ZodOptional<z.ZodString>;
        ssoUrl: z.ZodOptional<z.ZodString>;
        certificate: z.ZodOptional<z.ZodString>;
        clientId: z.ZodOptional<z.ZodString>;
        clientSecret: z.ZodOptional<z.ZodString>;
        domain: z.ZodOptional<z.ZodString>;
        tenantId: z.ZodOptional<z.ZodString>;
    }, z.core.$strip>;
    isEnabled: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
export declare const PermissionCheckSchema: z.ZodObject<{
    userId: z.ZodString;
    resource: z.ZodString;
    action: z.ZodString;
    scope: z.ZodOptional<z.ZodRecord<z.ZodAny, z.core.SomeType>>;
}, z.core.$strip>;
export declare const CreateAPIKeySchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    permissions: z.ZodArray<z.ZodString>;
    expiresAt: z.ZodOptional<z.ZodDate>;
}, z.core.$strip>;
declare const LoginDto_base: any;
export declare class LoginDto extends LoginDto_base {
}
declare const RegisterDto_base: any;
export declare class RegisterDto extends RegisterDto_base {
}
declare const RefreshTokenDto_base: any;
export declare class RefreshTokenDto extends RefreshTokenDto_base {
}
declare const ForgotPasswordDto_base: any;
export declare class ForgotPasswordDto extends ForgotPasswordDto_base {
}
declare const ResetPasswordDto_base: any;
export declare class ResetPasswordDto extends ResetPasswordDto_base {
}
declare const ChangePasswordDto_base: any;
export declare class ChangePasswordDto extends ChangePasswordDto_base {
}
declare const EnableMFADto_base: any;
export declare class EnableMFADto extends EnableMFADto_base {
}
declare const VerifyMFADto_base: any;
export declare class VerifyMFADto extends VerifyMFADto_base {
}
declare const UpdateProfileDto_base: any;
export declare class UpdateProfileDto extends UpdateProfileDto_base {
}
declare const InviteUserDto_base: any;
export declare class InviteUserDto extends InviteUserDto_base {
}
declare const AcceptInviteDto_base: any;
export declare class AcceptInviteDto extends AcceptInviteDto_base {
}
declare const UpdateUserRoleDto_base: any;
export declare class UpdateUserRoleDto extends UpdateUserRoleDto_base {
}
declare const SSOConfigDto_base: any;
export declare class SSOConfigDto extends SSOConfigDto_base {
}
declare const PermissionCheckDto_base: any;
export declare class PermissionCheckDto extends PermissionCheckDto_base {
}
declare const CreateAPIKeyDto_base: any;
export declare class CreateAPIKeyDto extends CreateAPIKeyDto_base {
}
export interface AuthResponse {
    user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: string;
        avatar?: string;
        organization: {
            id: string;
            name: string;
            slug: string;
            settings: any;
            branding: any;
        };
        permissions: string[];
        preferences: any;
    };
    tokens: {
        accessToken: string;
        refreshToken: string;
        expiresAt: number;
    };
    session: {
        id: string;
        expiresAt: string;
    };
}
export interface MFASetupResponse {
    secret: string;
    qrCode: string;
    backupCodes: string[];
}
export interface APIKeyResponse {
    id: string;
    name: string;
    key: string;
    permissions: string[];
    createdAt: string;
    expiresAt?: string;
}
export {};
