import { 
  Controller, 
  Post, 
  Body, 
  Get, 
  Put, 
  Delete,
  UseGuards, 
  Request,
  HttpCode,
  HttpStatus,
  Param,
  Query,
  Headers,
  Ip
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard';
import { RolesGuard } from './roles.guard';
import { TenantGuard } from './tenant.guard';
import { Roles } from './roles.decorator';
import { 
  LoginDto, 
  RegisterDto, 
  RefreshTokenDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  ChangePasswordDto,
  EnableMFADto,
  VerifyMFADto,
  UpdateProfileDto,
  InviteUserDto,
  AcceptInviteDto,
  UpdateUserRoleDto,
  CreateAPIKeyDto,
  AuthResponse,
  MFASetupResponse,
  APIKeyResponse
} from './dto/auth.dto';
import { Role } from '@prisma/client';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register a new user and organization' })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 409, description: 'User or organization already exists' })
  async register(
    @Body() registerDto: RegisterDto,
    @Headers('user-agent') userAgent?: string,
    @Ip() ipAddress?: string,
  ): Promise<AuthResponse> {
    return this.authService.register(registerDto, userAgent, ipAddress);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'User logged in successfully' })
  @ApiResponse({ status: 401, description: 'Invalid credentials or MFA required' })
  async login(
    @Body() loginDto: LoginDto,
    @Headers('user-agent') userAgent?: string,
    @Ip() ipAddress?: string,
  ): Promise<AuthResponse> {
    return this.authService.login(loginDto, userAgent, ipAddress);
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<{ accessToken: string; expiresAt: number }> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 204, description: 'User logged out successfully' })
  async logout(@Request() req: any): Promise<void> {
    return this.authService.logout(req.user.id);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 204, description: 'Password reset email sent if user exists' })
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 204, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired reset token' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<void> {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Put('change-password')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({ status: 204, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Current password is incorrect' })
  async changePassword(
    @Request() req: any,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    return this.authService.changePassword(req.user.id, changePasswordDto);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully' })
  async getProfile(@Request() req: any) {
    return {
      id: req.user.id,
      email: req.user.email,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      role: req.user.role,
      avatar: req.user.avatar,
      organization: req.user.organization,
      preferences: req.user.preferences,
      lastLoginAt: req.user.lastLoginAt,
      createdAt: req.user.createdAt,
    };
  }

  @Put('profile')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 204, description: 'Profile updated successfully' })
  async updateProfile(
    @Request() req: any,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<void> {
    return this.authService.updateProfile(req.user.id, updateProfileDto);
  }

  @Get('mfa/setup')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Setup MFA for user' })
  @ApiResponse({ status: 200, description: 'MFA setup data generated' })
  async setupMFA(@Request() req: any): Promise<MFASetupResponse> {
    return this.authService.setupMFA(req.user.id);
  }

  @Post('mfa/enable')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enable MFA for user' })
  @ApiResponse({ status: 204, description: 'MFA enabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid MFA code or setup session' })
  async enableMFA(
    @Request() req: any,
    @Body() enableMFADto: EnableMFADto,
  ): Promise<void> {
    return this.authService.enableMFA(req.user.id, enableMFADto);
  }

  @Post('mfa/disable')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Disable MFA for user' })
  @ApiResponse({ status: 204, description: 'MFA disabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid MFA code or MFA not enabled' })
  async disableMFA(
    @Request() req: any,
    @Body() verifyMFADto: VerifyMFADto,
  ): Promise<void> {
    return this.authService.disableMFA(req.user.id, verifyMFADto);
  }

  @Get('permissions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiResponse({ status: 200, description: 'User permissions retrieved' })
  async getPermissions(@Request() req: any) {
    const permissions = await this.authService.checkPermission(req.user.id, '*', '*');
    return {
      userId: req.user.id,
      role: req.user.role,
      permissions: permissions ? ['*:*'] : [], // Simplified for now
    };
  }

  @Get('permissions/check')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Check specific permission' })
  @ApiResponse({ status: 200, description: 'Permission check result' })
  async checkPermission(
    @Request() req: any,
    @Query('resource') resource: string,
    @Query('action') action: string,
  ) {
    const hasPermission = await this.authService.checkPermission(
      req.user.id,
      resource,
      action,
    );
    
    return {
      userId: req.user.id,
      resource,
      action,
      hasPermission,
    };
  }

  @Post('api-keys')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.ORG_ADMIN, Role.DEVELOPER)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create API key' })
  @ApiResponse({ status: 201, description: 'API key created successfully' })
  async createAPIKey(
    @Request() req: any,
    @Body() createAPIKeyDto: CreateAPIKeyDto,
  ): Promise<APIKeyResponse> {
    return this.authService.createAPIKey(req.user.id, createAPIKeyDto);
  }

  @Get('sessions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user sessions' })
  @ApiResponse({ status: 200, description: 'User sessions retrieved' })
  async getSessions(@Request() req: any) {
    // This would be implemented in SessionsService
    return {
      userId: req.user.id,
      sessions: [], // Placeholder
    };
  }

  @Delete('sessions/:sessionId')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke specific session' })
  @ApiResponse({ status: 204, description: 'Session revoked successfully' })
  async revokeSession(
    @Request() req: any,
    @Param('sessionId') sessionId: string,
  ): Promise<void> {
    // This would be implemented in SessionsService
    // await this.sessionsService.revokeSession(req.user.id, sessionId);
  }

  @Delete('sessions')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Revoke all sessions except current' })
  @ApiResponse({ status: 204, description: 'All other sessions revoked successfully' })
  async revokeAllSessions(@Request() req: any): Promise<void> {
    // This would be implemented in SessionsService
    // await this.sessionsService.revokeAllSessions(req.user.id, req.sessionId);
  }

  // Organization management endpoints (for ORG_ADMIN)
  @Post('invite')
  @UseGuards(JwtAuthGuard, RolesGuard, TenantGuard)
  @Roles(Role.ORG_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Invite user to organization' })
  @ApiResponse({ status: 201, description: 'User invited successfully' })
  async inviteUser(
    @Request() req: any,
    @Body() inviteUserDto: InviteUserDto,
  ) {
    // This would be implemented in a separate service
    return {
      message: 'User invited successfully',
      inviteId: 'placeholder',
    };
  }

  @Post('accept-invite')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Accept organization invite' })
  @ApiResponse({ status: 200, description: 'Invite accepted successfully' })
  async acceptInvite(@Body() acceptInviteDto: AcceptInviteDto) {
    // This would be implemented in a separate service
    return {
      message: 'Invite accepted successfully',
    };
  }

  @Put('users/:userId/role')
  @UseGuards(JwtAuthGuard, RolesGuard, TenantGuard)
  @Roles(Role.ORG_ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user role' })
  @ApiResponse({ status: 204, description: 'User role updated successfully' })
  async updateUserRole(
    @Request() req: any,
    @Param('userId') userId: string,
    @Body() updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<void> {
    // This would be implemented in a separate service
    // await this.userManagementService.updateUserRole(req.user.organizationId, userId, updateUserRoleDto.role);
  }

  @Get('audit-logs')
  @UseGuards(JwtAuthGuard, RolesGuard, TenantGuard)
  @Roles(Role.ORG_ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get audit logs' })
  @ApiResponse({ status: 200, description: 'Audit logs retrieved successfully' })
  async getAuditLogs(
    @Request() req: any,
    @Query('page') page = '1',
    @Query('limit') limit = '50',
    @Query('action') action?: string,
    @Query('resource') resource?: string,
    @Query('userId') userId?: string,
  ) {
    // This would be implemented in a separate service
    return {
      logs: [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: 0,
        totalPages: 0,
      },
    };
  }
}