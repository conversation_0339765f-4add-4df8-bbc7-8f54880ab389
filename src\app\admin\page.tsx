"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
    BarChart3,
    Activity,
    Users,
    Shield,
    Server,
    AlertTriangle,
    Building2,
    Zap,
    Cpu,
    HardDrive,
    MemoryStick,
    Wifi,
} from "lucide-react";
import apiClient from "@/lib/api-client";

export default function AdminDashboard() {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [systemMetrics, setSystemMetrics] = useState({
        totalUsers: 0,
        totalOrganizations: 0,
        systemUptime: "0%",
        errorRate: 0,
        avgResponseTime: 0,
        totalRequests: 0,
    });
    const [systemHealth, setSystemHealth] = useState({
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkIO: "0 GB/s",
    });

    useEffect(() => {
        const loadDashboardData = async () => {
            setLoading(true);
            setError(null);
            try {
                // Load real system metrics from backend
                const [metricsResponse, healthResponse] = await Promise.all([
                    apiClient.get('/admin/system/metrics'),
                    apiClient.get('/admin/system/health'),
                ]);

                setSystemMetrics((metricsResponse as any).data || {
                    totalUsers: 0,
                    totalOrganizations: 0,
                    systemUptime: "0%",
                    errorRate: 0,
                    avgResponseTime: 0,
                    totalRequests: 0,
                });

                setSystemHealth((healthResponse as any).data || {
                    cpuUsage: 0,
                    memoryUsage: 0,
                    diskUsage: 0,
                    networkIO: "0 GB/s",
                });
            } catch (error: any) {
                const errorMessage = error?.response?.data?.message || 'Failed to load dashboard data';
                setError(errorMessage);
                console.error("Failed to load admin dashboard data:", error);
            } finally {
                setLoading(false);
            }
        };

        loadDashboardData();

        // Set up real-time updates
        const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, []);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
                    <span>Loading admin dashboard...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Failed to Load Dashboard</h3>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={() => window.location.reload()}>
                        Try Again
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h1 className="text-3xl font-bold">System Overview</h1>
                    <p className="text-muted-foreground">
                        Monitor and manage the entire SynapseAI platform
                    </p>
                </div>
                <div className="flex items-center space-x-3">
                    <Button variant="outline">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Generate Report
                    </Button>
                    <Button>
                        <Shield className="mr-2 h-4 w-4" />
                        Security Center
                    </Button>
                </div>
            </div>

            {/* System Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-primary/10 rounded-lg text-blue-600">
                                    <Users className="h-5 w-5" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                                    <p className="text-2xl font-bold">{systemMetrics.totalUsers.toLocaleString()}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-1 text-sm text-green-600">
                                <span>+12%</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-primary/10 rounded-lg text-green-600">
                                    <Building2 className="h-5 w-5" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Organizations</p>
                                    <p className="text-2xl font-bold">{systemMetrics.totalOrganizations}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-1 text-sm text-green-600">
                                <span>+3%</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-primary/10 rounded-lg text-emerald-600">
                                    <Server className="h-5 w-5" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">System Uptime</p>
                                    <p className="text-2xl font-bold">{systemMetrics.systemUptime}</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-1 text-sm text-green-600">
                                <span>+0.01%</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="p-2 bg-primary/10 rounded-lg text-red-600">
                                    <AlertTriangle className="h-5 w-5" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Error Rate</p>
                                    <p className="text-2xl font-bold">{systemMetrics.errorRate.toFixed(2)}%</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-1 text-sm text-green-600">
                                <span>-0.02%</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* System Health Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
                        <Cpu className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{systemHealth.cpuUsage}%</div>
                        <Progress value={systemHealth.cpuUsage} className="mt-2" />
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                        <MemoryStick className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{systemHealth.memoryUsage}%</div>
                        <Progress value={systemHealth.memoryUsage} className="mt-2" />
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
                        <HardDrive className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{systemHealth.diskUsage}%</div>
                        <Progress value={systemHealth.diskUsage} className="mt-2" />
                    </CardContent>
                </Card>

                <Card className="bg-card/80 backdrop-blur-sm">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Network I/O</CardTitle>
                        <Wifi className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{systemHealth.networkIO}</div>
                        <p className="text-xs text-muted-foreground mt-1">
                            Network throughput
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <Card className="bg-card/80 backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Button className="h-20 flex flex-col items-center justify-center">
                            <Users className="h-6 w-6 mb-2" />
                            <span>Manage Users</span>
                        </Button>
                        <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                            <Building2 className="h-6 w-6 mb-2" />
                            <span>Organizations</span>
                        </Button>
                        <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                            <Activity className="h-6 w-6 mb-2" />
                            <span>System Health</span>
                        </Button>
                        <Button variant="outline" className="h-20 flex flex-col items-center justify-center">
                            <Shield className="h-6 w-6 mb-2" />
                            <span>Security</span>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}