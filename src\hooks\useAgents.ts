import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import agent<PERSON><PERSON>, { AgentInstance, AgentTemplate, AgentSession, AgentTask, AgentMessage } from '@/lib/agent-api';

// Hook for managing agent instances
export function useAgents(initialParams?: Parameters<typeof agentApi.getAgents>[0]) {
  const { toast } = useToast();
  const [agents, setAgents] = useState<AgentInstance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [params, setParams] = useState(initialParams || {});

  const fetchAgents = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);
      const result = await agent<PERSON>pi.getAgents(queryParams) as { data: { agents: AgentInstance[], total: number, hasMore: boolean } };
      setAgents(result.data.agents as AgentInstance[]);
      setTotal(result.data.total);
      setHasMore(result.data.hasMore);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agents');
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch agents',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [params, toast]);

  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  const updateParams = useCallback((newParams: typeof params) => {
    setParams(prev => ({ ...prev, ...newParams }));
  }, []);

  const createAgent = useCallback(async (data: Parameters<typeof agentApi.createAgent>[0]) => {
    try {
      const result = await agentApi.createAgent(data);
      setAgents(prev => [result as AgentInstance, ...prev]);
      setTotal(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Agent created successfully',
      });
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to create agent',
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  const updateAgent = useCallback(async (id: string, data: Parameters<typeof agentApi.updateAgent>[1]) => {
    try {
      const result = await agentApi.updateAgent(id, data);
      setAgents(prev => prev.map(agent => agent.id === id ? result as AgentInstance : agent));
      toast({
        title: 'Success',
        description: 'Agent updated successfully',
      });
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to update agent',
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  const deleteAgent = useCallback(async (id: string) => {
    try {
      await agentApi.deleteAgent(id);
      setAgents(prev => prev.filter(agent => agent.id !== id));
      setTotal(prev => prev - 1);
      toast({
        title: 'Success',
        description: 'Agent deleted successfully',
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete agent',
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  return {
    agents,
    loading,
    error,
    total,
    hasMore,
    params,
    fetchAgents,
    updateParams,
    createAgent,
    updateAgent,
    deleteAgent,
  };
}

// Hook for managing a single agent
export function useAgent(id: string) {
  const { toast } = useToast();
  const [agent, setAgent] = useState<AgentInstance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAgent = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await agentApi.getAgent(id);
      setAgent(result as AgentInstance);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agent');
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch agent',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    if (id) {
      fetchAgent();
    }
  }, [id, fetchAgent]);

  const executeAgent = useCallback(async (data: Parameters<typeof agentApi.executeAgent>[1]) => {
    try {
      setLoading(true);
      const result = await agentApi.executeAgent(id, data);
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to execute agent',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  const updateAgent = useCallback(async (data: Parameters<typeof agentApi.updateAgent>[1]) => {
    try {
      const result = await agentApi.updateAgent(id, data);
      setAgent(result as AgentInstance);
      toast({
        title: 'Success',
        description: 'Agent updated successfully',
      });
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to update agent',
        variant: 'destructive',
      });
      throw err;
    }
  }, [id, toast]);

  return {
    agent,
    loading,
    error,
    fetchAgent,
    executeAgent,
    updateAgent,
  };
}

// Hook for managing agent templates
export function useAgentTemplates(initialParams?: Parameters<typeof agentApi.getTemplates>[0]) {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<AgentTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [params, setParams] = useState(initialParams || {});

  const fetchTemplates = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);
      const result = await agentApi.getTemplates(queryParams) as { data: { templates: AgentTemplate[], total: number } };
      setTemplates(result.data.templates as AgentTemplate[]);
      setTotal(result.data.total);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch templates');
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch templates',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [params, toast]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const updateParams = useCallback((newParams: typeof params) => {
    setParams(prev => ({ ...prev, ...newParams }));
  }, []);

  const createTemplate = useCallback(async (data: Parameters<typeof agentApi.createTemplate>[0]) => {
    try {
      const result = await agentApi.createTemplate(data);
      setTemplates(prev => [result as AgentTemplate, ...prev]);
      setTotal(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Template created successfully',
      });
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to create template',
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  return {
    templates,
    loading,
    error,
    total,
    params,
    fetchTemplates,
    updateParams,
    createTemplate,
  };
}

// Hook for managing agent sessions
export function useAgentSessions(agentId: string, initialParams?: Parameters<typeof agentApi.getAgentSessions>[1]) {
  const { toast } = useToast();
  const [sessions, setSessions] = useState<AgentSession[]>([]);
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [params, setParams] = useState(initialParams || {});

  const fetchSessions = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);
      const result = await agentApi.getAgentSessions(agentId, queryParams) as { data: { sessions: AgentSession[], total: number, hasMore: boolean } };
      setSessions(result.data.sessions as AgentSession[]);
      setTotal(result.data.total);
      setHasMore(result.data.hasMore);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch sessions');
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch sessions',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [agentId, params, toast]);

  useEffect(() => {
    if (agentId) {
      fetchSessions();
    }
  }, [agentId, fetchSessions]);

  const updateParams = useCallback((newParams: typeof params) => {
    setParams(prev => ({ ...prev, ...newParams }));
  }, []);

  return {
    sessions,
    loading,
    error,
    total,
    hasMore,
    params,
    fetchSessions,
    updateParams,
  };
}

// Hook for managing agent tasks
export function useAgentTasks(agentId: string, initialParams?: Parameters<typeof agentApi.getAgentTasks>[1]) {
  const { toast } = useToast();
  const [tasks, setTasks] = useState<AgentTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [params, setParams] = useState(initialParams || {});

  const fetchTasks = useCallback(async (queryParams = params) => {
    try {
      setLoading(true);
      setError(null);
      const result = await agentApi.getAgentTasks(agentId, queryParams) as { data: { tasks: AgentTask[], total: number, hasMore: boolean } };
      setTasks(result.data.tasks as AgentTask[]);
      setTotal(result.data.total);
      setHasMore(result.data.hasMore);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch tasks');
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch tasks',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [agentId, params, toast]);

  useEffect(() => {
    if (agentId) {
      fetchTasks();
    }
  }, [agentId, fetchTasks]);

  const updateParams = useCallback((newParams: typeof params) => {
    setParams(prev => ({ ...prev, ...newParams }));
  }, []);

  const createTask = useCallback(async (data: Parameters<typeof agentApi.createTask>[1]) => {
    try {
      const result = await agentApi.createTask(agentId, data);
      setTasks(prev => [result as AgentTask, ...prev]);
      setTotal(prev => prev + 1);
      toast({
        title: 'Success',
        description: 'Task created successfully',
      });
      return result;
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to create task',
        variant: 'destructive',
      });
      throw err;
    }
  }, [agentId, toast]);

  return {
    tasks,
    loading,
    error,
    total,
    hasMore,
    params,
    fetchTasks,
    updateParams,
    createTask,
  };
}

// Hook for agent real-time events
export function useAgentEvents(agentId: string) {
  const [events, setEvents] = useState<any[]>([]);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if (!agentId) return;

    const unsubscribe = agentApi.subscribeToAgentEvents(agentId, (event) => {
      setEvents(prev => [event, ...prev].slice(0, 100)); // Keep last 100 events
      setConnected(true);
    });

    return () => {
      unsubscribe();
    };
  }, [agentId]);

  return { events, connected };
}

export default {
  useAgents,
  useAgent,
  useAgentTemplates,
  useAgentSessions,
  useAgentTasks,
  useAgentEvents,
};