"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Zap,
  Home,
  Workflow,
  Bot,
  Wrench,
  BarChart3,
  Users,
  Settings,
  Bell,
  Search,
  Menu,
  ChevronLeft,
  ChevronRight,
  LogOut,
  User,
  Building,
  Moon,
  Sun,
  Monitor,
  Command,
  Shield,
} from "lucide-react";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { ThemeSwitcher } from "@/components/theme-switcher";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
}

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const navigationItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
    badge: null,
  },
  {
    title: "Workflows",
    href: "/dashboard/workflows",
    icon: Workflow,
    badge: "12",
  },
  {
    title: "Agents",
    href: "/dashboard/agents",
    icon: Bot,
    badge: "8",
  },
  {
    title: "Tools",
    href: "/dashboard/tools",
    icon: Wrench,
    badge: null,
  },
  {
    title: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
    badge: null,
  },
  {
    title: "Team",
    href: "/dashboard/team",
    icon: Users,
    badge: null,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
    badge: null,
  },
];

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [notifications, setNotifications] = useState(3);
  const [searchOpen, setSearchOpen] = useState(false);

  const router = useRouter();
  const pathname = usePathname();

  // Load user data on mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const response = await apiClient.getProfile();
        setUser(response.user);

        // Connect to APIX if not already connected
        if (!apixClient.getConnectionStatus().connected) {
          try {
            const token = localStorage.getItem("authToken");
            if (token) {
              await apixClient.connect(token, response.user.organization.id);
              apixClient.subscribeToOrganization();
              apixClient.subscribeToSystem();
            }
          } catch (wsError) {
            console.warn("WebSocket connection failed:", wsError);
          }
        }
      } catch (error) {
        console.error("Failed to load user data:", error);
        router.push("/auth/login");
      }
    };

    loadUserData();
  }, [router]);

  // Set up real-time notifications
  useEffect(() => {
    const unsubscribeSystem = apixClient.on('system_alert', (event) => {
      setNotifications(prev => prev + 1);
    });

    const unsubscribeWorkflow = apixClient.on('workflow_completed', (event) => {
      setNotifications(prev => prev + 1);
    });

    const unsubscribeAgent = apixClient.on('agent_status', (event) => {
      if (event.data.status === 'error') {
        setNotifications(prev => prev + 1);
      }
    });

    return () => {
      unsubscribeSystem();
      unsubscribeWorkflow();
      unsubscribeAgent();
    };
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case "k":
            e.preventDefault();
            setSearchOpen(true);
            break;
          case "b":
            e.preventDefault();
            setSidebarCollapsed(!sidebarCollapsed);
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [sidebarCollapsed]);

  const handleLogout = async () => {
    try {
      await apiClient.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      apiClient.clearToken();
      apixClient.disconnect();
      router.push("/auth/login");
    }
  };

  const SidebarContent = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="flex items-center px-4 py-6">
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Zap className="h-6 w-6 text-primary" />
          </div>
          {(!sidebarCollapsed || mobile) && (
            <div>
              <h1 className="text-xl font-bold">SynapseAI</h1>
              <p className="text-xs text-muted-foreground">
                {user?.organization.name}
              </p>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <TooltipProvider key={item.href}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href={item.href}>
                      <Button
                        variant={isActive ? "secondary" : "ghost"}
                        className={`w-full justify-start ${sidebarCollapsed && !mobile ? "px-2" : ""
                          }`}
                        onClick={() => mobile && setMobileMenuOpen(false)}
                      >
                        <item.icon className="h-5 w-5" />
                        {(!sidebarCollapsed || mobile) && (
                          <>
                            <span className="ml-3">{item.title}</span>
                            {item.badge && (
                              <Badge
                                variant="secondary"
                                className="ml-auto text-xs"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        )}
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  {sidebarCollapsed && !mobile && (
                    <TooltipContent side="right">
                      <p>{item.title}</p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </nav>
      </ScrollArea>

      <Separator />

      {/* User Profile */}
      <div className="p-4">
        {user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={`w-full ${sidebarCollapsed && !mobile ? "px-2" : "justify-start"
                  }`}
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                    alt={`${user.firstName} ${user.lastName}`}
                  />
                  <AvatarFallback>
                    {user.firstName[0]}
                    {user.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                {(!sidebarCollapsed || mobile) && (
                  <div className="ml-3 text-left">
                    <p className="text-sm font-medium">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-xs text-muted-foreground capitalize">
                      {user.role.toLowerCase().replace("_", " ")}
                    </p>
                  </div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Building className="mr-2 h-4 w-4" />
                Organization
              </DropdownMenuItem>
              {user?.role.includes('ADMIN') && (
                <DropdownMenuItem onClick={() => router.push('/admin')}>
                  <Shield className="mr-2 h-4 w-4" />
                  Admin Panel
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <aside
        className={`hidden md:flex flex-col border-r bg-card/50 backdrop-blur-sm transition-all duration-300 ${sidebarCollapsed ? "w-16" : "w-64"
          }`}
      >
        <SidebarContent />

        {/* Collapse Toggle */}
        <div className="absolute -right-3 top-6 z-10">
          <Button
            variant="outline"
            size="icon"
            className="h-6 w-6 rounded-full bg-background border shadow-md"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-3 w-3" />
            ) : (
              <ChevronLeft className="h-3 w-3" />
            )}
          </Button>
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
        <SheetContent side="left" className="p-0 w-64">
          <SidebarContent mobile />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <header className="flex items-center justify-between px-6 py-4 border-b bg-card/50 backdrop-blur-sm">
          <div className="flex items-center space-x-4">
            {/* Mobile Menu Button */}
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setMobileMenuOpen(true)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
            </Sheet>

            {/* Search */}
            <div className="relative">
              <Button
                variant="outline"
                className="w-64 justify-start text-muted-foreground"
                onClick={() => setSearchOpen(true)}
              >
                <Search className="mr-2 h-4 w-4" />
                Search...
                <kbd className="ml-auto pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                  <span className="text-xs">⌘</span>K
                </kbd>
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Switcher */}
            <ThemeSwitcher />

            {/* Notifications */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="h-5 w-5" />
                  {notifications > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
                    >
                      {notifications}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="space-y-2 p-2">
                  <div className="p-3 rounded-lg bg-muted/50">
                    <p className="text-sm font-medium">Workflow Completed</p>
                    <p className="text-xs text-muted-foreground">
                      Customer Support Bot finished successfully
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      2 minutes ago
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-muted/50">
                    <p className="text-sm font-medium">New Agent Deployed</p>
                    <p className="text-xs text-muted-foreground">
                      Data Analyst agent is now online
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      5 minutes ago
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-muted/50">
                    <p className="text-sm font-medium">System Alert</p>
                    <p className="text-xs text-muted-foreground">
                      High API usage detected
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      1 hour ago
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-center">
                  View all notifications
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                      alt={`${user.firstName} ${user.lastName}`}
                    />
                    <AvatarFallback>
                      {user.firstName[0]}
                      {user.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Building className="mr-2 h-4 w-4" />
                  Organization
                </DropdownMenuItem>
                {user?.role.includes('ADMIN') && (
                  <DropdownMenuItem onClick={() => router.push('/admin')}>
                    <Shield className="mr-2 h-4 w-4" />
                    Admin Panel
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto bg-background">
          {children}
        </main>
      </div>

      {/* Command Palette */}
      {searchOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
            <div className="flex items-center space-x-2">
              <Command className="h-4 w-4" />
              <span className="text-sm font-medium">Quick Actions</span>
            </div>
            <div className="grid gap-2">
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/dashboard/workflows/new");
                }}
              >
                <Workflow className="mr-2 h-4 w-4" />
                Create Workflow
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/dashboard/agents/new");
                }}
              >
                <Bot className="mr-2 h-4 w-4" />
                Deploy Agent
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/dashboard/tools/new");
                }}
              >
                <Wrench className="mr-2 h-4 w-4" />
                Add Tool
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={() => setSearchOpen(false)}
              className="mt-2"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}