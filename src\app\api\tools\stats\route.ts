import {  NextResponse } from 'next/server';
import apiClient  from '@/lib/api-client';
import { ValidatedRequest, AuthenticatedRequest, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { z } from 'zod';
import { Permissions, Role } from '@/lib/types/auth';

const getToolStatsSchema = z.object({
  toolId: z.string(),
});

export const getToolStats = withAuthAndValidation(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const toolStats = await apiClient.get(`/tools/stats/${req.validatedParams.toolId}`);
    return NextResponse.json(toolStats);
  },
  {
    requiredRoles: [Role.SUPER_ADMIN as Role  ],
    requiredPermissions: [Permissions.TOOL_READ],
  },
  {
    params: getToolStatsSchema,
  }
);


