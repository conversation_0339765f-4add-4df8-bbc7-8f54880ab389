import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';
import { CheckCircle, XCircle, AlertTriangle, Clock } from 'lucide-react';

const delayConfigSchema = z.object({
  delay: z.number().min(100).max(3600000), // 100ms to 1 hour
  unit: z.enum(['milliseconds', 'seconds', 'minutes']),
  dynamic: z.boolean(),
  variableName: z.string().optional(),
  jitter: z.object({
    enabled: z.boolean(),
    percentage: z.number().min(0).max(100),
  }),
});

type DelayConfigFormData = z.infer<typeof delayConfigSchema>;

interface DelayNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<DelayConfigFormData>;
  onConfigChange: (config: DelayConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function DelayNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
}: DelayNodeConfigPanelProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const {
    register,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<DelayConfigFormData>({
    resolver: zodResolver(delayConfigSchema),
    defaultValues: {
      delay: 1000,
      unit: 'milliseconds',
      dynamic: false,
      variableName: '',
      jitter: {
        enabled: false,
        percentage: 0,
      },
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map(error => error.message).filter(Boolean) as string[];
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, errors, onConfigChange, onValidationChange]);

  const getDelayInMs = () => {
    const delay = watch('delay') || 1000;
    const unit = watch('unit');

    switch (unit) {
      case 'seconds':
        return delay * 1000;
      case 'minutes':
        return delay * 60 * 1000;
      default:
        return delay;
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <CardTitle>Delay Node Configuration</CardTitle>
          </div>
            <div className="ml-auto flex items-center space-x-2">
            {isValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}


          <div className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Delay Duration</Label>
                <div className="space-y-2">
                  <Slider
                    value={[watch('delay') || 1000]}
                    onValueChange={([value]) => setValue('delay', value)}
                    min={100}
                    max={watch('unit') === 'minutes' ? 60 : watch('unit') === 'seconds' ? 3600 : 3600000}
                    step={watch('unit') === 'minutes' ? 1 : watch('unit') === 'seconds' ? 1 : 100}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>
                      {watch('unit') === 'minutes' ? '1m' : watch('unit') === 'seconds' ? '1s' : '100ms'}
                    </span>
                    <span className="font-medium">
                      {formatDuration(getDelayInMs())}
                    </span>
                    <span>
                      {watch('unit') === 'minutes' ? '60m' : watch('unit') === 'seconds' ? '1h' : '1h'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                {(['milliseconds', 'seconds', 'minutes'] as const).map((unit) => (
                  <button
                    key={unit}
                    type="button"
                    onClick={() => setValue('unit', unit)}
                    className={`px-3 py-2 text-sm rounded-md border transition-colors ${watch('unit') === unit
                        ? 'bg-primary text-primary-foreground border-primary'
                        : 'bg-background hover:bg-muted border-border'
                      }`}
                  >
                    {unit}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={watch('jitter.enabled')}
                  onChange={(e) => setValue('jitter.enabled', e.target.checked)}
                  className="rounded"
                />
                <Label>Add Random Jitter</Label>
              </div>
                
              {watch('jitter.enabled') && (
                <div className="space-y-2 pl-6 border-l-2 border-muted">
                  <Label>Jitter Percentage</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('jitter.percentage') || 10]}
                      onValueChange={([value]) => setValue('jitter.percentage', value)}
                      min={0}
                      max={100}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      ±{watch('jitter.percentage')}% random variation
                    </div>
                  </div>
                  <div className="p-3 bg-muted rounded-lg text-sm">
                    <div className="font-medium mb-1">Actual delay range:</div>
                    <div className="text-muted-foreground">
                      {formatDuration(getDelayInMs() * (1 - (watch('jitter.percentage') || 10) / 100))} - {' '}
                      {formatDuration(getDelayInMs() * (1 + (watch('jitter.percentage') || 10) / 100))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={watch('dynamic')}
                  onChange={(e) => setValue('dynamic', e.target.checked)}
                  className="rounded"
                />
                <Label>Dynamic Delay</Label>
              </div>

              {watch('dynamic') && (
                <div className="space-y-2 pl-6 border-l-2 border-muted">
                  <Label>Variable Name</Label>
                  <Input
                    value={watch('variableName') || ''}
                    onChange={(e) => setValue('variableName', e.target.value)}
                    placeholder="delay_duration"
                  />
                  <p className="text-xs text-muted-foreground">
                    Use a workflow variable to determine delay duration (in milliseconds)
                  </p>
                </div>
              )}
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Configuration Summary</h4>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium">Base Delay:</span>
                  <span className="ml-2">{formatDuration(getDelayInMs())}</span>
                </div>
                {watch('jitter.enabled') && (
                  <div>
                    <span className="font-medium">With Jitter:</span>
                    <span className="ml-2">
                      {formatDuration(getDelayInMs() * (1 - (watch('jitter.percentage') || 10) / 100))} - {' '}
                      {formatDuration(getDelayInMs() * (1 + (watch('jitter.percentage') || 10) / 100))}
                    </span>
                  </div>
                )}
                {watch('dynamic') && (
                  <div>
                    <span className="font-medium">Dynamic Variable:</span>
                    <span className="ml-2">${watch('variableName') || 'delay_duration'}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}