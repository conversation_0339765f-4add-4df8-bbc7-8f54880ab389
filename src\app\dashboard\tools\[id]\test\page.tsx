'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Save, 
  Download, 
  Upload, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Code,
  FileText,
  Settings,
  ArrowLeft,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Tool {
  id: string;
  name: string;
  description: string;
  type: string;
  parameters: ToolParameter[];
  config: any;
  version: string;
  status: string;
}

interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  validation?: any;
}

interface ExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  timestamp: Date;
  metadata?: any;
}

interface ExecutionHistory {
  id: string;
  input: any;
  result: ExecutionResult;
  createdAt: string;
}

export default function ToolTestPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const toolId = params.id as string;

  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState(false);
  const [inputValues, setInputValues] = useState<Record<string, any>>({});
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [executionHistory, setExecutionHistory] = useState<ExecutionHistory[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [timeout, setTimeout] = useState(30000);
  const [saveResults, setSaveResults] = useState(true);
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [presets, setPresets] = useState<any[]>([]);

  useEffect(() => {
    if (toolId) {
      loadTool();
      loadExecutionHistory();
      loadPresets();
    }
  }, [toolId]);

  const loadTool = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/tools/${toolId}`);
      const data = await response.json();
      
      if (data.success) {
        setTool(data.data);
        // Initialize input values with default values
        const initialValues: Record<string, any> = {};
        data.data.parameters?.forEach((param: ToolParameter) => {
          if (param.defaultValue !== undefined) {
            initialValues[param.name] = param.defaultValue;
          }
        });
        setInputValues(initialValues);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load tool',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load tool',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadExecutionHistory = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/executions?limit=10`);
      const data = await response.json();
      
      if (data.success) {
        setExecutionHistory(data.data);
      }
    } catch (error) {
      console.error('Failed to load execution history:', error);
    }
  };

  const loadPresets = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/presets`);
      const data = await response.json();
      
      if (data.success) {
        setPresets(data.data);
      }
    } catch (error) {
      console.error('Failed to load presets:', error);
    }
  };

  const handleExecute = async () => {
    if (!tool) return;

    try {
      setExecuting(true);
      setExecutionResult(null);

      const response = await fetch(`/api/tools/${toolId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: inputValues,
          timeout,
          metadata: {
            testMode: true,
            timestamp: new Date().toISOString(),
          },
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setExecutionResult(data.data);
        if (saveResults) {
          loadExecutionHistory();
        }
        toast({
          title: 'Success',
          description: 'Tool executed successfully',
        });
      } else {
        setExecutionResult({
          success: false,
          error: data.message || 'Execution failed',
          executionTime: 0,
          timestamp: new Date(),
        });
        toast({
          title: 'Execution Failed',
          description: data.message || 'Tool execution failed',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setExecutionResult({
        success: false,
        error: 'Network error or server unavailable',
        executionTime: 0,
        timestamp: new Date(),
      });
      toast({
        title: 'Error',
        description: 'Failed to execute tool',
        variant: 'destructive',
      });
    } finally {
      setExecuting(false);
    }
  };

  const handleInputChange = (paramName: string, value: any) => {
    setInputValues(prev => ({
      ...prev,
      [paramName]: value,
    }));
  };

  const handlePresetSelect = (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (preset) {
      setInputValues(preset.input);
      setSelectedPreset(presetId);
    }
  };

  const handleSavePreset = async () => {
    const name = prompt('Enter preset name:');
    if (!name) return;

    try {
      const response = await fetch(`/api/tools/${toolId}/presets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          input: inputValues,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        loadPresets();
        toast({
          title: 'Success',
          description: 'Preset saved successfully',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save preset',
        variant: 'destructive',
      });
    }
  };

  const handleClearInputs = () => {
    const clearedValues: Record<string, any> = {};
    tool?.parameters?.forEach(param => {
      if (param.defaultValue !== undefined) {
        clearedValues[param.name] = param.defaultValue;
      }
    });
    setInputValues(clearedValues);
    setSelectedPreset('');
  };

  const handleCopyResult = () => {
    if (executionResult) {
      navigator.clipboard.writeText(JSON.stringify(executionResult, null, 2));
      toast({
        title: 'Copied',
        description: 'Result copied to clipboard',
      });
    }
  };

  const renderParameterInput = (param: ToolParameter) => {
    const value = inputValues[param.name] || '';

    switch (param.type) {
      case 'string':
        if (param.validation?.multiline) {
          return (
            <Textarea
              value={value}
              onChange={(e) => handleInputChange(param.name, e.target.value)}
              placeholder={param.description}
              className="bg-white/50"
              rows={3}
            />
          );
        }
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => handleInputChange(param.name, e.target.value)}
            placeholder={param.description}
            className="bg-white/50"
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleInputChange(param.name, parseFloat(e.target.value) || 0)}
            placeholder={param.description}
            className="bg-white/50"
            min={param.validation?.min}
            max={param.validation?.max}
          />
        );
      
      case 'boolean':
        return (
          <Switch
            checked={value}
            onCheckedChange={(checked) => handleInputChange(param.name, checked)}
          />
        );
      
      case 'select':
        return (
          <Select value={value} onValueChange={(val) => handleInputChange(param.name, val)}>
            <SelectTrigger className="bg-white/50">
              <SelectValue placeholder={param.description} />
            </SelectTrigger>
            <SelectContent>
              {param.validation?.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'json':
        return (
          <Textarea
            value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleInputChange(param.name, parsed);
              } catch {
                handleInputChange(param.name, e.target.value);
              }
            }}
            placeholder={param.description}
            className="bg-white/50 font-mono text-sm"
            rows={4}
          />
        );
      
      default:
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => handleInputChange(param.name, e.target.value)}
            placeholder={param.description}
            className="bg-white/50"
          />
        );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tool...</p>
        </div>
      </div>
    );
  }

  if (!tool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="bg-white/80 backdrop-blur-sm border border-white/20 p-8 text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Tool Not Found</h2>
          <p className="text-gray-600 mb-4">The requested tool could not be found.</p>
          <Button onClick={() => router.push('/dashboard/tools')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tools
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/dashboard/tools')}
              className="bg-white/80 backdrop-blur-sm border border-white/20"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-1">Test Tool</h1>
              <p className="text-gray-600">{tool.name} - {tool.description}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-blue-500 text-white">
              {tool.type}
            </Badge>
            <Badge className="bg-green-500 text-white">
              v{tool.version}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <div className="space-y-6">
            <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Input Parameters
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {presets.length > 0 && (
                      <Select value={selectedPreset} onValueChange={handlePresetSelect}>
                        <SelectTrigger className="w-40 bg-white/50">
                          <SelectValue placeholder="Load preset" />
                        </SelectTrigger>
                        <SelectContent>
                          {presets.map(preset => (
                            <SelectItem key={preset.id} value={preset.id}>
                              {preset.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleSavePreset}
                      className="bg-white/50"
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleClearInputs}
                      className="bg-white/50"
                    >
                      <RotateCcw className="h-4 w-4 mr-1" />
                      Clear
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {tool.parameters && tool.parameters.length > 0 ? (
                  tool.parameters.map(param => (
                    <div key={param.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor={param.name} className="text-sm font-medium">
                          {param.name}
                          {param.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <Badge variant="outline" className="text-xs">
                          {param.type}
                        </Badge>
                      </div>
                      {param.description && (
                        <p className="text-xs text-gray-500">{param.description}</p>
                      )}
                      {renderParameterInput(param)}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>This tool doesn't require any input parameters</p>
                  </div>
                )}

                {/* Advanced Options */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <Label className="text-sm font-medium">Advanced Options</Label>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowAdvanced(!showAdvanced)}
                      className="h-6 px-2"
                    >
                      {showAdvanced ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                    </Button>
                  </div>
                  
                  {showAdvanced && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="timeout" className="text-sm">
                          Timeout (ms)
                        </Label>
                        <Input
                          id="timeout"
                          type="number"
                          value={timeout}
                          onChange={(e) => setTimeout(parseInt(e.target.value) || 30000)}
                          className="bg-white/50"
                          min={1000}
                          max={300000}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <Label htmlFor="saveResults" className="text-sm">
                          Save execution results
                        </Label>
                        <Switch
                          id="saveResults"
                          checked={saveResults}
                          onCheckedChange={setSaveResults}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Execute Button */}
                <div className="pt-4">
                  <Button
                    onClick={handleExecute}
                    disabled={executing}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    size="lg"
                  >
                    {executing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Executing...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Execute Tool
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results Panel */}
          <div className="space-y-6">
            <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Execution Results
                  </CardTitle>
                  {executionResult && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCopyResult}
                      className="bg-white/50"
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {executing && (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Executing tool...</p>
                    <Progress value={undefined} className="w-full mt-4" />
                  </div>
                )}

                {executionResult && (
                  <div className="space-y-4">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {executionResult.success ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        <span className={`font-medium ${
                          executionResult.success ? 'text-green-700' : 'text-red-700'
                        }`}>
                          {executionResult.success ? 'Success' : 'Failed'}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {executionResult.executionTime}ms
                        </div>
                        <div>
                          {new Date(executionResult.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>

                    {/* Error */}
                    {!executionResult.success && executionResult.error && (
                      <Alert className="border-red-200 bg-red-50">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-700">
                          {executionResult.error}
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Result Data */}
                    {executionResult.success && executionResult.data && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Output</Label>
                        <ScrollArea className="h-64 w-full rounded-md border bg-gray-50 p-4">
                          <pre className="text-sm font-mono whitespace-pre-wrap">
                            {typeof executionResult.data === 'string' 
                              ? executionResult.data 
                              : JSON.stringify(executionResult.data, null, 2)
                            }
                          </pre>
                        </ScrollArea>
                      </div>
                    )}

                    {/* Metadata */}
                    {executionResult.metadata && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Metadata</Label>
                        <ScrollArea className="h-32 w-full rounded-md border bg-gray-50 p-4">
                          <pre className="text-xs font-mono">
                            {JSON.stringify(executionResult.metadata, null, 2)}
                          </pre>
                        </ScrollArea>
                      </div>
                    )}
                  </div>
                )}

                {!executing && !executionResult && (
                  <div className="text-center py-8 text-gray-500">
                    <Play className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>Execute the tool to see results here</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Execution History */}
            {executionHistory.length > 0 && (
              <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Recent Executions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-3">
                      {executionHistory.map((execution, index) => (
                        <div key={execution.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 border">
                          <div className="flex items-center gap-3">
                            {execution.result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                            <div>
                              <div className="text-sm font-medium">
                                Execution #{executionHistory.length - index}
                              </div>
                              <div className="text-xs text-gray-500">
                                {new Date(execution.createdAt).toLocaleString()}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              {execution.result.executionTime}ms
                            </div>
                            <div className={`text-xs ${
                              execution.result.success ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {execution.result.success ? 'Success' : 'Failed'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}