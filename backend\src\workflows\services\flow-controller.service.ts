import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { ExecutionStatus } from '@prisma/client';
import { WorkflowDefinition, WorkflowNode } from '../workflows.service';
import { NodeExecutorService } from './node-executor.service';
import { VariableResolverService } from './variable-resolver.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface ExecutionContext {
  executionId: string;
  workflowId: string;
  organizationId: string;
  userId: string;
  variables: Record<string, any>;
  nodeStates: Record<string, any>;
  currentNode?: string;
  completedNodes: Set<string>;
  failedNodes: Set<string>;
  pausedNodes: Set<string>;
  startTime: number;
  options: {
    async?: boolean;
    priority?: 'low' | 'normal' | 'high';
    timeout?: number;
    retryPolicy?: {
      maxRetries: number;
      backoffStrategy: 'linear' | 'exponential';
      retryDelay: number;
    };
  };
}

@Injectable()
export class FlowControllerService {
  private readonly logger = new Logger(FlowControllerService.name);
  private activeExecutions = new Map<string, ExecutionContext>();

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private sessionsService: SessionsService,
    private nodeExecutor: NodeExecutorService,
    private variableResolver: VariableResolverService,
    private eventEmitter: EventEmitter2,
  ) {}

  async startExecution(
    workflowId: string,
    userId: string,
    organizationId: string,
    input: Record<string, any> = {},
    options: any = {}
  ): Promise<string> {
    this.logger.log(`Starting workflow execution: ${workflowId}`);

    // Get workflow definition
    const workflow = await this.prisma.workflow.findFirst({
      where: { id: workflowId, organizationId, isActive: true },
    });

    if (!workflow) {
      throw new Error('Workflow not found or inactive');
    }

    const definition = workflow.definition as WorkflowDefinition;

    // Create execution record
    const execution = await this.prisma.workflowExecution.create({
      data: {
        workflowId,
        userId,
        status: ExecutionStatus.PENDING,
        input: input as any,
        variables: input as any,
        startedAt: new Date(),
        options: options as any,
      },
    });

    // Create execution context
    const context: ExecutionContext = {
      executionId: execution.id,
      workflowId,
      organizationId,
      userId,
      variables: { ...input, ...definition.variables },
      nodeStates: {},
      completedNodes: new Set(),
      failedNodes: new Set(),
      pausedNodes: new Set(),
      startTime: Date.now(),
      options,
    };

    this.activeExecutions.set(execution.id, context);

    // Emit workflow started event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      workflowId,
      'workflow_started',
      {
        executionId: execution.id,
        startTime: context.startTime,
        input,
      }
    );

    // Start execution asynchronously
    if (options.async !== false) {
      this.executeWorkflowAsync(context, definition);
    } else {
      await this.executeWorkflow(context, definition);
    }

    return execution.id;
  }

  private async executeWorkflowAsync(context: ExecutionContext, definition: WorkflowDefinition) {
    try {
      await this.executeWorkflow(context, definition);
    } catch (error) {
      this.logger.error(`Async workflow execution failed: ${error.message}`, error.stack);
      await this.handleExecutionError(context, error);
    }
  }

  private async executeWorkflow(context: ExecutionContext, definition: WorkflowDefinition) {
    this.logger.log(`Executing workflow: ${context.workflowId}`);

    // Update execution status to running
    await this.updateExecutionStatus(context.executionId, ExecutionStatus.RUNNING);

    try {
      // Find start nodes (nodes with no incoming edges)
      const startNodes = this.findStartNodes(definition);
      
      if (startNodes.length === 0) {
        throw new Error('No start nodes found in workflow');
      }

      // Execute start nodes in parallel
      const startPromises = startNodes.map(node => 
        this.executeNode(context, definition, node)
      );

      await Promise.all(startPromises);

      // Continue execution flow
      await this.continueExecution(context, definition);

      // Mark execution as completed
      await this.completeExecution(context);

    } catch (error) {
      await this.handleExecutionError(context, error);
    }
  }

  private async executeNode(
    context: ExecutionContext,
    definition: WorkflowDefinition,
    node: WorkflowNode
  ): Promise<any> {
    this.logger.log(`Executing node: ${node.id} (${node.type})`);

    // Check if node is already completed or failed
    if (context.completedNodes.has(node.id) || context.failedNodes.has(node.id)) {
      return context.nodeStates[node.id];
    }

    // Check if node is paused
    if (context.pausedNodes.has(node.id)) {
      this.logger.log(`Node ${node.id} is paused, skipping execution`);
      return null;
    }

    try {
      // Emit node start event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'node_started',
        {
          executionId: context.executionId,
          nodeId: node.id,
          nodeType: node.type,
          nodeName: node.name,
        }
      );

      // Create execution step record
      const step = await this.prisma.workflowExecutionStep.create({
        data: {
          executionId: context.executionId,
          nodeId: node.id,
          nodeType: node.type,
          nodeName: node.name,
          status: ExecutionStatus.RUNNING,
          startedAt: new Date(),
          input: context.variables as any,
        },
      });

      // Resolve variables for this node
      const resolvedConfig = await this.variableResolver.resolveVariables(
        node.config,
        context.variables
      );

      // Execute the node
      const result = await this.nodeExecutor.executeNode(
        node.type,
        resolvedConfig,
        context.variables,
        {
          executionId: context.executionId,
          nodeId: node.id,
          organizationId: context.organizationId,
          userId: context.userId,
        }
      );

      // Update node state
      context.nodeStates[node.id] = result;
      context.completedNodes.add(node.id);

      // Update execution step
      await this.prisma.workflowExecutionStep.update({
        where: { id: step.id },
        data: {
          status: ExecutionStatus.COMPLETED,
          completedAt: new Date(),
          output: result as any,
          duration: Date.now() - step.startedAt.getTime(),
        },
      });

      // Emit node completed event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'node_completed',
        {
          executionId: context.executionId,
          nodeId: node.id,
          result,
          duration: Date.now() - step.startedAt.getTime(),
        }
      );

      // Update context variables with node output
      if (result && typeof result === 'object') {
        context.variables = { ...context.variables, ...result };
      }

      return result;

    } catch (error) {
      this.logger.error(`Node execution failed: ${node.id}`, error.stack);
      
      context.failedNodes.add(node.id);

      // Update execution step with error
      await this.prisma.workflowExecutionStep.updateMany({
        where: {
          executionId: context.executionId,
          nodeId: node.id,
          status: ExecutionStatus.RUNNING,
        },
        data: {
          status: ExecutionStatus.FAILED,
          completedAt: new Date(),
          error: error.message,
        },
      });

      // Emit node failed event
      await this.apixGateway.emitWorkflowEvent(
        context.organizationId,
        context.workflowId,
        'node_failed',
        {
          executionId: context.executionId,
          nodeId: node.id,
          error: error.message,
        }
      );

      // Handle error based on workflow settings
      const errorHandling = definition.settings?.errorHandling;
      if (errorHandling?.onError === 'continue') {
        this.logger.warn(`Continuing execution despite node failure: ${node.id}`);
        return null;
      } else if (errorHandling?.onError === 'retry') {
        // Implement retry logic
        return await this.retryNode(context, definition, node, error);
      } else {
        // Stop execution
        throw error;
      }
    }
  }

  private async retryNode(
    context: ExecutionContext,
    definition: WorkflowDefinition,
    node: WorkflowNode,
    originalError: Error,
    retryCount = 0
  ): Promise<any> {
    const retryPolicy = definition.settings?.retryPolicy || context.options.retryPolicy;
    const maxRetries = retryPolicy?.maxRetries || 3;

    if (retryCount >= maxRetries) {
      throw originalError;
    }

    this.logger.log(`Retrying node ${node.id}, attempt ${retryCount + 1}/${maxRetries}`);

    // Calculate delay
    const baseDelay = retryPolicy?.retryDelay || 1000;
    const delay = retryPolicy?.backoffStrategy === 'exponential' 
      ? baseDelay * Math.pow(2, retryCount)
      : baseDelay * (retryCount + 1);

    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, delay));

    // Remove from failed nodes to allow retry
    context.failedNodes.delete(node.id);

    try {
      return await this.executeNode(context, definition, node);
    } catch (error) {
      return await this.retryNode(context, definition, node, originalError, retryCount + 1);
    }
  }

  private async continueExecution(context: ExecutionContext, definition: WorkflowDefinition) {
    // Find next nodes to execute based on completed nodes
    const nextNodes = this.findNextNodes(definition, context.completedNodes);
    
    if (nextNodes.length === 0) {
      this.logger.log('No more nodes to execute, workflow completed');
      return;
    }

    // Execute next nodes
    const promises = nextNodes.map(node => 
      this.executeNode(context, definition, node)
    );

    await Promise.all(promises);

    // Continue recursively until no more nodes
    await this.continueExecution(context, definition);
  }

  private findStartNodes(definition: WorkflowDefinition): WorkflowNode[] {
    return definition.nodes.filter(node => {
      // A start node has no incoming edges
      return !definition.edges.some(edge => edge.target === node.id);
    });
  }

  private findNextNodes(definition: WorkflowDefinition, completedNodes: Set<string>): WorkflowNode[] {
    const nextNodes: WorkflowNode[] = [];

    for (const node of definition.nodes) {
      // Skip if already completed or failed
      if (completedNodes.has(node.id)) {
        continue;
      }

      // Check if all input nodes are completed
      const inputEdges = definition.edges.filter(edge => edge.target === node.id);
      const allInputsCompleted = inputEdges.every(edge => completedNodes.has(edge.source));

      if (allInputsCompleted && inputEdges.length > 0) {
        nextNodes.push(node);
      }
    }

    return nextNodes;
  }

  private async completeExecution(context: ExecutionContext) {
    this.logger.log(`Completing workflow execution: ${context.executionId}`);

    const duration = Date.now() - context.startTime;

    // Update execution record
    await this.prisma.workflowExecution.update({
      where: { id: context.executionId },
      data: {
        status: ExecutionStatus.COMPLETED,
        completedAt: new Date(),
        duration,
        output: context.nodeStates as any,
      },
    });

    // Emit workflow completed event
    await this.apixGateway.emitWorkflowEvent(
      context.organizationId,
      context.workflowId,
      'workflow_completed',
      {
        executionId: context.executionId,
        duration,
        completedNodes: Array.from(context.completedNodes),
        output: context.nodeStates,
      }
    );

    // Clean up active execution
    this.activeExecutions.delete(context.executionId);

    // Emit completion event
    this.eventEmitter.emit('workflow.completed', {
      executionId: context.executionId,
      workflowId: context.workflowId,
      organizationId: context.organizationId,
      duration,
    });
  }

  private async handleExecutionError(context: ExecutionContext, error: Error) {
    this.logger.error(`Workflow execution failed: ${context.executionId}`, error.stack);

    const duration = Date.now() - context.startTime;

    // Update execution record
    await this.prisma.workflowExecution.update({
      where: { id: context.executionId },
      data: {
        status: ExecutionStatus.FAILED,
        completedAt: new Date(),
        duration,
        error: error.message,
      },
    });

    // Emit workflow failed event
    await this.apixGateway.emitWorkflowEvent(
      context.organizationId,
      context.workflowId,
      'workflow_failed',
      {
        executionId: context.executionId,
        error: error.message,
        duration,
        completedNodes: Array.from(context.completedNodes),
        failedNodes: Array.from(context.failedNodes),
      }
    );

    // Clean up active execution
    this.activeExecutions.delete(context.executionId);

    // Emit failure event
    this.eventEmitter.emit('workflow.failed', {
      executionId: context.executionId,
      workflowId: context.workflowId,
      organizationId: context.organizationId,
      error: error.message,
    });
  }

  async cancelExecution(executionId: string, organizationId: string) {
    const context = this.activeExecutions.get(executionId);
    if (!context) {
      throw new Error('Execution not found or not active');
    }

    this.logger.log(`Cancelling workflow execution: ${executionId}`);

    // Update execution status
    await this.updateExecutionStatus(executionId, ExecutionStatus.CANCELLED);

    // Emit cancellation event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      context.workflowId,
      'workflow_cancelled',
      {
        executionId,
        cancelledAt: Date.now(),
      }
    );

    // Clean up
    this.activeExecutions.delete(executionId);
  }

  async pauseExecution(executionId: string, organizationId: string) {
    const context = this.activeExecutions.get(executionId);
    if (!context) {
      throw new Error('Execution not found or not active');
    }

    this.logger.log(`Pausing workflow execution: ${executionId}`);

    // Update execution status
    await this.updateExecutionStatus(executionId, ExecutionStatus.PAUSED);

    // Emit pause event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      context.workflowId,
      'workflow_paused',
      {
        executionId,
        pausedAt: Date.now(),
      }
    );
  }

  async resumeExecution(executionId: string, organizationId: string) {
    const context = this.activeExecutions.get(executionId);
    if (!context) {
      throw new Error('Execution not found or not active');
    }

    this.logger.log(`Resuming workflow execution: ${executionId}`);

    // Update execution status
    await this.updateExecutionStatus(executionId, ExecutionStatus.RUNNING);

    // Emit resume event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      context.workflowId,
      'workflow_resumed',
      {
        executionId,
        resumedAt: Date.now(),
      }
    );

    // Continue execution
    const workflow = await this.prisma.workflow.findUnique({
      where: { id: context.workflowId },
    });

    if (workflow) {
      const definition = workflow.definition as WorkflowDefinition;
      await this.continueExecution(context, definition);
    }
  }

  async getExecutionStatus(executionId: string, organizationId: string) {
    const execution = await this.prisma.workflowExecution.findFirst({
      where: {
        id: executionId,
        workflow: { organizationId },
      },
      include: {
        steps: {
          orderBy: { startedAt: 'asc' },
        },
      },
    });

    if (!execution) {
      throw new Error('Execution not found');
    }

    const context = this.activeExecutions.get(executionId);

    return {
      ...execution,
      isActive: !!context,
      currentNode: context?.currentNode,
      completedNodes: context ? Array.from(context.completedNodes) : [],
      failedNodes: context ? Array.from(context.failedNodes) : [],
      variables: context?.variables || execution.variables,
    };
  }

  private async updateExecutionStatus(executionId: string, status: ExecutionStatus) {
    await this.prisma.workflowExecution.update({
      where: { id: executionId },
      data: { status },
    });
  }

  // Get all active executions for monitoring
  getActiveExecutions(): ExecutionContext[] {
    return Array.from(this.activeExecutions.values());
  }

  // Health check for flow controller
  getHealthStatus() {
    return {
      activeExecutions: this.activeExecutions.size,
      status: 'healthy',
      uptime: process.uptime(),
    };
  }
}