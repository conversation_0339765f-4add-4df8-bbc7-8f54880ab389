/**
 * 🔧 Prisma JSON Type Helpers - FIXED
 * Production-ready type conversion utilities that actually work with Prisma
 */

import { JsonValue } from '@prisma/client/runtime/library';
import { Prisma } from '@prisma/client';

// Use Prisma's own InputJsonValue type instead of custom types
export type SafeJsonValue = Prisma.InputJsonValue;
export type SafeJsonObject = Prisma.InputJsonObject;
export type SafeJsonArray = Prisma.InputJsonArray;

/**
 * Convert any value to Prisma-compatible JSON
 * FIXED: Handles all Prisma JsonValue scenarios properly
 */
export function toPrismaJson(value: any): Prisma.InputJsonValue {
    if (value === null || value === undefined) {
        return null as any;
    }

    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        return value;
    }

    if (Array.isArray(value)) {
        return value.map(toPrismaJson);
    }

    if (typeof value === 'object') {
        const result: Record<string, Prisma.InputJsonValue> = {};
        for (const [key, val] of Object.entries(value)) {
            result[key] = toPrismaJson(val);
        }
        return result;
    }

    return value;
}

/**
 * Convert Prisma JsonValue to safe JavaScript object
 * FIXED: Properly handles null and undefined values
 */
export function fromPrismaJson<T = any>(value: JsonValue | null | undefined): T | null {
    if (value === null || value === undefined) {
        return null;
    }

    // Prisma JsonValue is already a valid JavaScript value
    return value as T;
}

/**
 * Safe object creation for Prisma JSON fields
 */
export function createSafeJsonObject(obj: Record<string, any>): Record<string, Prisma.InputJsonValue> {
    const result: Record<string, Prisma.InputJsonValue> = {};
    for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
            result[key] = toPrismaJson(value);
        }
    }
    return result;
}

/**
 * Helper for updating JSON fields in Prisma
 */
export function updateJsonField<T extends Record<string, any>>(
    existing: JsonValue | null,
    updates: Partial<T>
): Prisma.InputJsonValue {
    const current = fromPrismaJson<T>(existing) || {} as T;
    const merged = { ...current, ...updates };
    return toPrismaJson(merged);
}