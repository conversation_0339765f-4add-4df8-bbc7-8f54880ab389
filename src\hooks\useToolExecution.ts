"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { io, Socket } from 'socket.io-client';

interface ToolExecutionOptions {
  toolId: string;
  input: Record<string, any>;
  sessionId?: string;
  metadata?: Record<string, any>;
  timeout?: number;
  onProgress?: (progress: number, message?: string) => void;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface ToolExecutionResult {
  success: boolean;
  output?: any;
  error?: string;
  duration: number;
  cached: boolean;
  metadata?: Record<string, any>;
  usage?: {
    tokensUsed?: number;
    apiCalls?: number;
    cost?: number;
    memoryUsed?: number;
    cpuTime?: number;
  };
}

interface ToolExecutionState {
  isExecuting: boolean;
  progress: number;
  message?: string;
  result?: ToolExecutionResult;
  error?: string;
  executionId?: string;
}

export function useToolExecution() {
  const [state, setState] = useState<ToolExecutionState>({
    isExecuting: false,
    progress: 0,
  });

  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);
  const socketRef = useRef<Socket | null>(null);
  const isConnectedRef = useRef(false);

  // Initialize WebSocket connection
  const initializeSocket = useCallback(() => {
    if (socketRef.current?.connected || isConnectedRef.current) {
      return socketRef.current;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const organizationId = localStorage.getItem('organization_id');

      if (!token || !organizationId) {
        console.warn('Missing authentication credentials for WebSocket');
        return null;
      }

      const socket = io('/apix', {
        auth: {
          token,
          organizationId,
        },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000,
      });

      socket.on('connect', () => {
        isConnectedRef.current = true;
        // APIX WebSocket connected
      });

      socket.on('disconnect', () => {
        isConnectedRef.current = false;
        // APIX WebSocket disconnected
      });

      socket.on('connect_error', (error) => {
        console.error('APIX WebSocket connection error:', error);
        isConnectedRef.current = false;
      });

      socket.on('apix:connected', (data) => {
        // APIX connection confirmed
      });

      socket.on('tool-execution-event', (event) => {
        handleExecutionEvent(event);
      });

      socket.on('tool-event', (event) => {
        handleToolEvent(event);
      });

      socketRef.current = socket;
      return socket;
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
      return null;
    }
  }, []);

  // Handle execution events from WebSocket
  const handleExecutionEvent = useCallback((event: any) => {
    const { type, payload } = event;

    switch (type) {
      case 'tool.execution.start':
        setState(prev => ({
          ...prev,
          isExecuting: true,
          progress: 0,
          message: 'Execution started...',
          executionId: payload.executionId,
        }));
        break;

      case 'tool.execution.progress':
        setState(prev => ({
          ...prev,
          progress: payload.progress || prev.progress,
          message: payload.message || prev.message,
        }));
        break;

      case 'tool.execution.complete':
        setState(prev => ({
          ...prev,
          isExecuting: false,
          progress: 100,
          message: 'Execution completed',
          result: payload.result,
        }));
        break;

      case 'tool.execution.error':
        setState(prev => ({
          ...prev,
          isExecuting: false,
          error: payload.error || 'Execution failed',
          message: 'Execution failed',
        }));
        break;

      case 'tool.execution.cancelled':
        setState(prev => ({
          ...prev,
          isExecuting: false,
          message: 'Execution cancelled',
        }));
        break;
    }
  }, []);

  // Handle general tool events
  const handleToolEvent = useCallback((event: any) => {
    // Tool event received
  }, []);

  // Subscribe to execution events
  const subscribeToExecution = useCallback((executionId: string) => {
    const socket = initializeSocket();
    if (!socket) return;

    socket.emit('subscribe-tool-execution', { executionId });

    socket.on('subscription-confirmed', (data) => {
      if (data.channel === `tool-execution.${executionId}`) {
        // Subscribed to execution events
      }
    });
  }, [initializeSocket]);

  // Subscribe to tool events
  const subscribeToToolEvents = useCallback((toolId?: string) => {
    const socket = initializeSocket();
    if (!socket) return;

    const organizationId = localStorage.getItem('organization_id');
    if (!organizationId) return;

    socket.emit('subscribe-tool-events', { organizationId, toolId });

    socket.on('subscription-confirmed', (data) => {
      // Subscribed to tool events
    });
  }, [initializeSocket]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
        isConnectedRef.current = false;
      }
    };
  }, []);

  const executeTool = useCallback(async (options: ToolExecutionOptions) => {
    const {
      toolId,
      input,
      sessionId,
      metadata,
      timeout,
      onProgress,
      onComplete,
      onError,
    } = options;

    setState({
      isExecuting: true,
      progress: 0,
      message: 'Initializing execution...',
      result: undefined,
      error: undefined,
    });

    abortControllerRef.current = new AbortController();

    try {
      // Initialize WebSocket connection for real-time updates
      initializeSocket();

      const response = await fetch(`/api/tools/${toolId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          input,
          sessionId,
          metadata,
          timeout,
        }),
        signal: abortControllerRef.current.signal,
      });

      const result = await response.json();

      if (result.success) {
        const executionId = result.data.executionId;

        if (executionId) {
          subscribeToExecution(executionId);
        }

        // If no real-time updates, set final state
        if (!executionId) {
          setState(prev => ({
            ...prev,
            isExecuting: false,
            progress: 100,
            message: 'Execution completed',
            result: result.data,
          }));

          onComplete?.(result.data);

          toast({
            title: "Execution Successful",
            description: `Tool executed in ${result.data.duration}ms`,
          });
        }
      } else {
        const errorMessage = result.message || 'Execution failed';
        setState(prev => ({
          ...prev,
          isExecuting: false,
          error: errorMessage,
        }));

        onError?.(errorMessage);

        toast({
          title: "Execution Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setState(prev => ({
          ...prev,
          isExecuting: false,
          message: 'Execution cancelled',
        }));
        return;
      }

      const errorMessage = error.message || 'Network error';
      setState(prev => ({
        ...prev,
        isExecuting: false,
        error: errorMessage,
      }));

      onError?.(errorMessage);

      toast({
        title: "Execution Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [toast, initializeSocket, subscribeToExecution]);

  const testTool = useCallback(async (toolId: string, input: Record<string, any>) => {
    setState({
      isExecuting: true,
      progress: 0,
      message: 'Testing tool...',
      result: undefined,
      error: undefined,
    });

    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch(`/api/tools/${toolId}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({ input }),
        signal: abortControllerRef.current.signal,
      });

      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          isExecuting: false,
          progress: 100,
          message: 'Test completed',
          result: result.data,
        }));

        toast({
          title: "Test Successful",
          description: `Tool test completed in ${result.data.duration}ms`,
        });
      } else {
        const errorMessage = result.message || 'Test failed';
        setState(prev => ({
          ...prev,
          isExecuting: false,
          error: errorMessage,
        }));

        toast({
          title: "Test Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setState(prev => ({
          ...prev,
          isExecuting: false,
          message: 'Test cancelled',
        }));
        return;
      }

      const errorMessage = error.message || 'Network error';
      setState(prev => ({
        ...prev,
        isExecuting: false,
        error: errorMessage,
      }));

      toast({
        title: "Test Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  }, [toast]);

  const cancelExecution = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setState(prev => ({
      ...prev,
      isExecuting: false,
      message: 'Execution cancelled',
    }));
  }, []);

  const clearState = useCallback(() => {
    setState({
      isExecuting: false,
      progress: 0,
      result: undefined,
      error: undefined,
      message: undefined,
    });
  }, []);

  return {
    // State
    isExecuting: state.isExecuting,
    progress: state.progress,
    message: state.message,
    result: state.result,
    error: state.error,
    executionId: state.executionId,

    // Actions
    executeTool,
    testTool,
    cancelExecution,
    clearState,
    subscribeToExecution,
    subscribeToToolEvents,

    // WebSocket status
    isConnected: isConnectedRef.current,
  };
}

// Hook for managing multiple tool executions
export function useToolExecutionQueue() {
  const [queue, setQueue] = useState<ToolExecutionOptions[]>([]);
  const [currentExecution, setCurrentExecution] = useState<ToolExecutionOptions | null>(null);
  const [results, setResults] = useState<Map<string, ToolExecutionResult>>(new Map());
  const [isProcessing, setIsProcessing] = useState(false);

  const { executeTool } = useToolExecution();

  const addToQueue = useCallback((options: ToolExecutionOptions) => {
    setQueue(prev => [...prev, options]);
  }, []);

  const processQueue = useCallback(async () => {
    if (isProcessing || queue.length === 0) return;

    setIsProcessing(true);
    const nextExecution = queue[0];
    setCurrentExecution(nextExecution);
    setQueue(prev => prev.slice(1));

    try {
      await executeTool({
        ...nextExecution,
        onComplete: (result) => {
          setResults(prev => new Map(prev.set(nextExecution.toolId, result)));
          nextExecution.onComplete?.(result);
        },
        onError: (error) => {
          nextExecution.onError?.(error);
        },
      });
    } finally {
      setCurrentExecution(null);
      setIsProcessing(false);

      setTimeout(() => processQueue(), 100);
    }
  }, [queue, isProcessing, executeTool]);

  const clearQueue = useCallback(() => {
    setQueue([]);
    setCurrentExecution(null);
    setResults(new Map());
  }, []);

  const removeFromQueue = useCallback((toolId: string) => {
    setQueue(prev => prev.filter(item => item.toolId !== toolId));
  }, []);

  useEffect(() => {
    if (!isProcessing && queue.length > 0) {
      processQueue();
    }
  }, [queue, isProcessing, processQueue]);

  return {
    queue,
    currentExecution,
    results: Object.fromEntries(results),
    isProcessing,
    queueLength: queue.length,

    addToQueue,
    processQueue,
    clearQueue,
    removeFromQueue,
  };
}

// Hook for tool analytics and monitoring
export function useToolAnalytics(toolId?: string) {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAnalytics = useCallback(async (
    startDate?: Date,
    endDate?: Date,
    granularity: 'hour' | 'day' = 'day'
  ) => {
    if (!toolId) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        granularity,
        ...(startDate && { startDate: startDate.toISOString() }),
        ...(endDate && { endDate: endDate.toISOString() }),
      });

      const response = await fetch(`/api/tools/${toolId}/analytics?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setAnalytics(data.data);
      } else {
        setError(data.message || 'Failed to load analytics');
      }
    } catch (err: any) {
      setError(err.message || 'Network error');
    } finally {
      setLoading(false);
    }
  }, [toolId]);

  const refreshAnalytics = useCallback(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  useEffect(() => {
    if (toolId) {
      loadAnalytics();
    }
  }, [toolId, loadAnalytics]);

  return {
    analytics,
    loading,
    error,
    loadAnalytics,
    refreshAnalytics,
  };
}

// Hook for tool execution history
export function useToolExecutionHistory(toolId?: string) {
  const [history, setHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  const loadHistory = useCallback(async (page = 1, limit = 20) => {
    if (!toolId) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await fetch(`/api/tools/${toolId}/executions?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setHistory(data.data.executions);
        setPagination({
          page: data.data.page,
          limit: data.data.limit,
          total: data.data.total,
          totalPages: data.data.totalPages,
        });
      } else {
        setError(data.message || 'Failed to load execution history');
      }
    } catch (err: any) {
      setError(err.message || 'Network error');
    } finally {
      setLoading(false);
    }
  }, [toolId]);

  const refreshHistory = useCallback(() => {
    loadHistory(pagination.page, pagination.limit);
  }, [loadHistory, pagination.page, pagination.limit]);

  const loadPage = useCallback((page: number) => {
    loadHistory(page, pagination.limit);
  }, [loadHistory, pagination.limit]);

  useEffect(() => {
    if (toolId) {
      loadHistory();
    }
  }, [toolId, loadHistory]);

  return {
    history,
    loading,
    error,
    pagination,
    loadHistory,
    refreshHistory,
    loadPage,
  };
}