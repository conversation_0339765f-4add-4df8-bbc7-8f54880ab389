import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getAuthenticatedSession, unauthorizedResponse } from "@/lib/auth";

const prisma = new PrismaClient();

// POST /api/workflows/executions/[id]/stop - Stop workflow execution
export async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
) { 
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        // Check if execution exists and user has access
        const execution = await prisma.workflowExecution.findFirst({
            where: {
                id: params.id,
                workflow: {
                    organizationId: session.user.organizationId,
                },
            },
            include: {
                workflow: {
                    select: {
                        name: true,
                    },
                },
            },
        });

        if (!execution) {
            return NextResponse.json(
                { error: "Execution not found" },
                { status: 404 }
            );
        }

        // Check if execution can be stopped
        if (execution.status !== "PENDING" && execution.status !== "RUNNING") {
            return NextResponse.json(
                { error: "Execution cannot be stopped in current state" },
                { status: 400 }
            );
        }

        // Stop execution
        await prisma.workflowExecution.update({
            where: { id: params.id },
            data: {
                status: "CANCELLED",
                completedAt: new Date(),
                error: "Execution cancelled by user",
            },
        });

        // Cancel any running steps
        await prisma.workflowStep.updateMany({
            where: {
                executionId: params.id,
                status: { in: ["PENDING", "RUNNING"] },
            },
            data: {
                status: "CANCELLED",
                completedAt: new Date(),
                error: "Step cancelled due to execution stop",
            },
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.user.id,
                action: "WORKFLOW_EXECUTION_STOPPED",
                resource: "WORKFLOW_EXECUTION",
                resourceId: params.id,
                details: {
                    workflowName: execution.workflow.name,
                    executionId: params.id,
                },
                organizationId: session.user.organizationId,
            },
        });

        return NextResponse.json({
            message: "Execution stopped successfully",
            executionId: params.id,
            status: "cancelled",
        });
    } catch (error) {
        console.error("Failed to stop workflow execution:", error);
        return NextResponse.json(
            { error: "Failed to stop workflow execution" },
            { status: 500 }
        );
    }
}