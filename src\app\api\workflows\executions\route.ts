import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getAuthenticatedSession, unauthorizedResponse } from "@/lib/auth";

const prisma = new PrismaClient();

// GET /api/workflows/executions - List workflow executions
export async function GET(request: NextRequest) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get("page") || "1");
        const limit = parseInt(url.searchParams.get("limit") || "20");
        const status = url.searchParams.get("status");
        const workflowId = url.searchParams.get("workflowId");

        const skip = (page - 1) * limit;

        // Build where clause
        const where: any = {
            workflow: {
                organizationId: session?.user?.organizationId,
            },
            ...(status && status !== "all" && { status }),
            ...(workflowId && { workflowId }),
        };

        // Get executions
        const [executions, total] = await Promise.all([
            prisma.workflowExecution.findMany({
                where,
                skip,
                take: limit,
                orderBy: { startedAt: "desc" },
                include: {
                    workflow: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    steps: {
                        select: {
                            id: true,
                            stepId: true,
                            name: true,
                            type: true,
                            status: true,
                            startedAt: true,
                            completedAt: true,
                            duration: true,
                            error: true,
                        },
                        orderBy: { startedAt: "asc" },
                    },
                },
            }),
            prisma.workflowExecution.count({ where }),
        ]);

        // Transform executions for frontend
        const transformedExecutions = executions.map((execution) => ({
            id: execution.id,
            workflowId: execution.workflowId,
            workflowName: execution.workflow.name,
            status: execution.status.toLowerCase(),
            startedAt: execution.startedAt,
            completedAt: execution.completedAt,
            duration: execution.duration,
            error: execution.error,
            steps: execution.steps.map((step) => ({
                id: step.stepId,
                name: step.name,
                status: step.status.toLowerCase(),
                duration: step.duration,
                error: step.error,
                startedAt: step.startedAt,
                completedAt: step.completedAt,
            })),
            metadata: {
                input: execution.input,
                output: execution.output,
            },
        }));

        return NextResponse.json({
            executions: transformedExecutions,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        console.error("Failed to fetch workflow executions:", error);
        return NextResponse.json(
            { error: "Failed to fetch workflow executions" },
            { status: 500 }
        );
    }
}