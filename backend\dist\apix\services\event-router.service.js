"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EventRouterService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventRouterService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const apix_dto_1 = require("../dto/apix.dto");
const connection_manager_service_1 = require("./connection-manager.service");
const pako = require("pako");
let EventRouterService = EventRouterService_1 = class EventRouterService {
    constructor(prisma, cacheManager, connectionManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.connectionManager = connectionManager;
        this.logger = new common_1.Logger(EventRouterService_1.name);
        this.eventHistory = new Map();
        this.compressionThreshold = 1024;
        this.maxEventHistory = 1000;
    }
    setServer(server) {
        this.server = server;
    }
    async routeEvent(event) {
        try {
            const storedEvent = await this.storeEvent(event);
            this.addToHistory(event);
            await this.routeToChannels(event);
            await this.routeToTargets(event);
            this.logger.debug(`Event routed: ${event.type} to channel ${event.channel}`);
        }
        catch (error) {
            this.logger.error("Failed to route event:", error);
            throw error;
        }
    }
    async routeToUser(userId, event) {
        const connections = this.connectionManager.getUserConnections(userId);
        for (const socketId of connections) {
            const socket = this.server.sockets.sockets.get(socketId);
            if (socket) {
                await this.sendEventToSocket(socket, event);
            }
        }
    }
    async routeToOrganization(organizationId, event) {
        const connections = this.connectionManager.getOrganizationConnections(organizationId);
        for (const socketId of connections) {
            const socket = this.server.sockets.sockets.get(socketId);
            if (socket) {
                await this.sendEventToSocket(socket, event);
            }
        }
    }
    async routeToChannel(channel, event) {
        this.server
            .to(channel)
            .emit("apix:event", await this.prepareEventForTransmission(event));
    }
    async routeToSession(sessionId, event) {
        this.server
            .to(`session:${sessionId}`)
            .emit("apix:event", await this.prepareEventForTransmission(event));
    }
    async getEventHistory(filter) {
        if (filter.organizationId && !filter.since) {
            const cached = this.eventHistory.get(filter.organizationId) || [];
            let filtered = cached;
            if (filter.eventTypes) {
                filtered = filtered.filter((e) => filter.eventTypes.includes(e.type));
            }
            if (filter.channels) {
                filtered = filtered.filter((e) => filter.channels.includes(e.channel));
            }
            if (filter.userId) {
                filtered = filtered.filter((e) => e.userId === filter.userId);
            }
            if (filter.sessionId) {
                filtered = filtered.filter((e) => e.sessionId === filter.sessionId);
            }
            if (filter.limit) {
                filtered = filtered.slice(-filter.limit);
            }
            return filtered;
        }
        const where = {};
        if (filter.eventTypes) {
            where.eventType = { in: filter.eventTypes };
        }
        if (filter.organizationId) {
            where.organizationId = filter.organizationId;
        }
        if (filter.userId) {
            where.userId = filter.userId;
        }
        if (filter.sessionId) {
            where.sessionId = filter.sessionId;
        }
        if (filter.since) {
            where.createdAt = { gte: filter.since };
        }
        const events = await this.prisma.apiXEvent.findMany({
            where,
            orderBy: { createdAt: "desc" },
            take: filter.limit || 100,
        });
        return events.map((e) => ({
            id: e.id,
            type: e.eventType,
            channel: e.channel,
            payload: e.payload,
            sessionId: e.sessionId || undefined,
            userId: e.userId || undefined,
            organizationId: e.organizationId || undefined,
            priority: e.priority,
            compressed: e.compressed,
            correlationId: e.correlationId || undefined,
            metadata: e.metadata,
            timestamp: e.createdAt.getTime(),
        }));
    }
    async replayEvents(socketId, filter) {
        const connection = this.connectionManager.getConnection(socketId);
        if (!connection)
            return;
        const events = await this.getEventHistory({
            ...filter,
            organizationId: connection.organizationId,
        });
        const socket = this.server.sockets.sockets.get(socketId);
        if (socket) {
            socket.emit("apix:replay", {
                events: await Promise.all(events.map((e) => this.prepareEventForTransmission(e))),
                totalCount: events.length,
                timestamp: Date.now(),
            });
        }
    }
    async broadcastSystemEvent(event) {
        const systemEvent = {
            ...event,
            timestamp: Date.now(),
            priority: event.priority || apix_dto_1.EventPriority.NORMAL,
        };
        this.server.emit("apix:event", await this.prepareEventForTransmission(systemEvent));
        await this.storeEvent(systemEvent);
    }
    getEventStats(organizationId) {
        const events = organizationId
            ? this.eventHistory.get(organizationId) || []
            : Array.from(this.eventHistory.values()).flat();
        const stats = {
            totalEvents: events.length,
            eventsByType: {},
            eventsByChannel: {},
            eventsByPriority: {},
        };
        Object.values(apix_dto_1.EventPriority).forEach((priority) => {
            stats.eventsByPriority[priority] = 0;
        });
        for (const event of events) {
            stats.eventsByType[event.type] =
                (stats.eventsByType[event.type] || 0) + 1;
            stats.eventsByChannel[event.channel] =
                (stats.eventsByChannel[event.channel] || 0) + 1;
            stats.eventsByPriority[event.priority]++;
        }
        return stats;
    }
    async storeEvent(event) {
        return this.prisma.apiXEvent.create({
            data: {
                eventType: event.type,
                channel: event.channel,
                payload: event.payload,
                sessionId: event.sessionId,
                userId: event.userId,
                organizationId: event.organizationId,
                priority: event.priority,
                compressed: event.compressed || false,
                correlationId: event.correlationId,
                metadata: event.metadata || {},
            },
        });
    }
    addToHistory(event) {
        if (!event.organizationId)
            return;
        if (!this.eventHistory.has(event.organizationId)) {
            this.eventHistory.set(event.organizationId, []);
        }
        const history = this.eventHistory.get(event.organizationId);
        history.push(event);
        if (history.length > this.maxEventHistory) {
            history.shift();
        }
    }
    async routeToChannels(event) {
        await this.routeToChannel(event.channel, event);
        if (event.organizationId) {
            await this.routeToChannel(`org:${event.organizationId}`, event);
        }
        if (event.userId) {
            await this.routeToChannel(`user:${event.userId}`, event);
        }
        if (event.sessionId) {
            await this.routeToChannel(`session:${event.sessionId}`, event);
        }
    }
    async routeToTargets(event) {
        if (event.userId) {
            await this.routeToUser(event.userId, event);
        }
        if (event.organizationId) {
            await this.routeToOrganization(event.organizationId, event);
        }
    }
    async sendEventToSocket(socket, event) {
        const preparedEvent = await this.prepareEventForTransmission(event);
        socket.emit("apix:event", preparedEvent);
    }
    async prepareEventForTransmission(event) {
        let processedEvent = { ...event };
        if (!event.compressed && this.shouldCompress(event.payload)) {
            try {
                const jsonString = JSON.stringify(event.payload);
                const compressed = pako.deflate(jsonString);
                processedEvent.payload = Array.from(compressed);
                processedEvent.compressed = true;
                processedEvent.metadata = {
                    ...processedEvent.metadata,
                    originalSize: jsonString.length,
                    compressedSize: compressed.length,
                };
            }
            catch (error) {
                this.logger.warn("Failed to compress event payload:", error);
            }
        }
        return processedEvent;
    }
    shouldCompress(payload) {
        try {
            const size = JSON.stringify(payload).length;
            return size > this.compressionThreshold;
        }
        catch {
            return false;
        }
    }
};
exports.EventRouterService = EventRouterService;
exports.EventRouterService = EventRouterService = EventRouterService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, connection_manager_service_1.ConnectionManagerService])
], EventRouterService);
//# sourceMappingURL=event-router.service.js.map