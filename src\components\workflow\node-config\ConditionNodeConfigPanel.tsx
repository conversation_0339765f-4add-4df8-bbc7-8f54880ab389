import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { <PERSON>Circle, XCircle, AlertTriangle, GitBranch, Code2, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const conditionConfigSchema = z.object({
  condition: z.string(),
  conditionType: z.enum(['javascript', 'jsonpath', 'simple']),
  truePath: z.string().optional(),
  falsePath: z.string().optional(),
  multipleConditions: z.array(z.object({
    id: z.string(),
    condition: z.string(),
    label: z.string(),
    targetPath: z.string().optional(),
  })),
  defaultPath: z.string().optional(),
  timeout: z.number().min(100).max(30000),
  variables: z.record(z.string(), z.any()),
  errorHandling: z.object({
    onError: z.enum(['stop', 'continue', 'default']),
    defaultValue: z.boolean(),
  }),
  validation: z.object({
    validateSyntax: z.boolean(),
    allowUndefinedVariables: z.boolean(),
  }),
});

type ConditionConfigFormData = z.infer<typeof conditionConfigSchema>;

interface ConditionNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<ConditionConfigFormData>;
  onConfigChange: (config: ConditionConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
  availableVariables?: string[];
}

export default function ConditionNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
  availableVariables = [],
}: ConditionNodeConfigPanelProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [syntaxValid, setSyntaxValid] = useState(true);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm<ConditionConfigFormData>({
    resolver: zodResolver(conditionConfigSchema),
    defaultValues: {
      condition: '',
      conditionType: "javascript",
      truePath: '',
      falsePath: '',
      multipleConditions: [],
      defaultPath: '',
      timeout: 5000,
      errorHandling: {
        onError: 'stop',
        defaultValue: false,
      },
      validation: {
        validateSyntax: true,
        allowUndefinedVariables: false,
      },
      variables: {},
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const conditionType = watch('conditionType');
  const condition = watch('condition');
  const multipleConditions = watch('multipleConditions');

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid && syntaxValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map((error: any) => error.message).filter(Boolean) as string[];
        if (!syntaxValid) {
          errorMessages.push('Invalid condition syntax');
        }
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid && syntaxValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, syntaxValid, errors, onConfigChange, onValidationChange]);

  // Validate condition syntax
  useEffect(() => {
    if (condition && watch('validation.validateSyntax')) {
      validateConditionSyntax(condition, conditionType);
    }
  }, [condition, conditionType, watch]);

  const validateConditionSyntax = (expr: string, type: string) => {
    try {
      switch (type) {
        case 'javascript':
          // Basic JavaScript syntax validation
          new Function('variables', `return ${expr}`);
          break;
        case 'jsonpath':
          // Basic JSONPath validation (simplified)
          if (!expr.startsWith('$')) {
            throw new Error('JSONPath must start with $');
          }
          break;
        case 'simple':
          // Simple expression validation (variable comparisons)
          const simplePattern = /^\$\{[\w.]+\}\s*(==|!=|>|<|>=|<=)\s*(.+)$/;
          if (!simplePattern.test(expr)) {
            throw new Error('Simple condition format: ${variable} operator value');
          }
          break;
      }
      setSyntaxValid(true);
    } catch (error) {
      setSyntaxValid(false);
    }
  };

  const addMultipleCondition = () => {
    const newCondition = {
      id: `condition_${Date.now()}`,
      condition: '',
      label: `Condition ${multipleConditions.length + 1}`,
      targetPath: '',
    };
    setValue('multipleConditions', [...multipleConditions, newCondition]);
  };

  const removeMultipleCondition = (id: string) => {
    setValue('multipleConditions', multipleConditions.filter((c: any) => c.id !== id));
  };

  const updateMultipleCondition = (id: string, field: string, value: string) => {
    const updated = multipleConditions.map((c: any) =>
      c.id === id ? { ...c, [field]: value } : c
    );
    setValue('multipleConditions', updated);
  };

  const getConditionExamples = (type: string) => {
    switch (type) {
      case 'javascript':
        return [
          'variables.age > 18',
          'variables.status === "active" && variables.score > 80',
          'variables.items.length > 0',
        ];
      case 'jsonpath':
        return [
          '$.user.age > 18',
          '$.data[0].status == "completed"',
          '$.results.length > 5',
        ];
      case 'simple':
        return [
          '${user.age} > 18',
          '${status} == "active"',
          '${score} >= 80',
        ];
      default:
        return [];
    }
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <GitBranch className="h-5 w-5" />
            <CardTitle>Condition Node Configuration</CardTitle>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            {isValid && syntaxValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="multiple">Multiple Paths</TabsTrigger>
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="conditionType">Condition Type</Label>
                  <Select
                    value={watch('conditionType')}
                    onValueChange={(value: 'javascript' | 'jsonpath' | 'simple') =>
                      setValue('conditionType', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="simple">Simple Expression</SelectItem>
                      <SelectItem value="javascript">JavaScript</SelectItem>
                      <SelectItem value="jsonpath">JSONPath</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Choose the type of condition expression to use
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="condition">Condition Expression *</Label>
                  <div className="space-y-2">
                    <Textarea
                      {...register('condition')}
                      rows={3}
                      className={`font-mono text-sm ${!syntaxValid ? 'border-red-500' : ''}`}
                      placeholder={`Enter ${conditionType} expression...`}
                    />
                    {!syntaxValid && (
                      <p className="text-sm text-red-600">Invalid syntax for {conditionType} expression</p>
                    )}
                    {errors.condition && (
                      <p className="text-sm text-red-600">{errors.condition.message}</p>
                    )}
                  </div>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">Examples for {conditionType}:</h4>
                  <ul className="space-y-1 text-sm font-mono">
                    {getConditionExamples(conditionType).map((example: any, index: number) => (
                      <li key={index} className="text-muted-foreground">
                        <button
                          type="button"
                          onClick={() => setValue('condition', example)}
                          className="hover:text-foreground cursor-pointer"
                        >
                          {example}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="truePath">True Path (Node ID)</Label>
                    <Input
                      {...register('truePath')}
                      placeholder="node_id_for_true"
                    />
                    <p className="text-xs text-muted-foreground">
                      Node to execute when condition is true
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="falsePath">False Path (Node ID)</Label>
                    <Input
                      {...register('falsePath')}
                      placeholder="node_id_for_false"
                    />
                    <p className="text-xs text-muted-foreground">
                      Node to execute when condition is false
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Evaluation Timeout</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('timeout') || 5000]}
                      onValueChange={([value]) => setValue('timeout', value)}
                      min={100}
                      max={30000}
                      step={100}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>100ms</span>
                      <span>{watch('timeout')}ms</span>
                      <span>30s</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="multiple" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Multiple Condition Paths</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addMultipleCondition}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Condition
                  </Button>
                </div>

                <p className="text-sm text-muted-foreground">
                  Define multiple conditions with different execution paths. Conditions are evaluated in order.
                </p>

                <div className="space-y-4">
                  {multipleConditions.map((cond: any, index: number) => (
                    <Card key={cond.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-medium">Condition {index + 1}</h5>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMultipleCondition(cond.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Label</Label>
                          <Input
                            value={cond.label}
                            onChange={(e) => updateMultipleCondition(cond.id, 'label', e.target.value)}
                            placeholder="Condition label"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Condition</Label>
                          <Input
                            value={cond.condition}
                            onChange={(e) => updateMultipleCondition(cond.id, 'condition', e.target.value)}
                            placeholder="Expression"
                            className="font-mono text-sm"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Target Path</Label>
                          <Input
                            value={cond.targetPath}
                            onChange={(e) => updateMultipleCondition(cond.id, 'targetPath', e.target.value)}
                            placeholder="node_id"
                          />
                        </div>
                      </div>
                    </Card>
                  ))}

                  {multipleConditions.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <GitBranch className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No multiple conditions defined</p>
                      <p className="text-sm">Add conditions to create complex branching logic</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultPath">Default Path (Node ID)</Label>
                  <Input
                    {...register('defaultPath')}
                    placeholder="node_id_for_default"
                  />
                  <p className="text-xs text-muted-foreground">
                    Node to execute when no conditions match
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="variables" className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium">Available Variables</h4>

                {availableVariables.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {availableVariables.map((variable: any) => (
                      <Badge
                        key={variable}
                        variant="outline"
                        className="cursor-pointer hover:bg-muted"
                        onClick={() => {
                          const currentCondition = watch('condition');
                          const newCondition = currentCondition + `\${${variable}}`;
                          setValue('condition', newCondition);
                        }}
                      >
                        ${variable}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    No variables available from previous nodes
                  </p>
                )}

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Custom Variables</h4>
                  <p className="text-sm text-muted-foreground">
                    Define custom variables for use in conditions
                  </p>

                  <div className="space-y-2">
                    <Label htmlFor="variables">Variables (JSON)</Label>
                    <Textarea
                      value={JSON.stringify(watch('variables') || {}, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          setValue('variables', parsed);
                          trigger('variables');
                        } catch (error) {
                          // Invalid JSON, don't update
                        }
                      }}
                      rows={6}
                      className="font-mono text-sm"
                      placeholder='{\n  "threshold": 100,\n  "status": "active"\n}'
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Error Handling</h4>

                  <div className="space-y-2">
                    <Label>On Error</Label>
                    <Select
                      value={watch('errorHandling.onError')}
                      onValueChange={(value: 'stop' | 'continue' | 'default') =>
                        setValue('errorHandling.onError', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="stop">Stop workflow</SelectItem>
                        <SelectItem value="continue">Continue with default</SelectItem>
                        <SelectItem value="default">Use default path</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('errorHandling.defaultValue')}
                      onCheckedChange={(checked) => setValue('errorHandling.defaultValue', checked)}
                    />
                    <Label>Default value when error occurs</Label>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Validation Settings</h4>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={watch('validation.validateSyntax')}
                        onCheckedChange={(checked) => setValue('validation.validateSyntax', checked)}
                      />
                      <Label>Validate condition syntax</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={watch('validation.allowUndefinedVariables')}
                        onCheckedChange={(checked) => setValue('validation.allowUndefinedVariables', checked)}
                      />
                      <Label>Allow undefined variables</Label>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Performance</h4>

                  <div className="p-4 bg-muted rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Condition Type:</span>
                        <span className="ml-2">{conditionType}</span>
                      </div>
                      <div>
                        <span className="font-medium">Timeout:</span>
                        <span className="ml-2">{watch('timeout')}ms</span>
                      </div>
                      <div>
                        <span className="font-medium">Multiple Paths:</span>
                        <span className="ml-2">{multipleConditions.length}</span>
                      </div>
                      <div>
                        <span className="font-medium">Syntax Valid:</span>
                        <span className={`ml-2 ${syntaxValid ? 'text-green-600' : 'text-red-600'}`}>
                          {syntaxValid ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}