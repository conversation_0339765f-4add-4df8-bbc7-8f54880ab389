-- Add AgentCollaboration related tables
CREATE TABLE IF NOT EXISTS "agent_communication_channels" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "type" TEXT NOT NULL DEFAULT 'group',
  "organizationId" TEXT NOT NULL,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "metadata" JSONB NOT NULL DEFAULT '{}',
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "agent_communication_channels_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "agent_communication_channels_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE IF NOT EXISTS "agent_communication_participants" (
  "id" TEXT NOT NULL,
  "channelId" TEXT NOT NULL,
  "agentId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "agent_communication_participants_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "agent_communication_participants_channelId_fkey" FOREIGN KEY ("channelId") REFERENCES "agent_communication_channels"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT "agent_communication_participants_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "agent_instances"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT "agent_communication_participants_channelId_agentId_key" UNIQUE ("channelId", "agentId")
);

CREATE TABLE IF NOT EXISTS "agent_messages" (
  "id" TEXT NOT NULL,
  "fromAgentId" TEXT NOT NULL,
  "toAgentId" TEXT NOT NULL,
  "type" TEXT NOT NULL,
  "content" JSONB NOT NULL,
  "metadata" JSONB NOT NULL DEFAULT '{}',
  "status" TEXT NOT NULL DEFAULT 'pending',
  "organizationId" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "agent_messages_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "agent_messages_fromAgentId_fkey" FOREIGN KEY ("fromAgentId") REFERENCES "agent_instances"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT "agent_messages_toAgentId_fkey" FOREIGN KEY ("toAgentId") REFERENCES "agent_instances"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT "agent_messages_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX "idx_agent_messages_fromAgentId" ON "agent_messages"("fromAgentId");
CREATE INDEX "idx_agent_messages_toAgentId" ON "agent_messages"("toAgentId");
CREATE INDEX "idx_agent_messages_status" ON "agent_messages"("status");
CREATE INDEX "idx_agent_messages_createdAt" ON "agent_messages"("createdAt");
CREATE INDEX "idx_agent_communication_channels_organizationId" ON "agent_communication_channels"("organizationId");