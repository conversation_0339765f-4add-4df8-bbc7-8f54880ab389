/**
 * 🔧 Backend Prisma JSON Types - FIXED
 * Centralized type definitions for JSON fields stored in Prisma
 */

import { JsonValue } from '@prisma/client/runtime/library';
import { Prisma } from '@prisma/client';

// Helper types
export type SafeJsonValue = Prisma.InputJsonValue;
export type SafeJsonObject = Prisma.InputJsonObject;
export type SafeJsonArray = Prisma.InputJsonArray;

/**
 * Convert any value to Prisma-compatible JSON
 */
export function toPrismaJson(value: any): Prisma.InputJsonValue {
    if (value === null || value === undefined) {
        return Prisma.JsonNull;
    }

    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        return value;
    }

    if (Array.isArray(value)) {
        return value.map(toPrismaJson);
    }

    if (typeof value === 'object') {
        const result: Prisma.InputJsonObject = {};
        for (const [key, val] of Object.entries(value)) {
            result[key] = toPrismaJson(val);
        }
        return result;
    }

    return value;
}

/**
 * Convert Prisma JsonValue to safe JavaScript object
 */
export function fromPrismaJson<T = any>(value: JsonValue | null | undefined): T | null {
    if (value === null || value === undefined) {
        return null;
    }

    return value as T;
}

// FIXED: Aligned with security.service.ts interface
export interface SecurityPolicy {
    passwordPolicy: {
        minLength: number;
        requireUppercase: boolean;
        requireLowercase: boolean;
        requireNumbers: boolean;
        requireSymbols: boolean; // FIXED: Match the service interface
        preventReuse: number;
        maxAge: number;
    };
    sessionPolicy: {
        maxConcurrentSessions: number;
        sessionTimeout: number;
        idleTimeout: number;
        requireMFA: boolean;
    };
    accessPolicy: {
        maxLoginAttempts: number;
        lockoutDuration: number;
        passwordResetExpiry: number;
        emailVerificationExpiry: number;
    };
    complianceSettings: {
        enableAuditLog: boolean;
        dataRetentionDays: number;
        enableEncryption: boolean;
        requireTLS: boolean;
    };
}

export interface OrganizationSettings extends SafeJsonObject {
    securityPolicy?: SecurityPolicy;
    branding?: {
        logo?: string;
        primaryColor?: string;
        secondaryColor?: string;
        theme?: 'light' | 'dark' | 'auto';
    };
    features?: {
        enabledModules?: string[];
        betaFeatures?: string[];
        limits?: {
            maxUsers?: number;
            maxWorkflows?: number;
            maxAgents?: number;
            maxTools?: number;
        };
    };
    notifications?: {
        email?: boolean;
        slack?: boolean;
        webhooks?: string[];
    };
    [key: string]: any; // FIXED: Index signature for compatibility
}

// FIXED: Complete default policy matching service interface
export const DEFAULT_SECURITY_POLICY: SecurityPolicy = {
    passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSymbols: false, // FIXED: Match service interface
        preventReuse: 5,
        maxAge: 90 * 24 * 60 * 60 * 1000, // 90 days
    },
    sessionPolicy: {
        maxConcurrentSessions: 5,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
        idleTimeout: 2 * 60 * 60 * 1000, // 2 hours
        requireMFA: false,
    },
    accessPolicy: {
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        passwordResetExpiry: 60 * 60 * 1000, // 1 hour
        emailVerificationExpiry: 24 * 60 * 60 * 1000, // 24 hours
    },
    complianceSettings: {
        enableAuditLog: true,
        dataRetentionDays: 365,
        enableEncryption: true,
        requireTLS: true,
    },
};

export const DEFAULT_ORGANIZATION_SETTINGS: OrganizationSettings = {
    securityPolicy: DEFAULT_SECURITY_POLICY,
    branding: {
        theme: 'auto',
    },
    features: {
        enabledModules: ['workflows', 'agents', 'tools', 'analytics'],
        betaFeatures: [],
    },
    notifications: {
        email: true,
        slack: false,
        webhooks: [],
    },
};