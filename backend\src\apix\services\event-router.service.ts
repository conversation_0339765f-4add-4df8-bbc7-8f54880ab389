import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../../prisma/prisma.service";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject } from "@nestjs/common";
import { Cache } from "cache-manager";
import { EventPriority } from "../dto/apix.dto";
import { ConnectionManagerService } from "./connection-manager.service";
import { Server } from "socket.io";
import * as pako from "pako";

interface RouteableEvent {
  id?: string;
  type: string;
  channel: string;
  payload: any;
  sessionId?: string;
  userId?: string;
  organizationId?: string;
  priority: EventPriority;
  compressed?: boolean;
  correlationId?: string;
  metadata?: Record<string, any>;
  timestamp: number;
}

interface EventFilter {
  eventTypes?: string[];
  channels?: string[];
  userId?: string;
  organizationId?: string;
  sessionId?: string;
  since?: Date;
  limit?: number;
}

@Injectable()
export class EventRouterService {
  private readonly logger = new Logger(EventRouterService.name);
  private server: Server;
  private eventHistory = new Map<string, RouteableEvent[]>();
  private compressionThreshold = 1024; // 1KB
  private maxEventHistory = 1000;

  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private connectionManager: ConnectionManagerService,
  ) {}

  setServer(server: Server): void {
    this.server = server;
  }

  async routeEvent(event: RouteableEvent): Promise<void> {
    try {
      // Store event in database
      const storedEvent = await this.storeEvent(event);

      // Add to history for replay
      this.addToHistory(event);

      // Route to appropriate channels
      await this.routeToChannels(event);

      // Route to specific targets
      await this.routeToTargets(event);

      this.logger.debug(
        `Event routed: ${event.type} to channel ${event.channel}`,
      );
    } catch (error) {
      this.logger.error("Failed to route event:", error);
      throw error;
    }
  }

  async routeToUser(userId: string, event: RouteableEvent): Promise<void> {
    const connections = this.connectionManager.getUserConnections(userId);

    for (const socketId of connections) {
      const socket = this.server.sockets.sockets.get(socketId);
      if (socket) {
        await this.sendEventToSocket(socket, event);
      }
    }
  }

  async routeToOrganization(
    organizationId: string,
    event: RouteableEvent,
  ): Promise<void> {
    const connections =
      this.connectionManager.getOrganizationConnections(organizationId);

    for (const socketId of connections) {
      const socket = this.server.sockets.sockets.get(socketId);
      if (socket) {
        await this.sendEventToSocket(socket, event);
      }
    }
  }

  async routeToChannel(channel: string, event: RouteableEvent): Promise<void> {
    this.server
      .to(channel)
      .emit("apix:event", await this.prepareEventForTransmission(event));
  }

  async routeToSession(
    sessionId: string,
    event: RouteableEvent,
  ): Promise<void> {
    this.server
      .to(`session:${sessionId}`)
      .emit("apix:event", await this.prepareEventForTransmission(event));
  }

  async getEventHistory(filter: EventFilter): Promise<RouteableEvent[]> {
    // Try cache first for recent events
    if (filter.organizationId && !filter.since) {
      const cached = this.eventHistory.get(filter.organizationId) || [];
      let filtered = cached;

      if (filter.eventTypes) {
        filtered = filtered.filter((e) => filter.eventTypes!.includes(e.type));
      }
      if (filter.channels) {
        filtered = filtered.filter((e) => filter.channels!.includes(e.channel));
      }
      if (filter.userId) {
        filtered = filtered.filter((e) => e.userId === filter.userId);
      }
      if (filter.sessionId) {
        filtered = filtered.filter((e) => e.sessionId === filter.sessionId);
      }

      if (filter.limit) {
        filtered = filtered.slice(-filter.limit);
      }

      return filtered;
    }

    // Fallback to database
    const where: any = {};

    if (filter.eventTypes) {
      where.eventType = { in: filter.eventTypes };
    }
    if (filter.organizationId) {
      where.organizationId = filter.organizationId;
    }
    if (filter.userId) {
      where.userId = filter.userId;
    }
    if (filter.sessionId) {
      where.sessionId = filter.sessionId;
    }
    if (filter.since) {
      where.createdAt = { gte: filter.since };
    }

    const events = await this.prisma.apiXEvent.findMany({
      where,
      orderBy: { createdAt: "desc" },
      take: filter.limit || 100,
    });

    return events.map((e) => ({
      id: e.id,
      type: e.eventType,
      channel: e.channel,
      payload: e.payload,
      sessionId: e.sessionId || undefined,
      userId: e.userId || undefined,
      organizationId: e.organizationId || undefined,
      priority: e.priority,
      compressed: e.compressed,
      correlationId: e.correlationId || undefined,
      metadata: e.metadata as Record<string, any>,
      timestamp: e.createdAt.getTime(),
    }));
  }

  async replayEvents(socketId: string, filter: EventFilter): Promise<void> {
    const connection = this.connectionManager.getConnection(socketId);
    if (!connection) return;

    const events = await this.getEventHistory({
      ...filter,
      organizationId: connection.organizationId,
    });

    const socket = this.server.sockets.sockets.get(socketId);
    if (socket) {
      socket.emit("apix:replay", {
        events: await Promise.all(
          events.map((e) => this.prepareEventForTransmission(e)),
        ),
        totalCount: events.length,
        timestamp: Date.now(),
      });
    }
  }

  async broadcastSystemEvent(
    event: Omit<RouteableEvent, "timestamp">,
  ): Promise<void> {
    const systemEvent: RouteableEvent = {
      ...event,
      timestamp: Date.now(),
      priority: event.priority || EventPriority.NORMAL,
    };

    this.server.emit(
      "apix:event",
      await this.prepareEventForTransmission(systemEvent),
    );
    await this.storeEvent(systemEvent);
  }

  getEventStats(organizationId?: string): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsByChannel: Record<string, number>;
    eventsByPriority: Record<EventPriority, number>;
  } {
    const events = organizationId
      ? this.eventHistory.get(organizationId) || []
      : Array.from(this.eventHistory.values()).flat();

    const stats = {
      totalEvents: events.length,
      eventsByType: {} as Record<string, number>,
      eventsByChannel: {} as Record<string, number>,
      eventsByPriority: {} as Record<EventPriority, number>,
    };

    Object.values(EventPriority).forEach((priority) => {
      stats.eventsByPriority[priority] = 0;
    });

    for (const event of events) {
      stats.eventsByType[event.type] =
        (stats.eventsByType[event.type] || 0) + 1;
      stats.eventsByChannel[event.channel] =
        (stats.eventsByChannel[event.channel] || 0) + 1;
      stats.eventsByPriority[event.priority]++;
    }

    return stats;
  }

  private async storeEvent(event: RouteableEvent): Promise<any> {
    return this.prisma.apiXEvent.create({
      data: {
        eventType: event.type,
        channel: event.channel,
        payload: event.payload,
        sessionId: event.sessionId,
        userId: event.userId,
        organizationId: event.organizationId,
        priority: event.priority,
        compressed: event.compressed || false,
        correlationId: event.correlationId,
        metadata: event.metadata || {},
      },
    });
  }

  private addToHistory(event: RouteableEvent): void {
    if (!event.organizationId) return;

    if (!this.eventHistory.has(event.organizationId)) {
      this.eventHistory.set(event.organizationId, []);
    }

    const history = this.eventHistory.get(event.organizationId)!;
    history.push(event);

    if (history.length > this.maxEventHistory) {
      history.shift();
    }
  }

  private async routeToChannels(event: RouteableEvent): Promise<void> {
    // Route to specific channel
    await this.routeToChannel(event.channel, event);

    // Route to organization channel if event has organizationId
    if (event.organizationId) {
      await this.routeToChannel(`org:${event.organizationId}`, event);
    }

    // Route to user channel if event has userId
    if (event.userId) {
      await this.routeToChannel(`user:${event.userId}`, event);
    }

    // Route to session channel if event has sessionId
    if (event.sessionId) {
      await this.routeToChannel(`session:${event.sessionId}`, event);
    }
  }

  private async routeToTargets(event: RouteableEvent): Promise<void> {
    // Route to specific user if specified
    if (event.userId) {
      await this.routeToUser(event.userId, event);
    }

    // Route to organization if specified
    if (event.organizationId) {
      await this.routeToOrganization(event.organizationId, event);
    }
  }

  private async sendEventToSocket(
    socket: any,
    event: RouteableEvent,
  ): Promise<void> {
    const preparedEvent = await this.prepareEventForTransmission(event);
    socket.emit("apix:event", preparedEvent);
  }

  private async prepareEventForTransmission(
    event: RouteableEvent,
  ): Promise<any> {
    let processedEvent = { ...event };

    // Compress large payloads if not already compressed
    if (!event.compressed && this.shouldCompress(event.payload)) {
      try {
        const jsonString = JSON.stringify(event.payload);
        const compressed = pako.deflate(jsonString);
        processedEvent.payload = Array.from(compressed);
        processedEvent.compressed = true;
        processedEvent.metadata = {
          ...processedEvent.metadata,
          originalSize: jsonString.length,
          compressedSize: compressed.length,
        };
      } catch (error) {
        this.logger.warn("Failed to compress event payload:", error);
      }
    }

    return processedEvent;
  }

  private shouldCompress(payload: any): boolean {
    try {
      const size = JSON.stringify(payload).length;
      return size > this.compressionThreshold;
    } catch {
      return false;
    }
  }
}
