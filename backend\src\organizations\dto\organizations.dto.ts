import { IsString, IsOptional, IsBoolean, IsArray, IsNumber, IsEmail, IsEnum, IsObject, IsDate, <PERSON>UUI<PERSON>, MinLength, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Role } from '@prisma/client';

export class CreateOrganizationDto {
    @IsString()
    @MinLength(2)
    name: string;

    @IsString()
    @MinLength(2)
    slug: string;

    @IsOptional()
    @IsString()
    domain?: string;

    @IsOptional()
    @IsObject()
    settings?: Record<string, any>;

    @IsOptional()
    @IsObject()
    branding?: Record<string, any>;

    @IsOptional()
    @IsString()
    plan?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    maxUsers?: number;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    features?: string[];
}

export class UpdateOrganizationDto {
    @IsOptional()
    @IsString()
    @MinLength(2)
    name?: string;

    @IsOptional()
    @IsString()
    @MinLength(2)
    slug?: string;

    @IsOptional()
    @IsString()
    domain?: string;

    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsString()
    plan?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    maxUsers?: number;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    features?: string[];

    @IsOptional()
    @Type(() => Date)
    @IsDate()
    planExpires?: Date;
}

export class OrganizationListQueryDto {
    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number = 20;

    @IsOptional()
    @IsString()
    search?: string;

    @IsOptional()
    @IsString()
    plan?: string;

    @IsOptional()
    @Transform(({ value }) => value === 'true')
    @IsBoolean()
    isActive?: boolean;

    @IsOptional()
    @IsString()
    sortBy?: string = 'createdAt';

    @IsOptional()
    @IsEnum(['asc', 'desc'])
    sortOrder?: 'asc' | 'desc' = 'desc';
}

export class OrganizationSettingsDto {
    @IsOptional()
    @IsString()
    timezone?: string;

    @IsOptional()
    @IsString()
    language?: string;

    @IsOptional()
    @IsString()
    dateFormat?: string;

    @IsOptional()
    @IsString()
    currency?: string;

    @IsOptional()
    @IsBoolean()
    allowSignups?: boolean;

    @IsOptional()
    @IsBoolean()
    requireEmailVerification?: boolean;

    @IsOptional()
    @IsBoolean()
    enforcePasswordPolicy?: boolean;

    @IsOptional()
    @IsBoolean()
    requireMFA?: boolean;

    @IsOptional()
    @IsNumber()
    @Min(1)
    sessionTimeout?: number;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    allowedDomains?: string[];

    @IsOptional()
    @IsObject()
    apiLimits?: {
        requestsPerMinute?: number;
        requestsPerHour?: number;
        requestsPerDay?: number;
    };

    @IsOptional()
    @IsObject()
    integrations?: Record<string, any>;

    @IsOptional()
    @IsObject()
    notifications?: {
        email?: boolean;
        push?: boolean;
        webhook?: string;
    };

    [key: string]: any;
}

export class OrganizationBrandingDto {
    @IsOptional()
    @IsString()
    logo?: string;

    @IsOptional()
    @IsString()
    favicon?: string;

    @IsOptional()
    @IsString()
    primaryColor?: string;

    @IsOptional()
    @IsString()
    secondaryColor?: string;

    @IsOptional()
    @IsString()
    accentColor?: string;

    @IsOptional()
    @IsString()
    customCSS?: string;

    @IsOptional()
    @IsString()
    customHeader?: string;

    @IsOptional()
    @IsString()
    customFooter?: string;

    @IsOptional()
    @IsObject()
    theme?: {
        mode?: 'light' | 'dark' | 'auto';
        colors?: Record<string, string>;
        fonts?: Record<string, string>;
    };

    [key: string]: any;
}

export class OrganizationInviteDto {
    @IsEmail()
    email: string;

    @IsEnum(Role)
    role: Role;

    @IsOptional()
    @IsString()
    message?: string;

    @IsOptional()
    @Type(() => Date)
    @IsDate()
    expiresAt?: Date;
}

export class OrganizationMemberDto {
    id: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    avatar?: string;
    role: Role;
    isActive: boolean;
    lastLoginAt?: Date;
    joinedAt: Date;
    stats?: {
        workflows: number;
        agents: number;
        tools: number;
        sessions: number;
    };
}

export class OrganizationDto {
    id: string;
    name: string;
    slug: string;
    domain?: string;
    isActive: boolean;
    plan: string;
    planExpires?: Date;
    maxUsers?: number;
    features: string[];
    settings: Record<string, any>;
    branding: Record<string, any>;
    members: OrganizationMemberDto[];
    stats?: {
        users: number;
        workflows: number;
        agents: number;
        tools: number;
        providers: number;
        sessions: number;
    };
    createdAt: Date;
    updatedAt: Date;
}

export class OrganizationListResponseDto {
    organizations: OrganizationDto[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export class OrganizationStatsDto {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    usersByRole: Record<string, number>;
    workflows: number;
    agents: number;
    tools: number;
    providers: number;
    activeSessions: number;
    recentActivity: number;
    storageUsed: number;
    apiCalls: number;
    costThisMonth: number;
}

export class OrganizationUsageDto {
    period: 'day' | 'week' | 'month';
    startDate: Date;
    endDate: Date;
    workflowExecutions: number;
    agentSessions: number;
    toolExecutions: number;
    apiCalls: number;
    totalTokens: number;
    totalCost: number;
    storageUsed: number;
    bandwidthUsed: number;
}

export class OrganizationPermissionDto {
    resource: string;
    actions: string[];
    conditions?: Record<string, any>;
}

export class BulkMemberActionDto {
    @IsArray()
    @IsUUID(undefined, { each: true })
    userIds: string[];

    @IsEnum(['activate', 'deactivate', 'updateRole', 'remove'])
    action: 'activate' | 'deactivate' | 'updateRole' | 'remove';

    @IsOptional()
    @IsObject()
    data?: {
        role?: Role;
        [key: string]: any;
    };
}