import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../apix/apix.gateway';
import { SessionsService } from '../sessions/sessions.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';
import { ExecutionStatus, AgentType, ToolType } from '@prisma/client';
import { FlowControllerService } from './services/flow-controller.service';
import { NodeRegistryService } from './services/node-registry.service';
import { VariableResolverService } from './services/variable-resolver.service';

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'parallel' | 'human_input' | 'delay' | 'hybrid';
  name: string;
  config: any;
  position: { x: number; y: number };
  inputs: string[];
  outputs: string[];
}

export interface WorkflowDefinition {
  nodes: WorkflowNode[];
  edges: Array<{
    id: string;
    source: string;
    target: string;
    condition?: string;
  }>;
  triggers: Array<{
    type: 'manual' | 'scheduled' | 'webhook' | 'event';
    config: any;
  }>;
  settings: {
    timeout?: number;
    retryPolicy?: {
      maxRetries: number;
      backoffStrategy: 'linear' | 'exponential';
      retryDelay: number;
    };
    errorHandling?: {
      onError: 'stop' | 'continue' | 'retry';
      fallbackNode?: string;
    };
  };
  variables?: Record<string, any>;
}

@Injectable()
export class WorkflowsService {
  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private sessionsService: SessionsService,
    private flowController: FlowControllerService,
    private nodeRegistry: NodeRegistryService,
    private variableResolver: VariableResolverService,
  ) {}

  async create(userId: string, organizationId: string, createWorkflowDto: CreateWorkflowDto) {
    const { name, description, definition, tags } = createWorkflowDto;

    // Validate workflow definition
    this.validateWorkflowDefinition(definition);

    const workflow = await this.prisma.workflow.create({
      data: {
        name,
        description,
        definition: definition as any,
        tags: tags || [],
        creatorId: userId,
        organizationId,
        version: 1,
      },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Emit workflow created event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      workflow.id,
      'workflow_created',
      {
        name: workflow.name,
        createdBy: workflow.creator,
        version: workflow.version,
      }
    );

    return workflow;
  }

  async findAll(organizationId: string, filters?: {
    isActive?: boolean;
    tags?: string[];
    creatorId?: string;
    search?: string;
  }) {
    const where: any = { organizationId };

    if (filters?.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters?.tags && filters.tags.length > 0) {
      where.tags = {
        hasSome: filters.tags,
      };
    }

    if (filters?.creatorId) {
      where.creatorId = filters.creatorId;
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    return this.prisma.workflow.findMany({
      where,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
  }

  async findOne(id: string, organizationId: string) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        executions: {
          take: 10,
          orderBy: {
            startedAt: 'desc',
          },
          select: {
            id: true,
            status: true,
            startedAt: true,
            completedAt: true,
            duration: true,
            error: true,
          },
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    return workflow;
  }

  async update(id: string, organizationId: string, userId: string, updateWorkflowDto: UpdateWorkflowDto) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    const updateData: any = {};

    if (updateWorkflowDto.name) updateData.name = updateWorkflowDto.name;
    if (updateWorkflowDto.description !== undefined) updateData.description = updateWorkflowDto.description;
    if (updateWorkflowDto.tags) updateData.tags = updateWorkflowDto.tags;
    if (updateWorkflowDto.isActive !== undefined) updateData.isActive = updateWorkflowDto.isActive;

    if (updateWorkflowDto.definition) {
      this.validateWorkflowDefinition(updateWorkflowDto.definition);
      updateData.definition = updateWorkflowDto.definition;
      updateData.version = workflow.version + 1;
    }

    const updatedWorkflow = await this.prisma.workflow.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Emit workflow updated event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      workflow.id,
      'workflow_updated',
      {
        name: updatedWorkflow.name,
        version: updatedWorkflow.version,
        changes: Object.keys(updateData),
      }
    );

    return updatedWorkflow;
  }

  async remove(id: string, organizationId: string) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    // Check if workflow has active executions
    const activeExecutions = await this.prisma.workflowExecution.count({
      where: {
        workflowId: id,
        status: {
          in: [ExecutionStatus.PENDING, ExecutionStatus.RUNNING],
        },
      },
    });

    if (activeExecutions > 0) {
      throw new BadRequestException('Cannot delete workflow with active executions');
    }

    await this.prisma.workflow.delete({
      where: { id },
    });

    // Emit workflow deleted event
    await this.apixGateway.emitWorkflowEvent(
      organizationId,
      workflow.id,
      'workflow_deleted',
      {
        name: workflow.name,
      }
    );
  }

  async execute(id: string, organizationId: string, userId: string, executeWorkflowDto: ExecuteWorkflowDto) {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId, isActive: true },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found or inactive');
    }

    // Start workflow execution using flow controller
    const executionId = await this.flowController.startExecution(
      id,
      userId,
      organizationId,
      executeWorkflowDto.input || {},
      executeWorkflowDto.options || {}
    );

    // Get execution details
    const execution = await this.prisma.workflowExecution.findUnique({
      where: { id: executionId },
    });

    return {
      executionId: execution.id,
      status: execution.status,
      startedAt: execution.startedAt,
    };
  }

  async getExecutions(workflowId: string, organizationId: string, limit = 50) {
    return this.prisma.workflowExecution.findMany({
      where: {
        workflowId,
        workflow: {
          organizationId,
        },
      },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        startedAt: 'desc',
      },
      take: limit,
    });
  }

  async getExecution(executionId: string, organizationId: string) {
    const execution = await this.prisma.workflowExecution.findFirst({
      where: {
        id: executionId,
        workflow: {
          organizationId,
        },
      },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
          },
        },
        steps: {
          orderBy: {
            startedAt: 'asc',
          },
        },
      },
    });

    if (!execution) {
      throw new NotFoundException('Execution not found');
    }

    return execution;
  }

  async cancelExecution(executionId: string, organizationId: string) {
    return this.flowController.cancelExecution(executionId, organizationId);
  }

  async pauseExecution(executionId: string, organizationId: string) {
    return this.flowController.pauseExecution(executionId, organizationId);
  }

  async resumeExecution(executionId: string, organizationId: string) {
    return this.flowController.resumeExecution(executionId, organizationId);
  }

  async getExecutionStatus(executionId: string, organizationId: string) {
    return this.flowController.getExecutionStatus(executionId, organizationId);
  }

  async getAvailableNodeTypes(organizationId: string) {
    // Get all registered node types
    const nodeTypes = this.nodeRegistry.getAllNodeDefinitions();
    
    // Get organization-specific agents and tools
    const agents = await this.prisma.agent.findMany({
      where: { organizationId, isActive: true },
      select: { id: true, name: true, type: true },
    });
    
    const tools = await this.prisma.tool.findMany({
      where: { organizationId, isActive: true },
      select: { id: true, name: true, type: true },
    });
    
    return {
      nodeTypes,
      agents,
      tools,
    };
  }

  private validateWorkflowDefinition(definition: WorkflowDefinition): void {
    if (!definition.nodes || definition.nodes.length === 0) {
      throw new BadRequestException('Workflow must have at least one node');
    }

    if (!definition.edges) {
      throw new BadRequestException('Workflow must have edges array');
    }

    // Validate node IDs are unique
    const nodeIds = definition.nodes.map(node => node.id);
    const uniqueNodeIds = new Set(nodeIds);
    if (nodeIds.length !== uniqueNodeIds.size) {
      throw new BadRequestException('Node IDs must be unique');
    }

    // Validate edges reference existing nodes
    for (const edge of definition.edges) {
      if (!nodeIds.includes(edge.source)) {
        throw new BadRequestException(`Edge source node ${edge.source} not found`);
      }
      if (!nodeIds.includes(edge.target)) {
        throw new BadRequestException(`Edge target node ${edge.target} not found`);
      }
    }

    // Validate at least one start node exists
    const startNodes = definition.nodes.filter(node => 
      node.inputs.length === 0 || 
      !definition.edges.some(edge => edge.target === node.id)
    );

    if (startNodes.length === 0) {
      throw new BadRequestException('Workflow must have at least one start node');
    }

    // Validate node types
    for (const node of definition.nodes) {
      const nodeDefinition = this.nodeRegistry.getNodeDefinition(node.type);
      if (!nodeDefinition) {
        throw new BadRequestException(`Unknown node type: ${node.type}`);
      }
    }
  }
}