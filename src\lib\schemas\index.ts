/**
 * 📋 Centralized Schema Definitions - FIXED
 * CORRECTED: Using actual Prisma enum values
 */

import { z } from 'zod';

// ==================================================
// COMMON SCHEMAS (reusable across all modules)
// ==================================================

export const CommonSchemas = {
    // IDs
    uuid: z.string().uuid('Invalid UUID format'),
    cuid: z.string().min(1, 'ID is required'), // Prisma uses cuid() by default

    // Pagination
    pagination: z.object({
        page: z.string().transform(val => Math.max(parseInt(val) || 1, 1)),
        limit: z.string().transform(val => Math.min(Math.max(parseInt(val) || 20, 1), 100)),
        search: z.string().optional(),
        sort: z.string().optional(),
        order: z.enum(['asc', 'desc']).default('desc')
    }),

    // Date ranges
    dateRange: z.object({
        startDate: z.string().datetime().optional(),
        endDate: z.string().datetime().optional()
    }).refine(data => {
        if (data.startDate && data.endDate) {
            return new Date(data.startDate) <= new Date(data.endDate);
        }
        return true;
    }, 'Start date must be before end date'),

    // Status filters
    statusFilter: z.object({
        status: z.enum(['active', 'inactive', 'all']).default('all')
    }),

    // Route parameters
    idParam: z.object({
        id: z.string().min(1, 'ID is required')
    })
};

// ==================================================
// USER & AUTH SCHEMAS - FIXED with correct Role enum
// ==================================================

export const AuthSchemas = {
    login: z.object({
        email: z.string().email('Invalid email format'),
        password: z.string().min(8, 'Password must be at least 8 characters'),
        organizationSlug: z.string().optional(),
        mfaCode: z.string().optional(),
        rememberMe: z.boolean().default(false)
    }),

    register: z.object({
        firstName: z.string().min(1, 'First name is required'),
        lastName: z.string().min(1, 'Last name is required'),
        email: z.string().email('Invalid email format'),
        password: z.string().min(8, 'Password must be at least 8 characters'),
        company: z.string().min(1, 'Company name is required'),
        subscribeNewsletter: z.boolean().default(false)
    }),

    updateProfile: z.object({
        firstName: z.string().min(1).optional(),
        lastName: z.string().min(1).optional(),
        email: z.string().email().optional(),
        avatar: z.string().url().optional(),
        // FIXED: Using correct Role enum from Prisma schema
        role: z.enum(['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER', 'VIEWER']).optional()
    })
};

// ==================================================
// WORKFLOW SCHEMAS
// ==================================================

export const WorkflowSchemas = {
    create: z.object({
        name: z.string().min(1, 'Name is required'),
        description: z.string().optional(),
        definition: z.object({
            nodes: z.array(z.object({
                id: z.string(),
                type: z.string(),
                data: z.record(z.string(), z.any()).default({}),
                position: z.object({
                    x: z.number(),
                    y: z.number()
                }).optional()
            })),
            edges: z.array(z.object({
                id: z.string(),
                source: z.string(),
                target: z.string(),
                type: z.string().optional(),
                data: z.record(z.string(), z.any()).optional()
            })),
            metadata: z.record(z.string(), z.any()).optional()
        }),
        tags: z.array(z.string()).default([]),
        isActive: z.boolean().default(true)
    }),

    update: z.object({
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        definition: z.object({
            nodes: z.array(z.any()),
            edges: z.array(z.any()),
            metadata: z.record(z.string(), z.any()).optional()
        }).optional(),
        tags: z.array(z.string()).optional(),
        isActive: z.boolean().optional()
    }),

    execute: z.object({
        input: z.record(z.string(), z.any()).default({}),
        options: z.object({
            waitForCompletion: z.boolean().default(false),
            timeout: z.number().min(1000).max(300000).default(30000),
            retryPolicy: z.object({
                maxRetries: z.number().min(0).max(5).default(0),
                retryDelay: z.number().min(1000).default(1000)
            }).optional()
        }).default(() => ({
            waitForCompletion: false,
            timeout: 30000
        }))
    }),

    list: CommonSchemas.pagination.extend({
        tags: z.string().optional().transform(val => val?.split(',').filter(Boolean)),
        isActive: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined)
    })
};

// ==================================================
// AGENT SCHEMAS - FIXED with correct enum values
// ==================================================

export const AgentSchemas = {
    create: z.object({
        name: z.string().min(1, 'Name is required'),
        description: z.string().optional(),
        // FIXED: Using exact enum values from Prisma schema
        type: z.enum(['STANDALONE', 'TOOL_DRIVEN', 'HYBRID', 'MULTI_TASKING', 'MULTI_PROVIDER']).default('STANDALONE'),
        config: z.object({
            provider: z.string().default('openai'),
            model: z.string().default('gpt-4'),
            temperature: z.number().min(0).max(2).default(0.7),
            maxTokens: z.number().min(1).max(32000).default(2000),
            systemPrompt: z.string().optional(),
            instructions: z.string().optional(),
            tools: z.array(z.string()).default([]),
            toolConfig: z.record(z.string(), z.any()).default({})
        }),
        skills: z.array(z.string()).default([]),
        isActive: z.boolean().default(true)
    }),

    update: z.object({
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        type: z.enum(['STANDALONE', 'TOOL_DRIVEN', 'HYBRID', 'MULTI_TASKING', 'MULTI_PROVIDER']).optional(),
        config: z.object({
            provider: z.string().optional(),
            model: z.string().optional(),
            temperature: z.number().min(0).max(2).optional(),
            maxTokens: z.number().min(1).max(32000).optional(),
            systemPrompt: z.string().optional(),
            instructions: z.string().optional(),
            tools: z.array(z.string()).optional(),
            toolConfig: z.record(z.string(), z.any()).optional()
        }).optional(),
        skills: z.array(z.string()).optional(),
        isActive: z.boolean().optional()
    }),

    list: CommonSchemas.pagination.extend({
        type: z.string().optional(),
        isActive: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined)
    })
};

// ==================================================
// TOOL SCHEMAS - FIXED with correct enum values
// ==================================================

export const ToolSchemas = {
    create: z.object({
        name: z.string().min(1, 'Tool name is required'),
        description: z.string().optional(),
        // FIXED: Using exact enum values from Prisma schema
        category: z.enum(['COMMUNICATION', 'DATA_ANALYSIS', 'AUTOMATION', 'CONTENT_GENERATION', 'DECISION_MAKING', 'INTEGRATION', 'CUSTOM']).default('CUSTOM'),
        type: z.enum(['FUNCTION_CALL', 'RAG', 'API_FETCH', 'BROWSER_AUTOMATION', 'DATABASE', 'CUSTOM_LOGIC']).default('FUNCTION_CALL'),
        config: z.record(z.string(), z.any()).default({}),
        inputSchema: z.record(z.string(), z.any()).default({}),
        outputSchema: z.record(z.string(), z.any()).default({}),
        timeout: z.number().int().min(1000).max(300000).default(30000),
        retryPolicy: z.record(z.string(), z.any()).default({}),
        cacheStrategy: z.enum(['NONE', 'INPUT_HASH', 'TIME_BASED', 'DEPENDENCY_BASED', 'CUSTOM']).default('INPUT_HASH'),
        cacheTTL: z.number().int().min(0).default(3600),
        version: z.string().default('1.0.0'),
        tags: z.array(z.string()).default([]),
        documentation: z.string().optional(),
        examples: z.array(z.record(z.string(), z.any())).default([]),
        isPublic: z.boolean().default(false),
        isActive: z.boolean().default(true),
        dependencies: z.array(z.string()).default([]),
        requirements: z.record(z.string(), z.any()).default({})
    }),

    update: z.object({
        name: z.string().min(1).optional(),
        description: z.string().optional(),
        category: z.enum(['COMMUNICATION', 'DATA_ANALYSIS', 'AUTOMATION', 'CONTENT_GENERATION', 'DECISION_MAKING', 'INTEGRATION', 'CUSTOM']).optional(),
        type: z.enum(['FUNCTION_CALL', 'RAG', 'API_FETCH', 'BROWSER_AUTOMATION', 'DATABASE', 'CUSTOM_LOGIC']).optional(),
        config: z.record(z.string(), z.any()).optional(),
        inputSchema: z.record(z.string(), z.any()).optional(),
        outputSchema: z.record(z.string(), z.any()).optional(),
        timeout: z.number().int().min(1000).max(300000).optional(),
        retryPolicy: z.record(z.string(), z.any()).optional(),
        cacheStrategy: z.enum(['NONE', 'INPUT_HASH', 'TIME_BASED', 'DEPENDENCY_BASED', 'CUSTOM']).optional(),
        cacheTTL: z.number().int().min(0).optional(),
        version: z.string().optional(),
        tags: z.array(z.string()).optional(),
        documentation: z.string().optional(),
        examples: z.array(z.record(z.string(), z.any())).optional(),
        isPublic: z.boolean().optional(),
        isActive: z.boolean().optional(),
        dependencies: z.array(z.string()).optional(),
        requirements: z.record(z.string(), z.any()).optional()
    }),

    execute: z.object({
        toolId: z.string().min(1, 'Tool ID is required'),
        input: z.record(z.string(), z.any()).default({}),
        executionId: z.string().optional(),
        sessionId: z.string().optional(),
        metadata: z.record(z.string(), z.any()).default({}),
        options: z.object({
            timeout: z.number().optional(),
            retryCount: z.number().optional(),
            useCache: z.boolean().default(false),
            priority: z.enum(['low', 'normal', 'high']).default('normal')
        })
    }),

    list: CommonSchemas.pagination.extend({
        category: z.string().optional(),
        type: z.string().optional(),
        isPublic: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
        isActive: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined)
    })
};

// ==================================================
// ORGANIZATION SCHEMAS
// ==================================================

export const OrganizationSchemas = {
    update: z.object({
        name: z.string().min(1).optional(),
        slug: z.string().min(1).optional(),
        domain: z.string().optional(),
        settings: z.record(z.string(), z.any()).optional(),
        branding: z.record(z.string(), z.any()).optional()
    })
};

// All schemas are already exported above as individual constants