import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

export interface SessionMemoryContext {
    sessionId: string;
    agentId: string;
    userId?: string;
    organizationId: string;
    conversationHistory: Array<{
        role: 'user' | 'assistant' | 'system' | 'tool';
        content: string;
        timestamp: Date;
        metadata?: any;
    }>;
    variables: Record<string, any>;
    metadata: Record<string, any>;
    lastActivity: Date;
    messageCount: number;
    tokenUsage: {
        total: number;
        input: number;
        output: number;
    };
}

@Injectable()
export class SessionMemoryService {
    private readonly logger = new Logger(SessionMemoryService.name);

    constructor(
        private prisma: PrismaService,
        private eventEmitter: EventEmitter2,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
    ) {}

    async initializeAgentMemory(agentId: string, config: any): Promise<void> {
        try {
            // Initialize agent-specific memory configuration
            const memoryConfig = {
                agentId,
                type: config.type,
                systemPrompt: config.systemPrompt,
                instructions: config.instructions,
                capabilities: config.capabilities,
                memoryWindow: config.capabilities?.memoryWindow || 10,
                createdAt: new Date(),
            };

            // Store in cache for quick access
            await this.cacheManager.set(`agent_memory:${agentId}`, memoryConfig, 3600000); // 1 hour

            // Emit memory initialized event
            this.eventEmitter.emit('agent.memory.initialized', {
                agentId,
                config: memoryConfig,
                timestamp: Date.now(),
            });

            this.logger.log(`Agent memory initialized for agent: ${agentId}`);
        } catch (error) {
            this.logger.error(`Failed to initialize agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getOrCreateSession(
        agentId: string,
        sessionId: string,
        userId?: string,
    ): Promise<SessionMemoryContext> {
        try {
            // Try to get from cache first
            let session = await this.cacheManager.get(`session:${sessionId}`) as SessionMemoryContext;

            if (!session) {
                // Get from database
                const dbSession = await this.prisma.agentSessionNew.findUnique({
                    where: { id: sessionId },
                    include: {
                        messages: {
                            orderBy: { createdAt: 'asc' },
                            take: 50, // Limit to recent messages
                        },
                    },
                });

                if (dbSession) {
                    session = {
                        sessionId: dbSession.id,
                        agentId: dbSession.agentId,
                        userId: dbSession.userId,
                        organizationId: dbSession.organizationId,
                        conversationHistory: dbSession.messages.map(msg => ({
                            role: msg.role as any,
                            content: msg.content,
                            timestamp: msg.createdAt,
                            metadata: msg.metadata as any,
                        })),
                        variables: dbSession.variables as Record<string, any> || {},
                        metadata: dbSession.metadata as Record<string, any> || {},
                        lastActivity: dbSession.lastActivity,
                        messageCount: dbSession.messageCount,
                        tokenUsage: dbSession.tokenUsage as any || { total: 0, input: 0, output: 0 },
                    };
                } else {
                    // Create new session
                    const agent = await this.prisma.agentInstance.findUnique({
                        where: { id: agentId },
                        select: { organizationId: true },
                    });

                    if (!agent) {
                        throw new Error(`Agent ${agentId} not found`);
                    }

                    const newSession = await this.prisma.agentSessionNew.create({
                        data: {
                            id: sessionId,
                            agentId,
                            userId,
                            organizationId: agent.organizationId,
                            status: 'active',
                            variables: {},
                            metadata: {},
                            messageCount: 0,
                            tokenUsage: { total: 0, input: 0, output: 0 },
                            lastActivity: new Date(),
                        },
                    });

                    session = {
                        sessionId: newSession.id,
                        agentId: newSession.agentId,
                        userId: newSession.userId,
                        organizationId: newSession.organizationId,
                        conversationHistory: [],
                        variables: {},
                        metadata: {},
                        lastActivity: newSession.lastActivity,
                        messageCount: 0,
                        tokenUsage: { total: 0, input: 0, output: 0 },
                    };

                    // Emit session created event
                    this.eventEmitter.emit('session.created', {
                        sessionId,
                        agentId,
                        userId,
                        organizationId: agent.organizationId,
                        timestamp: Date.now(),
                    });
                }

                // Cache the session
                await this.cacheManager.set(`session:${sessionId}`, session, 1800000); // 30 minutes
            }

            return session;
        } catch (error) {
            this.logger.error(`Failed to get or create session: ${error.message}`, error.stack);
            throw error;
        }
    }

    async addMessage(
        sessionId: string,
        message: {
            role: 'user' | 'assistant' | 'system' | 'tool';
            content: string;
            type?: string;
            tokens?: number;
            cost?: number;
            metadata?: any;
        },
    ): Promise<void> {
        try {
            // Add message to database
            await this.prisma.sessionMessage.create({
                data: {
                    sessionId,
                    role: message.role,
                    content: message.content,
                    type: message.type || 'text',
                    tokens: message.tokens,
                    cost: message.cost,
                    metadata: message.metadata || {},
                },
            });

            // Update session
            await this.prisma.agentSessionNew.update({
                where: { id: sessionId },
                data: {
                    messageCount: { increment: 1 },
                    lastActivity: new Date(),
                    tokenUsage: message.tokens ? {
                        update: {
                            total: { increment: message.tokens },
                            [message.role === 'user' ? 'input' : 'output']: { increment: message.tokens },
                        }
                    } : undefined,
                },
            });

            // Update cache
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`) as SessionMemoryContext;
            if (cachedSession) {
                cachedSession.conversationHistory.push({
                    role: message.role,
                    content: message.content,
                    timestamp: new Date(),
                    metadata: message.metadata,
                });
                cachedSession.messageCount += 1;
                cachedSession.lastActivity = new Date();
                if (message.tokens) {
                    cachedSession.tokenUsage.total += message.tokens;
                    if (message.role === 'user') {
                        cachedSession.tokenUsage.input += message.tokens;
                    } else {
                        cachedSession.tokenUsage.output += message.tokens;
                    }
                }

                // Keep only recent messages in cache
                if (cachedSession.conversationHistory.length > 50) {
                    cachedSession.conversationHistory = cachedSession.conversationHistory.slice(-50);
                }

                await this.cacheManager.set(`session:${sessionId}`, cachedSession, 1800000);
            }

            // Emit message added event
            this.eventEmitter.emit('session.message.added', {
                sessionId,
                message: {
                    role: message.role,
                    content: message.content,
                    tokens: message.tokens,
                    timestamp: Date.now(),
                },
            });

        } catch (error) {
            this.logger.error(`Failed to add message to session: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getConversationHistory(
        sessionId: string,
        limit: number = 20,
    ): Promise<Array<{ role: string; content: string; timestamp: Date }>> {
        try {
            // Try cache first
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`) as SessionMemoryContext;
            if (cachedSession) {
                return cachedSession.conversationHistory.slice(-limit);
            }

            // Get from database
            const messages = await this.prisma.sessionMessage.findMany({
                where: { sessionId },
                orderBy: { createdAt: 'desc' },
                take: limit,
                select: {
                    role: true,
                    content: true,
                    createdAt: true,
                },
            });

            return messages.reverse().map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.createdAt,
            }));
        } catch (error) {
            this.logger.error(`Failed to get conversation history: ${error.message}`, error.stack);
            return [];
        }
    }

    async updateSessionMemory(
        sessionId: string,
        updates: {
            lastInput?: any;
            lastOutput?: any;
            lastActivity?: Date;
            messageCount?: number;
            tokenUsage?: any;
            variables?: Record<string, any>;
            metadata?: Record<string, any>;
        },
    ): Promise<void> {
        try {
            // Update database
            await this.prisma.agentSessionNew.update({
                where: { id: sessionId },
                data: {
                    lastActivity: updates.lastActivity || new Date(),
                    messageCount: updates.messageCount,
                    tokenUsage: updates.tokenUsage,
                    variables: updates.variables,
                    metadata: updates.metadata,
                },
            });

            // Update cache
            const cachedSession = await this.cacheManager.get(`session:${sessionId}`) as SessionMemoryContext;
            if (cachedSession) {
                if (updates.lastActivity) cachedSession.lastActivity = updates.lastActivity;
                if (updates.messageCount) cachedSession.messageCount = updates.messageCount;
                if (updates.tokenUsage) cachedSession.tokenUsage = { ...cachedSession.tokenUsage, ...updates.tokenUsage };
                if (updates.variables) cachedSession.variables = { ...cachedSession.variables, ...updates.variables };
                if (updates.metadata) cachedSession.metadata = { ...cachedSession.metadata, ...updates.metadata };

                await this.cacheManager.set(`session:${sessionId}`, cachedSession, 1800000);
            }

            // Emit session updated event
            this.eventEmitter.emit('session.updated', {
                sessionId,
                updates,
                timestamp: Date.now(),
            });

        } catch (error) {
            this.logger.error(`Failed to update session memory: ${error.message}`, error.stack);
            throw error;
        }
    }

    async updateAgentMemory(
        agentId: string,
        updates: {
            systemPrompt?: string;
            instructions?: string;
            capabilities?: any;
        },
    ): Promise<void> {
        try {
            // Update cache
            const cachedConfig = await this.cacheManager.get(`agent_memory:${agentId}`) as any;
            if (cachedConfig) {
                if (updates.systemPrompt) cachedConfig.systemPrompt = updates.systemPrompt;
                if (updates.instructions) cachedConfig.instructions = updates.instructions;
                if (updates.capabilities) cachedConfig.capabilities = { ...cachedConfig.capabilities, ...updates.capabilities };

                await this.cacheManager.set(`agent_memory:${agentId}`, cachedConfig, 3600000);
            }

            // Emit agent memory updated event
            this.eventEmitter.emit('agent.memory.updated', {
                agentId,
                updates,
                timestamp: Date.now(),
            });

        } catch (error) {
            this.logger.error(`Failed to update agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }

    async clearAgentMemory(agentId: string): Promise<void> {
        try {
            // Clear cache
            await this.cacheManager.del(`agent_memory:${agentId}`);

            // Clear all sessions for this agent from cache
            const sessions = await this.prisma.agentSessionNew.findMany({
                where: { agentId },
                select: { id: true },
            });

            for (const session of sessions) {
                await this.cacheManager.del(`session:${session.id}`);
            }

            // Emit agent memory cleared event
            this.eventEmitter.emit('agent.memory.cleared', {
                agentId,
                timestamp: Date.now(),
            });

            this.logger.log(`Agent memory cleared for agent: ${agentId}`);
        } catch (error) {
            this.logger.error(`Failed to clear agent memory: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getSessionStats(sessionId: string): Promise<{
        messageCount: number;
        tokenUsage: any;
        duration: number;
        lastActivity: Date;
    } | null> {
        try {
            const session = await this.prisma.agentSessionNew.findUnique({
                where: { id: sessionId },
                select: {
                    messageCount: true,
                    tokenUsage: true,
                    createdAt: true,
                    lastActivity: true,
                },
            });

            if (!session) return null;

            return {
                messageCount: session.messageCount,
                tokenUsage: session.tokenUsage as any,
                duration: session.lastActivity.getTime() - session.createdAt.getTime(),
                lastActivity: session.lastActivity,
            };
        } catch (error) {
            this.logger.error(`Failed to get session stats: ${error.message}`, error.stack);
            return null;
        }
    }

    async cleanupInactiveSessions(inactiveThreshold: number = 86400000): Promise<number> {
        try {
            const cutoffDate = new Date(Date.now() - inactiveThreshold);

            // Update inactive sessions
            const result = await this.prisma.agentSessionNew.updateMany({
                where: {
                    lastActivity: { lt: cutoffDate },
                    status: 'active',
                },
                data: {
                    status: 'inactive',
                },
            });

            // Clear from cache
            const inactiveSessions = await this.prisma.agentSessionNew.findMany({
                where: {
                    lastActivity: { lt: cutoffDate },
                    status: 'inactive',
                },
                select: { id: true },
            });

            for (const session of inactiveSessions) {
                await this.cacheManager.del(`session:${session.id}`);
            }

            this.logger.log(`Cleaned up ${result.count} inactive sessions`);
            return result.count;
        } catch (error) {
            this.logger.error(`Failed to cleanup inactive sessions: ${error.message}`, error.stack);
            return 0;
        }
    }
}