/**
 * 🎯 Centralized API Handler
 * Production-grade API route handler with authentication, validation, and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema } from 'zod';
import { getAuthenticatedSession, hasRequiredRole, hasPermission } from '@/lib/auth/auth-service';
import { SessionUser, Role } from '@/lib/types/auth';
import { handleApiError, unauthorizedResponse, forbiddenResponse } from '@/lib/utils/api-responses';

// Extended request types
export interface AuthenticatedRequest extends NextRequest {
  user: SessionUser;
}

export interface ValidatedRequest extends NextRequest {
  validatedBody?: any;
  validatedQuery?: any;
  validatedParams?: any;
}

export interface ApiHandlerRequest extends AuthenticatedRequest, ValidatedRequest {}

// Handler function type
export type ApiHandler<T = any> = (
  req: ApiHandlerRequest,
  context?: { params?: any }
) => Promise<NextResponse>;

// Configuration options
export interface ApiHandlerOptions {
  // Authentication options
  requireAuth?: boolean;
  requiredRoles?: Role[];
  requiredPermissions?: string[];
  
  // Validation schemas
  bodySchema?: ZodSchema;
  querySchema?: ZodSchema;
  paramsSchema?: ZodSchema;
  
  // Other options
  allowedMethods?: string[];
}

/**
 * Create an API handler with authentication, validation, and error handling
 */
export function createApiHandler(
  handler: ApiHandler,
  options: ApiHandlerOptions = {}
) {
  return async (
    request: NextRequest,
    context?: { params?: any }
  ): Promise<NextResponse> => {
    try {
      // Check allowed methods
      if (options.allowedMethods && !options.allowedMethods.includes(request.method)) {
        return new NextResponse('Method Not Allowed', { status: 405 });
      }

      // Create extended request object
      const extendedRequest = request as ApiHandlerRequest;

      // Handle authentication
      if (options.requireAuth !== false) {
        const session = await getAuthenticatedSession(request);
        
        if (!session) {
          return unauthorizedResponse();
        }

        extendedRequest.user = session.user;

        // Check role-based access
        if (options.requiredRoles && options.requiredRoles.length > 0) {
          const hasRole = options.requiredRoles.some(role => 
            hasRequiredRole(session.user, [role])
          );

          if (!hasRole) {
            return forbiddenResponse('Insufficient role permissions');
          }
        }

        // Check permission-based access
        if (options.requiredPermissions && options.requiredPermissions.length > 0) {
          const hasAllPermissions = options.requiredPermissions.every(permission =>
            hasPermission(session.user, permission)
          );

          if (!hasAllPermissions) {
            return forbiddenResponse('Insufficient permissions');
          }
        }
      }

      // Handle validation
      if (options.bodySchema && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json();
          extendedRequest.validatedBody = options.bodySchema.parse(body);
        } catch (error) {
          return handleApiError(error);
        }
      }

      if (options.querySchema) {
        try {
          const url = new URL(request.url);
          const queryParams: Record<string, any> = {};
          
          url.searchParams.forEach((value, key) => {
            queryParams[key] = value;
          });
          
          extendedRequest.validatedQuery = options.querySchema.parse(queryParams);
        } catch (error) {
          return handleApiError(error);
        }
      }

      if (options.paramsSchema && context?.params) {
        try {
          extendedRequest.validatedParams = options.paramsSchema.parse(context.params);
        } catch (error) {
          return handleApiError(error);
        }
      }

      // Call the actual handler
      return await handler(extendedRequest, context);

    } catch (error) {
      return handleApiError(error);
    }
  };
}

/**
 * Convenience function for GET handlers
 */
export function createGetHandler(
  handler: ApiHandler,
  options: Omit<ApiHandlerOptions, 'allowedMethods'> = {}
) {
  return createApiHandler(handler, {
    ...options,
    allowedMethods: ['GET'],
  });
}

/**
 * Convenience function for POST handlers
 */
export function createPostHandler(
  handler: ApiHandler,
  options: Omit<ApiHandlerOptions, 'allowedMethods'> = {}
) {
  return createApiHandler(handler, {
    ...options,
    allowedMethods: ['POST'],
  });
}

/**
 * Convenience function for PUT handlers
 */
export function createPutHandler(
  handler: ApiHandler,
  options: Omit<ApiHandlerOptions, 'allowedMethods'> = {}
) {
  return createApiHandler(handler, {
    ...options,
    allowedMethods: ['PUT'],
  });
}

/**
 * Convenience function for DELETE handlers
 */
export function createDeleteHandler(
  handler: ApiHandler,
  options: Omit<ApiHandlerOptions, 'allowedMethods'> = {}
) {
  return createApiHandler(handler, {
    ...options,
    allowedMethods: ['DELETE'],
  });
}

/**
 * Convenience function for handlers that don't require authentication
 */
export function createPublicHandler(
  handler: ApiHandler,
  options: Omit<ApiHandlerOptions, 'requireAuth'> = {}
) {
  return createApiHandler(handler, {
    ...options,
    requireAuth: false,
  });
}
