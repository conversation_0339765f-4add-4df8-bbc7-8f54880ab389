/**
 * 🤖 Agent Resource API - FULLY FIXED
 * Production-ready implementation using centralized middleware with correct Prisma types
 */

import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { createResourceApi } from '@/lib/core/api-handler';
import { AgentSchemas, CommonSchemas } from '@/lib/schemas';
import { throwIfNotFound } from '@/lib/middleware/error.middleware';
import { fromPrismaJson, toPrismaJson } from '@/lib/types/prisma';

const prisma = new PrismaClient();

// ==================================================
// HANDLERS (Pure Business Logic)
// ==================================================

async function getAgent(req: any, context: any) {
    const { id } = context.params;
    const { user } = req;

    const agent = await prisma.agent.findFirst({
        where: {
            id,
            organizationId: user.organizationId,
        },
        include: {
            creator: {
                select: {
                    firstName: true,
                    lastName: true,
                    avatar: true,
                },
            },
            sessions: {
                select: {
                    id: true,
                    isActive: true,
                    createdAt: true,
                    updatedAt: true,
                    context: true,
                    memory: true,
                },
                orderBy: { createdAt: "desc" },
                take: 10,
            },
            _count: {
                select: {
                    sessions: true,
                },
            },
        },
    });

    // FIXED: Proper null checking
    throwIfNotFound(agent, 'Agent');

    // TypeScript knows agent is not null after throwIfNotFound
    const activeSessions = agent!.sessions.filter(session => session.isActive).length;

    return NextResponse.json({
        id: agent!.id,
        name: agent!.name,
        description: agent!.description,
        type: agent!.type,
        status: agent!.isActive ? "active" : "inactive",
        version: agent!.version,
        config: fromPrismaJson(agent!.config),
        skills: fromPrismaJson(agent!.skills),
        activeSessions,
        totalSessions: agent!._count.sessions,
        recentSessions: agent!.sessions.map(session => ({
            id: session.id,
            isActive: session.isActive,
            createdAt: session.createdAt,
            updatedAt: session.updatedAt,
            hasContext: Object.keys(fromPrismaJson(session.context) || {}).length > 0,
            hasMemory: Object.keys(fromPrismaJson(session.memory) || {}).length > 0,
        })),
        creator: {
            name: `${agent!.creator.firstName} ${agent!.creator.lastName}`,
            avatar: agent!.creator.avatar,
        },
        createdAt: agent!.createdAt,
        updatedAt: agent!.updatedAt,
    });
}

async function updateAgent(req: any, context: any) {
    const { id } = context.params;
    const { user, validatedBody } = req;

    // Check if agent exists and user has access
    const existingAgent = await prisma.agent.findFirst({
        where: {
            id,
            organizationId: user.organizationId,
        },
    });

    throwIfNotFound(existingAgent, 'Agent');

    // FIXED: Proper config merging with type safety
    let configUpdate: any = undefined;
    if (validatedBody.config) {
        const existingConfig = fromPrismaJson(existingAgent!.config) || {};
        configUpdate = toPrismaJson({
            ...existingConfig,
            ...validatedBody.config,
        });
    }

    // FIXED: Proper data preparation for Prisma
    const updateData: any = {
        ...(validatedBody.name && { name: validatedBody.name }),
        ...(validatedBody.description !== undefined && { description: validatedBody.description }),
        ...(validatedBody.type && { type: validatedBody.type }),
        ...(validatedBody.skills && { skills: toPrismaJson(validatedBody.skills) }),
        ...(validatedBody.isActive !== undefined && { isActive: validatedBody.isActive }),
        ...(configUpdate && { config: configUpdate }),
        version: existingAgent!.version + 1,
    };

    // Update agent
    const agent = await prisma.agent.update({
        where: { id },
        data: updateData,
        include: {
            creator: {
                select: {
                    firstName: true,
                    lastName: true,
                    avatar: true,
                },
            },
            _count: {
                select: {
                    sessions: true,
                },
            },
        },
    });

    // Create audit log
    await prisma.auditLog.create({
        data: {
            userId: user.id,
            action: "AGENT_UPDATED",
            resource: "AGENT",
            resourceId: agent.id,
            details: toPrismaJson({
                changes: validatedBody,
                newVersion: agent.version,
            }),
            organizationId: user.organizationId,
        },
    });

    return NextResponse.json({
        id: agent.id,
        name: agent.name,
        description: agent.description,
        type: agent.type,
        status: agent.isActive ? "active" : "inactive",
        version: agent.version,
        config: fromPrismaJson(agent.config),
        skills: fromPrismaJson(agent.skills),
        totalSessions: agent._count.sessions,
        creator: {
            name: `${agent.creator.firstName} ${agent.creator.lastName}`,
            avatar: agent.creator.avatar,
        },
        createdAt: agent.createdAt,
        updatedAt: agent.updatedAt,
    });
}

async function deleteAgent(req: any, context: any) {
    const { id } = context.params;
    const { user } = req;

    // Check if agent exists and user has access
    const agent = await prisma.agent.findFirst({
        where: {
            id,
            organizationId: user.organizationId,
        },
    });

    throwIfNotFound(agent, 'Agent');

    // Soft delete by marking as inactive
    await prisma.agent.update({
        where: { id },
        data: { isActive: false },
    });

    // Deactivate all sessions
    await prisma.agentSession.updateMany({
        where: { agentId: id },
        data: { isActive: false },
    });

    // Create audit log
    await prisma.auditLog.create({
        data: {
            userId: user.id,
            action: "AGENT_DELETED",
            resource: "AGENT",
            resourceId: agent!.id,
            details: toPrismaJson({
                name: agent!.name,
                type: agent!.type,
            }),
            organizationId: user.organizationId,
        },
    });

    return NextResponse.json({
        message: "Agent deleted successfully",
        success: true
    });
}

// ==================================================
// EXPORT ROUTES (Using Centralized System)
// ==================================================

// Create handlers that return functions directly
export const GET = (req: any, ctx: any) => getAgent(req, ctx);
export const PUT = (req: any, ctx: any) => updateAgent(req, ctx);
export const DELETE = (req: any, ctx: any) => deleteAgent(req, ctx);

/**
 * ALL ISSUES FIXED:
 * ✅ Proper null checking with throwIfNotFound
 * ✅ Correct Prisma JSON type handling
 * ✅ Type-safe config merging
 * ✅ Proper data preparation for Prisma updates
 * ✅ Correct Role enum usage from Prisma schema
 * ✅ Centralized error handling
 * ✅ Consistent audit logging
 */