import {
    Controller,
    Get,
    Post,
    Patch,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Request,
    HttpCode,
    HttpStatus,
    ParseUUIDPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { NotificationsService, CreateNotificationDto, NotificationDto } from './notifications.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { TenantGuard } from '../auth/tenant.guard';

@ApiTags('notifications')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('notifications')
export class NotificationsController {
    constructor(private readonly notificationsService: NotificationsService) { }

    @Get()
    @ApiOperation({ summary: 'Get user notifications' })
    @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
    async getUserNotifications(
        @Request() req: any,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 20
    ) {
        return this.notificationsService.getUserNotifications(
            req.user.id,
            req.user.organizationId,
            page,
            limit
        );
    }

    @Patch(':id/read')
    @ApiOperation({ summary: 'Mark notification as read' })
    @ApiResponse({ status: 200, description: 'Notification marked as read' })
    @HttpCode(HttpStatus.OK)
    async markAsRead(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ) {
        await this.notificationsService.markAsRead(id, req.user.id);
        return { message: 'Notification marked as read' };
    }

    @Patch('read-all')
    @ApiOperation({ summary: 'Mark all notifications as read' })
    @ApiResponse({ status: 200, description: 'All notifications marked as read' })
    @HttpCode(HttpStatus.OK)
    async markAllAsRead(@Request() req: any) {
        await this.notificationsService.markAllAsRead(req.user.id, req.user.organizationId);
        return { message: 'All notifications marked as read' };
    }

    @Delete(':id')
    @ApiOperation({ summary: 'Delete notification' })
    @ApiResponse({ status: 200, description: 'Notification deleted' })
    @HttpCode(HttpStatus.OK)
    async deleteNotification(
        @Param('id', ParseUUIDPipe) id: string,
        @Request() req: any
    ) {
        await this.notificationsService.deleteNotification(id, req.user.id);
        return { message: 'Notification deleted' };
    }
}