import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { AgentsService } from './agents.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantGuard } from '../auth/tenant.guard';
import { AgentOrchestratorService, CreateAgentInstanceDto, UpdateAgentInstanceDto } from './services/agent-orchestrator.service';
import { AgentTemplateService } from './services/agent-template.service';
import { AgentCommunicationService } from './services/agent-communication.service';
import { SessionMemoryService } from './services/session-memory.service';
import { TaskTrackerService } from './services/task-tracker.service';
import { AgentType, AgentStatus } from '@prisma/client';

@Controller('agents')
@UseGuards(JwtAuthGuard, RolesGuard, TenantGuard)
export class AgentsController {
  constructor(
    private readonly agentsService: AgentsService,
    private readonly agentOrchestrator: AgentOrchestratorService,
    private readonly agentTemplateService: AgentTemplateService,
    private readonly agentCommunicationService: AgentCommunicationService,
    private readonly sessionMemoryService: SessionMemoryService,
    private readonly taskTrackerService: TaskTrackerService,
  ) {}

  // Agent Instance Management
  @Get()
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentInstances(
    @Req() req,
    @Query('type') type?: AgentType,
    @Query('status') status?: AgentStatus,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    return this.agentOrchestrator.getAgentInstances(
      req.user.organizationId,
      {
        type,
        status,
        search,
        limit: limit ? parseInt(limit.toString()) : 20,
        offset: offset ? parseInt(offset.toString()) : 0,
      }
    );
  }

  @Get(':id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentInstance(@Req() req, @Param('id') id: string) {
    return this.agentOrchestrator.getAgentInstance(id, req.user.organizationId);
  }

  @Post()
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async createAgentInstance(@Req() req, @Body() createDto: CreateAgentInstanceDto) {
    return this.agentOrchestrator.createAgentInstance(
      createDto,
      req.user.id,
      req.user.organizationId
    );
  }

  @Put(':id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async updateAgentInstance(
    @Req() req,
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentInstanceDto
  ) {
    return this.agentOrchestrator.updateAgentInstance(
      id,
      updateDto,
      req.user.id,
      req.user.organizationId
    );
  }

  @Delete(':id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async deleteAgentInstance(@Req() req, @Param('id') id: string) {
    return this.agentOrchestrator.deleteAgentInstance(
      id,
      req.user.id,
      req.user.organizationId
    );
  }

  // Agent Execution
  @Post(':id/execute')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN', 'VIEWER')
  async executeAgent(
    @Req() req,
    @Param('id') id: string,
    @Body() executionData: { input: any; sessionId?: string; context?: any; metadata?: any }
  ) {
    const sessionId = executionData.sessionId || `session_${Date.now()}`;
    
    return this.agentOrchestrator.executeAgent({
      agentId: id,
      sessionId,
      userId: req.user.id,
      organizationId: req.user.organizationId,
      input: executionData.input,
      context: executionData.context,
      metadata: executionData.metadata,
    });
  }

  // Agent Templates
  @Get('templates')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN', 'VIEWER')
  async getAgentTemplates(
    @Req() req,
    @Query('category') category?: string,
    @Query('type') type?: AgentType,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    return this.agentTemplateService.getTemplates(
      req.user.organizationId,
      {
        category,
        type,
        search,
        limit: limit ? parseInt(limit.toString()) : 20,
        offset: offset ? parseInt(offset.toString()) : 0,
      }
    );
  }

  @Get('templates/:id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN', 'VIEWER')
  async getAgentTemplate(@Req() req, @Param('id') id: string) {
    return this.agentTemplateService.getTemplate(id, req.user.organizationId);
  }

  @Post('templates')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async createAgentTemplate(@Req() req, @Body() createDto: any) {
    return this.agentTemplateService.createTemplate(
      createDto,
      req.user.id,
      req.user.organizationId
    );
  }

  @Put('templates/:id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async updateAgentTemplate(
    @Req() req,
    @Param('id') id: string,
    @Body() updateDto: any
  ) {
    return this.agentTemplateService.updateTemplate(
      id,
      updateDto,
      req.user.id,
      req.user.organizationId
    );
  }

  @Delete('templates/:id')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async deleteAgentTemplate(@Req() req, @Param('id') id: string) {
    return this.agentTemplateService.deleteTemplate(
      id,
      req.user.id,
      req.user.organizationId
    );
  }

  // Agent Sessions
  @Get(':id/sessions')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentSessions(
    @Req() req,
    @Param('id') id: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    const sessions = await this.agentsService.getAgentSessions(
      id,
      req.user.organizationId,
      {
        limit: limit ? parseInt(limit.toString()) : 20,
        offset: offset ? parseInt(offset.toString()) : 0,
      }
    );
    return sessions;
  }

  @Get('sessions/:sessionId')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN', 'VIEWER')
  async getAgentSession(@Req() req, @Param('sessionId') sessionId: string) {
    return this.sessionMemoryService.getSessionStats(sessionId);
  }

  @Get('sessions/:sessionId/messages')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN', 'VIEWER')
  async getSessionMessages(
    @Req() req,
    @Param('sessionId') sessionId: string,
    @Query('limit') limit?: number,
  ) {
    return this.sessionMemoryService.getConversationHistory(
      sessionId,
      limit ? parseInt(limit.toString()) : 20
    );
  }

  // Agent Tasks
  @Get(':id/tasks')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentTasks(
    @Req() req,
    @Param('id') id: string,
    @Query('status') status?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    return this.agentsService.getAgentTasks(
      id,
      req.user.organizationId,
      {
        status,
        limit: limit ? parseInt(limit.toString()) : 20,
        offset: offset ? parseInt(offset.toString()) : 0,
      }
    );
  }

  @Post(':id/tasks')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async createAgentTask(
    @Req() req,
    @Param('id') id: string,
    @Body() taskData: any
  ) {
    return this.taskTrackerService.createTask(
      id,
      taskData.sessionId || `session_${Date.now()}`,
      {
        name: taskData.name,
        description: taskData.description,
        type: taskData.type || 'general',
        priority: taskData.priority || 1,
        input: taskData.input,
        context: taskData.context,
        metadata: taskData.metadata,
        dependencies: taskData.dependencies,
        maxRetries: taskData.maxRetries,
        timeout: taskData.timeout,
      }
    );
  }

  // Agent Communication
  @Post(':id/messages')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async sendAgentMessage(
    @Req() req,
    @Param('id') fromAgentId: string,
    @Body() messageData: { toAgentId: string; content: any; options?: any }
  ) {
    return this.agentCommunicationService.sendMessage(
      fromAgentId,
      messageData.toAgentId,
      messageData.content,
      messageData.options || {}
    );
  }

  @Get(':id/messages')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentMessages(
    @Req() req,
    @Param('id') agentId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('status') status?: string,
    @Query('type') type?: string,
  ) {
    return this.agentCommunicationService.getAgentMessages(
      agentId,
      {
        limit: limit ? parseInt(limit.toString()) : 50,
        offset: offset ? parseInt(offset.toString()) : 0,
        status: status as any,
        type: type as any,
      }
    );
  }

  // Agent Metrics
  @Get(':id/metrics')
  @Roles('DEVELOPER', 'ORG_ADMIN', 'SUPER_ADMIN')
  async getAgentMetrics(
    @Req() req,
    @Param('id') id: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    let dateRange;
    if (from && to) {
      dateRange = {
        from: new Date(from),
        to: new Date(to),
      };
    }
    
    return this.agentOrchestrator.getAgentMetrics(
      id,
      req.user.organizationId,
      dateRange
    );
  }
}