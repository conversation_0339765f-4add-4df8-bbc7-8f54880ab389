"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const sessions_service_1 = require("../sessions/sessions.service");
const bcrypt = require("bcryptjs");
const speakeasy = require("speakeasy");
const qrcode = require("qrcode");
const crypto = require("crypto");
const client_1 = require("@prisma/client");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let AuthService = class AuthService {
    constructor(prisma, jwtService, configService, sessionsService, cacheManager) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.configService = configService;
        this.sessionsService = sessionsService;
        this.cacheManager = cacheManager;
        this.jwtSecret = this.configService.get('JWT_SECRET') || 'your-secret-key';
        this.jwtRefreshSecret = this.configService.get('JWT_REFRESH_SECRET') || 'your-refresh-secret-key';
        this.jwtExpiresIn = this.configService.get('JWT_EXPIRES_IN') || '15m';
        this.jwtRefreshExpiresIn = this.configService.get('JWT_REFRESH_EXPIRES_IN') || '7d';
    }
    async register(registerDto, userAgent, ipAddress) {
        const { email, password, firstName, lastName, organizationName, organizationSlug } = registerDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const existingOrg = await this.prisma.organization.findUnique({
            where: { slug: organizationSlug },
        });
        if (existingOrg) {
            throw new common_1.ConflictException('Organization slug is already taken');
        }
        const hashedPassword = await bcrypt.hash(password, 12);
        const result = await this.prisma.$transaction(async (tx) => {
            const organization = await tx.organization.create({
                data: {
                    name: organizationName,
                    slug: organizationSlug,
                    settings: {
                        theme: 'light',
                        language: 'en',
                        timezone: 'UTC',
                        dateFormat: 'MM/dd/yyyy',
                        timeFormat: '12h',
                        currency: 'USD',
                        features: {
                            workflows: true,
                            agents: true,
                            tools: true,
                            analytics: true,
                            apiAccess: true,
                        },
                        security: {
                            mfaRequired: false,
                            sessionTimeout: 24,
                            passwordPolicy: {
                                minLength: 8,
                                requireUppercase: true,
                                requireLowercase: true,
                                requireNumbers: true,
                                requireSpecialChars: true,
                            },
                        },
                    },
                    branding: {
                        primaryColor: '#3b82f6',
                        secondaryColor: '#1e40af',
                        logo: null,
                        favicon: null,
                        customCSS: null,
                    },
                },
            });
            const user = await tx.user.create({
                data: {
                    email,
                    password: hashedPassword,
                    firstName,
                    lastName,
                    role: client_1.Role.ORG_ADMIN,
                    organizationId: organization.id,
                    preferences: {
                        theme: 'system',
                        language: 'en',
                        timezone: 'UTC',
                        notifications: {
                            email: true,
                            push: true,
                            workflow: true,
                            agent: true,
                            system: true,
                        },
                        viewMode: 'grid',
                        sidebarCollapsed: false,
                    },
                },
                include: {
                    organization: true,
                },
            });
            await tx.auditLog.create({
                data: {
                    userId: user.id,
                    organizationId: organization.id,
                    action: 'USER_REGISTERED',
                    resource: 'user',
                    resourceId: user.id,
                    details: {
                        method: 'email',
                        userAgent,
                        ipAddress,
                        organizationCreated: true,
                    },
                },
            });
            return { user, organization };
        });
        const tokens = await this.generateTokens(result.user, false, ipAddress, userAgent);
        const session = await this.sessionsService.createSession(result.user.id, result.organization.id, {
            loginMethod: 'email',
            userAgent,
            ipAddress,
            mfaVerified: false,
        });
        return {
            user: {
                id: result.user.id,
                email: result.user.email,
                firstName: result.user.firstName,
                lastName: result.user.lastName,
                role: result.user.role,
                avatar: result.user.avatar,
                organization: {
                    id: result.organization.id,
                    name: result.organization.name,
                    slug: result.organization.slug,
                    settings: result.organization.settings,
                    branding: result.organization.branding,
                },
                permissions: this.getRolePermissions(result.user.role),
                preferences: result.user.preferences,
            },
            tokens,
            session: {
                id: session.id,
                expiresAt: session.expiresAt.toISOString(),
            },
        };
    }
    async login(loginDto, userAgent, ipAddress) {
        const { email, password, organizationSlug, mfaCode, rememberMe } = loginDto;
        const user = await this.prisma.user.findFirst({
            where: {
                email,
                isActive: true,
                ...(organizationSlug && {
                    organization: {
                        slug: organizationSlug,
                    },
                }),
            },
            include: {
                organization: true,
            },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (user.mfaEnabled && !mfaCode) {
            throw new common_1.UnauthorizedException('MFA code required');
        }
        if (user.mfaEnabled && mfaCode) {
            const isValidMFA = speakeasy.totp.verify({
                secret: user.mfaSecret,
                encoding: 'base32',
                token: mfaCode,
                window: 2,
            });
            if (!isValidMFA) {
                throw new common_1.UnauthorizedException('Invalid MFA code');
            }
        }
        await this.prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
        });
        const tokens = await this.generateTokens(user, rememberMe, ipAddress, userAgent);
        const session = await this.sessionsService.createSession(user.id, user.organizationId, {
            loginMethod: 'email',
            userAgent,
            ipAddress,
            mfaVerified: user.mfaEnabled,
            rememberMe,
        });
        await this.logAuditEvent(user.id, user.organizationId, 'USER_LOGIN', 'user', user.id, {
            method: 'email',
            userAgent,
            ipAddress,
            mfaUsed: user.mfaEnabled,
        });
        return {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                avatar: user.avatar,
                organization: {
                    id: user.organization.id,
                    name: user.organization.name,
                    slug: user.organization.slug,
                    settings: user.organization.settings,
                    branding: user.organization.branding,
                },
                permissions: this.getRolePermissions(user.role),
                preferences: user.preferences,
            },
            tokens,
            session: {
                id: session.id,
                expiresAt: session.expiresAt.toISOString(),
            },
        };
    }
    async refreshToken(refreshTokenDto, ipAddress, userAgent) {
        const { refreshToken } = refreshTokenDto;
        const storedToken = await this.prisma.refreshToken.findUnique({
            where: { token: refreshToken },
            include: {
                user: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (!storedToken || !storedToken.isActive || storedToken.expiresAt < new Date()) {
            throw new common_1.UnauthorizedException('Invalid or expired refresh token');
        }
        if (!storedToken.user.isActive) {
            throw new common_1.UnauthorizedException('User account is deactivated');
        }
        await this.prisma.refreshToken.update({
            where: { id: storedToken.id },
            data: {
                isActive: false,
                revokedAt: new Date(),
            },
        });
        const tokens = await this.generateTokens(storedToken.user, false, ipAddress, userAgent);
        const sessions = await this.prisma.session.findMany({
            where: {
                userId: storedToken.user.id,
                isActive: true,
                expiresAt: { gt: new Date() }
            },
            orderBy: { updatedAt: 'desc' },
            take: 1,
        });
        const session = sessions[0];
        if (session) {
            await this.prisma.session.update({
                where: { id: session.id },
                data: { updatedAt: new Date() },
            });
        }
        await this.logAuditEvent(storedToken.user.id, storedToken.user.organizationId, 'TOKEN_REFRESHED', 'auth', storedToken.user.id, { ipAddress, userAgent });
        return {
            user: {
                id: storedToken.user.id,
                email: storedToken.user.email,
                firstName: storedToken.user.firstName,
                lastName: storedToken.user.lastName,
                role: storedToken.user.role,
                organization: storedToken.user.organization,
            },
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            expiresAt: tokens.expiresAt,
            session: session ? {
                id: session.id,
                expiresAt: session.expiresAt.toISOString(),
            } : undefined,
        };
    }
    async logout(userId) {
        await this.sessionsService.invalidateUserSessions(userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (user) {
            await this.logAuditEvent(userId, user.organizationId, 'USER_LOGOUT', 'user', userId, {
                method: 'manual',
            });
        }
    }
    async forgotPassword(forgotPasswordDto) {
        const { email, organizationSlug } = forgotPasswordDto;
        const user = await this.prisma.user.findFirst({
            where: {
                email,
                isActive: true,
                ...(organizationSlug && {
                    organization: {
                        slug: organizationSlug,
                    },
                }),
            },
        });
        if (!user) {
            return;
        }
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetTokenExpiry = new Date(Date.now() + 3600000);
        await this.cacheManager.set(`password_reset:${resetToken}`, { userId: user.id, email: user.email }, 3600000);
        await this.logAuditEvent(user.id, user.organizationId, 'PASSWORD_RESET_REQUESTED', 'user', user.id, {
            email: user.email,
        });
    }
    async resetPassword(resetPasswordDto) {
        const { token, password } = resetPasswordDto;
        const resetData = await this.cacheManager.get(`password_reset:${token}`);
        if (!resetData) {
            throw new common_1.BadRequestException('Invalid or expired reset token');
        }
        const { userId } = resetData;
        const hashedPassword = await bcrypt.hash(password, 12);
        await this.prisma.user.update({
            where: { id: userId },
            data: { password: hashedPassword },
        });
        await this.cacheManager.del(`password_reset:${token}`);
        await this.sessionsService.invalidateUserSessions(userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (user) {
            await this.logAuditEvent(userId, user.organizationId, 'PASSWORD_RESET_COMPLETED', 'user', userId, {
                email: user.email,
            });
        }
    }
    async changePassword(userId, changePasswordDto) {
        const { currentPassword, newPassword } = changePasswordDto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.BadRequestException('Current password is incorrect');
        }
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        await this.prisma.user.update({
            where: { id: userId },
            data: { password: hashedPassword },
        });
        await this.logAuditEvent(userId, user.organizationId, 'PASSWORD_CHANGED', 'user', userId, {
            email: user.email,
        });
    }
    async setupMFA(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { organization: true },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const secret = speakeasy.generateSecret({
            name: `${user.organization.name} (${user.email})`,
            issuer: 'SynapseAI',
        });
        const qrCode = await qrcode.toDataURL(secret.otpauth_url);
        const backupCodes = Array.from({ length: 10 }, () => crypto.randomBytes(4).toString('hex').toUpperCase());
        await this.cacheManager.set(`mfa_setup:${userId}`, { secret: secret.base32, backupCodes }, 1800000);
        return {
            secret: secret.base32,
            qrCode,
            backupCodes,
        };
    }
    async enableMFA(userId, enableMFADto) {
        const { secret, code } = enableMFADto;
        const setupData = await this.cacheManager.get(`mfa_setup:${userId}`);
        if (!setupData || setupData.secret !== secret) {
            throw new common_1.BadRequestException('Invalid MFA setup session');
        }
        const isValid = speakeasy.totp.verify({
            secret,
            encoding: 'base32',
            token: code,
            window: 2,
        });
        if (!isValid) {
            throw new common_1.BadRequestException('Invalid MFA code');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                mfaEnabled: true,
                mfaSecret: secret,
            },
        });
        await this.cacheManager.del(`mfa_setup:${userId}`);
        await this.logAuditEvent(userId, user.organizationId, 'MFA_ENABLED', 'user', userId, {
            email: user.email,
        });
    }
    async disableMFA(userId, verifyMFADto) {
        const { code } = verifyMFADto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || !user.mfaEnabled) {
            throw new common_1.BadRequestException('MFA is not enabled for this user');
        }
        const isValid = speakeasy.totp.verify({
            secret: user.mfaSecret,
            encoding: 'base32',
            token: code,
            window: 2,
        });
        if (!isValid) {
            throw new common_1.BadRequestException('Invalid MFA code');
        }
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                mfaEnabled: false,
                mfaSecret: null,
            },
        });
        await this.logAuditEvent(userId, user.organizationId, 'MFA_DISABLED', 'user', userId, {
            email: user.email,
        });
    }
    async updateProfile(userId, updateProfileDto) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const updateData = {};
        if (updateProfileDto.firstName)
            updateData.firstName = updateProfileDto.firstName;
        if (updateProfileDto.lastName)
            updateData.lastName = updateProfileDto.lastName;
        if (updateProfileDto.avatar)
            updateData.avatar = updateProfileDto.avatar;
        if (updateProfileDto.preferences) {
            updateData.preferences = {
                ...user.preferences,
                ...updateProfileDto.preferences,
            };
        }
        await this.prisma.user.update({
            where: { id: userId },
            data: updateData,
        });
        await this.logAuditEvent(userId, user.organizationId, 'PROFILE_UPDATED', 'user', userId, {
            changes: Object.keys(updateData),
        });
    }
    async validateUser(payload) {
        const user = await this.prisma.user.findUnique({
            where: { id: payload.sub },
            include: { organization: true },
        });
        if (!user || !user.isActive) {
            throw new common_1.UnauthorizedException('User not found or inactive');
        }
        return user;
    }
    async checkPermission(userId, resource, action, scope) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || !user.isActive) {
            return false;
        }
        const permissions = this.getRolePermissions(user.role);
        const requiredPermission = `${resource}:${action}`;
        return permissions.includes(requiredPermission) || permissions.includes('*:*');
    }
    async createAPIKey(userId, createAPIKeyDto) {
        const { name, description, permissions, expiresAt } = createAPIKeyDto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const apiKey = `sk_${crypto.randomBytes(32).toString('hex')}`;
        const hashedKey = await bcrypt.hash(apiKey, 12);
        const keyData = {
            id: crypto.randomUUID(),
            name,
            description,
            permissions,
            userId,
            organizationId: user.organizationId,
            hashedKey,
            createdAt: new Date(),
            expiresAt,
        };
        await this.cacheManager.set(`api_key:${keyData.id}`, keyData, expiresAt ? expiresAt.getTime() - Date.now() : 0);
        await this.logAuditEvent(userId, user.organizationId, 'API_KEY_CREATED', 'api_key', keyData.id, {
            name,
            permissions,
        });
        return {
            id: keyData.id,
            name,
            key: apiKey,
            permissions,
            createdAt: keyData.createdAt.toISOString(),
            expiresAt: expiresAt?.toISOString(),
        };
    }
    async generateTokens(user, rememberMe = false, ipAddress, userAgent) {
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role,
            organizationId: user.organizationId,
        };
        const accessToken = this.jwtService.sign(payload, {
            secret: this.jwtSecret,
            expiresIn: this.jwtExpiresIn,
        });
        const refreshTokenValue = crypto.randomBytes(40).toString('hex');
        const refreshTokenExpiresAt = new Date();
        refreshTokenExpiresAt.setDate(refreshTokenExpiresAt.getDate() + (rememberMe ? 30 : 7));
        await this.prisma.refreshToken.create({
            data: {
                token: refreshTokenValue,
                userId: user.id,
                expiresAt: refreshTokenExpiresAt,
                ipAddress,
                userAgent,
            },
        });
        const decoded = this.jwtService.decode(accessToken);
        const expiresAt = decoded.exp * 1000;
        return { accessToken, refreshToken: refreshTokenValue, expiresAt };
    }
    getRolePermissions(role) {
        const permissions = {
            [client_1.Role.SUPER_ADMIN]: ['*:*'],
            [client_1.Role.ORG_ADMIN]: [
                'organization:*',
                'user:*',
                'workflow:*',
                'agent:*',
                'tool:*',
                'provider:*',
                'analytics:read',
                'audit:read',
            ],
            [client_1.Role.DEVELOPER]: [
                'workflow:*',
                'agent:*',
                'tool:*',
                'provider:read',
                'analytics:read',
            ],
            [client_1.Role.VIEWER]: [
                'workflow:read',
                'agent:read',
                'tool:read',
                'provider:read',
                'analytics:read',
            ],
        };
        return permissions[role] || [];
    }
    async logAuditEvent(userId, organizationId, action, resource, resourceId, details) {
        return this.prisma.auditLog.create({
            data: {
                userId,
                organizationId,
                action,
                resource,
                resourceId,
                details,
            },
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        config_1.ConfigService,
        sessions_service_1.SessionsService, Object])
], AuthService);
//# sourceMappingURL=auth.service.js.map