export declare enum ClientType {
    WEB_APP = "WEB_APP",
    MOBILE_APP = "MOBILE_APP",
    SDK_WIDGET = "SDK_WIDGET",
    API_CLIENT = "API_CLIENT",
    INTERNAL_SERVICE = "INTERNAL_SERVICE"
}
export declare enum ConnectionStatus {
    CONNECTED = "CONNECTED",
    DISCONNECTED = "DISCONNECTED",
    RECONNECTING = "RECONNECTING",
    SUSPENDED = "SUSPENDED"
}
export declare enum ChannelType {
    AGENT_EVENTS = "AGENT_EVENTS",
    TOOL_EVENTS = "TOOL_EVENTS",
    WORKFLOW_EVENTS = "WORKFLOW_EVENTS",
    PROVIDER_EVENTS = "PROVIDER_EVENTS",
    SYSTEM_EVENTS = "SYSTEM_EVENTS",
    SESSION_EVENTS = "SESSION_EVENTS",
    PRIVATE_USER = "PRIVATE_USER",
    ORGANIZATION = "ORGANIZATION",
    PUBLIC = "PUBLIC"
}
export declare enum EventPriority {
    LOW = "LOW",
    NORMAL = "NORMAL",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export declare enum HybridEventType {
    HYBRID_EXECUTION_STARTED = "hybrid_execution_started",
    HYBRID_EXECUTION_COMPLETED = "hybrid_execution_completed",
    HYBRID_EXECUTION_FAILED = "hybrid_execution_failed",
    HYBRID_EXECUTION_CANCELLED = "hybrid_execution_cancelled",
    AGENT_FIRST_STARTED = "agent_first_started",
    AGENT_FIRST_COMPLETED = "agent_first_completed",
    TOOL_FIRST_STARTED = "tool_first_started",
    TOOL_FIRST_COMPLETED = "tool_first_completed",
    PARALLEL_EXECUTION_STARTED = "parallel_execution_started",
    PARALLEL_EXECUTION_COMPLETED = "parallel_execution_completed",
    ORCHESTRATION_STARTED = "orchestration_started",
    ORCHESTRATION_DECISION = "orchestration_decision",
    ORCHESTRATION_COMPLETED = "orchestration_completed",
    AGENT_EXECUTION_STARTED = "agent_execution_started",
    AGENT_EXECUTION_COMPLETED = "agent_execution_completed",
    AGENT_EXECUTION_FAILED = "agent_execution_failed",
    TOOL_EXECUTION_STARTED = "tool_execution_started",
    TOOL_EXECUTION_COMPLETED = "tool_execution_completed",
    TOOL_EXECUTION_FAILED = "tool_execution_failed",
    SYNC_POINT_REACHED = "sync_point_reached",
    CONTEXT_SHARED = "context_shared",
    CONTEXT_UPDATED = "context_updated",
    FALLBACK_TRIGGERED = "fallback_triggered",
    FALLBACK_COMPLETED = "fallback_completed",
    FALLBACK_FAILED = "fallback_failed",
    HUMAN_INPUT_REQUESTED = "human_input_requested",
    HUMAN_INPUT_RECEIVED = "human_input_received",
    HUMAN_INPUT_TIMEOUT = "human_input_timeout",
    STATE_SYNCHRONIZATION = "state_synchronization",
    EXECUTION_HANDOFF = "execution_handoff",
    COORDINATION_EVENT = "coordination_event"
}
export declare class ApiXConnectionDto {
    sessionId: string;
    clientType: ClientType;
    token: string;
    organizationId?: string;
    subscriptions?: string[];
    metadata?: Record<string, any>;
}
export declare class ApiXEventDto {
    type: string;
    channel: string;
    payload: any;
    sessionId?: string;
    correlationId?: string;
    priority?: EventPriority;
    compressed?: boolean;
    metadata?: Record<string, any>;
}
export declare class ApiXSubscriptionDto {
    channels: string[];
    filters?: Record<string, any>;
    acknowledgment?: boolean;
}
export declare class ApiXChannelDto {
    name: string;
    type: ChannelType;
    organizationId?: string;
    permissions?: Record<string, any>;
    metadata?: Record<string, any>;
}
export declare class ApiXLatencyMetricDto {
    connectionId: string;
    eventType: string;
    latencyMs: number;
    organizationId: string;
}
export declare class ApiXEventReplayDto {
    since?: string;
    eventTypes?: string[];
    limit?: number;
}
export declare class ApiXHeartbeatDto {
    timestamp: number;
    latency?: number;
}
export declare class ApiXErrorDto {
    message: string;
    code?: string;
    stack?: string;
    context?: Record<string, any>;
}
export declare class HybridExecutionEventDto {
    executionId: string;
    nodeId: string;
    workflowId: string;
    organizationId: string;
    eventType: HybridEventType;
    payload: any;
    agentId?: string;
    toolIds?: string[];
    executionPattern?: string;
    duration?: number;
    iteration?: number;
    metadata?: Record<string, any>;
    timestamp: number;
}
export declare class HybridCoordinationEventDto {
    executionId: string;
    nodeId: string;
    coordinationType: 'state_sync' | 'context_share' | 'execution_handoff' | 'sync_point';
    payload: any;
    sourceComponent?: string;
    targetComponent?: string;
    contextData?: Record<string, any>;
    timestamp: number;
}
export declare class HumanInputRequestDto {
    executionId: string;
    nodeId: string;
    sessionId: string;
    prompt: string;
    inputType: string;
    timeout?: number;
    allowSkip?: boolean;
    validationRules?: Record<string, any>;
    timestamp: number;
}
export declare class HumanInputResponseDto {
    executionId: string;
    nodeId: string;
    sessionId: string;
    userInput: any;
    inputType: string;
    skipped?: boolean;
    metadata?: Record<string, any>;
    timestamp: number;
}
export declare class WorkflowStateUpdateDto {
    executionId: string;
    nodeId: string;
    stateType: 'node_started' | 'node_completed' | 'node_failed' | 'execution_progress' | 'context_updated';
    currentState: any;
    previousState?: any;
    stateChanges?: Record<string, any>;
    progress?: number;
    timestamp: number;
}
export declare class ExecutionHandoffDto {
    executionId: string;
    fromComponent: string;
    toComponent: string;
    handoffType: 'agent_to_tool' | 'tool_to_agent' | 'tool_to_tool' | 'agent_to_agent';
    contextData: any;
    handoffMetadata?: Record<string, any>;
    timestamp: number;
}
export declare class HybridAnalyticsEventDto {
    executionId: string;
    nodeId: string;
    metricType: 'performance' | 'error_rate' | 'usage_pattern' | 'resource_utilization';
    metricValue: any;
    dimensions?: Record<string, any>;
    tags?: Record<string, string>;
    timestamp: number;
}
