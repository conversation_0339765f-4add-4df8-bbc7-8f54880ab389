import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AuditLog, User } from '@prisma/client';

export interface CreateAuditLogDto {
    userId: string;
    action: string;
    resource: string;
    resourceId: string;
    details?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
    organizationId: string;
}

export interface AuditLogQuery {
    userId?: string;
    action?: string;
    resource?: string;
    resourceId?: string;
    organizationId: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

export interface AuditLogWithUser extends AuditLog {
    user: {
        firstName: string;
        lastName: string;
        email: string;
    } | null;
}

export interface AuditLogStatsDto {
    totalLogs: number;
    logsByAction: Record<string, number>;
    logsByResource: Record<string, number>;
    logsByUser: Array<{ userId: string; userName: string; count: number }>;
    recentActivity: number;
    securityEvents: number;
}

@Injectable()
export class AuditLogService {
    constructor(private prisma: PrismaService) { }

    async createAuditLog(data: CreateAuditLogDto): Promise<AuditLog> {
        return this.prisma.auditLog.create({
            data: {
                userId: data.userId,
                action: data.action,
                resource: data.resource,
                resourceId: data.resourceId,
                details: data.details || {},
                ipAddress: data.ipAddress,
                userAgent: data.userAgent,
                organizationId: data.organizationId,
            },
        });
    }

    async findAuditLogs(query: AuditLogQuery): Promise<{
        logs: AuditLogWithUser[];
        total: number;
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }> {
        const {
            userId,
            action,
            resource,
            resourceId,
            organizationId,
            startDate,
            endDate,
            search,
            page = 1,
            limit = 50,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = query;

        const skip = (page - 1) * limit;

        const where: any = {
            organizationId,
            ...(userId && { userId }),
            ...(action && { action: { contains: action, mode: 'insensitive' } }),
            ...(resource && { resource: { contains: resource, mode: 'insensitive' } }),
            ...(resourceId && { resourceId }),
            ...(startDate && endDate && {
                createdAt: {
                    gte: startDate,
                    lte: endDate,
                },
            }),
            ...(search && {
                OR: [
                    { action: { contains: search, mode: 'insensitive' } },
                    { resource: { contains: search, mode: 'insensitive' } },
                    { resourceId: { contains: search, mode: 'insensitive' } },
                    { user: { firstName: { contains: search, mode: 'insensitive' } } },
                    { user: { lastName: { contains: search, mode: 'insensitive' } } },
                    { user: { email: { contains: search, mode: 'insensitive' } } },
                ],
            }),
        };

        const [logs, total] = await Promise.all([
            this.prisma.auditLog.findMany({
                where,
                skip,
                take: limit,
                orderBy: { [sortBy]: sortOrder },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.auditLog.count({ where }),
        ]);

        return {
            logs: logs as AuditLogWithUser[],
            total,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }

    async getAuditLogById(id: string, organizationId: string): Promise<AuditLogWithUser | null> {
        return this.prisma.auditLog.findFirst({
            where: { id, organizationId },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        }) as Promise<AuditLogWithUser | null>;
    }

    async getAuditLogStats(organizationId: string, days: number = 30): Promise<AuditLogStatsDto> {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const [
            totalLogs,
            logsByAction,
            logsByResource,
            logsByUser,
            recentActivity,
            securityEvents
        ] = await Promise.all([
            // Total logs count
            this.prisma.auditLog.count({
                where: { organizationId, createdAt: { gte: startDate } }
            }),

            // Logs grouped by action
            this.prisma.auditLog.groupBy({
                by: ['action'],
                where: { organizationId, createdAt: { gte: startDate } },
                _count: { action: true },
                orderBy: { _count: { action: 'desc' } },
                take: 10,
            }),

            // Logs grouped by resource
            this.prisma.auditLog.groupBy({
                by: ['resource'],
                where: { organizationId, createdAt: { gte: startDate } },
                _count: { resource: true },
                orderBy: { _count: { resource: 'desc' } },
                take: 10,
            }),

            // Logs grouped by user
            this.prisma.auditLog.groupBy({
                by: ['userId'],
                where: { organizationId, createdAt: { gte: startDate } },
                _count: { userId: true },
                orderBy: { _count: { userId: 'desc' } },
                take: 10,
            }),

            // Recent activity (last 24 hours)
            this.prisma.auditLog.count({
                where: {
                    organizationId,
                    createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                }
            }),

            // Security-related events
            this.prisma.auditLog.count({
                where: {
                    organizationId,
                    createdAt: { gte: startDate },
                    OR: [
                        { action: { contains: 'LOGIN', mode: 'insensitive' } },
                        { action: { contains: 'LOGOUT', mode: 'insensitive' } },
                        { action: { contains: 'PASSWORD', mode: 'insensitive' } },
                        { action: { contains: 'MFA', mode: 'insensitive' } },
                        { action: { contains: 'PERMISSION', mode: 'insensitive' } },
                        { action: { contains: 'ROLE', mode: 'insensitive' } },
                        { action: { contains: 'ACCESS', mode: 'insensitive' } },
                    ]
                }
            })
        ]);

        // Get user names for user stats
        const userIds = logsByUser.map(log => log.userId);
        const users = await this.prisma.user.findMany({
            where: { id: { in: userIds } },
            select: { id: true, firstName: true, lastName: true, email: true }
        });

        const logsByUserWithNames = logsByUser.map(log => {
            const user = users.find(u => u.id === log.userId);
            return {
                userId: log.userId,
                userName: user ? `${user.firstName} ${user.lastName}` : 'Unknown User',
                count: log._count.userId,
            };
        });

        return {
            totalLogs,
            logsByAction: logsByAction.reduce((acc, log) => {
                acc[log.action] = log._count.action;
                return acc;
            }, {} as Record<string, number>),
            logsByResource: logsByResource.reduce((acc, log) => {
                acc[log.resource] = log._count.resource;
                return acc;
            }, {} as Record<string, number>),
            logsByUser: logsByUserWithNames,
            recentActivity,
            securityEvents,
        };
    }

    async getUserAuditLogs(
        userId: string,
        organizationId: string,
        page: number = 1,
        limit: number = 50
    ): Promise<{
        logs: AuditLogWithUser[];
        total: number;
    }> {
        const skip = (page - 1) * limit;

        const [logs, total] = await Promise.all([
            this.prisma.auditLog.findMany({
                where: { userId, organizationId },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.auditLog.count({
                where: { userId, organizationId }
            }),
        ]);

        return {
            logs: logs as AuditLogWithUser[],
            total,
        };
    }

    async getResourceAuditLogs(
        resource: string,
        resourceId: string,
        organizationId: string,
        page: number = 1,
        limit: number = 50
    ): Promise<{
        logs: AuditLogWithUser[];
        total: number;
    }> {
        const skip = (page - 1) * limit;

        const [logs, total] = await Promise.all([
            this.prisma.auditLog.findMany({
                where: { resource, resourceId, organizationId },
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
            this.prisma.auditLog.count({
                where: { resource, resourceId, organizationId }
            }),
        ]);

        return {
            logs: logs as AuditLogWithUser[],
            total,
        };
    }

    async deleteOldAuditLogs(retentionDays: number = 365): Promise<number> {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

        const result = await this.prisma.auditLog.deleteMany({
            where: {
                createdAt: { lt: cutoffDate }
            }
        });

        return result.count;
    }

    async exportAuditLogs(
        query: AuditLogQuery,
        format: 'json' | 'csv' = 'json'
    ): Promise<string> {
        // Get all logs without pagination for export
        const { logs } = await this.findAuditLogs({
            ...query,
            page: 1,
            limit: 10000, // Set a high limit for export
        });

        if (format === 'csv') {
            return this.convertToCSV(logs);
        } else {
            return JSON.stringify(logs, null, 2);
        }
    }

    private convertToCSV(logs: AuditLogWithUser[]): string {
        if (logs.length === 0) return '';

        const headers = [
            'Timestamp',
            'User',
            'Action',
            'Resource',
            'Resource ID',
            'IP Address',
            'User Agent',
            'Details'
        ];

        const rows = logs.map(log => [
            log.createdAt.toISOString(),
            log.user ? `${log.user.firstName} ${log.user.lastName} (${log.user.email})` : 'System',
            log.action,
            log.resource,
            log.resourceId,
            log.ipAddress || '',
            log.userAgent || '',
            JSON.stringify(log.details || {})
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        return csvContent;
    }

    // Helper methods for common audit log patterns
    async logUserAction(
        userId: string,
        organizationId: string,
        action: string,
        resource: string,
        resourceId: string,
        details?: Record<string, any>,
        ipAddress?: string,
        userAgent?: string
    ): Promise<void> {
        await this.createAuditLog({
            userId,
            organizationId,
            action,
            resource,
            resourceId,
            details,
            ipAddress,
            userAgent,
        });
    }

    async logSecurityEvent(
        userId: string,
        organizationId: string,
        event: string,
        details?: Record<string, any>,
        ipAddress?: string,
        userAgent?: string
    ): Promise<void> {
        await this.createAuditLog({
            userId,
            organizationId,
            action: `SECURITY_${event.toUpperCase()}`,
            resource: 'SECURITY',
            resourceId: userId,
            details: {
                ...details,
                severity: 'high',
                timestamp: new Date().toISOString(),
            },
            ipAddress,
            userAgent,
        });
    }

    async logSystemEvent(
        organizationId: string,
        event: string,
        details?: Record<string, any>
    ): Promise<void> {
        await this.createAuditLog({
            userId: 'system',
            organizationId,
            action: `SYSTEM_${event.toUpperCase()}`,
            resource: 'SYSTEM',
            resourceId: 'system',
            details: {
                ...details,
                automated: true,
                timestamp: new Date().toISOString(),
            },
        });
    }
}