import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { 
  Plus, 
  Settings, 
  Activity, 
  DollarSign, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  MoreHorizontal,
  Edit,
  Trash2,
  TestTube,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import apiClient from '@/lib/api-client';

interface Provider {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  priority: number;
  config: Record<string, any>;
  models: ProviderModel[];
  healthMetrics: ProviderHealth[];
  _count: { usageLogs: number };
  createdAt: string;
  updatedAt: string;
}

interface ProviderModel {
  id: string;
  modelId: string;
  displayName: string;
  description?: string;
  capabilities: Record<string, any>;
  contextWindow: number;
  maxTokens?: number;
  inputCostPer1k?: number;
  outputCostPer1k?: number;
  avgLatencyMs: number;
  successRate: number;
  isActive: boolean;
  isDefault: boolean;
  customEndpoint?: string;
}

interface ProviderHealth {
  id: string;
  status: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY' | 'MAINTENANCE' | 'RATE_LIMITED';
  uptime: number;
  avgLatencyMs: number;
  errorRate: number;
  lastCheckAt: string;
  lastError?: string;
  consecutiveErrors: number;
}

interface RoutingRule {
  id: string;
  name: string;
  description?: string;
  providerId?: string;
  modelId?: string;
  conditions: Record<string, any>;
  priority: number;
  isActive: boolean;
  fallbackRules: Array<{ providerId: string; modelId?: string }>;
  usageCount: number;
  successCount: number;
  provider?: Provider;
  model?: ProviderModel;
}

const PROVIDER_TYPES = [
  { value: 'OPENAI', label: 'OpenAI', icon: '🤖' },
  { value: 'CLAUDE', label: 'Claude', icon: '🧠' },
  { value: 'ANTHROPIC', label: 'Anthropic', icon: '🔬' },
  { value: 'GEMINI', label: 'Gemini', icon: '💎' },
  { value: 'MISTRAL', label: 'Mistral', icon: '🌪️' },
  { value: 'GROQ', label: 'Groq', icon: '⚡' },
  { value: 'DEEPSEEK', label: 'DeepSeek', icon: '🔍' },
  { value: 'HUGGING_FACE', label: 'Hugging Face', icon: '🤗' },
  { value: 'OLLAMA', label: 'Ollama', icon: '🦙' },
  { value: 'OPENROUTER', label: 'OpenRouter', icon: '🛣️' },
  { value: 'GROK', label: 'Grok', icon: '🚀' },
];

export default function ProviderManagement() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [routingRules, setRoutingRules] = useState<RoutingRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showCreateRuleDialog, setShowCreateRuleDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('providers');

  // Form states
  const [createForm, setCreateForm] = useState({
    name: '',
    type: '',
    config: {},
    priority: 0,
    isActive: true,
  });

  const [ruleForm, setRuleForm] = useState({
    name: '',
    description: '',
    providerId: '',
    modelId: '',
    conditions: {},
    priority: 0,
    isActive: true,
    fallbackRules: [],
  });

  useEffect(() => {
    loadProviders();
    loadRoutingRules();
  }, []);

  const loadProviders = async () => {
    try {
      const response = await apiClient.get('/providers') as Provider[];
      setProviders(response as Provider[]);
    } catch (error) {
      console.error('Failed to load providers:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRoutingRules = async () => {
    try {
      const response = await apiClient.get('/providers/routing-rules') as RoutingRule[];
      setRoutingRules(response as RoutingRule[]);
    } catch (error) {
      console.error('Failed to load routing rules:', error);
    }
  };

  const createProvider = async () => {
    try {
      const response = await apiClient.post('/providers', createForm) as { status: number };

      if (response.status === 200 || response.status === 201) {
        setShowCreateDialog(false);
        setCreateForm({ name: '', type: '', config: {}, priority: 0, isActive: true });
        loadProviders();
      }
    } catch (error) {
      console.error('Failed to create provider:', error);
    }
  };

  const createRoutingRule = async () => {
    try {
      const response = await apiClient.post('/providers/routing-rules', ruleForm) as { status: number };

      if (response.status === 200 || response.status === 201) {
        setShowCreateRuleDialog(false);
        setRuleForm({
          name: '',
          description: '',
          providerId: '',
          modelId: '',
          conditions: {},
          priority: 0,
          isActive: true,
          fallbackRules: [],
        });
        loadRoutingRules();
      }
    } catch (error) {
      console.error('Failed to create routing rule:', error);
    }
  };

  const testProvider = async (providerId: string) => {
    try {
      const response = await apiClient.post(`/providers/${providerId}/test`) as { status: number };

      if (response.status === 200 || response.status === 201) {
        alert('Provider test successful!');
      } else {
        alert('Provider test failed');
      }
    } catch (error) {
      console.error('Failed to test provider:', error);
      alert('Provider test failed');
    }
  };

  const toggleProvider = async (providerId: string, isActive: boolean) => {
    try {
      const response = await apiClient.put(`/providers/${providerId}`, { isActive }) as { status: number };

      if (response.status === 200 || response.status === 201) {
        loadProviders();
      }
    } catch (error) {
      console.error('Failed to toggle provider:', error);
    }
  };

  const deleteProvider = async (providerId: string) => {
    if (!confirm('Are you sure you want to delete this provider?')) return;

    try {
      const response = await apiClient.delete(`/providers/${providerId}`) as { status: number };

      if (response.status === 200 || response.status === 201) {
        loadProviders();
      }
    } catch (error) {
      console.error('Failed to delete provider:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'HEALTHY': return 'bg-green-500';
      case 'DEGRADED': return 'bg-yellow-500';
      case 'UNHEALTHY': return 'bg-red-500';
      case 'MAINTENANCE': return 'bg-blue-500';
      case 'RATE_LIMITED': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'HEALTHY': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'DEGRADED': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'UNHEALTHY': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'MAINTENANCE': return <Settings className="h-4 w-4 text-blue-500" />;
      case 'RATE_LIMITED': return <Zap className="h-4 w-4 text-orange-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Provider Management</h1>
            <p className="text-gray-600 mt-1">Manage AI providers, models, and routing rules</p>
          </div>
          <div className="flex gap-3">
            <Button onClick={() => setShowCreateRuleDialog(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Routing Rule
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Provider
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Providers</p>
                  <p className="text-2xl font-bold text-gray-900">{providers.length}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Providers</p>
                  <p className="text-2xl font-bold text-green-600">
                    {providers.filter(p => p.isActive).length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Healthy Providers</p>
                  <p className="text-2xl font-bold text-green-600">
                    {providers.filter(p => p.healthMetrics[0]?.status === 'HEALTHY').length}
                  </p>
                </div>
                <Zap className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Requests</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {providers.reduce((sum, p) => sum + p._count.usageLogs, 0)}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="routing">Smart Routing</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Providers Tab */}
          <TabsContent value="providers" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {providers.map((provider) => (
                <Card key={provider.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">
                          {PROVIDER_TYPES.find(t => t.value === provider.type)?.icon || '🤖'}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{provider.name}</CardTitle>
                          <CardDescription>{provider.type}</CardDescription>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setSelectedProvider(provider)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => testProvider(provider.id)}>
                            <TestTube className="h-4 w-4 mr-2" />
                            Test
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => deleteProvider(provider.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(provider.healthMetrics[0]?.status || 'UNKNOWN')}
                        <span className="text-sm font-medium">
                          {provider.healthMetrics[0]?.status || 'Unknown'}
                        </span>
                      </div>
                      <Switch
                        checked={provider.isActive}
                        onCheckedChange={(checked) => toggleProvider(provider.id, checked)}
                      />
                    </div>

                    {/* Health Metrics */}
                    {provider.healthMetrics[0] && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Uptime</span>
                          <span>{provider.healthMetrics[0].uptime.toFixed(1)}%</span>
                        </div>
                        <Progress value={provider.healthMetrics[0].uptime} className="h-2" />
                        
                        <div className="flex justify-between text-sm">
                          <span>Avg Latency</span>
                          <span>{provider.healthMetrics[0].avgLatencyMs.toFixed(0)}ms</span>
                        </div>
                        
                        <div className="flex justify-between text-sm">
                          <span>Error Rate</span>
                          <span>{provider.healthMetrics[0].errorRate.toFixed(1)}%</span>
                        </div>
                      </div>
                    )}

                    {/* Models */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Models</span>
                        <Badge variant="secondary">{provider.models.length}</Badge>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {provider.models.slice(0, 3).map((model) => (
                          <Badge 
                            key={model.id} 
                            variant={model.isDefault ? "default" : "outline"}
                            className="text-xs"
                          >
                            {model.displayName}
                          </Badge>
                        ))}
                        {provider.models.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{provider.models.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Usage Stats */}
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Priority: {provider.priority}</span>
                      <span>Requests: {provider._count.usageLogs}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Routing Tab */}
          <TabsContent value="routing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Smart Routing Rules</CardTitle>
                <CardDescription>
                  Configure intelligent routing rules to optimize AI provider selection
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {routingRules.map((rule) => (
                    <div key={rule.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{rule.name}</h4>
                          {rule.description && (
                            <p className="text-sm text-gray-600">{rule.description}</p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={rule.isActive ? "default" : "secondary"}>
                            {rule.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                          <Badge variant="outline">Priority: {rule.priority}</Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Provider:</span>
                          <p className="text-gray-600">{rule.provider?.name || 'Any'}</p>
                        </div>
                        <div>
                          <span className="font-medium">Model:</span>
                          <p className="text-gray-600">{rule.model?.displayName || 'Any'}</p>
                        </div>
                        <div>
                          <span className="font-medium">Usage:</span>
                          <p className="text-gray-600">{rule.usageCount} requests</p>
                        </div>
                        <div>
                          <span className="font-medium">Success Rate:</span>
                          <p className="text-gray-600">
                            {rule.usageCount > 0 ? ((rule.successCount / rule.usageCount) * 100).toFixed(1) : 0}%
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}

                  {routingRules.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No routing rules configured. Create your first rule to enable smart routing.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Provider Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {providers.map((provider) => (
                      <div key={provider.id} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <div className="text-lg">
                            {PROVIDER_TYPES.find(t => t.value === provider.type)?.icon || '🤖'}
                          </div>
                          <div>
                            <p className="font-medium">{provider.name}</p>
                            <p className="text-sm text-gray-600">{provider._count.usageLogs} requests</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {provider.healthMetrics[0]?.avgLatencyMs.toFixed(0) || 0}ms
                          </p>
                          <p className="text-sm text-gray-600">
                            {provider.healthMetrics[0]?.uptime.toFixed(1) || 0}% uptime
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Usage Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {providers.map((provider) => {
                      const totalRequests = providers.reduce((sum, p) => sum + p._count.usageLogs, 0);
                      const percentage = totalRequests > 0 ? (provider._count.usageLogs / totalRequests) * 100 : 0;
                      
                      return (
                        <div key={provider.id} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{provider.name}</span>
                            <span>{percentage.toFixed(1)}%</span>
                          </div>
                          <Progress value={percentage} className="h-2" />
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Create Provider Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Provider</DialogTitle>
              <DialogDescription>
                Configure a new AI provider for your organization
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Provider Name</Label>
                <Input
                  id="name"
                  value={createForm.name}
                  onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                  placeholder="My OpenAI Provider"
                />
              </div>

              <div>
                <Label htmlFor="type">Provider Type</Label>
                <Select
                  value={createForm.type}
                  onValueChange={(value) => setCreateForm({ ...createForm, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider type" />
                  </SelectTrigger>
                  <SelectContent>
                    {PROVIDER_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <span>{type.icon}</span>
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority">Priority (0-100)</Label>
                <Input
                  id="priority"
                  type="number"
                  min="0"
                  max="100"
                  value={createForm.priority}
                  onChange={(e) => setCreateForm({ ...createForm, priority: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div>
                <Label htmlFor="config">Configuration (JSON)</Label>
                <Textarea
                  id="config"
                  placeholder='{"apiKey": "your-api-key"}'
                  value={JSON.stringify(createForm.config, null, 2)}
                  onChange={(e) => {
                    try {
                      const config = JSON.parse(e.target.value);
                      setCreateForm({ ...createForm, config });
                    } catch {
                      // Invalid JSON, ignore
                    }
                  }}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={createForm.isActive}
                  onCheckedChange={(checked) => setCreateForm({ ...createForm, isActive: checked })}
                />
                <Label htmlFor="active">Active</Label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={createProvider}>Create Provider</Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Create Routing Rule Dialog */}
        <Dialog open={showCreateRuleDialog} onOpenChange={setShowCreateRuleDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add Routing Rule</DialogTitle>
              <DialogDescription>
                Create a smart routing rule to optimize provider selection
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="ruleName">Rule Name</Label>
                <Input
                  id="ruleName"
                  value={ruleForm.name}
                  onChange={(e) => setRuleForm({ ...ruleForm, name: e.target.value })}
                  placeholder="High Priority Chat Requests"
                />
              </div>

              <div>
                <Label htmlFor="ruleDescription">Description</Label>
                <Input
                  id="ruleDescription"
                  value={ruleForm.description}
                  onChange={(e) => setRuleForm({ ...ruleForm, description: e.target.value })}
                  placeholder="Route chat requests to fastest provider"
                />
              </div>

              <div>
                <Label htmlFor="ruleProvider">Target Provider</Label>
                <Select
                  value={ruleForm.providerId}
                  onValueChange={(value) => setRuleForm({ ...ruleForm, providerId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Any Provider</SelectItem>
                    {providers.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="rulePriority">Priority</Label>
                <Input
                  id="rulePriority"
                  type="number"
                  value={ruleForm.priority}
                  onChange={(e) => setRuleForm({ ...ruleForm, priority: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div>
                <Label htmlFor="conditions">Conditions (JSON)</Label>
                <Textarea
                  id="conditions"
                  placeholder='{"requestType": "chat", "maxLatencyMs": 1000}'
                  value={JSON.stringify(ruleForm.conditions, null, 2)}
                  onChange={(e) => {
                    try {
                      const conditions = JSON.parse(e.target.value);
                      setRuleForm({ ...ruleForm, conditions });
                    } catch {
                      // Invalid JSON, ignore
                    }
                  }}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="ruleActive"
                  checked={ruleForm.isActive}
                  onCheckedChange={(checked) => setRuleForm({ ...ruleForm, isActive: checked })}
                />
                <Label htmlFor="ruleActive">Active</Label>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <Button variant="outline" onClick={() => setShowCreateRuleDialog(false)}>
                Cancel
              </Button>
              <Button onClick={createRoutingRule}>Create Rule</Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}