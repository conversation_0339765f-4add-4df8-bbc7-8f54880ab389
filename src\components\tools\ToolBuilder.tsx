"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  Play,
  Code,
  Settings,
  Database,
  Globe,
  Bot,
  Wrench,
  Plus,
  Minus,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  Info,

} from 'lucide-react';
import apiClient from '@/lib/api-client';
import { useToast } from '@/components/ui/use-toast';


interface ToolConfig {
  name: string;
  description: string;
  type: 'FUNCTION_CALL' | 'API_FETCH' | 'RAG_QUERY' | 'DB_QUERY' | 'BROWSER_AUTOMATION' | 'CUSTOM_SCRIPT';
  category: string;
  tags: string[]; 
  isPublic: boolean;
  timeout: number;
  retryPolicy: {
    maxRetries: number;
    retryableErrors: string[];
    backoffMultiplier: number;
  };
  cacheStrategy: 'NONE' | 'INPUT_HASH' | 'TIME_BASED' | 'CUSTOM';
  cacheTTL: number;
  inputSchema: any;
  outputSchema: any;
  config: any;
  documentation: string;
  examples: any[];
}

const TOOL_TYPES = [
  {
    value: 'FUNCTION_CALL',
    label: 'Function Call',
    icon: Code,
    description: 'Execute custom JavaScript/TypeScript functions'
  },
  {
    value: 'API_FETCH',
    label: 'API Fetch',
    icon: Globe,
    description: 'Make HTTP requests to external APIs'
  },
  {
    value: 'RAG_QUERY',
    label: 'RAG Query',
    icon: Database,
    description: 'Query knowledge bases with semantic search'
  },
  {
    value: 'DB_QUERY',
    label: 'Database Query',
    icon: Database,
    description: 'Execute SQL queries on databases'
  },
  {
    value: 'BROWSER_AUTOMATION',
    label: 'Browser Automation',
    icon: Bot,
    description: 'Automate web browser interactions'
  },
  {
    value: 'CUSTOM_SCRIPT',
    label: 'Custom Script',
    icon: Wrench,
    description: 'Run custom scripts in sandboxed environment'
  },
];

const CATEGORIES = [
  'DATA_PROCESSING',
  'COMMUNICATION',
  'ANALYSIS',
  'AUTOMATION',
  'INTEGRATION',
  'CUSTOM'
];

const CACHE_STRATEGIES = [
  { value: 'NONE', label: 'No Caching' },
  { value: 'INPUT_HASH', label: 'Input Hash Based' },
  { value: 'TIME_BASED', label: 'Time Based' },
  { value: 'CUSTOM', label: 'Custom Strategy' },
];

export default function ProductionToolBuilder() {
  const [toolConfig, setToolConfig] = useState<ToolConfig>({
    name: '',
    description: '',
    type: 'FUNCTION_CALL',
    category: 'CUSTOM',
    tags: [],
    isPublic: false,
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      retryableErrors: ['TIMEOUT', 'NETWORK_ERROR', 'RATE_LIMIT'],
      backoffMultiplier: 2,
    },
    cacheStrategy: 'INPUT_HASH',
    cacheTTL: 3600,
    inputSchema: {},
    outputSchema: {},
    config: {},
    documentation: '',
    examples: [],
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [newTag, setNewTag] = useState('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValid, setIsValid] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const { toast } = useToast();
  // Validate tool configuration
  useEffect(() => {
    const errors: string[] = [];

    if (!toolConfig.name.trim()) {
      errors.push('Tool name is required');
    }

    if (!toolConfig.description.trim()) {
      errors.push('Tool description is required');
    }

    if (toolConfig.timeout < 1000 || toolConfig.timeout > 300000) {
      errors.push('Timeout must be between 1 second and 5 minutes');
    }

    if (toolConfig.cacheTTL < 60 || toolConfig.cacheTTL > 86400) {
      errors.push('Cache TTL must be between 1 minute and 24 hours');
    }

    // Type-specific validations
    if (toolConfig.type === 'API_FETCH' && !toolConfig.config.url) {
      errors.push('API URL is required for API Fetch tools');
    }

    if (toolConfig.type === 'FUNCTION_CALL' && !toolConfig.config.code) {
      errors.push('Function code is required for Function Call tools');
    }

    setValidationErrors(errors);
    setIsValid(errors.length === 0);
  }, [toolConfig]);

  const updateConfig = (path: string, value: any) => {
    setToolConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
          current[keys[i] as keyof typeof current] = {} as never;
        }
        current = current[keys[i] as keyof typeof current];
      }

      current[keys[keys.length - 1] as keyof typeof current] = value as never;
      return newConfig;
    });
  };

  const addTag = () => {
    if (newTag.trim() && !toolConfig.tags.includes(newTag.trim())) {
      setToolConfig(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setToolConfig(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addExample = () => {
    setToolConfig(prev => ({
      ...prev,
      examples: [...prev.examples, { name: '', input: {}, output: {}, description: '' }]
    }));
  };

  const removeExample = (index: number) => {
    setToolConfig(prev => ({
      ...prev,
      examples: prev.examples.filter((_, i) => i !== index)
    }));
  };

  const updateExample = (index: number, field: string, value: any) => {
    setToolConfig(prev => ({
      ...prev,
      examples: prev.examples.map((example, i) =>
        i === index ? { ...example, [field]: value } : example
      )
    }));
  };

  const getTypeConfig = () => {
    const typeInfo = TOOL_TYPES.find(t => t.value === toolConfig.type);
    return typeInfo || TOOL_TYPES[0];
  };

  const handleSave = async () => {
    if (!isValid) return;

    try {
      setSaving(true);
      const response = await apiClient.post('/tools', toolConfig) as { status: number };
      toast({
        title: "Success",
        description: "Tool configuration saved successfully.",
      });
      return response as { status: number };
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to save tool configuration.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    try {
      setTesting(true);
      const response = await apiClient.post('/tools/test', toolConfig) as { status: number };
      toast({
        title: "Success",
        description: "Tool test completed successfully.",
      });
      return response as { status: number } || { status: 200 };
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Tool test failed.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setTesting(false);
    }
  };

  function renderTypeSpecificConfig(): React.ReactNode {
    switch (toolConfig.type) {
      case 'API_FETCH':
        return <div>API Fetch Configuration</div>;
      case 'FUNCTION_CALL':
        return <div>Function Call Configuration</div>;
      case 'RAG_QUERY':
        return <div>RAG Query Configuration</div>;
      case 'DB_QUERY':
        return <div>Database Query Configuration</div>;
      case 'BROWSER_AUTOMATION':
        return <div>Browser Automation Configuration</div>;
      case 'CUSTOM_SCRIPT':
        return <div>Custom Script Configuration</div>;
      default:
        return null;
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tool Builder</h1>
            <p className="text-gray-600 mt-1">Create and configure production-ready tools</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
              {showPreview ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button>
            <Button variant="outline" onClick={handleTest} disabled={!isValid}>
              <Play className="h-4 w-4 mr-2" />
              Test Tool
            </Button>
            <Button onClick={handleSave} disabled={!isValid}>
              <Save className="h-4 w-4 mr-2" />
              Save Tool
            </Button>
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium">Please fix the following issues:</div>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Configuration */}
          <div className="lg:col-span-2">
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Tool Configuration</CardTitle>
                <CardDescription>Configure your tool's behavior and settings</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="basic">Basic</TabsTrigger>
                    <TabsTrigger value="config">Config</TabsTrigger>
                    <TabsTrigger value="schema">Schema</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    <TabsTrigger value="docs">Docs</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="tool-name">Tool Name *</Label>
                        <Input
                          id="tool-name"
                          value={toolConfig.name}
                          onChange={(e) => updateConfig('name', e.target.value)}
                          placeholder="My Awesome Tool"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="tool-category">Category</Label>
                        <Select value={toolConfig.category} onValueChange={(value) => updateConfig('category', value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {CATEGORIES.map(category => (
                              <SelectItem key={category} value={category}>
                                {category.replace('_', ' ')}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="tool-description">Description *</Label>
                      <Textarea
                        id="tool-description"
                        value={toolConfig.description}
                        onChange={(e) => updateConfig('description', e.target.value)}
                        placeholder="Describe what this tool does and how it should be used..."
                        className="mt-1"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label>Tool Type *</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                        {TOOL_TYPES.map(type => {
                          const Icon = type.icon;
                          return (
                            <Card
                              key={type.value}
                              className={`cursor-pointer transition-all ${toolConfig.type === type.value
                                ? 'ring-2 ring-blue-500 bg-blue-50'
                                : 'hover:bg-gray-50'
                                }`}
                              onClick={() => updateConfig('type', type.value)}
                            >
                              <CardContent className="p-4">
                                <div className="flex items-center gap-3">
                                  <Icon className="h-5 w-5 text-blue-600" />
                                  <div>
                                    <div className="font-medium">{type.label}</div>
                                    <div className="text-xs text-gray-600">{type.description}</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    </div>

                    <div>
                      <Label>Tags</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex gap-2">
                          <Input
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Add a tag..."
                            onKeyPress={(e) => e.key === 'Enter' && addTag()}
                          />
                          <Button type="button" onClick={addTag} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {toolConfig.tags.map(tag => (
                            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                              {tag}
                              <button onClick={() => removeTag(tag)} className="ml-1 hover:text-red-600">
                                <Minus className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={toolConfig.isPublic}
                        onCheckedChange={(checked) => updateConfig('isPublic', checked)}
                      />
                      <Label>Make this tool public</Label>
                      <Info className="h-4 w-4 text-gray-400" />
                    </div>
                  </TabsContent>

                  <TabsContent value="config" className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        {React.createElement(getTypeConfig().icon, { className: "h-5 w-5" })}
                        {getTypeConfig().label} Configuration
                      </h3>
                      {renderTypeSpecificConfig()}
                    </div>
                  </TabsContent>

                  <TabsContent value="schema" className="space-y-6">
                    <div>
                      <Label htmlFor="input-schema">Input Schema (JSON Schema)</Label>
                      <Textarea
                        id="input-schema"
                        value={JSON.stringify(toolConfig.inputSchema, null, 2)}
                        onChange={(e) => {
                          try {
                            updateConfig('inputSchema', JSON.parse(e.target.value));
                          } catch { }
                        }}
                        placeholder='{\n  "type": "object",\n  "properties": {\n    "input": {\n      "type": "string",\n      "description": "Input text"\n    }\n  },\n  "required": ["input"]\n}'
                        className="mt-1 font-mono text-sm"
                        rows={8}
                      />
                    </div>
                    <div>
                      <Label htmlFor="output-schema">Output Schema (JSON Schema)</Label>
                      <Textarea
                        id="output-schema"
                        value={JSON.stringify(toolConfig.outputSchema, null, 2)}
                        onChange={(e) => {
                          try {
                            updateConfig('outputSchema', JSON.parse(e.target.value));
                          } catch { }
                        }}
                        placeholder='{\n  "type": "object",\n  "properties": {\n    "result": {\n      "type": "string",\n      "description": "Processing result"\n    }\n  }\n}'
                        className="mt-1 font-mono text-sm"
                        rows={8}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="timeout">Timeout (ms)</Label>
                        <Input
                          id="timeout"
                          type="number"
                          value={toolConfig.timeout}
                          onChange={(e) => updateConfig('timeout', parseInt(e.target.value))}
                          min="1000"
                          max="300000"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="cache-ttl">Cache TTL (seconds)</Label>
                        <Input
                          id="cache-ttl"
                          type="number"
                          value={toolConfig.cacheTTL}
                          onChange={(e) => updateConfig('cacheTTL', parseInt(e.target.value))}
                          min="60"
                          max="86400"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="cache-strategy">Cache Strategy</Label>
                      <Select
                        value={toolConfig.cacheStrategy}
                        onValueChange={(value) => updateConfig('cacheStrategy', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {CACHE_STRATEGIES.map(strategy => (
                            <SelectItem key={strategy.value} value={strategy.value}>
                              {strategy.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Retry Policy</Label>
                      <div className="mt-2 space-y-3">
                        <div>
                          <Label htmlFor="max-retries">Max Retries</Label>
                          <Input
                            id="max-retries"
                            type="number"
                            value={toolConfig.retryPolicy.maxRetries}
                            onChange={(e) => updateConfig('retryPolicy.maxRetries', parseInt(e.target.value))}
                            min="0"
                            max="10"
                          />
                        </div>
                        <div>
                          <Label htmlFor="backoff-multiplier">Backoff Multiplier</Label>
                          <Input
                            id="backoff-multiplier"
                            type="number"
                            step="0.1"
                            value={toolConfig.retryPolicy.backoffMultiplier}
                            onChange={(e) => updateConfig('retryPolicy.backoffMultiplier', parseFloat(e.target.value))}
                            min="1"
                            max="10"
                          />
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="docs" className="space-y-6">
                    <div>
                      <Label htmlFor="documentation">Documentation</Label>
                      <Textarea
                        id="documentation"
                        value={toolConfig.documentation}
                        onChange={(e) => updateConfig('documentation', e.target.value)}
                        placeholder="Provide detailed documentation for this tool..."
                        className="mt-1"
                        rows={8}
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <Label>Examples</Label>
                        <Button type="button" onClick={addExample} size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Add Example
                        </Button>
                      </div>
                      <div className="mt-2 space-y-4">
                        {toolConfig.examples.map((example, index) => (
                          <Card key={index}>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-3">
                                <Label>Example {index + 1}</Label>
                                <Button
                                  type="button"
                                  onClick={() => removeExample(index)}
                                  size="sm"
                                  variant="ghost"
                                >
                                  <Minus className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="space-y-3">
                                <Input
                                  placeholder="Example name"
                                  value={example.name}
                                  onChange={(e) => updateExample(index, 'name', e.target.value)}
                                />
                                <Textarea
                                  placeholder="Example description"
                                  value={example.description}
                                  onChange={(e) => updateExample(index, 'description', e.target.value)}
                                  rows={2}
                                />
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <Label className="text-xs">Input</Label>
                                    <Textarea
                                      placeholder='{"key": "value"}'
                                      value={JSON.stringify(example.input, null, 2)}
                                      onChange={(e) => {
                                        try {
                                          updateExample(index, 'input', JSON.parse(e.target.value));
                                        } catch { }
                                      }}
                                      className="font-mono text-xs"
                                      rows={3}
                                    />
                                  </div>
                                  <div>
                                    <Label className="text-xs">Output</Label>
                                    <Textarea
                                      placeholder='{"result": "value"}'
                                      value={JSON.stringify(example.output, null, 2)}
                                      onChange={(e) => {
                                        try {
                                          updateExample(index, 'output', JSON.parse(e.target.value));
                                        } catch { }
                                      }}
                                      className="font-mono text-xs"
                                      rows={3}
                                    />
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-1">
            <Card className="bg-white/80 backdrop-blur-sm sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Tool Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    {React.createElement(getTypeConfig().icon, { className: "h-5 w-5 text-blue-600" })}
                  </div>
                  <div>
                    <div className="font-semibold">{toolConfig.name || 'Untitled Tool'}</div>
                    <div className="text-xs text-gray-600">{getTypeConfig().label}</div>
                  </div>
                </div>

                <p className="text-sm text-gray-600">
                  {toolConfig.description || 'No description provided'}
                </p>

                <div className="space-y-2">
                  <Badge variant="secondary" className="text-xs">
                    {toolConfig.category.replace('_', ' ')}
                  </Badge>
                  {toolConfig.isPublic && (
                    <Badge variant="default" className="text-xs bg-green-100 text-green-800 ml-2">
                      Public
                    </Badge>
                  )}
                </div>

                {toolConfig.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {toolConfig.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {toolConfig.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{toolConfig.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                <Separator />

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Timeout:</span>
                    <span>{toolConfig.timeout}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cache Strategy:</span>
                    <span>{toolConfig.cacheStrategy.replace('_', ' ')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cache TTL:</span>
                    <span>{toolConfig.cacheTTL}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Retries:</span>
                    <span>{toolConfig.retryPolicy.maxRetries}</span>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-2">
                  {isValid ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-600">Configuration valid</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm text-red-600">{validationErrors.length} error(s)</span>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}