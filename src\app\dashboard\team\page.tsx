"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    Users,
    UserPlus,
    Search,
    Filter,
    MoreHorizontal,
    Mail,
    Phone,
    Calendar,
    MapPin,
    Shield,
    Crown,
    User,
    Settings,
    Activity,
    Clock,
    CheckCircle,
    XCircle,
    Loader2
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import apiClient from '@/lib/api-client';

interface TeamMember {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: 'ADMIN' | 'MANAGER' | 'DEVELOPER' | 'VIEWER';
    status: 'ACTIVE' | 'INACTIVE' | 'PENDING';
    avatar?: string;
    phone?: string;
    location?: string;
    joinedAt: string;
    lastActive?: string;
    permissions: string[];
    stats: {
        workflowsCreated: number;
        toolsCreated: number;
        executionsRun: number;
        successRate: number;
    };
}

export default function TeamPage() {
    const [members, setMembers] = useState<TeamMember[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedRole, setSelectedRole] = useState<string>('all');
    const [selectedStatus, setSelectedStatus] = useState<string>('all');
    const [stats, setStats] = useState({
        totalMembers: 0,
        activeMembers: 0,
        admins: 0,
        pendingInvites: 0
    });
    const { toast } = useToast();

    // Load team members from real API
    const loadTeamMembers = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get('/team/members');
            const membersData = (response as any).members || [];

            setMembers(membersData);

            // Calculate stats
            const totalMembers = membersData.length;
            const activeMembers = membersData.filter((m: TeamMember) => m.status === 'ACTIVE').length;
            const admins = membersData.filter((m: TeamMember) => m.role === 'ADMIN').length;
            const pendingInvites = membersData.filter((m: TeamMember) => m.status === 'PENDING').length;

            setStats({
                totalMembers,
                activeMembers,
                admins,
                pendingInvites
            });

        } catch (error: any) {
            console.error('Failed to load team members:', error);
            toast({
                title: "Error",
                description: "Failed to load team members.",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadTeamMembers();
    }, []);

    const getRoleIcon = (role: string) => {
        switch (role) {
            case 'ADMIN': return Crown;
            case 'MANAGER': return Shield;
            case 'DEVELOPER': return Settings;
            default: return User;
        }
    };

    const getRoleColor = (role: string) => {
        switch (role) {
            case 'ADMIN': return 'bg-red-100 text-red-800';
            case 'MANAGER': return 'bg-blue-100 text-blue-800';
            case 'DEVELOPER': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ACTIVE': return 'bg-green-100 text-green-800';
            case 'INACTIVE': return 'bg-gray-100 text-gray-800';
            case 'PENDING': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getInitials = (firstName: string, lastName: string) => {
        return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
                <div className="max-w-7xl mx-auto">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center gap-3">
                            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                            <span className="text-lg text-gray-600">Loading team members...</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Team Management</h1>
                        <p className="text-gray-600 mt-1">Manage your organization's team members and permissions</p>
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline">
                            <Mail className="h-4 w-4 mr-2" />
                            Invite Members
                        </Button>
                        <Button>
                            <UserPlus className="h-4 w-4 mr-2" />
                            Add Member
                        </Button>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-50 rounded-lg">
                                    <Users className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{stats.totalMembers}</div>
                                    <div className="text-sm text-gray-600">Total Members</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-50 rounded-lg">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{stats.activeMembers}</div>
                                    <div className="text-sm text-gray-600">Active Members</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-red-50 rounded-lg">
                                    <Crown className="h-5 w-5 text-red-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{stats.admins}</div>
                                    <div className="text-sm text-gray-600">Administrators</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card className="bg-white/80 backdrop-blur-sm">
                        <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-yellow-50 rounded-lg">
                                    <Clock className="h-5 w-5 text-yellow-600" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold">{stats.pendingInvites}</div>
                                    <div className="text-sm text-gray-600">Pending Invites</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card className="bg-white/80 backdrop-blur-sm">
                    <CardContent className="p-4">
                        <div className="flex flex-col sm:flex-row gap-4">
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    <Input
                                        placeholder="Search team members..."
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <select
                                value={selectedRole}
                                onChange={(e) => setSelectedRole(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="all">All Roles</option>
                                <option value="ADMIN">Admin</option>
                                <option value="MANAGER">Manager</option>
                                <option value="DEVELOPER">Developer</option>
                                <option value="VIEWER">Viewer</option>
                            </select>
                            <select
                                value={selectedStatus}
                                onChange={(e) => setSelectedStatus(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="all">All Status</option>
                                <option value="ACTIVE">Active</option>
                                <option value="INACTIVE">Inactive</option>
                                <option value="PENDING">Pending</option>
                            </select>
                        </div>
                    </CardContent>
                </Card>

                {/* Team Members Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {members.length > 0 ? (
                        members.map(member => (
                            <Card key={member.id} className="bg-white/80 backdrop-blur-sm hover:shadow-lg transition-all duration-200">
                                <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <Avatar className="h-12 w-12">
                                                <AvatarImage src={member.avatar} />
                                                <AvatarFallback className="bg-blue-100 text-blue-600">
                                                    {getInitials(member.firstName, member.lastName)}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <CardTitle className="text-lg font-semibold text-gray-900">
                                                    {member.firstName} {member.lastName}
                                                </CardTitle>
                                                <CardDescription className="text-sm text-gray-600">
                                                    {member.email}
                                                </CardDescription>
                                            </div>
                                        </div>
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem>
                                                    <Mail className="h-4 w-4 mr-2" />
                                                    Send Message
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Settings className="h-4 w-4 mr-2" />
                                                    Edit Permissions
                                                </DropdownMenuItem>
                                                <DropdownMenuItem className="text-red-600">
                                                    <XCircle className="h-4 w-4 mr-2" />
                                                    Remove Member
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-2 flex-wrap">
                                            <Badge className={getRoleColor(member.role)}>
                                                {member.role}
                                            </Badge>
                                            <Badge className={getStatusColor(member.status)}>
                                                {member.status}
                                            </Badge>
                                        </div>

                                        {member.location && (
                                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                                <MapPin className="h-4 w-4" />
                                                {member.location}
                                            </div>
                                        )}

                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div className="text-center">
                                                <div className="font-semibold text-gray-900">{member.stats.workflowsCreated}</div>
                                                <div className="text-gray-500">Workflows</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-semibold text-gray-900">{member.stats.toolsCreated}</div>
                                                <div className="text-gray-500">Tools</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-semibold text-gray-900">{member.stats.executionsRun.toLocaleString()}</div>
                                                <div className="text-gray-500">Executions</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-semibold text-green-600">{(member.stats.successRate * 100).toFixed(1)}%</div>
                                                <div className="text-gray-500">Success Rate</div>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between text-xs text-gray-500">
                                            <span>Joined {new Date(member.joinedAt).toLocaleDateString()}</span>
                                            {member.lastActive && (
                                                <span>Last active {new Date(member.lastActive).toLocaleDateString()}</span>
                                            )}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    ) : (
                        <div className="col-span-full text-center py-12">
                            <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">No team members found</h3>
                            <p className="text-gray-600 mb-4">
                                {searchQuery || selectedRole !== 'all' || selectedStatus !== 'all'
                                    ? 'Try adjusting your search criteria'
                                    : 'Get started by inviting your first team member'
                                }
                            </p>
                            <Button>
                                <UserPlus className="h-4 w-4 mr-2" />
                                Add Member
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}