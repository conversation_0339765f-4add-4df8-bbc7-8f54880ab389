"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowsModule = void 0;
const common_1 = require("@nestjs/common");
const workflows_controller_1 = require("./workflows.controller");
const workflows_service_1 = require("./workflows.service");
const prisma_module_1 = require("../prisma/prisma.module");
const apix_module_1 = require("../apix/apix.module");
const sessions_module_1 = require("../sessions/sessions.module");
const workflow_executor_service_1 = require("./services/workflow-executor.service");
const node_registry_service_1 = require("./services/node-registry.service");
const flow_controller_service_1 = require("./services/flow-controller.service");
const variable_resolver_service_1 = require("./services/variable-resolver.service");
const branch_evaluator_service_1 = require("./services/branch-evaluator.service");
let WorkflowsModule = class WorkflowsModule {
};
exports.WorkflowsModule = WorkflowsModule;
exports.WorkflowsModule = WorkflowsModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule, apix_module_1.ApixModule, sessions_module_1.SessionsModule],
        controllers: [workflows_controller_1.WorkflowsController],
        providers: [
            workflows_service_1.WorkflowsService,
            workflow_executor_service_1.WorkflowExecutorService,
            node_registry_service_1.NodeRegistryService,
            flow_controller_service_1.FlowControllerService,
            variable_resolver_service_1.VariableResolverService,
            branch_evaluator_service_1.BranchEvaluatorService,
        ],
        exports: [
            workflows_service_1.WorkflowsService,
            workflow_executor_service_1.WorkflowExecutorService,
            node_registry_service_1.NodeRegistryService,
            flow_controller_service_1.FlowControllerService,
            variable_resolver_service_1.VariableResolverService,
            branch_evaluator_service_1.BranchEvaluatorService,
        ],
    })
], WorkflowsModule);
//# sourceMappingURL=workflows.module.js.map