import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { HybridNodeExecutorService } from './hybrid-node-executor.service';
import { HumanInTheLoopService } from './human-in-the-loop.service';
export declare class WorkflowExecutorService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private hybridNodeExecutor;
    private humanInTheLoopService;
    private readonly logger;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService, hybridNodeExecutor: HybridNodeExecutorService, humanInTheLoopService: HumanInTheLoopService);
    executeWorkflow(executionId: string, workflowId: string, sessionId: string, organizationId: string, input?: Record<string, any>): Promise<any>;
    private executeNode;
    private executeAgentNode;
    private executeToolNode;
    private executeConditionNode;
    private executeParallelNode;
    private executeHumanInputNode;
    private executeHybridNode;
    private orchestrateMultipleTools;
    private determineNextTool;
    private extractToolParameters;
    private getNextNodes;
    private evaluateEdgeCondition;
    private evaluateCondition;
    private processVariables;
}
