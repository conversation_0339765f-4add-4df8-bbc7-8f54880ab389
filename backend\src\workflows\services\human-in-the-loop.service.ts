import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

export interface HumanInputRequest {
  id: string;
  executionId: string;
  nodeId: string;
  sessionId: string;
  userId: string;
  organizationId: string;
  prompt: string;
  inputType: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'file' | 'json';
  validationRules?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    options?: string[];
    fileTypes?: string[];
    maxFileSize?: number;
  };
  timeout: number;
  allowSkip: boolean;
  skipValue?: any;
  status: 'pending' | 'completed' | 'expired' | 'cancelled' | 'skipped';
  response?: any;
  metadata?: Record<string, any>;
  createdAt: Date;
  expiresAt: Date;
  completedAt?: Date;
}

export interface HumanInputResponse {
  requestId: string;
  userInput: any;
  inputType: string;
  skipped: boolean;
  validationPassed: boolean;
  validationErrors?: string[];
  metadata?: Record<string, any>;
  completedAt: Date;
}

@Injectable()
export class HumanInTheLoopService {
  private readonly logger = new Logger(HumanInTheLoopService.name);
  private activeRequests = new Map<string, HumanInputRequest>();
  private requestTimeouts = new Map<string, NodeJS.Timeout>();

  constructor(
    private prisma: PrismaService,
    private apixGateway: ApixGateway,
    private sessionsService: SessionsService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    // Start cleanup interval
    setInterval(() => this.cleanupExpiredRequests(), 60000); // Every minute
  }

  async requestHumanInput(
    executionId: string,
    nodeId: string,
    sessionId: string,
    prompt: string,
    inputType: string = 'text',
    options: {
      timeout?: number;
      allowSkip?: boolean;
      skipValue?: any;
      validationRules?: any;
      metadata?: Record<string, any>;
    } = {},
  ): Promise<string> {
    try {
      // Get session to extract user and organization info
      const session = await this.sessionsService.getSession(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      const requestId = `human_input_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const timeout = options.timeout || 300000; // 5 minutes default
      const expiresAt = new Date(Date.now() + timeout);

      const request: HumanInputRequest = {
        id: requestId,
        executionId,
        nodeId,
        sessionId,
        userId: session.userId,
        organizationId: session.organizationId,
        prompt,
        inputType: inputType as any,
        validationRules: options.validationRules,
        timeout,
        allowSkip: options.allowSkip || false,
        skipValue: options.skipValue,
        status: 'pending',
        metadata: options.metadata,
        createdAt: new Date(),
        expiresAt,
      };

      // Store in database
      await this.prisma.workflowHumanInput.create({
        data: {
          id: requestId,
          executionId,
          nodeId,
          sessionId,
          userId: session.userId,
          organizationId: session.organizationId,
          prompt,
          inputType,
          validationRules: options.validationRules as any,
          timeout,
          allowSkip: options.allowSkip || false,
          skipValue: options.skipValue as any,
          status: 'PENDING',
          metadata: options.metadata as any,
          expiresAt,
        },
      });

      // Store in memory for quick access
      this.activeRequests.set(requestId, request);

      // Cache for quick lookup
      await this.cacheManager.set(`human_input:${requestId}`, request, timeout);

      // Set timeout
      const timeoutHandle = setTimeout(() => {
        this.handleTimeout(requestId);
      }, timeout);
      this.requestTimeouts.set(requestId, timeoutHandle);

      // Emit human input request event
      await this.apixGateway.emitToUser(
        session.userId,
        'human_input_requested',
        {
          requestId,
          executionId,
          nodeId,
          prompt,
          inputType,
          validationRules: options.validationRules,
          timeout,
          allowSkip: options.allowSkip || false,
          expiresAt: expiresAt.toISOString(),
          metadata: options.metadata,
        },
        { priority: 'high' }
      );

      // Also emit to workflow room
      await this.apixGateway.emitToRoom(
        `workflow:${executionId}`,
        'human_input_requested',
        {
          requestId,
          nodeId,
          prompt,
          inputType,
          timeout,
          allowSkip: options.allowSkip || false,
        }
      );

      // Emit internal event
      this.eventEmitter.emit('human_input.requested', {
        requestId,
        executionId,
        nodeId,
        sessionId,
        userId: session.userId,
        organizationId: session.organizationId,
        prompt,
        inputType,
        timeout,
      });

      this.logger.log(`Human input requested: ${requestId} for execution: ${executionId}`);
      return requestId;

    } catch (error) {
      this.logger.error(`Failed to request human input: ${error.message}`, error.stack);
      throw error;
    }
  }

  async provideHumanInput(
    requestId: string,
    userInput: any,
    userId: string,
    metadata?: Record<string, any>,
  ): Promise<HumanInputResponse> {
    try {
      const request = await this.getRequest(requestId);
      
      if (!request) {
        throw new NotFoundException(`Human input request not found: ${requestId}`);
      }

      if (request.userId !== userId) {
        throw new Error('Unauthorized: User does not own this request');
      }

      if (request.status !== 'pending') {
        throw new Error(`Request is not pending: ${request.status}`);
      }

      if (new Date() > request.expiresAt) {
        throw new Error('Request has expired');
      }

      // Validate input
      const validationResult = this.validateInput(userInput, request.inputType, request.validationRules);
      
      if (!validationResult.valid) {
        // Return validation errors but don't complete the request
        return {
          requestId,
          userInput,
          inputType: request.inputType,
          skipped: false,
          validationPassed: false,
          validationErrors: validationResult.errors,
          completedAt: new Date(),
        };
      }

      // Process and store the response
      const processedInput = this.processInput(userInput, request.inputType);
      const completedAt = new Date();

      // Update request status
      request.status = 'completed';
      request.response = processedInput;
      request.completedAt = completedAt;

      // Update in database
      await this.prisma.workflowHumanInput.update({
        where: { id: requestId },
        data: {
          status: 'COMPLETED',
          response: processedInput as any,
          completedAt,
          metadata: { ...request.metadata, ...metadata } as any,
        },
      });

      // Update cache
      await this.cacheManager.set(`human_input:${requestId}`, request, 3600000); // 1 hour

      // Clear timeout
      const timeoutHandle = this.requestTimeouts.get(requestId);
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
        this.requestTimeouts.delete(requestId);
      }

      const response: HumanInputResponse = {
        requestId,
        userInput: processedInput,
        inputType: request.inputType,
        skipped: false,
        validationPassed: true,
        metadata: { ...request.metadata, ...metadata },
        completedAt,
      };

      // Emit completion events
      await this.apixGateway.emitToUser(
        userId,
        'human_input_completed',
        response,
        { priority: 'high' }
      );

      await this.apixGateway.emitToRoom(
        `workflow:${request.executionId}`,
        'human_input_received',
        {
          requestId,
          nodeId: request.nodeId,
          userInput: processedInput,
          completedAt,
        }
      );

      // Emit internal event
      this.eventEmitter.emit('human_input.completed', {
        requestId,
        executionId: request.executionId,
        nodeId: request.nodeId,
        sessionId: request.sessionId,
        userId,
        userInput: processedInput,
        completedAt,
      });

      this.logger.log(`Human input completed: ${requestId}`);
      return response;

    } catch (error) {
      this.logger.error(`Failed to provide human input: ${error.message}`, error.stack);
      throw error;
    }
  }

  async skipHumanInput(
    requestId: string,
    userId: string,
    reason?: string,
  ): Promise<HumanInputResponse> {
    try {
      const request = await this.getRequest(requestId);
      
      if (!request) {
        throw new NotFoundException(`Human input request not found: ${requestId}`);
      }

      if (request.userId !== userId) {
        throw new Error('Unauthorized: User does not own this request');
      }

      if (!request.allowSkip) {
        throw new Error('Skipping is not allowed for this request');
      }

      if (request.status !== 'pending') {
        throw new Error(`Request is not pending: ${request.status}`);
      }

      const completedAt = new Date();

      // Update request status
      request.status = 'skipped';
      request.response = request.skipValue;
      request.completedAt = completedAt;

      // Update in database
      await this.prisma.workflowHumanInput.update({
        where: { id: requestId },
        data: {
          status: 'SKIPPED',
          response: request.skipValue as any,
          completedAt,
          metadata: { ...request.metadata, skipReason: reason } as any,
        },
      });

      // Clear timeout
      const timeoutHandle = this.requestTimeouts.get(requestId);
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
        this.requestTimeouts.delete(requestId);
      }

      const response: HumanInputResponse = {
        requestId,
        userInput: request.skipValue,
        inputType: request.inputType,
        skipped: true,
        validationPassed: true,
        metadata: { ...request.metadata, skipReason: reason },
        completedAt,
      };

      // Emit events
      await this.apixGateway.emitToUser(
        userId,
        'human_input_skipped',
        response,
        { priority: 'high' }
      );

      await this.apixGateway.emitToRoom(
        `workflow:${request.executionId}`,
        'human_input_skipped',
        {
          requestId,
          nodeId: request.nodeId,
          skipValue: request.skipValue,
          reason,
          completedAt,
        }
      );

      this.eventEmitter.emit('human_input.skipped', {
        requestId,
        executionId: request.executionId,
        nodeId: request.nodeId,
        sessionId: request.sessionId,
        userId,
        skipValue: request.skipValue,
        reason,
        completedAt,
      });

      this.logger.log(`Human input skipped: ${requestId}, reason: ${reason}`);
      return response;

    } catch (error) {
      this.logger.error(`Failed to skip human input: ${error.message}`, error.stack);
      throw error;
    }
  }

  async cancelHumanInput(
    requestId: string,
    userId: string,
    reason?: string,
  ): Promise<void> {
    try {
      const request = await this.getRequest(requestId);
      
      if (!request) {
        throw new NotFoundException(`Human input request not found: ${requestId}`);
      }

      if (request.userId !== userId) {
        throw new Error('Unauthorized: User does not own this request');
      }

      if (request.status !== 'pending') {
        throw new Error(`Request is not pending: ${request.status}`);
      }

      // Update request status
      request.status = 'cancelled';
      request.completedAt = new Date();

      // Update in database
      await this.prisma.workflowHumanInput.update({
        where: { id: requestId },
        data: {
          status: 'CANCELLED',
          completedAt: new Date(),
          metadata: { ...request.metadata, cancelReason: reason } as any,
        },
      });

      // Clear timeout
      const timeoutHandle = this.requestTimeouts.get(requestId);
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
        this.requestTimeouts.delete(requestId);
      }

      // Remove from active requests
      this.activeRequests.delete(requestId);

      // Emit events
      await this.apixGateway.emitToUser(
        userId,
        'human_input_cancelled',
        {
          requestId,
          reason,
          cancelledAt: new Date(),
        }
      );

      await this.apixGateway.emitToRoom(
        `workflow:${request.executionId}`,
        'human_input_cancelled',
        {
          requestId,
          nodeId: request.nodeId,
          reason,
          cancelledAt: new Date(),
        }
      );

      this.eventEmitter.emit('human_input.cancelled', {
        requestId,
        executionId: request.executionId,
        nodeId: request.nodeId,
        sessionId: request.sessionId,
        userId,
        reason,
        cancelledAt: new Date(),
      });

      this.logger.log(`Human input cancelled: ${requestId}, reason: ${reason}`);

    } catch (error) {
      this.logger.error(`Failed to cancel human input: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getRequest(requestId: string): Promise<HumanInputRequest | null> {
    try {
      // Try memory first
      let request = this.activeRequests.get(requestId);
      
      if (!request) {
        // Try cache
        request = await this.cacheManager.get(`human_input:${requestId}`) as HumanInputRequest;
      }

      if (!request) {
        // Try database
        const dbRequest = await this.prisma.workflowHumanInput.findUnique({
          where: { id: requestId },
        });

        if (dbRequest) {
          request = {
            id: dbRequest.id,
            executionId: dbRequest.executionId,
            nodeId: dbRequest.nodeId,
            sessionId: dbRequest.sessionId,
            userId: dbRequest.userId,
            organizationId: dbRequest.organizationId,
            prompt: dbRequest.prompt,
            inputType: dbRequest.inputType as any,
            validationRules: dbRequest.validationRules as any,
            timeout: dbRequest.timeout,
            allowSkip: dbRequest.allowSkip,
            skipValue: dbRequest.skipValue as any,
            status: dbRequest.status.toLowerCase() as any,
            response: dbRequest.response as any,
            metadata: dbRequest.metadata as any,
            createdAt: dbRequest.createdAt,
            expiresAt: dbRequest.expiresAt,
            completedAt: dbRequest.completedAt,
          };

          // Cache for future requests
          await this.cacheManager.set(`human_input:${requestId}`, request, 3600000);
        }
      }

      return request || null;

    } catch (error) {
      this.logger.error(`Failed to get request: ${error.message}`, error.stack);
      return null;
    }
  }

  async getUserPendingRequests(userId: string): Promise<HumanInputRequest[]> {
    try {
      const requests = await this.prisma.workflowHumanInput.findMany({
        where: {
          userId,
          status: 'PENDING',
          expiresAt: { gt: new Date() },
        },
        orderBy: { createdAt: 'desc' },
      });

      return requests.map(dbRequest => ({
        id: dbRequest.id,
        executionId: dbRequest.executionId,
        nodeId: dbRequest.nodeId,
        sessionId: dbRequest.sessionId,
        userId: dbRequest.userId,
        organizationId: dbRequest.organizationId,
        prompt: dbRequest.prompt,
        inputType: dbRequest.inputType as any,
        validationRules: dbRequest.validationRules as any,
        timeout: dbRequest.timeout,
        allowSkip: dbRequest.allowSkip,
        skipValue: dbRequest.skipValue as any,
        status: dbRequest.status.toLowerCase() as any,
        response: dbRequest.response as any,
        metadata: dbRequest.metadata as any,
        createdAt: dbRequest.createdAt,
        expiresAt: dbRequest.expiresAt,
        completedAt: dbRequest.completedAt,
      }));

    } catch (error) {
      this.logger.error(`Failed to get user pending requests: ${error.message}`, error.stack);
      return [];
    }
  }

  async getExecutionRequests(executionId: string): Promise<HumanInputRequest[]> {
    try {
      const requests = await this.prisma.workflowHumanInput.findMany({
        where: { executionId },
        orderBy: { createdAt: 'asc' },
      });

      return requests.map(dbRequest => ({
        id: dbRequest.id,
        executionId: dbRequest.executionId,
        nodeId: dbRequest.nodeId,
        sessionId: dbRequest.sessionId,
        userId: dbRequest.userId,
        organizationId: dbRequest.organizationId,
        prompt: dbRequest.prompt,
        inputType: dbRequest.inputType as any,
        validationRules: dbRequest.validationRules as any,
        timeout: dbRequest.timeout,
        allowSkip: dbRequest.allowSkip,
        skipValue: dbRequest.skipValue as any,
        status: dbRequest.status.toLowerCase() as any,
        response: dbRequest.response as any,
        metadata: dbRequest.metadata as any,
        createdAt: dbRequest.createdAt,
        expiresAt: dbRequest.expiresAt,
        completedAt: dbRequest.completedAt,
      }));

    } catch (error) {
      this.logger.error(`Failed to get execution requests: ${error.message}`, error.stack);
      return [];
    }
  }

  private async handleTimeout(requestId: string): Promise<void> {
    try {
      const request = this.activeRequests.get(requestId);
      
      if (!request || request.status !== 'pending') {
        return;
      }

      this.logger.log(`Human input request timed out: ${requestId}`);

      // Update status
      request.status = 'expired';
      request.completedAt = new Date();

      // Update in database
      await this.prisma.workflowHumanInput.update({
        where: { id: requestId },
        data: {
          status: 'EXPIRED',
          completedAt: new Date(),
        },
      });

      // Remove from active requests
      this.activeRequests.delete(requestId);
      this.requestTimeouts.delete(requestId);

      // Emit timeout events
      await this.apixGateway.emitToUser(
        request.userId,
        'human_input_timeout',
        {
          requestId,
          executionId: request.executionId,
          nodeId: request.nodeId,
          expiredAt: new Date(),
        },
        { priority: 'high' }
      );

      await this.apixGateway.emitToRoom(
        `workflow:${request.executionId}`,
        'human_input_timeout',
        {
          requestId,
          nodeId: request.nodeId,
          expiredAt: new Date(),
        }
      );

      this.eventEmitter.emit('human_input.timeout', {
        requestId,
        executionId: request.executionId,
        nodeId: request.nodeId,
        sessionId: request.sessionId,
        userId: request.userId,
        expiredAt: new Date(),
      });

    } catch (error) {
      this.logger.error(`Failed to handle timeout for request ${requestId}: ${error.message}`, error.stack);
    }
  }

  private validateInput(
    input: any,
    inputType: string,
    validationRules?: any,
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!validationRules) {
      return { valid: true, errors: [] };
    }

    // Required validation
    if (validationRules.required && (input === null || input === undefined || input === '')) {
      errors.push('Input is required');
      return { valid: false, errors };
    }

    // Skip further validation if input is empty and not required
    if (!validationRules.required && (input === null || input === undefined || input === '')) {
      return { valid: true, errors: [] };
    }

    // Type-specific validation
    switch (inputType) {
      case 'text':
        if (typeof input !== 'string') {
          errors.push('Input must be a string');
        } else {
          if (validationRules.minLength && input.length < validationRules.minLength) {
            errors.push(`Input must be at least ${validationRules.minLength} characters long`);
          }
          if (validationRules.maxLength && input.length > validationRules.maxLength) {
            errors.push(`Input must be no more than ${validationRules.maxLength} characters long`);
          }
          if (validationRules.pattern && !new RegExp(validationRules.pattern).test(input)) {
            errors.push('Input does not match the required pattern');
          }
        }
        break;

      case 'number':
        if (typeof input !== 'number' && !Number.isFinite(Number(input))) {
          errors.push('Input must be a valid number');
        }
        break;

      case 'boolean':
        if (typeof input !== 'boolean') {
          errors.push('Input must be true or false');
        }
        break;

      case 'select':
        if (validationRules.options && !validationRules.options.includes(input)) {
          errors.push(`Input must be one of: ${validationRules.options.join(', ')}`);
        }
        break;

      case 'multiselect':
        if (!Array.isArray(input)) {
          errors.push('Input must be an array');
        } else if (validationRules.options) {
          const invalidOptions = input.filter(item => !validationRules.options.includes(item));
          if (invalidOptions.length > 0) {
            errors.push(`Invalid options: ${invalidOptions.join(', ')}`);
          }
        }
        break;

      case 'json':
        try {
          if (typeof input === 'string') {
            JSON.parse(input);
          } else if (typeof input !== 'object') {
            errors.push('Input must be valid JSON');
          }
        } catch {
          errors.push('Input must be valid JSON');
        }
        break;
    }

    return { valid: errors.length === 0, errors };
  }

  private processInput(input: any, inputType: string): any {
    switch (inputType) {
      case 'number':
        return typeof input === 'number' ? input : Number(input);
      
      case 'boolean':
        return typeof input === 'boolean' ? input : Boolean(input);
      
      case 'json':
        return typeof input === 'string' ? JSON.parse(input) : input;
      
      default:
        return input;
    }
  }

  private async cleanupExpiredRequests(): Promise<void> {
    try {
      const expiredRequests = Array.from(this.activeRequests.entries())
        .filter(([_, request]) => new Date() > request.expiresAt && request.status === 'pending');

      for (const [requestId, _] of expiredRequests) {
        await this.handleTimeout(requestId);
      }

      // Also cleanup database
      await this.prisma.workflowHumanInput.updateMany({
        where: {
          status: 'PENDING',
          expiresAt: { lt: new Date() },
        },
        data: {
          status: 'EXPIRED',
          completedAt: new Date(),
        },
      });

    } catch (error) {
      this.logger.error(`Failed to cleanup expired requests: ${error.message}`, error.stack);
    }
  }

  // Analytics and monitoring methods

  async getHumanInputAnalytics(organizationId: string, timeRange?: { start: Date; end: Date }) {
    try {
      const where: any = { organizationId };
      
      if (timeRange) {
        where.createdAt = {
          gte: timeRange.start,
          lte: timeRange.end,
        };
      }

      const requests = await this.prisma.workflowHumanInput.findMany({
        where,
        select: {
          status: true,
          inputType: true,
          timeout: true,
          createdAt: true,
          completedAt: true,
          allowSkip: true,
        },
      });

      const totalRequests = requests.length;
      const completedRequests = requests.filter(r => r.status === 'COMPLETED').length;
      const skippedRequests = requests.filter(r => r.status === 'SKIPPED').length;
      const expiredRequests = requests.filter(r => r.status === 'EXPIRED').length;
      const cancelledRequests = requests.filter(r => r.status === 'CANCELLED').length;

      const completionRate = totalRequests > 0 ? completedRequests / totalRequests : 0;
      const skipRate = totalRequests > 0 ? skippedRequests / totalRequests : 0;
      const timeoutRate = totalRequests > 0 ? expiredRequests / totalRequests : 0;

      // Calculate average response time
      const completedWithTimes = requests.filter(r => r.completedAt && r.status === 'COMPLETED');
      const avgResponseTime = completedWithTimes.length > 0
        ? completedWithTimes.reduce((sum, r) => 
            sum + (r.completedAt!.getTime() - r.createdAt.getTime()), 0) / completedWithTimes.length
        : 0;

      // Input type distribution
      const inputTypeDistribution = requests.reduce((acc, r) => {
        acc[r.inputType] = (acc[r.inputType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalRequests,
        completedRequests,
        skippedRequests,
        expiredRequests,
        cancelledRequests,
        completionRate,
        skipRate,
        timeoutRate,
        avgResponseTime: Math.round(avgResponseTime / 1000), // in seconds
        inputTypeDistribution,
      };

    } catch (error) {
      this.logger.error(`Failed to get human input analytics: ${error.message}`, error.stack);
      return {
        totalRequests: 0,
        completedRequests: 0,
        skippedRequests: 0,
        expiredRequests: 0,
        cancelledRequests: 0,
        completionRate: 0,
        skipRate: 0,
        timeoutRate: 0,
        avgResponseTime: 0,
        inputTypeDistribution: {},
      };
    }
  }

  getActiveRequestsCount(): number {
    return this.activeRequests.size;
  }

  getActiveRequestsForUser(userId: string): HumanInputRequest[] {
    return Array.from(this.activeRequests.values())
      .filter(request => request.userId === userId && request.status === 'pending');
  }
}