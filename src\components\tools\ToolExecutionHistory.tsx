"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  RefreshCw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Timer,
  DollarSign,
  Zap,
  Eye,
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  PieChart,
  Users,
  Database,
  Globe,
  Code,
  Bot,
  Wrench,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Play,
  Pause,
  Square,
  ChevronRight,
  ChevronDown,
  Copy,
  ExternalLink
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import apiClient from '@/lib/api-client';
import { useToast } from '@/components/ui/use-toast';

interface ToolExecution {
  id: string;
  toolId: string;
  toolName: string;
  toolType: 'FUNCTION_CALL' | 'API_FETCH' | 'RAG_QUERY' | 'DB_QUERY' | 'BROWSER_AUTOMATION' | 'CUSTOM_SCRIPT';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'TIMEOUT';
  executorType: string;
  executorId?: string;
  sessionId?: string;
  input: any;
  output?: any;
  error?: string;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  tokensUsed: number;
  cost: number;
  cached: boolean;
  retryCount: number;
  metadata?: any;
}

interface ExecutionStats {
  totalExecutions: number;
  successRate: number;
  avgDuration: number;
  totalCost: number;
  cacheHitRate: number;
  topTools: Array<{
    toolId: string;
    toolName: string;
    executions: number;
    successRate: number;
  }>;
}

const TOOL_TYPES = [
  { value: 'FUNCTION_CALL', label: 'Function Call', icon: Code },
  { value: 'API_FETCH', label: 'API Fetch', icon: Globe },
  { value: 'RAG_QUERY', label: 'RAG Query', icon: Database },
  { value: 'DB_QUERY', label: 'Database Query', icon: Database },
  { value: 'BROWSER_AUTOMATION', label: 'Browser Automation', icon: Bot },
  { value: 'CUSTOM_SCRIPT', label: 'Custom Script', icon: Wrench },
];

const STATUS_CONFIG = {
  PENDING: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  PROCESSING: { color: 'bg-blue-100 text-blue-800', icon: RefreshCw },
  COMPLETED: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
  FAILED: { color: 'bg-red-100 text-red-800', icon: XCircle },
  TIMEOUT: { color: 'bg-orange-100 text-orange-800', icon: AlertCircle },
};

export default function ProductionToolExecutionHistory() {
  const [executions, setExecutions] = useState<ToolExecution[]>([]);
  const [filteredExecutions, setFilteredExecutions] = useState<ToolExecution[]>([]);
  const [stats, setStats] = useState<ExecutionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [toolTypeFilter, setToolTypeFilter] = useState<string>('all');
  const [timeRangeFilter, setTimeRangeFilter] = useState<string>('24h');
  const [selectedExecution, setSelectedExecution] = useState<ToolExecution | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const { toast } = useToast();
  // Load execution history from real API
  const loadExecutionHistory = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/tools/executions?limit=50');
      const executionsData = (response as any).executions || [];

      setExecutions(executionsData);

      // Calculate real stats from actual data
      const totalExecutions = executionsData.length;
      const successfulExecutions = executionsData.filter((e: any) => e.status === 'COMPLETED').length;
      const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;
      const avgDuration = executionsData.filter((e: any) => e.duration).reduce((sum: any, e: any) => sum + (e.duration || 0), 0) / executionsData.filter((e: any) => e.duration).length || 0;
      const totalCost = executionsData.reduce((sum: any, e: any) => sum + (e.cost || 0), 0);
      const cacheHitRate = executionsData.length > 0 ? (executionsData.filter((e: any) => e.cached).length / executionsData.length) * 100 : 0;

      // Get top tools by execution count
      const toolStats = executionsData.reduce((acc: any, execution: any) => {
        const toolId = execution.toolId || 'unknown';
        const toolName = execution.tool?.name || 'Unknown Tool';

        if (!acc[toolId]) {
          acc[toolId] = { toolId, toolName, executions: 0, successCount: 0 };
        }

        acc[toolId].executions++;
        if (execution.status === 'COMPLETED') {
          acc[toolId].successCount++;
        }

        return acc;
      }, {} as Record<string, any>);

      const topTools = Object.values(toolStats)
        .map((tool: any) => ({
          ...tool,
          successRate: tool.executions > 0 ? (tool.successCount / tool.executions) * 100 : 0
        }))
        .sort((a: any, b: any) => b.executions - a.executions)
        .slice(0, 3);

      setStats({
        totalExecutions,
        successRate,
        avgDuration,
        totalCost,
        cacheHitRate,
        topTools
      });

    } catch (error: any) {
      console.error('Failed to load execution history:', error);
      toast({
        title: "Error",
        description: "Failed to load execution history.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadExecutionHistory();
  }, [loadExecutionHistory]);

  // Filter executions
  useEffect(() => {
    let filtered = executions;

    if (searchQuery) {
      filtered = filtered.filter((execution: any) =>
        execution.toolName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        execution.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        execution.executorType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter((execution: any) => execution.status === statusFilter);
    }

    if (toolTypeFilter !== 'all') {
      filtered = filtered.filter((execution: any) => execution.toolType === toolTypeFilter);
    }

    if (timeRangeFilter !== 'all') {
      const now = new Date();
      let cutoff: Date;

      switch (timeRangeFilter) {
        case '1h':
          cutoff = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          cutoff = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          cutoff = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoff = new Date(0);
      }

      filtered = filtered.filter((execution: any) => new Date(execution.startedAt) >= cutoff);
    }

    setFilteredExecutions(filtered);
    setCurrentPage(1);
  }, [executions, searchQuery, statusFilter, toolTypeFilter, timeRangeFilter]);

  const getTypeIcon = (type: string) => {
    const typeConfig = TOOL_TYPES.find((t: any) => t.value === type);
    return typeConfig?.icon || Code;
  };

  const getStatusConfig = (status: string) => {
    return STATUS_CONFIG[status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.PENDING;
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const formatCost = (cost: number) => {
    return `$${cost.toFixed(4)}`;
  };

  const toggleRowExpansion = (executionId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(executionId)) {
      newExpanded.delete(executionId);
    } else {
      newExpanded.add(executionId);
    }
    setExpandedRows(newExpanded);
  };

  const paginatedExecutions = filteredExecutions.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const totalPages = Math.ceil(filteredExecutions.length / pageSize);

  const ExecutionRow = ({ execution }: { execution: ToolExecution }) => {
    const TypeIcon = getTypeIcon(execution.toolType);
    const statusConfig = getStatusConfig(execution.status);
    const StatusIcon = statusConfig.icon;
    const isExpanded = expandedRows.has(execution.id);

    return (
      <>
        <tr className="hover:bg-gray-50 border-b border-gray-100">
          <td className="px-4 py-3">
            <button
              onClick={() => toggleRowExpansion(execution.id)}
              className="flex items-center gap-2 text-left"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 text-gray-400" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-400" />
              )}
              <div className="flex items-center gap-2">
                <TypeIcon className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="font-medium text-sm">{execution.toolName}</div>
                  <div className="text-xs text-gray-500">{execution.id}</div>
                </div>
              </div>
            </button>
          </td>
          <td className="px-4 py-3">
            <Badge className={`${statusConfig.color} text-xs`}>
              <StatusIcon className="h-3 w-3 mr-1" />
              {execution.status}
            </Badge>
          </td>
          <td className="px-4 py-3">
            <div className="text-sm text-gray-600">{execution.executorType}</div>
          </td>
          <td className="px-4 py-3">
            <div className="text-sm text-gray-600">{formatDuration(execution.duration)}</div>
          </td>
          <td className="px-4 py-3">
            <div className="text-sm text-gray-600">{formatCost(execution.cost)}</div>
          </td>
          <td className="px-4 py-3">
            {execution.cached && (
              <Badge variant="outline" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                Cached
              </Badge>
            )}
          </td>
          <td className="px-4 py-3">
            <div className="text-sm text-gray-600">{new Date(execution.startedAt).toLocaleString()}</div>
          </td>
          <td className="px-4 py-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => {
                  setSelectedExecution(execution);
                  setShowDetails(true);
                }}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy ID
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Tool
                </DropdownMenuItem>
                {execution.status === 'FAILED' && (
                  <DropdownMenuItem>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </td>
        </tr>
        {isExpanded && (
          <tr className="bg-gray-50">
            <td colSpan={8} className="px-4 py-4">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-sm mb-2">Input</h4>
                    <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-32">
                      {JSON.stringify(execution.input, null, 2)}
                    </pre>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm mb-2">
                      {execution.status === 'FAILED' ? 'Error' : 'Output'}
                    </h4>
                    <pre className="bg-white p-3 rounded border text-xs overflow-auto max-h-32">
                      {execution.status === 'FAILED'
                        ? execution.error
                        : JSON.stringify(execution.output, null, 2)
                      }
                    </pre>
                  </div>
                </div>
                {execution.metadata && (
                  <div>
                    <h4 className="font-medium text-sm mb-2">Metadata</h4>
                    <div className="flex gap-2 flex-wrap">
                      {Object.entries(execution.metadata).map(([key, value]) => (
                        <Badge key={key} variant="outline" className="text-xs">
                          {key}: {String(value)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </td>
          </tr>
        )}
      </>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-4 gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Execution History</h1>
            <p className="text-gray-600 mt-1">Monitor and analyze tool execution performance</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <Activity className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.totalExecutions.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">Total Executions</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{stats.successRate.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-50 rounded-lg">
                    <Timer className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{formatDuration(stats.avgDuration)}</div>
                    <div className="text-sm text-gray-600">Avg Duration</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-50 rounded-lg">
                    <DollarSign className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{formatCost(stats.totalCost)}</div>
                    <div className="text-sm text-gray-600">Total Cost</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-50 rounded-lg">
                    <Zap className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.cacheHitRate.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Cache Hit Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search executions..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full lg:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                  <SelectItem value="PROCESSING">Processing</SelectItem>
                  <SelectItem value="TIMEOUT">Timeout</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                </SelectContent>
              </Select>
              <Select value={toolTypeFilter} onValueChange={setToolTypeFilter}>
                <SelectTrigger className="w-full lg:w-48">
                  <SelectValue placeholder="Tool Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {TOOL_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={timeRangeFilter} onValueChange={setTimeRangeFilter}>
                <SelectTrigger className="w-full lg:w-32">
                  <SelectValue placeholder="Time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Execution Table */}
        <Card className="bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>Execution History</CardTitle>
            <CardDescription>
              {filteredExecutions.length} execution{filteredExecutions.length !== 1 ? 's' : ''} found
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tool
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Executor
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cost
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cache
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Started
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {paginatedExecutions.map(execution => (
                    <ExecutionRow key={execution.id} execution={execution} />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredExecutions.length)} of {filteredExecutions.length} results
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Execution Details Dialog */}
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Execution Details</DialogTitle>
              <DialogDescription>
                Detailed information about the tool execution
              </DialogDescription>
            </DialogHeader>
            {selectedExecution && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Basic Information</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Execution ID:</span>
                        <span className="font-mono">{selectedExecution.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tool:</span>
                        <span>{selectedExecution.toolName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Type:</span>
                        <span>{selectedExecution.toolType.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <Badge className={getStatusConfig(selectedExecution.status).color}>
                          {selectedExecution.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Performance Metrics</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration:</span>
                        <span>{formatDuration(selectedExecution.duration)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Cost:</span>
                        <span>{formatCost(selectedExecution.cost)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tokens Used:</span>
                        <span>{selectedExecution.tokensUsed.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Cached:</span>
                        <span>{selectedExecution.cached ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <Tabs defaultValue="input" className="w-full">
                  <TabsList>
                    <TabsTrigger value="input">Input</TabsTrigger>
                    <TabsTrigger value="output">
                      {selectedExecution.status === 'FAILED' ? 'Error' : 'Output'}
                    </TabsTrigger>
                    <TabsTrigger value="metadata">Metadata</TabsTrigger>
                  </TabsList>
                  <TabsContent value="input">
                    <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-auto max-h-64">
                      {JSON.stringify(selectedExecution.input, null, 2)}
                    </pre>
                  </TabsContent>
                  <TabsContent value="output">
                    <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-auto max-h-64">
                      {selectedExecution.status === 'FAILED'
                        ? selectedExecution.error
                        : JSON.stringify(selectedExecution.output, null, 2)
                      }
                    </pre>
                  </TabsContent>
                  <TabsContent value="metadata">
                    <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-auto max-h-64">
                      {JSON.stringify(selectedExecution.metadata, null, 2)}
                    </pre>
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}