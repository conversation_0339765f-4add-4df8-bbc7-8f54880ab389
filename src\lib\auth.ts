/**
 * @deprecated Use @/lib/auth/auth-service.ts instead
 * This file is maintained for backward compatibility and will be removed in future versions
 */

import { NextRequest } from 'next/server';
import {
    getAuthenticatedSession as getSession,
    hasPermission as checkPermission,
    hasRole as checkRole,
    unauthorizedResponse as unauthorized,
    forbiddenResponse as forbidden
} from '@/lib/auth/auth-service';
import { SessionUser, Session } from '@/lib/types/auth';

// Re-export functions from auth-service for backward compatibility
export async function getAuthenticatedSession(): Promise<Session | null> {
    return getSession();
}

export async function getAuthenticatedUser(request: NextRequest): Promise<SessionUser | null> {
    const session = await getSession(request);
    return session?.user || null;
}

export function hasPermission(user: SessionUser, permission: string): boolean {
    return checkPermission(user, permission);
}

export function hasRole(user: SessionUser, roles: string[]): boolean {
    return checkRole(user, roles as any);
}

export function unauthorizedResponse() {
    return unauthorized();
}

export function forbiddenResponse() {
    return forbidden();
}

// Re-export types for backward compatibility
export type { SessionUser, Session };