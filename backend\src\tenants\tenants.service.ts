import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTenantDto, UpdateTenantDto, CreateUserDto } from './dto/tenant.dto';
import { Role } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class TenantsService {
  constructor(private prisma: PrismaService) {}

  async createTenant(createTenantDto: CreateTenantDto) {
    const { name, slug, domain, adminUser } = createTenantDto;

    return this.prisma.$transaction(async (tx) => {
      // Create organization
      const organization = await tx.organization.create({
        data: {
          name,
          slug,
          domain,
          settings: {
            theme: 'light',
            language: 'en',
            timezone: 'UTC',
            features: {
              workflows: true,
              agents: true,
              tools: true,
              analytics: true,
            },
          },
          branding: {
            primaryColor: '#3b82f6',
            secondaryColor: '#64748b',
            logo: null,
            favicon: null,
          },
        },
      });

      // Create admin user
      const hashedPassword = await bcrypt.hash(adminUser.password, 12);
      const user = await tx.user.create({
        data: {
          email: adminUser.email,
          password: hashedPassword,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName,
          role: Role.ORG_ADMIN,
          organizationId: organization.id,
          preferences: {
            theme: 'system',
            notifications: true,
            viewMode: 'grid',
            language: 'en',
          },
        },
      });

      return { organization, user };
    });
  }

  async getTenants(page = 1, limit = 10, search?: string) {
    const skip = (page - 1) * limit;
    
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { slug: { contains: search, mode: 'insensitive' as const } },
            { domain: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [organizations, total] = await Promise.all([
      this.prisma.organization.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              users: true,
              workflows: true,
              agents: true,
              tools: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.organization.count({ where }),
    ]);

    return {
      data: organizations,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getTenantById(id: string) {
    return this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            workflows: true,
            agents: true,
            tools: true,
            sessions: true,
          },
        },
      },
    });
  }

  async updateTenant(id: string, updateTenantDto: UpdateTenantDto) {
    return this.prisma.organization.update({
      where: { id },
      data: updateTenantDto,
    });
  }

  async deleteTenant(id: string) {
    // This will cascade delete all related data due to Prisma schema
    return this.prisma.organization.delete({
      where: { id },
    });
  }

  async getTenantUsers(organizationId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where: { organizationId },
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          lastLoginAt: true,
          createdAt: true,
          preferences: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.user.count({ where: { organizationId } }),
    ]);

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async createTenantUser(organizationId: string, createUserDto: CreateUserDto) {
    const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

    return this.prisma.user.create({
      data: {
        email: createUserDto.email,
        password: hashedPassword,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        role: createUserDto.role || Role.VIEWER,
        organizationId,
        preferences: {
          theme: 'system',
          notifications: true,
          viewMode: 'grid',
          language: 'en',
        },
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });
  }

  async updateTenantUser(organizationId: string, userId: string, updateData: Partial<CreateUserDto>) {
    const data: any = { ...updateData };
    
    if (updateData.password) {
      data.password = await bcrypt.hash(updateData.password, 12);
    }

    return this.prisma.user.update({
      where: { 
        id: userId,
        organizationId, // Ensure user belongs to organization
      },
      data,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        updatedAt: true,
      },
    });
  }

  async deleteTenantUser(organizationId: string, userId: string) {
    return this.prisma.user.delete({
      where: { 
        id: userId,
        organizationId, // Ensure user belongs to organization
      },
    });
  }

  async getTenantAnalytics(organizationId: string) {
    const [
      userStats,
      workflowStats,
      agentStats,
      sessionStats,
      recentActivity,
    ] = await Promise.all([
      this.prisma.user.groupBy({
        by: ['role'],
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.workflow.groupBy({
        by: ['isActive'],
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.agent.groupBy({
        by: ['type'],
        where: { organizationId },
        _count: { id: true },
      }),
      this.prisma.session.count({
        where: { 
          organizationId,
          isActive: true,
        },
      }),
      this.prisma.auditLog.findMany({
        where: { organizationId },
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      }),
    ]);

    return {
      users: userStats,
      workflows: workflowStats,
      agents: agentStats,
      activeSessions: sessionStats,
      recentActivity,
    };
  }
}