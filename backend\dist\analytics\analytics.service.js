"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let AnalyticsService = class AnalyticsService {
    constructor(prisma, cacheManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
    }
    async trackEvent(event) {
        await this.prisma.auditLog.create({
            data: {
                userId: event.userId,
                organizationId: event.organizationId,
                action: event.event,
                resource: 'analytics',
                details: event.properties,
            },
        });
        const cacheKey = `analytics:${event.organizationId}:${event.event}:${new Date().toISOString().split('T')[0]}`;
        const currentCount = await this.cacheManager.get(cacheKey) || 0;
        await this.cacheManager.set(cacheKey, currentCount + 1, 86400);
        return true;
    }
    async getDashboardAnalytics(organizationId) {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const lastWeek = new Date(today);
        lastWeek.setDate(lastWeek.getDate() - 7);
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        const [totalUsers, activeUsers, totalWorkflows, activeWorkflows, totalAgents, activeAgents, totalSessions, activeSessions, workflowExecutions, recentActivity,] = await Promise.all([
            this.prisma.user.count({
                where: { organizationId },
            }),
            this.prisma.user.count({
                where: {
                    organizationId,
                    isActive: true,
                    lastLoginAt: { gte: lastWeek },
                },
            }),
            this.prisma.workflow.count({
                where: { organizationId },
            }),
            this.prisma.workflow.count({
                where: {
                    organizationId,
                    isActive: true,
                },
            }),
            this.prisma.agent.count({
                where: { organizationId },
            }),
            this.prisma.agent.count({
                where: {
                    organizationId,
                    isActive: true,
                },
            }),
            this.prisma.session.count({
                where: { organizationId },
            }),
            this.prisma.session.count({
                where: {
                    organizationId,
                    isActive: true,
                },
            }),
            this.prisma.workflowExecution.count({
                where: {
                    workflow: { organizationId },
                    startedAt: { gte: lastMonth },
                },
            }),
            this.prisma.auditLog.findMany({
                where: { organizationId },
                take: 20,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
        ]);
        return {
            overview: {
                totalUsers,
                activeUsers,
                totalWorkflows,
                activeWorkflows,
                totalAgents,
                activeAgents,
                totalSessions,
                activeSessions,
                workflowExecutions,
            },
            recentActivity,
        };
    }
    async getWorkflowAnalytics(organizationId, timeRange = '7d') {
        const endDate = new Date();
        const startDate = new Date();
        switch (timeRange) {
            case '1d':
                startDate.setDate(startDate.getDate() - 1);
                break;
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(startDate.getDate() - 90);
                break;
        }
        const [executionStats, statusDistribution, topWorkflows, executionTrends,] = await Promise.all([
            this.prisma.workflowExecution.aggregate({
                where: {
                    workflow: { organizationId },
                    startedAt: { gte: startDate },
                },
                _count: { id: true },
                _avg: { duration: true },
            }),
            this.prisma.workflowExecution.groupBy({
                by: ['status'],
                where: {
                    workflow: { organizationId },
                    startedAt: { gte: startDate },
                },
                _count: { id: true },
            }),
            this.prisma.workflowExecution.groupBy({
                by: ['workflowId'],
                where: {
                    workflow: { organizationId },
                    startedAt: { gte: startDate },
                },
                _count: { id: true },
                orderBy: { _count: { id: 'desc' } },
                take: 10,
            }),
            this.getExecutionTrends(organizationId, startDate, endDate),
        ]);
        return {
            stats: {
                totalExecutions: executionStats._count.id,
                avgDuration: executionStats._avg.duration,
            },
            statusDistribution,
            topWorkflows,
            trends: executionTrends,
        };
    }
    async getAgentAnalytics(organizationId, timeRange = '7d') {
        const endDate = new Date();
        const startDate = new Date();
        switch (timeRange) {
            case '1d':
                startDate.setDate(startDate.getDate() - 1);
                break;
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                break;
        }
        const [agentStats, typeDistribution, sessionStats, topAgents,] = await Promise.all([
            this.prisma.agent.aggregate({
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.agent.groupBy({
                by: ['type'],
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.agentSession.aggregate({
                where: {
                    agent: { organizationId },
                    createdAt: { gte: startDate },
                },
                _count: { id: true },
            }),
            this.prisma.agentSession.groupBy({
                by: ['agentId'],
                where: {
                    agent: { organizationId },
                    createdAt: { gte: startDate },
                },
                _count: { id: true },
                orderBy: { _count: { id: 'desc' } },
                take: 10,
            }),
        ]);
        return {
            stats: {
                totalAgents: agentStats._count.id,
                totalSessions: sessionStats._count.id,
            },
            typeDistribution,
            topAgents,
        };
    }
    async getUserAnalytics(organizationId, timeRange = '7d') {
        const endDate = new Date();
        const startDate = new Date();
        switch (timeRange) {
            case '1d':
                startDate.setDate(startDate.getDate() - 1);
                break;
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                break;
        }
        const [userStats, roleDistribution, activeUsers, loginTrends,] = await Promise.all([
            this.prisma.user.aggregate({
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.user.groupBy({
                by: ['role'],
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.user.count({
                where: {
                    organizationId,
                    lastLoginAt: { gte: startDate },
                },
            }),
            this.getLoginTrends(organizationId, startDate, endDate),
        ]);
        return {
            stats: {
                totalUsers: userStats._count.id,
                activeUsers,
            },
            roleDistribution,
            trends: loginTrends,
        };
    }
    async getSystemHealth(organizationId) {
        const [errorRate, avgResponseTime, uptime, resourceUsage,] = await Promise.all([
            this.calculateErrorRate(organizationId),
            this.calculateAvgResponseTime(organizationId),
            this.calculateUptime(organizationId),
            this.getResourceUsage(organizationId),
        ]);
        return {
            errorRate,
            avgResponseTime,
            uptime,
            resourceUsage,
            status: this.getSystemStatus(errorRate, avgResponseTime, uptime),
        };
    }
    async getExecutionTrends(organizationId, startDate, endDate) {
        const executions = await this.prisma.workflowExecution.findMany({
            where: {
                workflow: { organizationId },
                startedAt: { gte: startDate, lte: endDate },
            },
            select: {
                startedAt: true,
                status: true,
            },
        });
        const trends = {};
        executions.forEach(execution => {
            const day = execution.startedAt.toISOString().split('T')[0];
            if (!trends[day]) {
                trends[day] = {};
            }
            trends[day][execution.status] = (trends[day][execution.status] || 0) + 1;
        });
        return trends;
    }
    async getLoginTrends(organizationId, startDate, endDate) {
        const logins = await this.prisma.auditLog.findMany({
            where: {
                organizationId,
                action: 'USER_LOGIN',
                createdAt: { gte: startDate, lte: endDate },
            },
            select: {
                createdAt: true,
            },
        });
        const trends = {};
        logins.forEach(login => {
            const day = login.createdAt.toISOString().split('T')[0];
            trends[day] = (trends[day] || 0) + 1;
        });
        return trends;
    }
    async calculateErrorRate(organizationId) {
        const totalExecutions = await this.prisma.workflowExecution.count({
            where: { workflow: { organizationId } },
        });
        const failedExecutions = await this.prisma.workflowExecution.count({
            where: {
                workflow: { organizationId },
                status: 'FAILED',
            },
        });
        return totalExecutions > 0 ? (failedExecutions / totalExecutions) * 100 : 0;
    }
    async calculateAvgResponseTime(organizationId) {
        const result = await this.prisma.workflowExecution.aggregate({
            where: { workflow: { organizationId } },
            _avg: { duration: true },
        });
        return result._avg.duration || 0;
    }
    async calculateUptime(organizationId) {
        const recentExecutions = await this.prisma.workflowExecution.count({
            where: {
                workflow: { organizationId },
                startedAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
            },
        });
        const successfulExecutions = await this.prisma.workflowExecution.count({
            where: {
                workflow: { organizationId },
                status: 'COMPLETED',
                startedAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
            },
        });
        return recentExecutions > 0 ? (successfulExecutions / recentExecutions) * 100 : 100;
    }
    async getResourceUsage(organizationId) {
        const [sessions, workflows, agents] = await Promise.all([
            this.prisma.session.count({
                where: { organizationId, isActive: true },
            }),
            this.prisma.workflow.count({
                where: { organizationId, isActive: true },
            }),
            this.prisma.agent.count({
                where: { organizationId, isActive: true },
            }),
        ]);
        return {
            activeSessions: sessions,
            activeWorkflows: workflows,
            activeAgents: agents,
        };
    }
    getSystemStatus(errorRate, avgResponseTime, uptime) {
        if (errorRate > 5 || avgResponseTime > 5000 || uptime < 95) {
            return 'critical';
        }
        else if (errorRate > 2 || avgResponseTime > 2000 || uptime < 98) {
            return 'warning';
        }
        else {
            return 'healthy';
        }
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map