/**
 * Session API Route
 * Production-grade session endpoint for client-side authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession } from '@/lib/auth/auth-service';

export async function GET(req: NextRequest): Promise<NextResponse> {
    try {
        // Get authenticated session
        const user = await getAuthenticatedSession(req);

        if (!user) {
            return NextResponse.json({
                user: null,
                isAuthenticated: false
            });
        }

        return NextResponse.json({
            user,
            isAuthenticated: true,
            expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutes from now
        });
    } catch (error: any) {
        console.error('Session error:', error);

        return NextResponse.json({
            user: null,
            isAuthenticated: false,
            error: error.message || 'Session verification failed'
        });
    }
}