"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var HumanInTheLoopService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanInTheLoopService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const sessions_service_1 = require("../../sessions/sessions.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let HumanInTheLoopService = HumanInTheLoopService_1 = class HumanInTheLoopService {
    constructor(prisma, apixGateway, sessionsService, eventEmitter, cacheManager) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.sessionsService = sessionsService;
        this.eventEmitter = eventEmitter;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(HumanInTheLoopService_1.name);
        this.activeRequests = new Map();
        this.requestTimeouts = new Map();
        setInterval(() => this.cleanupExpiredRequests(), 60000);
    }
    async requestHumanInput(executionId, nodeId, sessionId, prompt, inputType = 'text', options = {}) {
        try {
            const session = await this.sessionsService.getSession(sessionId);
            if (!session) {
                throw new Error(`Session not found: ${sessionId}`);
            }
            const requestId = `human_input_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const timeout = options.timeout || 300000;
            const expiresAt = new Date(Date.now() + timeout);
            const request = {
                id: requestId,
                executionId,
                nodeId,
                sessionId,
                userId: session.userId,
                organizationId: session.organizationId,
                prompt,
                inputType: inputType,
                validationRules: options.validationRules,
                timeout,
                allowSkip: options.allowSkip || false,
                skipValue: options.skipValue,
                status: 'pending',
                metadata: options.metadata,
                createdAt: new Date(),
                expiresAt,
            };
            await this.prisma.workflowHumanInput.create({
                data: {
                    id: requestId,
                    executionId,
                    nodeId,
                    sessionId,
                    userId: session.userId,
                    organizationId: session.organizationId,
                    prompt,
                    inputType,
                    validationRules: options.validationRules,
                    timeout,
                    allowSkip: options.allowSkip || false,
                    skipValue: options.skipValue,
                    status: 'PENDING',
                    metadata: options.metadata,
                    expiresAt,
                },
            });
            this.activeRequests.set(requestId, request);
            await this.cacheManager.set(`human_input:${requestId}`, request, timeout);
            const timeoutHandle = setTimeout(() => {
                this.handleTimeout(requestId);
            }, timeout);
            this.requestTimeouts.set(requestId, timeoutHandle);
            await this.apixGateway.emitToUser(session.userId, 'human_input_requested', {
                requestId,
                executionId,
                nodeId,
                prompt,
                inputType,
                validationRules: options.validationRules,
                timeout,
                allowSkip: options.allowSkip || false,
                expiresAt: expiresAt.toISOString(),
                metadata: options.metadata,
            }, { priority: 'high' });
            await this.apixGateway.emitToRoom(`workflow:${executionId}`, 'human_input_requested', {
                requestId,
                nodeId,
                prompt,
                inputType,
                timeout,
                allowSkip: options.allowSkip || false,
            });
            this.eventEmitter.emit('human_input.requested', {
                requestId,
                executionId,
                nodeId,
                sessionId,
                userId: session.userId,
                organizationId: session.organizationId,
                prompt,
                inputType,
                timeout,
            });
            this.logger.log(`Human input requested: ${requestId} for execution: ${executionId}`);
            return requestId;
        }
        catch (error) {
            this.logger.error(`Failed to request human input: ${error.message}`, error.stack);
            throw error;
        }
    }
    async provideHumanInput(requestId, userInput, userId, metadata) {
        try {
            const request = await this.getRequest(requestId);
            if (!request) {
                throw new common_1.NotFoundException(`Human input request not found: ${requestId}`);
            }
            if (request.userId !== userId) {
                throw new Error('Unauthorized: User does not own this request');
            }
            if (request.status !== 'pending') {
                throw new Error(`Request is not pending: ${request.status}`);
            }
            if (new Date() > request.expiresAt) {
                throw new Error('Request has expired');
            }
            const validationResult = this.validateInput(userInput, request.inputType, request.validationRules);
            if (!validationResult.valid) {
                return {
                    requestId,
                    userInput,
                    inputType: request.inputType,
                    skipped: false,
                    validationPassed: false,
                    validationErrors: validationResult.errors,
                    completedAt: new Date(),
                };
            }
            const processedInput = this.processInput(userInput, request.inputType);
            const completedAt = new Date();
            request.status = 'completed';
            request.response = processedInput;
            request.completedAt = completedAt;
            await this.prisma.workflowHumanInput.update({
                where: { id: requestId },
                data: {
                    status: 'COMPLETED',
                    response: processedInput,
                    completedAt,
                    metadata: { ...request.metadata, ...metadata },
                },
            });
            await this.cacheManager.set(`human_input:${requestId}`, request, 3600000);
            const timeoutHandle = this.requestTimeouts.get(requestId);
            if (timeoutHandle) {
                clearTimeout(timeoutHandle);
                this.requestTimeouts.delete(requestId);
            }
            const response = {
                requestId,
                userInput: processedInput,
                inputType: request.inputType,
                skipped: false,
                validationPassed: true,
                metadata: { ...request.metadata, ...metadata },
                completedAt,
            };
            await this.apixGateway.emitToUser(userId, 'human_input_completed', response, { priority: 'high' });
            await this.apixGateway.emitToRoom(`workflow:${request.executionId}`, 'human_input_received', {
                requestId,
                nodeId: request.nodeId,
                userInput: processedInput,
                completedAt,
            });
            this.eventEmitter.emit('human_input.completed', {
                requestId,
                executionId: request.executionId,
                nodeId: request.nodeId,
                sessionId: request.sessionId,
                userId,
                userInput: processedInput,
                completedAt,
            });
            this.logger.log(`Human input completed: ${requestId}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Failed to provide human input: ${error.message}`, error.stack);
            throw error;
        }
    }
    async skipHumanInput(requestId, userId, reason) {
        try {
            const request = await this.getRequest(requestId);
            if (!request) {
                throw new common_1.NotFoundException(`Human input request not found: ${requestId}`);
            }
            if (request.userId !== userId) {
                throw new Error('Unauthorized: User does not own this request');
            }
            if (!request.allowSkip) {
                throw new Error('Skipping is not allowed for this request');
            }
            if (request.status !== 'pending') {
                throw new Error(`Request is not pending: ${request.status}`);
            }
            const completedAt = new Date();
            request.status = 'skipped';
            request.response = request.skipValue;
            request.completedAt = completedAt;
            await this.prisma.workflowHumanInput.update({
                where: { id: requestId },
                data: {
                    status: 'SKIPPED',
                    response: request.skipValue,
                    completedAt,
                    metadata: { ...request.metadata, skipReason: reason },
                },
            });
            const timeoutHandle = this.requestTimeouts.get(requestId);
            if (timeoutHandle) {
                clearTimeout(timeoutHandle);
                this.requestTimeouts.delete(requestId);
            }
            const response = {
                requestId,
                userInput: request.skipValue,
                inputType: request.inputType,
                skipped: true,
                validationPassed: true,
                metadata: { ...request.metadata, skipReason: reason },
                completedAt,
            };
            await this.apixGateway.emitToUser(userId, 'human_input_skipped', response, { priority: 'high' });
            await this.apixGateway.emitToRoom(`workflow:${request.executionId}`, 'human_input_skipped', {
                requestId,
                nodeId: request.nodeId,
                skipValue: request.skipValue,
                reason,
                completedAt,
            });
            this.eventEmitter.emit('human_input.skipped', {
                requestId,
                executionId: request.executionId,
                nodeId: request.nodeId,
                sessionId: request.sessionId,
                userId,
                skipValue: request.skipValue,
                reason,
                completedAt,
            });
            this.logger.log(`Human input skipped: ${requestId}, reason: ${reason}`);
            return response;
        }
        catch (error) {
            this.logger.error(`Failed to skip human input: ${error.message}`, error.stack);
            throw error;
        }
    }
    async cancelHumanInput(requestId, userId, reason) {
        try {
            const request = await this.getRequest(requestId);
            if (!request) {
                throw new common_1.NotFoundException(`Human input request not found: ${requestId}`);
            }
            if (request.userId !== userId) {
                throw new Error('Unauthorized: User does not own this request');
            }
            if (request.status !== 'pending') {
                throw new Error(`Request is not pending: ${request.status}`);
            }
            request.status = 'cancelled';
            request.completedAt = new Date();
            await this.prisma.workflowHumanInput.update({
                where: { id: requestId },
                data: {
                    status: 'CANCELLED',
                    completedAt: new Date(),
                    metadata: { ...request.metadata, cancelReason: reason },
                },
            });
            const timeoutHandle = this.requestTimeouts.get(requestId);
            if (timeoutHandle) {
                clearTimeout(timeoutHandle);
                this.requestTimeouts.delete(requestId);
            }
            this.activeRequests.delete(requestId);
            await this.apixGateway.emitToUser(userId, 'human_input_cancelled', {
                requestId,
                reason,
                cancelledAt: new Date(),
            });
            await this.apixGateway.emitToRoom(`workflow:${request.executionId}`, 'human_input_cancelled', {
                requestId,
                nodeId: request.nodeId,
                reason,
                cancelledAt: new Date(),
            });
            this.eventEmitter.emit('human_input.cancelled', {
                requestId,
                executionId: request.executionId,
                nodeId: request.nodeId,
                sessionId: request.sessionId,
                userId,
                reason,
                cancelledAt: new Date(),
            });
            this.logger.log(`Human input cancelled: ${requestId}, reason: ${reason}`);
        }
        catch (error) {
            this.logger.error(`Failed to cancel human input: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getRequest(requestId) {
        try {
            let request = this.activeRequests.get(requestId);
            if (!request) {
                request = await this.cacheManager.get(`human_input:${requestId}`);
            }
            if (!request) {
                const dbRequest = await this.prisma.workflowHumanInput.findUnique({
                    where: { id: requestId },
                });
                if (dbRequest) {
                    request = {
                        id: dbRequest.id,
                        executionId: dbRequest.executionId,
                        nodeId: dbRequest.nodeId,
                        sessionId: dbRequest.sessionId,
                        userId: dbRequest.userId,
                        organizationId: dbRequest.organizationId,
                        prompt: dbRequest.prompt,
                        inputType: dbRequest.inputType,
                        validationRules: dbRequest.validationRules,
                        timeout: dbRequest.timeout,
                        allowSkip: dbRequest.allowSkip,
                        skipValue: dbRequest.skipValue,
                        status: dbRequest.status.toLowerCase(),
                        response: dbRequest.response,
                        metadata: dbRequest.metadata,
                        createdAt: dbRequest.createdAt,
                        expiresAt: dbRequest.expiresAt,
                        completedAt: dbRequest.completedAt,
                    };
                    await this.cacheManager.set(`human_input:${requestId}`, request, 3600000);
                }
            }
            return request || null;
        }
        catch (error) {
            this.logger.error(`Failed to get request: ${error.message}`, error.stack);
            return null;
        }
    }
    async getUserPendingRequests(userId) {
        try {
            const requests = await this.prisma.workflowHumanInput.findMany({
                where: {
                    userId,
                    status: 'PENDING',
                    expiresAt: { gt: new Date() },
                },
                orderBy: { createdAt: 'desc' },
            });
            return requests.map(dbRequest => ({
                id: dbRequest.id,
                executionId: dbRequest.executionId,
                nodeId: dbRequest.nodeId,
                sessionId: dbRequest.sessionId,
                userId: dbRequest.userId,
                organizationId: dbRequest.organizationId,
                prompt: dbRequest.prompt,
                inputType: dbRequest.inputType,
                validationRules: dbRequest.validationRules,
                timeout: dbRequest.timeout,
                allowSkip: dbRequest.allowSkip,
                skipValue: dbRequest.skipValue,
                status: dbRequest.status.toLowerCase(),
                response: dbRequest.response,
                metadata: dbRequest.metadata,
                createdAt: dbRequest.createdAt,
                expiresAt: dbRequest.expiresAt,
                completedAt: dbRequest.completedAt,
            }));
        }
        catch (error) {
            this.logger.error(`Failed to get user pending requests: ${error.message}`, error.stack);
            return [];
        }
    }
    async getExecutionRequests(executionId) {
        try {
            const requests = await this.prisma.workflowHumanInput.findMany({
                where: { executionId },
                orderBy: { createdAt: 'asc' },
            });
            return requests.map(dbRequest => ({
                id: dbRequest.id,
                executionId: dbRequest.executionId,
                nodeId: dbRequest.nodeId,
                sessionId: dbRequest.sessionId,
                userId: dbRequest.userId,
                organizationId: dbRequest.organizationId,
                prompt: dbRequest.prompt,
                inputType: dbRequest.inputType,
                validationRules: dbRequest.validationRules,
                timeout: dbRequest.timeout,
                allowSkip: dbRequest.allowSkip,
                skipValue: dbRequest.skipValue,
                status: dbRequest.status.toLowerCase(),
                response: dbRequest.response,
                metadata: dbRequest.metadata,
                createdAt: dbRequest.createdAt,
                expiresAt: dbRequest.expiresAt,
                completedAt: dbRequest.completedAt,
            }));
        }
        catch (error) {
            this.logger.error(`Failed to get execution requests: ${error.message}`, error.stack);
            return [];
        }
    }
    async handleTimeout(requestId) {
        try {
            const request = this.activeRequests.get(requestId);
            if (!request || request.status !== 'pending') {
                return;
            }
            this.logger.log(`Human input request timed out: ${requestId}`);
            request.status = 'expired';
            request.completedAt = new Date();
            await this.prisma.workflowHumanInput.update({
                where: { id: requestId },
                data: {
                    status: 'EXPIRED',
                    completedAt: new Date(),
                },
            });
            this.activeRequests.delete(requestId);
            this.requestTimeouts.delete(requestId);
            await this.apixGateway.emitToUser(request.userId, 'human_input_timeout', {
                requestId,
                executionId: request.executionId,
                nodeId: request.nodeId,
                expiredAt: new Date(),
            }, { priority: 'high' });
            await this.apixGateway.emitToRoom(`workflow:${request.executionId}`, 'human_input_timeout', {
                requestId,
                nodeId: request.nodeId,
                expiredAt: new Date(),
            });
            this.eventEmitter.emit('human_input.timeout', {
                requestId,
                executionId: request.executionId,
                nodeId: request.nodeId,
                sessionId: request.sessionId,
                userId: request.userId,
                expiredAt: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to handle timeout for request ${requestId}: ${error.message}`, error.stack);
        }
    }
    validateInput(input, inputType, validationRules) {
        const errors = [];
        if (!validationRules) {
            return { valid: true, errors: [] };
        }
        if (validationRules.required && (input === null || input === undefined || input === '')) {
            errors.push('Input is required');
            return { valid: false, errors };
        }
        if (!validationRules.required && (input === null || input === undefined || input === '')) {
            return { valid: true, errors: [] };
        }
        switch (inputType) {
            case 'text':
                if (typeof input !== 'string') {
                    errors.push('Input must be a string');
                }
                else {
                    if (validationRules.minLength && input.length < validationRules.minLength) {
                        errors.push(`Input must be at least ${validationRules.minLength} characters long`);
                    }
                    if (validationRules.maxLength && input.length > validationRules.maxLength) {
                        errors.push(`Input must be no more than ${validationRules.maxLength} characters long`);
                    }
                    if (validationRules.pattern && !new RegExp(validationRules.pattern).test(input)) {
                        errors.push('Input does not match the required pattern');
                    }
                }
                break;
            case 'number':
                if (typeof input !== 'number' && !Number.isFinite(Number(input))) {
                    errors.push('Input must be a valid number');
                }
                break;
            case 'boolean':
                if (typeof input !== 'boolean') {
                    errors.push('Input must be true or false');
                }
                break;
            case 'select':
                if (validationRules.options && !validationRules.options.includes(input)) {
                    errors.push(`Input must be one of: ${validationRules.options.join(', ')}`);
                }
                break;
            case 'multiselect':
                if (!Array.isArray(input)) {
                    errors.push('Input must be an array');
                }
                else if (validationRules.options) {
                    const invalidOptions = input.filter(item => !validationRules.options.includes(item));
                    if (invalidOptions.length > 0) {
                        errors.push(`Invalid options: ${invalidOptions.join(', ')}`);
                    }
                }
                break;
            case 'json':
                try {
                    if (typeof input === 'string') {
                        JSON.parse(input);
                    }
                    else if (typeof input !== 'object') {
                        errors.push('Input must be valid JSON');
                    }
                }
                catch {
                    errors.push('Input must be valid JSON');
                }
                break;
        }
        return { valid: errors.length === 0, errors };
    }
    processInput(input, inputType) {
        switch (inputType) {
            case 'number':
                return typeof input === 'number' ? input : Number(input);
            case 'boolean':
                return typeof input === 'boolean' ? input : Boolean(input);
            case 'json':
                return typeof input === 'string' ? JSON.parse(input) : input;
            default:
                return input;
        }
    }
    async cleanupExpiredRequests() {
        try {
            const expiredRequests = Array.from(this.activeRequests.entries())
                .filter(([_, request]) => new Date() > request.expiresAt && request.status === 'pending');
            for (const [requestId, _] of expiredRequests) {
                await this.handleTimeout(requestId);
            }
            await this.prisma.workflowHumanInput.updateMany({
                where: {
                    status: 'PENDING',
                    expiresAt: { lt: new Date() },
                },
                data: {
                    status: 'EXPIRED',
                    completedAt: new Date(),
                },
            });
        }
        catch (error) {
            this.logger.error(`Failed to cleanup expired requests: ${error.message}`, error.stack);
        }
    }
    async getHumanInputAnalytics(organizationId, timeRange) {
        try {
            const where = { organizationId };
            if (timeRange) {
                where.createdAt = {
                    gte: timeRange.start,
                    lte: timeRange.end,
                };
            }
            const requests = await this.prisma.workflowHumanInput.findMany({
                where,
                select: {
                    status: true,
                    inputType: true,
                    timeout: true,
                    createdAt: true,
                    completedAt: true,
                    allowSkip: true,
                },
            });
            const totalRequests = requests.length;
            const completedRequests = requests.filter(r => r.status === 'COMPLETED').length;
            const skippedRequests = requests.filter(r => r.status === 'SKIPPED').length;
            const expiredRequests = requests.filter(r => r.status === 'EXPIRED').length;
            const cancelledRequests = requests.filter(r => r.status === 'CANCELLED').length;
            const completionRate = totalRequests > 0 ? completedRequests / totalRequests : 0;
            const skipRate = totalRequests > 0 ? skippedRequests / totalRequests : 0;
            const timeoutRate = totalRequests > 0 ? expiredRequests / totalRequests : 0;
            const completedWithTimes = requests.filter(r => r.completedAt && r.status === 'COMPLETED');
            const avgResponseTime = completedWithTimes.length > 0
                ? completedWithTimes.reduce((sum, r) => sum + (r.completedAt.getTime() - r.createdAt.getTime()), 0) / completedWithTimes.length
                : 0;
            const inputTypeDistribution = requests.reduce((acc, r) => {
                acc[r.inputType] = (acc[r.inputType] || 0) + 1;
                return acc;
            }, {});
            return {
                totalRequests,
                completedRequests,
                skippedRequests,
                expiredRequests,
                cancelledRequests,
                completionRate,
                skipRate,
                timeoutRate,
                avgResponseTime: Math.round(avgResponseTime / 1000),
                inputTypeDistribution,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get human input analytics: ${error.message}`, error.stack);
            return {
                totalRequests: 0,
                completedRequests: 0,
                skippedRequests: 0,
                expiredRequests: 0,
                cancelledRequests: 0,
                completionRate: 0,
                skipRate: 0,
                timeoutRate: 0,
                avgResponseTime: 0,
                inputTypeDistribution: {},
            };
        }
    }
    getActiveRequestsCount() {
        return this.activeRequests.size;
    }
    getActiveRequestsForUser(userId) {
        return Array.from(this.activeRequests.values())
            .filter(request => request.userId === userId && request.status === 'pending');
    }
};
exports.HumanInTheLoopService = HumanInTheLoopService;
exports.HumanInTheLoopService = HumanInTheLoopService = HumanInTheLoopService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        sessions_service_1.SessionsService,
        event_emitter_1.EventEmitter2, Object])
], HumanInTheLoopService);
//# sourceMappingURL=human-in-the-loop.service.js.map