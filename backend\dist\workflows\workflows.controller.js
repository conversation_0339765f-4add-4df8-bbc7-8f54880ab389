"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowsController = void 0;
const common_1 = require("@nestjs/common");
const workflows_service_1 = require("./workflows.service");
const jwt_auth_guard_1 = require("../auth/jwt-auth.guard");
const roles_guard_1 = require("../auth/roles.guard");
const tenant_guard_1 = require("../auth/tenant.guard");
const roles_decorator_1 = require("../auth/roles.decorator");
const client_1 = require("@prisma/client");
const workflow_dto_1 = require("./dto/workflow.dto");
const swagger_1 = require("@nestjs/swagger");
const flow_controller_service_1 = require("./services/flow-controller.service");
const node_registry_service_1 = require("./services/node-registry.service");
let WorkflowsController = class WorkflowsController {
    constructor(workflowsService, flowController, nodeRegistry) {
        this.workflowsService = workflowsService;
        this.flowController = flowController;
        this.nodeRegistry = nodeRegistry;
    }
    async create(req, createWorkflowDto) {
        return this.workflowsService.create(req.user.id, req.organizationId, createWorkflowDto);
    }
    async findAll(req, filters) {
        return this.workflowsService.findAll(req.organizationId, filters);
    }
    async getTemplates(req) {
        return {
            templates: [
                {
                    id: 'data-processing',
                    name: 'Data Processing Pipeline',
                    description: 'Process and transform data using multiple tools',
                    category: 'Data',
                    definition: {
                        nodes: [
                            {
                                id: 'start',
                                type: 'tool',
                                name: 'Data Ingestion',
                                config: { toolType: 'data_fetch' },
                                position: { x: 100, y: 100 },
                                inputs: [],
                                outputs: ['data'],
                            },
                            {
                                id: 'process',
                                type: 'tool',
                                name: 'Data Processing',
                                config: { toolType: 'data_transform' },
                                position: { x: 300, y: 100 },
                                inputs: ['data'],
                                outputs: ['processed_data'],
                            },
                        ],
                        edges: [
                            {
                                id: 'start-process',
                                source: 'start',
                                target: 'process',
                            },
                        ],
                        triggers: [{ type: 'manual', config: {} }],
                        settings: {},
                    },
                },
                {
                    id: 'hybrid-agent-tool',
                    name: 'Hybrid Agent-Tool Workflow',
                    description: 'Combine agent intelligence with tool capabilities',
                    category: 'Hybrid',
                    definition: {
                        nodes: [
                            {
                                id: 'start',
                                type: 'input',
                                name: 'Start',
                                config: {},
                                position: { x: 100, y: 100 },
                                inputs: [],
                                outputs: ['input'],
                            },
                            {
                                id: 'hybrid',
                                type: 'hybrid',
                                name: 'Hybrid Processing',
                                config: {
                                    executionPattern: 'agent-first',
                                    agentId: 'placeholder',
                                    toolIds: ['placeholder'],
                                },
                                position: { x: 300, y: 100 },
                                inputs: ['input'],
                                outputs: ['result'],
                            },
                            {
                                id: 'end',
                                type: 'output',
                                name: 'End',
                                config: {},
                                position: { x: 500, y: 100 },
                                inputs: ['result'],
                                outputs: [],
                            },
                        ],
                        edges: [
                            {
                                id: 'start-hybrid',
                                source: 'start',
                                target: 'hybrid',
                            },
                            {
                                id: 'hybrid-end',
                                source: 'hybrid',
                                target: 'end',
                            },
                        ],
                        triggers: [{ type: 'manual', config: {} }],
                        settings: {},
                    },
                },
            ],
        };
    }
    async getAnalytics(req, days = '30') {
        return {
            totalWorkflows: 0,
            activeWorkflows: 0,
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            avgExecutionTime: 0,
            executionsByStatus: {},
            executionsByDay: [],
            topWorkflows: [],
        };
    }
    async findOne(req, id) {
        return this.workflowsService.findOne(id, req.organizationId);
    }
    async update(req, id, updateWorkflowDto) {
        return this.workflowsService.update(id, req.organizationId, req.user.id, updateWorkflowDto);
    }
    async remove(req, id) {
        return this.workflowsService.remove(id, req.organizationId);
    }
    async execute(req, id, executeWorkflowDto) {
        return this.workflowsService.execute(id, req.organizationId, req.user.id, executeWorkflowDto);
    }
    async getExecutions(req, id, limit = '50') {
        return this.workflowsService.getExecutions(id, req.organizationId, parseInt(limit));
    }
    async getExecution(req, id, executionId) {
        return this.workflowsService.getExecution(executionId, req.organizationId);
    }
    async cancelExecution(req, id, executionId) {
        return this.workflowsService.cancelExecution(executionId, req.organizationId);
    }
    async pauseExecution(req, id, executionId) {
        return this.workflowsService.pauseExecution(executionId, req.organizationId);
    }
    async resumeExecution(req, id, executionId) {
        return this.workflowsService.resumeExecution(executionId, req.organizationId);
    }
    async getExecutionStatus(req, id, executionId) {
        return this.workflowsService.getExecutionStatus(executionId, req.organizationId);
    }
    async scheduleWorkflow(req, id, scheduleDto) {
        return {
            message: 'Workflow scheduled successfully',
            scheduleId: 'placeholder',
        };
    }
    async getSchedule(req, id) {
        return {
            schedules: [],
        };
    }
    async deleteSchedule(req, id, scheduleId) {
    }
    async duplicate(req, id, body) {
        const originalWorkflow = await this.workflowsService.findOne(id, req.organizationId);
        const duplicateDto = {
            name: body.name || `${originalWorkflow.name} (Copy)`,
            description: originalWorkflow.description,
            definition: originalWorkflow.definition,
            tags: originalWorkflow.tags,
        };
        return this.workflowsService.create(req.user.id, req.organizationId, duplicateDto);
    }
    async import(req, importDto) {
        let workflowData;
        try {
            if (importDto.format === 'json') {
                workflowData = JSON.parse(importDto.data);
            }
            else {
                throw new Error('YAML import not yet implemented');
            }
        }
        catch (error) {
            throw new Error(`Invalid ${importDto.format} format: ${error.message}`);
        }
        return this.workflowsService.create(req.user.id, req.organizationId, workflowData);
    }
    async export(req, id, exportDto) {
        const workflow = await this.workflowsService.findOne(id, req.organizationId);
        const exportData = {
            name: workflow.name,
            description: workflow.description,
            definition: workflow.definition,
            tags: workflow.tags,
            ...(exportDto.includeMetadata && {
                metadata: {
                    version: workflow.version,
                    createdAt: workflow.createdAt,
                    updatedAt: workflow.updatedAt,
                    creator: workflow.creator,
                },
            }),
        };
        if (exportDto.format === 'json') {
            return {
                format: 'json',
                data: JSON.stringify(exportData, null, 2),
                filename: `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`,
            };
        }
        else {
            return {
                format: 'yaml',
                data: '# YAML export not yet implemented',
                filename: `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.yaml`,
            };
        }
    }
    async getNodeTypes(req) {
        return this.workflowsService.getAvailableNodeTypes(req.organizationId);
    }
    async createTemplate(req, templateDto) {
        return {
            message: 'Template created successfully',
            templateId: 'placeholder',
        };
    }
    async useTemplate(req, templateId, body) {
        const templateWorkflow = {
            name: body.name,
            description: 'Created from template',
            definition: {
                nodes: [],
                edges: [],
                triggers: [],
                settings: {},
            },
        };
        return this.workflowsService.create(req.user.id, req.organizationId, templateWorkflow);
    }
};
exports.WorkflowsController = WorkflowsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid workflow definition' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_dto_1.CreateWorkflowDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all workflows' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflows retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_dto_1.WorkflowFilterDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('templates'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow templates' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Templates retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getTemplates", null);
__decorate([
    (0, common_1.Get)('analytics'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow analytics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Analytics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getAnalytics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Update workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, workflow_dto_1.UpdateWorkflowDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete workflow' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Workflow deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/execute'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER, client_1.Role.VIEWER),
    (0, swagger_1.ApiOperation)({ summary: 'Execute workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow execution started' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Workflow not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, workflow_dto_1.ExecuteWorkflowDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "execute", null);
__decorate([
    (0, common_1.Get)(':id/executions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow executions' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Executions retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getExecutions", null);
__decorate([
    (0, common_1.Get)(':id/executions/:executionId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get specific workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Execution not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('executionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getExecution", null);
__decorate([
    (0, common_1.Post)(':id/executions/:executionId/cancel'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Cancel workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Execution cancelled successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Execution not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('executionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "cancelExecution", null);
__decorate([
    (0, common_1.Post)(':id/executions/:executionId/pause'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Pause workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Execution paused successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Execution not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('executionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "pauseExecution", null);
__decorate([
    (0, common_1.Post)(':id/executions/:executionId/resume'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Resume workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Execution resumed successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Execution not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('executionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "resumeExecution", null);
__decorate([
    (0, common_1.Get)(':id/executions/:executionId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow execution status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Execution status retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Execution not found' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('executionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getExecutionStatus", null);
__decorate([
    (0, common_1.Post)(':id/schedule'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Schedule workflow execution' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow scheduled successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, workflow_dto_1.ScheduleWorkflowDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "scheduleWorkflow", null);
__decorate([
    (0, common_1.Get)(':id/schedule'),
    (0, swagger_1.ApiOperation)({ summary: 'Get workflow schedule' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Schedule retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getSchedule", null);
__decorate([
    (0, common_1.Delete)(':id/schedule/:scheduleId'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete workflow schedule' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Schedule deleted successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Param)('scheduleId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "deleteSchedule", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicate workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow duplicated successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)('import'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Import workflow' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow imported successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_dto_1.WorkflowImportDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "import", null);
__decorate([
    (0, common_1.Post)(':id/export'),
    (0, swagger_1.ApiOperation)({ summary: 'Export workflow' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Workflow exported successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, workflow_dto_1.WorkflowExportDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "export", null);
__decorate([
    (0, common_1.Get)('node-types'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available node types' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Node types retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "getNodeTypes", null);
__decorate([
    (0, common_1.Post)('templates'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Create workflow template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Template created successfully' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, workflow_dto_1.WorkflowTemplateDto]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "createTemplate", null);
__decorate([
    (0, common_1.Post)('templates/:templateId/use'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.Role.ORG_ADMIN, client_1.Role.DEVELOPER),
    (0, swagger_1.ApiOperation)({ summary: 'Create workflow from template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Workflow created from template' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('templateId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], WorkflowsController.prototype, "useTemplate", null);
exports.WorkflowsController = WorkflowsController = __decorate([
    (0, swagger_1.ApiTags)('Workflows'),
    (0, common_1.Controller)('workflows'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [workflows_service_1.WorkflowsService,
        flow_controller_service_1.FlowControllerService,
        node_registry_service_1.NodeRegistryService])
], WorkflowsController);
//# sourceMappingURL=workflows.controller.js.map