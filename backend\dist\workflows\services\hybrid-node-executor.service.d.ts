import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { AgentsService } from '../../agents/agents.service';
import { ToolsService } from '../../tools/tools.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
export interface HybridExecutionContext {
    executionId: string;
    sessionId: string;
    organizationId: string;
    userId: string;
    nodeId: string;
    workflowId: string;
}
export interface HybridNodeConfig {
    agentId: string;
    toolIds: string[];
    executionPattern: 'agent-first' | 'tool-first' | 'parallel' | 'multi-tool-orchestration';
    maxIterations: number;
    agentConfig: {
        systemPrompt?: string;
        maxTokens: number;
        temperature: number;
        contextWindow: number;
    };
    toolConfigs: Record<string, {
        timeout: number;
        retryPolicy: {
            enabled: boolean;
            maxRetries: number;
        };
        parameters: Record<string, any>;
    }>;
    coordination: {
        shareContext: boolean;
        contextStrategy: 'full' | 'summary' | 'selective';
        syncPoints: string[];
        errorPropagation: boolean;
    };
    performance: {
        parallelLimit: number;
        timeout: number;
        memoryLimit: number;
    };
    fallback: {
        enabled: boolean;
        fallbackAgent?: string;
        fallbackTools: string[];
        conditions: string[];
    };
}
export declare class HybridNodeExecutorService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private agentsService;
    private toolsService;
    private eventEmitter;
    private readonly logger;
    private activeExecutions;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService, agentsService: AgentsService, toolsService: ToolsService, eventEmitter: EventEmitter2);
    executeHybridNode(config: HybridNodeConfig, variables: Record<string, any>, context: HybridExecutionContext): Promise<any>;
    private executeAgentFirstPattern;
    private executeToolFirstPattern;
    private executeParallelPattern;
    private executeOrchestrationPattern;
    private executeAgent;
    private executeTool;
    private executeWithRetry;
    private executeWithParallelLimit;
    private extractToolParameters;
    private buildOrchestrationPrompt;
    private buildSummaryPrompt;
    private parseOrchestrationDecision;
    private initializeSharedContext;
    private updateSharedContext;
    private getSharedContext;
    private processSyncPoints;
    private shouldTriggerFallback;
    private executeFallback;
    private emitHybridEvent;
    private storeExecutionAnalytics;
    getActiveExecutions(): Array<{
        executionKey: string;
        status: string;
        startTime: number;
        duration?: number;
    }>;
    cancelExecution(executionKey: string): Promise<boolean>;
    getExecutionAnalytics(organizationId: string, timeRange?: {
        start: Date;
        end: Date;
    }): Promise<{
        totalExecutions: any;
        successRate: number;
        averageDuration: number;
        patternDistribution: {};
        errorAnalysis: {
            totalErrors: number;
            errorTypes: {};
            errorRate: number;
        };
        performanceMetrics: {
            min: any;
            max: any;
            median: any;
            p95: any;
            p99: any;
        };
    }>;
    private calculatePatternDistribution;
    private analyzeErrors;
    private categorizeError;
    private calculatePerformanceMetrics;
}
