import { WorkflowsService } from './workflows.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto, WorkflowFilterDto, ScheduleWorkflowDto, WorkflowTemplateDto, WorkflowImportDto, WorkflowExportDto, WorkflowResponse, WorkflowExecutionResponse, WorkflowAnalyticsResponse } from './dto/workflow.dto';
import { FlowControllerService } from './services/flow-controller.service';
import { NodeRegistryService } from './services/node-registry.service';
export declare class WorkflowsController {
    private readonly workflowsService;
    private readonly flowController;
    private readonly nodeRegistry;
    constructor(workflowsService: WorkflowsService, flowController: FlowControllerService, nodeRegistry: NodeRegistryService);
    create(req: any, createWorkflowDto: CreateWorkflowDto): Promise<WorkflowResponse>;
    findAll(req: any, filters: WorkflowFilterDto): Promise<WorkflowResponse[]>;
    getTemplates(req: any): Promise<{
        templates: ({
            id: string;
            name: string;
            description: string;
            category: string;
            definition: {
                nodes: {
                    id: string;
                    type: string;
                    name: string;
                    config: {
                        toolType: string;
                    };
                    position: {
                        x: number;
                        y: number;
                    };
                    inputs: string[];
                    outputs: string[];
                }[];
                edges: {
                    id: string;
                    source: string;
                    target: string;
                }[];
                triggers: {
                    type: string;
                    config: {};
                }[];
                settings: {};
            };
        } | {
            id: string;
            name: string;
            description: string;
            category: string;
            definition: {
                nodes: ({
                    id: string;
                    type: string;
                    name: string;
                    config: {
                        executionPattern?: undefined;
                        agentId?: undefined;
                        toolIds?: undefined;
                    };
                    position: {
                        x: number;
                        y: number;
                    };
                    inputs: any[];
                    outputs: string[];
                } | {
                    id: string;
                    type: string;
                    name: string;
                    config: {
                        executionPattern: string;
                        agentId: string;
                        toolIds: string[];
                    };
                    position: {
                        x: number;
                        y: number;
                    };
                    inputs: string[];
                    outputs: string[];
                } | {
                    id: string;
                    type: string;
                    name: string;
                    config: {
                        executionPattern?: undefined;
                        agentId?: undefined;
                        toolIds?: undefined;
                    };
                    position: {
                        x: number;
                        y: number;
                    };
                    inputs: string[];
                    outputs: any[];
                })[];
                edges: {
                    id: string;
                    source: string;
                    target: string;
                }[];
                triggers: {
                    type: string;
                    config: {};
                }[];
                settings: {};
            };
        })[];
    }>;
    getAnalytics(req: any, days?: string): Promise<WorkflowAnalyticsResponse>;
    findOne(req: any, id: string): Promise<WorkflowResponse>;
    update(req: any, id: string, updateWorkflowDto: UpdateWorkflowDto): Promise<WorkflowResponse>;
    remove(req: any, id: string): Promise<void>;
    execute(req: any, id: string, executeWorkflowDto: ExecuteWorkflowDto): Promise<{
        executionId: string;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
    }>;
    getExecutions(req: any, id: string, limit?: string): Promise<WorkflowExecutionResponse[]>;
    getExecution(req: any, id: string, executionId: string): Promise<WorkflowExecutionResponse>;
    cancelExecution(req: any, id: string, executionId: string): Promise<void>;
    pauseExecution(req: any, id: string, executionId: string): Promise<void>;
    resumeExecution(req: any, id: string, executionId: string): Promise<void>;
    getExecutionStatus(req: any, id: string, executionId: string): Promise<any>;
    scheduleWorkflow(req: any, id: string, scheduleDto: ScheduleWorkflowDto): Promise<{
        message: string;
        scheduleId: string;
    }>;
    getSchedule(req: any, id: string): Promise<{
        schedules: any[];
    }>;
    deleteSchedule(req: any, id: string, scheduleId: string): Promise<void>;
    duplicate(req: any, id: string, body: {
        name?: string;
    }): Promise<WorkflowResponse>;
    import(req: any, importDto: WorkflowImportDto): Promise<WorkflowResponse>;
    export(req: any, id: string, exportDto: WorkflowExportDto): Promise<{
        format: string;
        data: string;
        filename: string;
    }>;
    getNodeTypes(req: any): Promise<{
        nodeTypes: import("./services/node-registry.service").NodeDefinition[];
        agents: {
            id: string;
            name: string;
            type: import(".prisma/client").$Enums.AgentType;
        }[];
        tools: {
            id: string;
            name: string;
            type: import(".prisma/client").$Enums.ToolType;
        }[];
    }>;
    createTemplate(req: any, templateDto: WorkflowTemplateDto): Promise<{
        message: string;
        templateId: string;
    }>;
    useTemplate(req: any, templateId: string, body: {
        name: string;
        variables?: Record<string, any>;
    }): Promise<WorkflowResponse>;
}
