"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Badge } from "../ui/badge";
import { AlertCircle, CheckCircle2, Settings, Zap } from "lucide-react";
import { Alert, AlertDescription } from "../ui/alert";
import apiClient from "@/lib/api-client";

interface ProviderIntegrationProps {
  onSave?: (providerConfig: ProviderConfig) => void;
  initialProvider?: ProviderConfig;
}

interface ProviderConfig {
  type: string;
  apiKey: string;
  name: string;
  isActive: boolean;
  smartRouting: boolean;
  fallbackEnabled: boolean;
  fallbackProvider?: string;
  costLimit?: number;
  capabilities: string[];
}

const ProviderIntegration: React.FC<ProviderIntegrationProps> = ({
  onSave = () => {},
  initialProvider = {
    type: "openai",
    apiKey: "",
    name: "OpenAI Integration",
    isActive: true,
    smartRouting: true,
    fallbackEnabled: false,
    capabilities: ["chat", "embedding", "function-call"],
  },
}) => {
  const [provider, setProvider] = useState<ProviderConfig>(initialProvider);
  const [testStatus, setTestStatus] = useState<
    "idle" | "testing" | "success" | "error"
  >("idle");
  const [testMessage, setTestMessage] = useState<string>("");

  const handleProviderTypeChange = (type: string) => {
    const defaultCapabilities: Record<string, string[]> = {
      openai: ["chat", "embedding", "function-call", "vision"],
      claude: ["chat", "embedding", "function-call"],
      gemini: ["chat", "embedding", "vision"],
      local: ["chat"],
    };

    setProvider({
      ...provider,
      type,
      capabilities:
        defaultCapabilities[type as keyof typeof defaultCapabilities] || [],
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Integration`,
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProvider({ ...provider, [name]: value });
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setProvider({ ...provider, [name]: checked });
  };

  const handleTestConnection = async () => {
    setTestStatus("testing");
    setTestMessage("Testing connection to provider...");

    try {
      const response = await apiClient.get("/providers/test-connection") as { status: number };
      console.log(response);

      if (response.status === 200 || response.status === 201) {
        setTestStatus("success");
        setTestMessage("Connection successful! Provider is ready to use.");
      } else {
        throw new Error("Failed to connect to provider");
      } 
      if (!provider.apiKey) {
        throw new Error("API key is required");
      }

      setTestStatus("success");
      setTestMessage("Connection successful! Provider is ready to use.");
    } catch (error) {
      setTestStatus("error");
      setTestMessage(
        error instanceof Error
          ? error.message
          : "Failed to connect to provider",
      );
    }
  };

  const handleSaveIntegration = () => {
    onSave(provider);
  };

  return (
    <div className="bg-background p-6 rounded-lg border border-border w-full max-w-4xl mx-auto">
      <Tabs
        defaultValue={provider.type}
        onValueChange={handleProviderTypeChange}
        className="w-full"
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">Provider Integration</h2>
            <p className="text-muted-foreground mb-4">
              Configure and manage AI provider connections
            </p>
          </div>
          <TabsList className="mb-4 sm:mb-0">
            <TabsTrigger value="openai">OpenAI</TabsTrigger>
            <TabsTrigger value="claude">Claude</TabsTrigger>
            <TabsTrigger value="gemini">Gemini</TabsTrigger>
            <TabsTrigger value="local">Local Model</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="openai" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>OpenAI Configuration</CardTitle>
              <CardDescription>
                Connect to OpenAI's API for GPT models, embeddings, and function
                calling capabilities.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Integration Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={provider.name}
                    onChange={handleInputChange}
                    placeholder="OpenAI Integration"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    name="apiKey"
                    type="password"
                    value={provider.apiKey}
                    onChange={handleInputChange}
                    placeholder="sk-..."
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="costLimit">Monthly Cost Limit ($)</Label>
                  <Input
                    id="costLimit"
                    name="costLimit"
                    type="number"
                    value={provider.costLimit || ""}
                    onChange={handleInputChange}
                    placeholder="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fallbackProvider">Fallback Provider</Label>
                  <Select
                    disabled={!provider.fallbackEnabled}
                    value={provider.fallbackProvider}
                    onValueChange={(value) =>
                      setProvider({ ...provider, fallbackProvider: value })
                    }
                  >
                    <SelectTrigger id="fallbackProvider">
                      <SelectValue placeholder="Select fallback provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="claude">Claude</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                      <SelectItem value="local">Local Model</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-2">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="isActive" className="text-base">
                      Active
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Enable this provider for use in workflows
                    </p>
                  </div>
                  <Switch
                    id="isActive"
                    checked={provider.isActive}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isActive", checked)
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="smartRouting" className="text-base">
                      Smart Routing
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Route requests based on latency and performance
                    </p>
                  </div>
                  <Switch
                    id="smartRouting"
                    checked={provider.smartRouting}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("smartRouting", checked)
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="fallbackEnabled" className="text-base">
                      Enable Fallback
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically switch to fallback provider on failure
                    </p>
                  </div>
                  <Switch
                    id="fallbackEnabled"
                    checked={provider.fallbackEnabled}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("fallbackEnabled", checked)
                    }
                  />
                </div>
              </div>

              <div className="pt-2">
                <Label className="mb-2 block">Capabilities</Label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "chat",
                    "embedding",
                    "function-call",
                    "vision",
                    "code-generation",
                  ].map((capability) => (
                    <Badge
                      key={capability}
                      variant={
                        provider.capabilities.includes(capability)
                          ? "default"
                          : "outline"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const newCapabilities = provider.capabilities.includes(
                          capability,
                        )
                          ? provider.capabilities.filter(
                              (c) => c !== capability,
                            )
                          : [...provider.capabilities, capability];
                        setProvider({
                          ...provider,
                          capabilities: newCapabilities,
                        });
                      }}
                    >
                      {capability}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between items-stretch sm:items-center border-t pt-6">
              <div className="w-full sm:w-auto order-2 sm:order-1">
                {testStatus !== "idle" && (
                  <Alert
                    variant={testStatus === "error" ? "destructive" : "default"}
                    className="py-2"
                  >
                    {testStatus === "testing" && (
                      <AlertCircle className="h-4 w-4 animate-pulse" />
                    )}
                    {testStatus === "success" && (
                      <CheckCircle2 className="h-4 w-4" />
                    )}
                    {testStatus === "error" && (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>{testMessage}</AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="flex gap-3 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testStatus === "testing"}
                  className="flex-1 sm:flex-none"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Test Connection
                </Button>
                <Button
                  onClick={handleSaveIntegration}
                  className="flex-1 sm:flex-none"
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Save Integration
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="claude" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Claude Configuration</CardTitle>
              <CardDescription>
                Connect to Anthropic's Claude models for advanced reasoning and
                long context capabilities.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Similar structure to OpenAI tab with Claude-specific fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Integration Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={provider.name}
                    onChange={handleInputChange}
                    placeholder="Claude Integration"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    name="apiKey"
                    type="password"
                    value={provider.apiKey}
                    onChange={handleInputChange}
                    placeholder="sk-ant-..."
                  />
                </div>
              </div>

              {/* Additional Claude-specific configuration fields would go here */}
              <div className="pt-2">
                <Label className="mb-2 block">Capabilities</Label>
                <div className="flex flex-wrap gap-2">
                  {["chat", "embedding", "function-call", "long-context"].map(
                    (capability) => (
                      <Badge
                        key={capability}
                        variant={
                          provider.capabilities.includes(capability)
                            ? "default"
                            : "outline"
                        }
                        className="cursor-pointer"
                        onClick={() => {
                          const newCapabilities =
                            provider.capabilities.includes(capability)
                              ? provider.capabilities.filter(
                                  (c) => c !== capability,
                                )
                              : [...provider.capabilities, capability];
                          setProvider({
                            ...provider,
                            capabilities: newCapabilities,
                          });
                        }}
                      >
                        {capability}
                      </Badge>
                    ),
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between items-stretch sm:items-center border-t pt-6">
              <div className="w-full sm:w-auto order-2 sm:order-1">
                {testStatus !== "idle" && (
                  <Alert
                    variant={testStatus === "error" ? "destructive" : "default"}
                    className="py-2"
                  >
                    {testStatus === "testing" && (
                      <AlertCircle className="h-4 w-4 animate-pulse" />
                    )}
                    {testStatus === "success" && (
                      <CheckCircle2 className="h-4 w-4" />
                    )}
                    {testStatus === "error" && (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>{testMessage}</AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="flex gap-3 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testStatus === "testing"}
                  className="flex-1 sm:flex-none"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Test Connection
                </Button>
                <Button
                  onClick={handleSaveIntegration}
                  className="flex-1 sm:flex-none"
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Save Integration
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="gemini" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Gemini Configuration</CardTitle>
              <CardDescription>
                Connect to Google's Gemini models for multimodal capabilities
                and advanced reasoning.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Gemini-specific configuration fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Integration Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={provider.name}
                    onChange={handleInputChange}
                    placeholder="Gemini Integration"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    name="apiKey"
                    type="password"
                    value={provider.apiKey}
                    onChange={handleInputChange}
                    placeholder="AIza..."
                  />
                </div>
              </div>

              <div className="pt-2">
                <Label className="mb-2 block">Capabilities</Label>
                <div className="flex flex-wrap gap-2">
                  {["chat", "embedding", "vision", "multimodal"].map(
                    (capability) => (
                      <Badge
                        key={capability}
                        variant={
                          provider.capabilities.includes(capability)
                            ? "default"
                            : "outline"
                        }
                        className="cursor-pointer"
                        onClick={() => {
                          const newCapabilities =
                            provider.capabilities.includes(capability)
                              ? provider.capabilities.filter(
                                  (c) => c !== capability,
                                )
                              : [...provider.capabilities, capability];
                          setProvider({
                            ...provider,
                            capabilities: newCapabilities,
                          });
                        }}
                      >
                        {capability}
                      </Badge>
                    ),
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between items-stretch sm:items-center border-t pt-6">
              <div className="w-full sm:w-auto order-2 sm:order-1">
                {testStatus !== "idle" && (
                  <Alert
                    variant={testStatus === "error" ? "destructive" : "default"}
                    className="py-2"
                  >
                    {testStatus === "testing" && (
                      <AlertCircle className="h-4 w-4 animate-pulse" />
                    )}
                    {testStatus === "success" && (
                      <CheckCircle2 className="h-4 w-4" />
                    )}
                    {testStatus === "error" && (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>{testMessage}</AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="flex gap-3 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testStatus === "testing"}
                  className="flex-1 sm:flex-none"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Test Connection
                </Button>
                <Button
                  onClick={handleSaveIntegration}
                  className="flex-1 sm:flex-none"
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Save Integration
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="local" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Local Model Configuration</CardTitle>
              <CardDescription>
                Connect to locally hosted models via Ollama or other local
                inference servers.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Local model specific configuration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Integration Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={provider.name}
                    onChange={handleInputChange}
                    placeholder="Local Model Integration"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endpoint">API Endpoint</Label>
                  <Input
                    id="endpoint"
                    name="endpoint"
                    value={provider.apiKey}
                    onChange={handleInputChange}
                    placeholder="http://localhost:11434"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="modelName">Model Name</Label>
                <Select
                  value={provider.fallbackProvider || "llama2"}
                  onValueChange={(value) =>
                    setProvider({ ...provider, fallbackProvider: value })
                  }
                >
                  <SelectTrigger id="modelName">
                    <SelectValue placeholder="Select local model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="llama2">Llama 2</SelectItem>
                    <SelectItem value="mistral">Mistral</SelectItem>
                    <SelectItem value="phi">Phi-2</SelectItem>
                    <SelectItem value="custom">Custom Model</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="pt-2">
                <Label className="mb-2 block">Capabilities</Label>
                <div className="flex flex-wrap gap-2">
                  {["chat", "embedding", "local-inference"].map(
                    (capability) => (
                      <Badge
                        key={capability}
                        variant={
                          provider.capabilities.includes(capability)
                            ? "default"
                            : "outline"
                        }
                        className="cursor-pointer"
                        onClick={() => {
                          const newCapabilities =
                            provider.capabilities.includes(capability)
                              ? provider.capabilities.filter(
                                  (c) => c !== capability,
                                )
                              : [...provider.capabilities, capability];
                          setProvider({
                            ...provider,
                            capabilities: newCapabilities,
                          });
                        }}
                      >
                        {capability}
                      </Badge>
                    ),
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3 justify-between items-stretch sm:items-center border-t pt-6">
              <div className="w-full sm:w-auto order-2 sm:order-1">
                {testStatus !== "idle" && (
                  <Alert
                    variant={testStatus === "error" ? "destructive" : "default"}
                    className="py-2"
                  >
                    {testStatus === "testing" && (
                      <AlertCircle className="h-4 w-4 animate-pulse" />
                    )}
                    {testStatus === "success" && (
                      <CheckCircle2 className="h-4 w-4" />
                    )}
                    {testStatus === "error" && (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription>{testMessage}</AlertDescription>
                  </Alert>
                )}
              </div>
              <div className="flex gap-3 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={testStatus === "testing"}
                  className="flex-1 sm:flex-none"
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Test Connection
                </Button>
                <Button
                  onClick={handleSaveIntegration}
                  className="flex-1 sm:flex-none"
                >
                  <Zap className="mr-2 h-4 w-4" />
                  Save Integration
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProviderIntegration;
