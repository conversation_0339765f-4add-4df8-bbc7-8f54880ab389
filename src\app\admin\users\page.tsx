"use client";

import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
    Users,
    Search,
    Plus,
    Filter,
    Download,
    Eye,
    Edit,
    Trash2,
    CheckCircle,
    XCircle,
    Activity,
    Clock,
    Building2,
    Mail,
    MoreHorizontal,
    <PERSON>r<PERSON><PERSON><PERSON>,
    User<PERSON>,
} from "lucide-react";
import Link from "next/link";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: "USER" | "ADMIN" | "SUPER_ADMIN" | "ORG_ADMIN";
    status: "active" | "suspended" | "inactive" | "pending";
    emailVerified: boolean;
    lastLoginAt: string | null;
    createdAt: string;
    organization: {
        id: string;
        name: string;
        slug: string;
    };
    profile: {
        phoneNumber?: string;
        jobTitle?: string;
        department?: string;
        timezone?: string;
    };
    permissions: string[];
    sessions: {
        total: number;
        active: number;
        lastDevice?: string;
        lastLocation?: string;
    };
}

interface UserFormData {
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    organizationId: string;
    phoneNumber?: string;
    jobTitle?: string;
    department?: string;
}

export default function UsersPage() {
    const { toast } = useToast();
    const [users, setUsers] = useState<User[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState("");
    const [roleFilter, setRoleFilter] = useState<string>("all");
    const [statusFilter, setStatusFilter] = useState<string>("all");
    const [organizationFilter, setOrganizationFilter] = useState<string>("all");
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [organizations, setOrganizations] = useState<Array<{ id: string, name: string }>>([]);
    const [formData, setFormData] = useState<UserFormData>({
        email: "",
        firstName: "",
        lastName: "",
        role: "USER",
        organizationId: "",
        phoneNumber: "",
        jobTitle: "",
        department: "",
    });

    // Load users data
    useEffect(() => {
        const loadUsers = async () => {
            setLoading(true);
            setError(null);
            try {
                const [usersResponse, orgsResponse] = await Promise.all([
                    apiClient.get('/admin/users'),
                    apiClient.get('/admin/organizations/list'),
                ]);
                setUsers((usersResponse as any).data?.users || []);
                setOrganizations((orgsResponse as any).data?.organizations || []);
            } catch (error: any) {
                const errorMessage = error?.response?.data?.message || 'Failed to load users';
                setError(errorMessage);
                toast({
                    title: "Error",
                    description: errorMessage,
                    variant: "destructive",
                });
                setUsers([]);
                setOrganizations([]);
            } finally {
                setLoading(false);
            }
        };

        loadUsers();
    }, []);

    // Set up real-time updates
    useEffect(() => {
        const unsubscribeUserUpdate = apixClient.on('admin_user_updated', (event) => {
            setUsers(prev =>
                prev.map(user =>
                    user.id === event.data.userId
                        ? { ...user, ...event.data.updates }
                        : user
                )
            );
        });

        const unsubscribeUserCreated = apixClient.on('admin_user_created', (event) => {
            setUsers(prev => [event.data.user, ...prev]);
        });

        return () => {
            unsubscribeUserUpdate();
            unsubscribeUserCreated();
        };
    }, []);

    // Filter users
    const filteredUsers = users.filter(user => {
        const matchesSearch = user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.organization.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesRole = roleFilter === "all" || user.role === roleFilter;
        const matchesStatus = statusFilter === "all" || user.status === statusFilter;
        const matchesOrganization = organizationFilter === "all" || user.organization.id === organizationFilter;

        return matchesSearch && matchesRole && matchesStatus && matchesOrganization;
    });

    const handleCreateUser = async () => {
        try {
            const response = await apiClient.post('/admin/users', formData) as any;
            setUsers(prev => [response.data?.user, ...prev]);
            setIsCreateDialogOpen(false);
            resetForm();
        } catch (error) {
            console.error("Failed to create user:", error);
        }
    };

    const handleUpdateUser = async () => {
        if (!selectedUser) return;

        try {
            const response = await apiClient.put(`/admin/users/${selectedUser.id}`, formData) as any;
            setUsers(prev =>
                prev.map(user =>
                    user.id === selectedUser.id
                        ? { ...user, ...response.data?.user }
                        : user
                )
            );
            setIsEditDialogOpen(false);
            setSelectedUser(null);
        } catch (error) {
            console.error("Failed to update user:", error);
        }
    };

    const handleDeleteUser = async () => {
        if (!selectedUser) return;

        try {
            await apiClient.delete(`/admin/users/${selectedUser.id}`);
            setUsers(prev => prev.filter(user => user.id !== selectedUser.id));
            setIsDeleteDialogOpen(false);
            setSelectedUser(null);
        } catch (error) {
            console.error("Failed to delete user:", error);
        }
    };

    const handleSuspendUser = async (userId: string) => {
        try {
            await apiClient.put(`/admin/users/${userId}/suspend`);
            setUsers(prev =>
                prev.map(user =>
                    user.id === userId
                        ? { ...user, status: "suspended" as const }
                        : user
                )
            );
        } catch (error) {
            console.error("Failed to suspend user:", error);
        }
    };

    const handleActivateUser = async (userId: string) => {
        try {
            await apiClient.put(`/admin/users/${userId}/activate`);
            setUsers(prev =>
                prev.map(user =>
                    user.id === userId
                        ? { ...user, status: "active" as const }
                        : user
                )
            );
        } catch (error) {
            console.error("Failed to activate user:", error);
        }
    };

    const resetForm = () => {
        setFormData({
            email: "",
            firstName: "",
            lastName: "",
            role: "USER",
            organizationId: "",
            phoneNumber: "",
            jobTitle: "",
            department: "",
        });
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "active":
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case "suspended":
                return <XCircle className="h-4 w-4 text-red-600" />;
            case "inactive":
                return <Activity className="h-4 w-4 text-gray-600" />;
            case "pending":
                return <Clock className="h-4 w-4 text-yellow-600" />;
            default:
                return <Activity className="h-4 w-4 text-gray-600" />;
        }
    };

    const getRoleBadgeVariant = (role: string) => {
        switch (role) {
            case "SUPER_ADMIN":
                return "destructive";
            case "ADMIN":
                return "default";
            case "ORG_ADMIN":
                return "secondary";
            case "USER":
                return "outline";
            default:
                return "outline";
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                    <span>Loading users...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
                <div className="text-center">
                    <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Users</h3>
                    <p className="text-muted-foreground mb-4">{error}</p>
                    <Button onClick={() => window.location.reload()} variant="outline">
                        Try Again
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h1 className="text-3xl font-bold">User Management</h1>
                    <p className="text-muted-foreground">
                        Manage users across all organizations
                    </p>
                </div>
                <div className="flex items-center space-x-3">
                    <Button variant="outline">
                        <Download className="mr-2 h-4 w-4" />
                        Export Users
                    </Button>
                    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Create User
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[600px]">
                            <DialogHeader>
                                <DialogTitle>Create New User</DialogTitle>
                                <DialogDescription>
                                    Add a new user to the platform with specified role and organization.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="firstName">First Name</Label>
                                        <Input
                                            id="firstName"
                                            value={formData.firstName}
                                            onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                                            placeholder="John"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="lastName">Last Name</Label>
                                        <Input
                                            id="lastName"
                                            value={formData.lastName}
                                            onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                                            placeholder="Smith"
                                        />
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="role">Role</Label>
                                        <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="USER">User</SelectItem>
                                                <SelectItem value="ORG_ADMIN">Organization Admin</SelectItem>
                                                <SelectItem value="ADMIN">System Admin</SelectItem>
                                                <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="organization">Organization</Label>
                                        <Select value={formData.organizationId} onValueChange={(value) => setFormData(prev => ({ ...prev, organizationId: value }))}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select organization" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {organizations.map((org) => (
                                                    <SelectItem key={org.id} value={org.id}>
                                                        {org.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="jobTitle">Job Title</Label>
                                        <Input
                                            id="jobTitle"
                                            value={formData.jobTitle}
                                            onChange={(e) => setFormData(prev => ({ ...prev, jobTitle: e.target.value }))}
                                            placeholder="Software Engineer"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="department">Department</Label>
                                        <Input
                                            id="department"
                                            value={formData.department}
                                            onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                                            placeholder="Engineering"
                                        />
                                    </div>
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleCreateUser}>
                                    Create User
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {/* Filters and Search */}
            <Card className="bg-card/80 backdrop-blur-sm">
                <CardContent className="p-4">
                    <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search users..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Filter className="h-4 w-4 text-muted-foreground" />
                            <Select value={roleFilter} onValueChange={setRoleFilter}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Roles</SelectItem>
                                    <SelectItem value="USER">User</SelectItem>
                                    <SelectItem value="ORG_ADMIN">Org Admin</SelectItem>
                                    <SelectItem value="ADMIN">Admin</SelectItem>
                                    <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="suspended">Suspended</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="pending">Pending</SelectItem>
                                </SelectContent>
                            </Select>
                            <Select value={organizationFilter} onValueChange={setOrganizationFilter}>
                                <SelectTrigger className="w-48">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Organizations</SelectItem>
                                    {organizations.map((org) => (
                                        <SelectItem key={org.id} value={org.id}>
                                            {org.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Users Table */}
            <Card className="bg-card/80 backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>Users ({filteredUsers.length})</span>
                        <Badge variant="outline">
                            {users.filter(user => user.status === 'active').length} Active
                        </Badge>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>User</TableHead>
                                <TableHead>Organization</TableHead>
                                <TableHead>Role</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Last Login</TableHead>
                                <TableHead>Sessions</TableHead>
                                <TableHead>Created</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredUsers.map((user) => (
                                <TableRow key={user.id}>
                                    <TableCell>
                                        <div className="flex items-center space-x-3">
                                            <Avatar className="h-10 w-10">
                                                <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`} />
                                                <AvatarFallback>
                                                    {user.firstName[0]}{user.lastName[0]}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <p className="font-medium">{user.firstName} {user.lastName}</p>
                                                <div className="flex items-center space-x-2">
                                                    <p className="text-sm text-muted-foreground">{user.email}</p>
                                                    {user.emailVerified ? (
                                                        <CheckCircle className="h-3 w-3 text-green-600" />
                                                    ) : (
                                                        <XCircle className="h-3 w-3 text-red-600" />
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center space-x-2">
                                            <Building2 className="h-4 w-4 text-muted-foreground" />
                                            <span>{user.organization.name}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <Badge variant={getRoleBadgeVariant(user.role)}>
                                            {user.role.replace('_', ' ')}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center space-x-2">
                                            {getStatusIcon(user.status)}
                                            <span className="capitalize">{user.status}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        {user.lastLoginAt ? (
                                            <div className="text-sm">
                                                <p>{formatDate(user.lastLoginAt)}</p>
                                                {user.sessions.lastLocation && (
                                                    <p className="text-muted-foreground">{user.sessions.lastLocation}</p>
                                                )}
                                            </div>
                                        ) : (
                                            <span className="text-muted-foreground">Never</span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="text-sm">
                                            <p>{user.sessions.active} active</p>
                                            <p className="text-muted-foreground">{user.sessions.total} total</p>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="text-sm">
                                            <p>{formatDate(user.createdAt)}</p>
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="icon">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end" className="w-48">
                                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View Profile
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => {
                                                    setSelectedUser(user);
                                                    setFormData({
                                                        email: user.email,
                                                        firstName: user.firstName,
                                                        lastName: user.lastName,
                                                        role: user.role,
                                                        organizationId: user.organization.id,
                                                        phoneNumber: user.profile.phoneNumber || "",
                                                        jobTitle: user.profile.jobTitle || "",
                                                        department: user.profile.department || "",
                                                    });
                                                    setIsEditDialogOpen(true);
                                                }}>
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit User
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Mail className="mr-2 h-4 w-4" />
                                                    Send Email
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                {user.status === 'active' ? (
                                                    <DropdownMenuItem
                                                        onClick={() => handleSuspendUser(user.id)}
                                                        className="text-yellow-600"
                                                    >
                                                        <UserX className="mr-2 h-4 w-4" />
                                                        Suspend User
                                                    </DropdownMenuItem>
                                                ) : (
                                                    <DropdownMenuItem
                                                        onClick={() => handleActivateUser(user.id)}
                                                        className="text-green-600"
                                                    >
                                                        <UserCheck className="mr-2 h-4 w-4" />
                                                        Activate User
                                                    </DropdownMenuItem>
                                                )}
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        setSelectedUser(user);
                                                        setIsDeleteDialogOpen(true);
                                                    }}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete User
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                        <DialogDescription>
                            Update user information and permissions.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="edit-firstName">First Name</Label>
                                <Input
                                    id="edit-firstName"
                                    value={formData.firstName}
                                    onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="edit-lastName">Last Name</Label>
                                <Input
                                    id="edit-lastName"
                                    value={formData.lastName}
                                    onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="edit-email">Email</Label>
                            <Input
                                id="edit-email"
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="edit-role">Role</Label>
                                <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="USER">User</SelectItem>
                                        <SelectItem value="ORG_ADMIN">Organization Admin</SelectItem>
                                        <SelectItem value="ADMIN">System Admin</SelectItem>
                                        <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="edit-organization">Organization</Label>
                                <Select value={formData.organizationId} onValueChange={(value) => setFormData(prev => ({ ...prev, organizationId: value }))}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {organizations.map((org) => (
                                            <SelectItem key={org.id} value={org.id}>
                                                {org.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="edit-jobTitle">Job Title</Label>
                                <Input
                                    id="edit-jobTitle"
                                    value={formData.jobTitle}
                                    onChange={(e) => setFormData(prev => ({ ...prev, jobTitle: e.target.value }))}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="edit-department">Department</Label>
                                <Input
                                    id="edit-department"
                                    value={formData.department}
                                    onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                                />
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleUpdateUser}>
                            Save Changes
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Delete User</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete {selectedUser?.firstName} {selectedUser?.lastName}? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button variant="destructive" onClick={handleDeleteUser}>
                            Delete User
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}