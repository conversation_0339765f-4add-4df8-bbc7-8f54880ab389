'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Save,
  ArrowLeft,
  Plus,
  Trash2,
  Code,
  Settings,
  Play,
  Eye,
  FileText,
  Database,
  Globe,
  Bot,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  Copy
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  validation?: any;
}

interface ToolConfig {
  apiConfig?: {
    baseUrl?: string;
    headers?: Record<string, string>;
    authentication?: {
      type: 'bearer' | 'basic' | 'api_key';
      credentials: Record<string, string>;
    };
  };
  browserConfig?: {
    headless?: boolean;
    timeout?: number;
    viewport?: { width: number; height: number };
  };
  databaseConfig?: {
    connectionString?: string;
    type: 'postgresql' | 'mysql' | 'mongodb';
    ssl?: boolean;
  };
  ragConfig?: {
    embeddingModel?: string;
    chunkSize?: number;
    overlap?: number;
    vectorStore?: string;
  };
}

interface Tool {
  id?: string;
  name: string;
  description: string;
  type: 'FUNCTION_CALL' | 'RAG' | 'API_FETCH' | 'BROWSER_AUTOMATION' | 'DATABASE' | 'CUSTOM_LOGIC';
  parameters: ToolParameter[];
  config: ToolConfig;
  code?: string;
  tags: string[];
  category: string;
  isPublic: boolean;
  version: string;
  status?: string;
}

const TOOL_TYPES = [
  {
    value: 'FUNCTION_CALL',
    label: 'Function Call',
    icon: Code,
    description: 'Execute custom JavaScript/TypeScript functions',
    color: 'bg-blue-500'
  },
  {
    value: 'RAG',
    label: 'RAG (Retrieval)',
    icon: FileText,
    description: 'Retrieve and process documents with AI',
    color: 'bg-green-500'
  },
  {
    value: 'API_FETCH',
    label: 'API Fetch',
    icon: Globe,
    description: 'Make HTTP requests to external APIs',
    color: 'bg-purple-500'
  },
  {
    value: 'BROWSER_AUTOMATION',
    label: 'Browser Automation',
    icon: Bot,
    description: 'Automate web browser interactions',
    color: 'bg-orange-500'
  },
  {
    value: 'DATABASE',
    label: 'Database',
    icon: Database,
    description: 'Execute database queries and operations',
    color: 'bg-red-500'
  },
  {
    value: 'CUSTOM_LOGIC',
    label: 'Custom Logic',
    icon: Zap,
    description: 'Custom business logic and processing',
    color: 'bg-gray-500'
  },
];

const PARAMETER_TYPES = [
  'string',
  'number',
  'boolean',
  'array',
  'object',
  'json',
  'select',
  'multiselect',
  'file',
  'url',
  'email',
  'date',
  'datetime'
];

const TOOL_CATEGORIES = [
  'Data Processing',
  'Web Scraping',
  'API Integration',
  'Database Operations',
  'File Operations',
  'Communication',
  'Analytics',
  'Automation',
  'AI/ML',
  'Utilities'
];

export default function ToolEditorPage() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const isEditing = !!params.id;
  const toolId = params.id as string;

  const [tool, setTool] = useState<Tool>({
    name: '',
    description: '',
    type: 'FUNCTION_CALL',
    parameters: [],
    config: {},
    code: '',
    tags: [],
    category: '',
    isPublic: false,
    version: '1.0.0',
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (isEditing && toolId) {
      loadTool();
    }
  }, [isEditing, toolId]);

  const loadTool = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/tools/${toolId}`);
      const data = await response.json();

      if (data.success) {
        setTool(data.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load tool',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load tool',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const validateTool = (): string[] => {
    const errors: string[] = [];

    if (!tool.name.trim()) {
      errors.push('Tool name is required');
    }

    if (!tool.description.trim()) {
      errors.push('Tool description is required');
    }

    if (!tool.category) {
      errors.push('Tool category is required');
    }

    // Validate parameters
    tool.parameters.forEach((param, index) => {
      if (!param.name.trim()) {
        errors.push(`Parameter ${index + 1}: Name is required`);
      }
      if (!param.type) {
        errors.push(`Parameter ${index + 1}: Type is required`);
      }
    });

    // Type-specific validations
    if (tool.type === 'API_FETCH' && !tool.config.apiConfig?.baseUrl) {
      errors.push('API base URL is required for API Fetch tools');
    }

    if (tool.type === 'DATABASE' && !tool.config.databaseConfig?.connectionString) {
      errors.push('Database connection string is required for Database tools');
    }

    if (tool.type === 'FUNCTION_CALL' && !tool.code?.trim()) {
      errors.push('Function code is required for Function Call tools');
    }

    return errors;
  };

  const handleSave = async () => {
    const errors = validateTool();
    setValidationErrors(errors);

    if (errors.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the validation errors before saving',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSaving(true);
      const url = isEditing ? `/api/tools/${toolId}` : '/api/tools';
      const method = isEditing ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tool),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: `Tool ${isEditing ? 'updated' : 'created'} successfully`,
        });
        router.push('/dashboard/tools');
      } else {
        toast({
          title: 'Error',
          description: data.message || `Failed to ${isEditing ? 'update' : 'create'} tool`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${isEditing ? 'update' : 'create'} tool`,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    const errors = validateTool();
    if (errors.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fix validation errors before testing',
        variant: 'destructive',
      });
      return;
    }

    try {
      setTesting(true);
      // Create a temporary tool for testing
      const response = await fetch('/api/tools/test-draft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tool),
      });

      const data = await response.json();

      if (data.success) {
        // Open test page in new tab
        window.open(`/dashboard/tools/test-draft/${data.data.id}`, '_blank');
      } else {
        toast({
          title: 'Test Error',
          description: data.message || 'Failed to create test environment',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to test tool',
        variant: 'destructive',
      });
    } finally {
      setTesting(false);
    }
  };

  const addParameter = () => {
    setTool(prev => ({
      ...prev,
      parameters: [
        ...prev.parameters,
        {
          name: '',
          type: 'string',
          description: '',
          required: false,
          validation: {}
        }
      ]
    }));
  };

  const updateParameter = (index: number, field: keyof ToolParameter, value: any) => {
    setTool(prev => ({
      ...prev,
      parameters: prev.parameters.map((param, i) =>
        i === index ? { ...param, [field]: value } : param
      )
    }));
  };

  const removeParameter = (index: number) => {
    setTool(prev => ({
      ...prev,
      parameters: prev.parameters.filter((_, i) => i !== index)
    }));
  };

  const addTag = (tag: string) => {
    if (tag && !tool.tags.includes(tag)) {
      setTool(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const removeTag = (tag: string) => {
    setTool(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = TOOL_TYPES.find(t => t.value === type);
    return typeConfig?.icon || Code;
  };

  const renderTypeSpecificConfig = () => {
    switch (tool.type) {
      case 'API_FETCH':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Base URL *</Label>
              <Input
                value={tool.config.apiConfig?.baseUrl || ''}
                onChange={(e) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    apiConfig: {
                      ...prev.config.apiConfig,
                      baseUrl: e.target.value
                    }
                  }
                }))}
                placeholder="https://api.example.com"
                className="bg-white/50"
              />
            </div>

            <div className="space-y-2">
              <Label>Authentication Type</Label>
              <Select
                value={tool.config.apiConfig?.authentication?.type || 'none'}
                onValueChange={(value) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    apiConfig: {
                      ...prev.config.apiConfig,
                      authentication: value === 'none' ? undefined : {
                        type: value as any,
                        credentials: {}
                      }
                    }
                  }
                }))}
              >
                <SelectTrigger className="bg-white/50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="bearer">Bearer Token</SelectItem>
                  <SelectItem value="basic">Basic Auth</SelectItem>
                  <SelectItem value="api_key">API Key</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 'DATABASE':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Database Type</Label>
              <Select
                value={tool.config.databaseConfig?.type || 'postgresql'}
                onValueChange={(value) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    databaseConfig: {
                      ...prev.config.databaseConfig,
                      type: value as any
                    }
                  }
                }))}
              >
                <SelectTrigger className="bg-white/50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="postgresql">PostgreSQL</SelectItem>
                  <SelectItem value="mysql">MySQL</SelectItem>
                  <SelectItem value="mongodb">MongoDB</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Connection String *</Label>
              <Input
                type="password"
                value={tool.config.databaseConfig?.connectionString || ''}
                onChange={(e) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    databaseConfig: {
                      ...prev.config.databaseConfig,
                      connectionString: e.target.value,
                      type: prev.config.databaseConfig?.type || 'postgresql'
                    }
                  }
                }))}
                placeholder="postgresql://user:password@host:port/database"
                className="bg-white/50"
              />
            </div>
          </div>
        );

      case 'BROWSER_AUTOMATION':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Headless Mode</Label>
              <Switch
                checked={tool.config.browserConfig?.headless ?? true}
                onCheckedChange={(checked) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    browserConfig: {
                      ...prev.config.browserConfig,
                      headless: checked
                    }
                  }
                }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Timeout (ms)</Label>
              <Input
                type="number"
                value={tool.config.browserConfig?.timeout || 30000}
                onChange={(e) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    browserConfig: {
                      ...prev.config.browserConfig,
                      timeout: parseInt(e.target.value) || 30000
                    }
                  }
                }))}
                className="bg-white/50"
              />
            </div>
          </div>
        );

      case 'RAG':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Embedding Model</Label>
              <Select
                value={tool.config.ragConfig?.embeddingModel || 'text-embedding-ada-002'}
                onValueChange={(value) => setTool(prev => ({
                  ...prev,
                  config: {
                    ...prev.config,
                    ragConfig: {
                      ...prev.config.ragConfig,
                      embeddingModel: value
                    }
                  }
                }))}
              >
                <SelectTrigger className="bg-white/50">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text-embedding-ada-002">OpenAI Ada 002</SelectItem>
                  <SelectItem value="text-embedding-3-small">OpenAI 3 Small</SelectItem>
                  <SelectItem value="text-embedding-3-large">OpenAI 3 Large</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Chunk Size</Label>
                <Input
                  type="number"
                  value={tool.config.ragConfig?.chunkSize || 1000}
                  onChange={(e) => setTool(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      ragConfig: {
                        ...prev.config.ragConfig,
                        chunkSize: parseInt(e.target.value) || 1000
                      }
                    }
                  }))}
                  className="bg-white/50"
                />
              </div>

              <div className="space-y-2">
                <Label>Overlap</Label>
                <Input
                  type="number"
                  value={tool.config.ragConfig?.overlap || 200}
                  onChange={(e) => setTool(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      ragConfig: {
                        ...prev.config.ragConfig,
                        overlap: parseInt(e.target.value) || 200
                      }
                    }
                  }))}
                  className="bg-white/50"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tool...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/dashboard/tools')}
              className="bg-white/80 backdrop-blur-sm border border-white/20"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-1">
                {isEditing ? 'Edit Tool' : 'Create Tool'}
              </h1>
              <p className="text-gray-600">
                {isEditing ? 'Modify your existing tool' : 'Build a new AI tool for your workflows'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={handleTest}
              disabled={testing}
              className="bg-white/80 backdrop-blur-sm border-white/20"
            >
              {testing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Test
                </>
              )}
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update' : 'Create'}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <div className="font-medium mb-2">Please fix the following errors:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Editor */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-sm">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="parameters">Parameters</TabsTrigger>
                <TabsTrigger value="config">Configuration</TabsTrigger>
                <TabsTrigger value="code">Code</TabsTrigger>
              </TabsList>

              {/* Basic Info Tab */}
              <TabsContent value="basic" className="space-y-6">
                <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                    <CardDescription>
                      Define the basic properties of your tool
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Tool Name *</Label>
                      <Input
                        id="name"
                        value={tool.name}
                        onChange={(e) => setTool(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter tool name"
                        className="bg-white/50"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description *</Label>
                      <Textarea
                        id="description"
                        value={tool.description}
                        onChange={(e) => setTool(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Describe what this tool does"
                        className="bg-white/50"
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Tool Type *</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {TOOL_TYPES.map(type => {
                          const Icon = type.icon;
                          return (
                            <Card
                              key={type.value}
                              className={`cursor-pointer transition-all duration-200 ${tool.type === type.value
                                  ? 'ring-2 ring-blue-500 bg-blue-50'
                                  : 'hover:bg-gray-50'
                                }`}
                              onClick={() => setTool(prev => ({ ...prev, type: type.value as any }))}
                            >
                              <CardContent className="p-4">
                                <div className="flex items-center gap-3">
                                  <div className={`p-2 rounded-lg ${type.color} text-white`}>
                                    <Icon className="h-4 w-4" />
                                  </div>
                                  <div>
                                    <div className="font-medium">{type.label}</div>
                                    <div className="text-xs text-gray-500">{type.description}</div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Category *</Label>
                        <Select
                          value={tool.category}
                          onValueChange={(value) => setTool(prev => ({ ...prev, category: value }))}
                        >
                          <SelectTrigger className="bg-white/50">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {TOOL_CATEGORIES.map(category => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Version</Label>
                        <Input
                          value={tool.version}
                          onChange={(e) => setTool(prev => ({ ...prev, version: e.target.value }))}
                          placeholder="1.0.0"
                          className="bg-white/50"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Tags</Label>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {tool.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <button
                              onClick={() => removeTag(tag)}
                              className="ml-1 hover:text-red-500"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                      <Input
                        placeholder="Add tags (press Enter)"
                        className="bg-white/50"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            const input = e.target as HTMLInputElement;
                            addTag(input.value.trim());
                            input.value = '';
                          }
                        }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="isPublic">Make this tool public</Label>
                      <Switch
                        id="isPublic"
                        checked={tool.isPublic}
                        onCheckedChange={(checked) => setTool(prev => ({ ...prev, isPublic: checked }))}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Parameters Tab */}
              <TabsContent value="parameters" className="space-y-6">
                <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Input Parameters</CardTitle>
                        <CardDescription>
                          Define the input parameters your tool accepts
                        </CardDescription>
                      </div>
                      <Button onClick={addParameter} size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Parameter
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {tool.parameters.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No parameters defined</p>
                        <p className="text-sm">Add parameters to define tool inputs</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {tool.parameters.map((param, index) => (
                          <Card key={index} className="border border-gray-200">
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-4">
                                <h4 className="font-medium">Parameter {index + 1}</h4>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => removeParameter(index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label>Name *</Label>
                                  <Input
                                    value={param.name}
                                    onChange={(e) => updateParameter(index, 'name', e.target.value)}
                                    placeholder="Parameter name"
                                    className="bg-white/50"
                                  />
                                </div>

                                <div className="space-y-2">
                                  <Label>Type *</Label>
                                  <Select
                                    value={param.type}
                                    onValueChange={(value) => updateParameter(index, 'type', value)}
                                  >
                                    <SelectTrigger className="bg-white/50">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {PARAMETER_TYPES.map(type => (
                                        <SelectItem key={type} value={type}>
                                          {type}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>

                              <div className="mt-4 space-y-2">
                                <Label>Description</Label>
                                <Textarea
                                  value={param.description || ''}
                                  onChange={(e) => updateParameter(index, 'description', e.target.value)}
                                  placeholder="Describe this parameter"
                                  className="bg-white/50"
                                  rows={2}
                                />
                              </div>

                              <div className="mt-4 flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Switch
                                    checked={param.required || false}
                                    onCheckedChange={(checked) => updateParameter(index, 'required', checked)}
                                  />
                                  <Label>Required</Label>
                                </div>

                                <div className="space-y-2">
                                  <Label>Default Value</Label>
                                  <Input
                                    value={param.defaultValue || ''}
                                    onChange={(e) => updateParameter(index, 'defaultValue', e.target.value)}
                                    placeholder="Default value"
                                    className="bg-white/50 w-32"
                                  />
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Configuration Tab */}
              <TabsContent value="config" className="space-y-6">
                <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {React.createElement(getTypeIcon(tool.type), { className: "h-5 w-5" })}
                      {TOOL_TYPES.find(t => t.value === tool.type)?.label} Configuration
                    </CardTitle>
                    <CardDescription>
                      Configure type-specific settings for your tool
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {renderTypeSpecificConfig() || (
                      <div className="text-center py-8 text-gray-500">
                        <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No additional configuration required for this tool type</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Code Tab */}
              <TabsContent value="code" className="space-y-6">
                <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Code className="h-5 w-5" />
                      Function Code
                    </CardTitle>
                    <CardDescription>
                      {tool.type === 'FUNCTION_CALL'
                        ? 'Write the JavaScript/TypeScript code for your function'
                        : 'Optional custom code for advanced tool behavior'
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      value={tool.code || ''}
                      onChange={(e) => setTool(prev => ({ ...prev, code: e.target.value }))}
                      placeholder={tool.type === 'FUNCTION_CALL'
                        ? `// Your function code here
async function execute(params) {
  // Access parameters: params.parameterName
  
  // Your logic here
  
  return {
    success: true,
    data: result
  };
}`
                        : '// Optional custom code'
                      }
                      className="bg-white/50 font-mono text-sm min-h-[400px]"
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Tool Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {React.createElement(getTypeIcon(tool.type), { className: "h-4 w-4" })}
                    <span className="font-medium">{tool.name || 'Untitled Tool'}</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {tool.description || 'No description provided'}
                  </p>
                </div>

                <div className="flex flex-wrap gap-2">
                  <Badge className={TOOL_TYPES.find(t => t.value === tool.type)?.color + ' text-white'}>
                    {TOOL_TYPES.find(t => t.value === tool.type)?.label}
                  </Badge>
                  {tool.category && (
                    <Badge variant="outline">{tool.category}</Badge>
                  )}
                  <Badge variant="outline">v{tool.version}</Badge>
                  {tool.isPublic && (
                    <Badge className="bg-green-500 text-white">Public</Badge>
                  )}
                </div>

                {tool.tags.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm">Tags</Label>
                    <div className="flex flex-wrap gap-1">
                      {tool.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {tool.parameters.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm">Parameters ({tool.parameters.length})</Label>
                    <div className="space-y-1">
                      {tool.parameters.map((param, index) => (
                        <div key={index} className="text-xs bg-gray-50 p-2 rounded">
                          <span className="font-medium">{param.name}</span>
                          <span className="text-gray-500 ml-2">({param.type})</span>
                          {param.required && (
                            <span className="text-red-500 ml-1">*</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-white/80 backdrop-blur-sm border border-white/20">
              <CardHeader>
                <CardTitle className="text-sm">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {showPreview ? 'Hide' : 'Show'} Preview
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    navigator.clipboard.writeText(JSON.stringify(tool, null, 2));
                    toast({ title: 'Copied', description: 'Tool configuration copied to clipboard' });
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Config
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}