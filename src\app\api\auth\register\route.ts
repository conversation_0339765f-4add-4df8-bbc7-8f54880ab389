/**
 * Register API Route
 * Production-grade user registration endpoint with MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import {
  registerUser,
  setAuthCookies
} from '@/lib/auth/auth-service';
import { withValidation } from '@/lib/middleware/auth.middleware';
import { trackAuthEvent } from '@/lib/mcp-tools';

// Registration request schema validation
const registerSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^a-zA-Z0-9]/, 'Password must contain at least one special character'),
  company: z.string().min(2, 'Company name must be at least 2 characters'),
  subscribeNewsletter: z.boolean().optional().default(false)
});

// Registration handler
async function handler(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();

    // Register user
    const authResponse = await registerUser({
      firstName: body.firstName,
      lastName: body.lastName,
      email: body.email,
      password: body.password,
      company: body.company,
      subscribeNewsletter: body.subscribeNewsletter
    });

    // Create response
    const response = NextResponse.json({
      success: true,
      user: authResponse.user
    });

    // Set auth cookies
    if (authResponse.tokens) {
      setAuthCookies(response, authResponse.tokens, true);
    }

    // Track successful registration in MCP Memory
    if (authResponse.user) {
      trackAuthEvent(authResponse.user.id, 'register', {
        email: body.email,
        company: body.company,
        ip: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      });
    }

    return response;
  } catch (error: any) {
    console.error('Registration error:', error);

    // Check for duplicate email
    if (error.message?.includes('email') && error.message?.includes('already')) {
      return NextResponse.json(
        {
          success: false,
          message: 'Email already registered',
          code: 'EMAIL_EXISTS'
        },
        { status: 409 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Registration failed',
        code: error.code || 'REGISTRATION_ERROR'
      },
      { status: 400 }
    );
  }
}

// Export with validation middleware
export const POST = withValidation(handler, {
  body: registerSchema
});