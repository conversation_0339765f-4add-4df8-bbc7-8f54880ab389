import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowDown, Play } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface StartNodeData {
  label: string;
  description?: string;
  status?: 'idle' | 'running' | 'success';
  isConfigValid?: boolean;
}

const StartNode: React.FC<NodeProps<StartNodeData>> = ({ data, selected }) => {
  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      default:
        return 'border-green-200 bg-green-50';
    }
  };

  return (
    <Card
      className={cn(
        'min-w-[150px] max-w-[200px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor()
      )}
    >
      <CardContent className="p-4 text-center">
        {/* Icon */}
        <div className="flex justify-center mb-2">
          <div className="p-2 bg-green-100 rounded-full">
            {data.status === 'running' ? (
              <Play className="h-4 w-4 text-green-600 animate-pulse" />
            ) : (
              <ArrowDown className="h-4 w-4 text-green-600" />
            )}
          </div>
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-300">
          Start
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mt-2">
            {data.description}
          </p>
        )}
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(StartNode);