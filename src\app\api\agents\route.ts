import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getAuthenticatedSession, unauthorizedResponse } from "@/lib/auth";
import { z } from "zod";

const prisma = new PrismaClient();

// Validation schemas
const createAgentSchema = z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    type: z.enum(["STANDALONE", "TOOL_DRIVEN", "HYBRID", "MULTI_TASKING"]).optional(),
    config: z.object({
        provider: z.string().default("openai"),
        model: z.string().default("gpt-4"),
        temperature: z.number().min(0).max(2).default(0.7),
        maxTokens: z.number().min(1).max(32000).default(2000),
        systemPrompt: z.string().optional(),
        instructions: z.string().optional(),
    }),
    skills: z.array(z.string()).optional(),
    isActive: z.boolean().optional(),
});

const updateAgentSchema = createAgentSchema.partial();

// GET /api/agents - List agents
export async function GET(request: NextRequest) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get("page") || "1");
        const limit = parseInt(url.searchParams.get("limit") || "20");
        const search = url.searchParams.get("search");
        const type = url.searchParams.get("type");
        const isActive = url.searchParams.get("isActive");

        const skip = (page - 1) * limit;

        // Build where clause
        const where: any = {
            organizationId: session?.user?.organizationId,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: "insensitive" } },
                    { description: { contains: search, mode: "insensitive" } },
                ],
            }),
            ...(type && type !== "all" && { type }),
            ...(isActive !== null && isActive !== undefined && {
                isActive: isActive === "true",
            }),
        };

        // Get agents
        const [agents, total] = await Promise.all([
            prisma.agent.findMany({
                where,
                skip,
                take: limit,
                orderBy: { updatedAt: "desc" },
                include: {
                    creator: {
                        select: {
                            firstName: true,
                            lastName: true,
                            avatar: true,
                        },
                    },
                    sessions: {
                        select: {
                            id: true,
                            isActive: true,
                            createdAt: true,
                        },
                        take: 5,
                        orderBy: { createdAt: "desc" },
                    },
                    _count: {
                        select: {
                            sessions: true,
                        },
                    },
                },
            }),
            prisma.agent.count({ where }),
        ]);

        // Transform agents for frontend
        const transformedAgents = agents.map((agent) => {
            const activeSessions = agent.sessions.filter(session => session.isActive).length;

            return {
                id: agent.id,
                name: agent.name,
                description: agent.description,
                type: agent.type,
                status: agent.isActive ? "active" : "inactive",
                version: agent.version,
                config: agent.config,
                skills: agent.skills,
                activeSessions,
                totalSessions: agent._count.sessions,
                lastActivity: agent.sessions[0]?.createdAt,
                creator: {
                    name: `${agent.creator.firstName} ${agent.creator.lastName}`,
                    avatar: agent.creator.avatar,
                },
                createdAt: agent.createdAt,
                updatedAt: agent.updatedAt,
            };
        });

        // Get stats
        const [totalAgents, activeAgents, recentSessions] = await Promise.all([
            prisma.agent.count({
                where: { organizationId: session.user.organizationId },
            }),
            prisma.agent.count({
                where: { organizationId: session.user.organizationId, isActive: true },
            }),
            prisma.agentSession.count({
                where: {
                    agent: { organizationId: session.user.organizationId },
                    createdAt: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
                    },
                },
            }),
        ]);

        const stats = {
            total: totalAgents,
            active: activeAgents,
            inactive: totalAgents - activeAgents,
            recentSessions,
        };

        return NextResponse.json({
            agents: transformedAgents,
            stats,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        console.error("Failed to fetch agents:", error);
        return NextResponse.json(
            { error: "Failed to fetch agents" },
            { status: 500 }
        );
    }
}

// POST /api/agents - Create agent
export async function POST(request: NextRequest) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const body = await request.json();
        const validatedData = createAgentSchema.parse(body);

        // Create agent
        const agent = await prisma.agent.create({
            data: {
                name: validatedData.name,
                description: validatedData.description,
                type: validatedData.type || "STANDALONE",
                config: validatedData.config,
                skills: validatedData.skills || [],
                isActive: validatedData.isActive ?? true,
                creatorId: session.user.id,
                organizationId: session.user.organizationId,
            },
            include: {
                creator: {
                    select: {
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        sessions: true,
                    },
                },
            },
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.user.id,
                action: "AGENT_CREATED",
                resource: "AGENT",
                resourceId: agent.id,
                details: {
                    name: agent.name,
                    type: agent.type,
                    isActive: agent.isActive,
                },
                organizationId: session.user.organizationId,
            },
        });

        const response = {
            id: agent.id,
            name: agent.name,
            description: agent.description,
            type: agent.type,
            status: agent.isActive ? "active" : "inactive",
            version: agent.version,
            config: agent.config,
            skills: agent.skills,
            activeSessions: 0,
            totalSessions: 0,
            creator: {
                name: `${agent.creator.firstName} ${agent.creator.lastName}`,
                avatar: agent.creator.avatar,
            },
            createdAt: agent.createdAt,
            updatedAt: agent.updatedAt,
        };

        return NextResponse.json(response, { status: 201 });
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { error: "Validation failed", details: error.issues.map((err: any) => err.message) },
                { status: 400 }
            );
        }

        console.error("Failed to create agent:", error);
        return NextResponse.json(
            { error: "Failed to create agent" },
            { status: 500 }
        );
    }
}