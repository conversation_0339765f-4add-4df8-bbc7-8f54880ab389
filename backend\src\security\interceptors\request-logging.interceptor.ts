import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LoggingService } from '../../logging/logging.service';

@Injectable()
export class RequestLoggingInterceptor implements NestInterceptor {
    constructor(private logger: LoggingService) { }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const start = Date.now();

        return next.handle().pipe(
            tap(() => {
                const duration = Date.now() - start;
                const { method, url, headers, user } = request;

                this.logger.logApiRequest(
                    method,
                    url,
                    response.statusCode,
                    duration,
                    user?.id,
                    user?.organizationId,
                    headers['x-request-id']
                );
            }),
        );
    }
}