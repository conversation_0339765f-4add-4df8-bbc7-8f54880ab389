import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ApixGateway } from '../../apix/apix.gateway';
import * as z from 'zod';

export interface SkillDefinition {
    id: string;
    name: string;
    description: string;
    category: string;
    version: string;

    // Function signature
    parameters: z.ZodSchema;
    returns: z.ZodSchema;

    // Execution details
    implementation: string | Function;
    timeout?: number;
    retryPolicy?: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
        initialDelay: number;
    };

    // Security and access
    permissions: string[];
    rateLimits?: {
        requestsPerMinute: number;
        requestsPerHour: number;
    };

    // Metadata
    tags: string[];
    author: string;
    documentation?: string;
    examples?: any[];

    // Dependencies
    dependencies?: string[];
    requirements?: {
        providers?: string[];
        tools?: string[];
        capabilities?: string[];
    };
}

export interface SkillExecutionContext {
    agentId: string;
    sessionId: string;
    userId?: string;
    organizationId: string;
    metadata?: any;
}

export interface SkillExecutionResult {
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
    metadata?: any;
    usage?: {
        tokensUsed?: number;
        apiCalls?: number;
        cost?: number;
    };
}

@Injectable()
export class SkillExecutorService {
    private readonly logger = new Logger(SkillExecutorService.name);
    private readonly skillRegistry = new Map<string, SkillDefinition>();
    private readonly executionStats = new Map<string, any>();

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configService: ConfigService,
        private apixGateway: ApixGateway,
    ) {
        this.initializeBuiltInSkills();
    }

    // ============================================================================
    // SKILL REGISTRY MANAGEMENT
    // ============================================================================

    async registerSkill(skill: SkillDefinition): Promise<void> {
        // Validate skill definition
        this.validateSkillDefinition(skill);

        // Store in registry
        this.skillRegistry.set(skill.id, skill);

        // Cache for quick access
        await this.cacheManager.set(`skill:${skill.id}`, skill, 3600000); // 1 hour

        this.logger.log(`Registered skill: ${skill.id} (${skill.name})`);

        await this.apixGateway.emitAgentEvent(
            'system',
            'system',
            'skill.registered',
            {
                skillId: skill.id,
                name: skill.name,
                category: skill.category,
            },
            { priority: 'normal' }
        );
    }

    async unregisterSkill(skillId: string): Promise<void> {
        this.skillRegistry.delete(skillId);
        await this.cacheManager.del(`skill:${skillId}`);

        this.logger.log(`Unregistered skill: ${skillId}`);

        await this.apixGateway.emitAgentEvent(
            'system',
            'system',
            'skill.unregistered',
            { skillId },
            { priority: 'normal' }
        );
    }

    async getSkill(skillId: string): Promise<SkillDefinition | null> {
        // Try cache first
        let skill = await this.cacheManager.get(`skill:${skillId}`) as SkillDefinition;

        if (!skill) {
            skill = this.skillRegistry.get(skillId) || null;
            if (skill) {
                await this.cacheManager.set(`skill:${skillId}`, skill, 3600000);
            }
        }

        return skill;
    }

    async getSkillsByCategory(category: string): Promise<SkillDefinition[]> {
        const skills = Array.from(this.skillRegistry.values());
        return skills.filter(skill => skill.category === category);
    }

    async getSkillsForAgent(agentId: string): Promise<SkillDefinition[]> {
        const agent = await this.prisma.agentInstance.findUnique({
            where: { id: agentId },
            select: { skills: true, capabilities: true },
        });

        if (!agent) {
            return [];
        }

        const skills: SkillDefinition[] = [];

        for (const skillId of agent.skills) {
            const skill = await this.getSkill(skillId);
            if (skill) {
                skills.push(skill);
            }
        }

        return skills;
    }

    async getAgentTools(agentId: string): Promise<any[]> {
        const skills = await this.getSkillsForAgent(agentId);

        return skills.map(skill => ({
            type: 'function',
            definition: {
                name: skill.id,
                description: skill.description,
                parameters: this.zodSchemaToOpenAPISchema(skill.parameters),
            },
            skill,
        }));
    }

    // ============================================================================
    // SKILL EXECUTION
    // ============================================================================

    async executeSkill(
        agentId: string,
        skillId: string,
        parameters: any,
        context?: SkillExecutionContext,
    ): Promise<SkillExecutionResult> {
        const startTime = Date.now();

        try {
            // Get skill definition
            const skill = await this.getSkill(skillId);
            if (!skill) {
                throw new NotFoundException(`Skill not found: ${skillId}`);
            }

            // Validate agent has access to skill
            await this.validateSkillAccess(agentId, skill);

            // Validate parameters
            const validatedParams = this.validateParameters(skill, parameters);

            // Check rate limits
            await this.checkRateLimits(agentId, skillId, skill);

            // Execute skill
            const result = await this.performSkillExecution(
                skill,
                validatedParams,
                context || { agentId, sessionId: '', organizationId: '' },
            );

            // Validate result
            const validatedResult = this.validateResult(skill, result);

            const executionTime = Date.now() - startTime;

            // Track execution stats
            await this.trackSkillExecution(agentId, skillId, executionTime, true);

            // Emit real-time execution event
            await this.apixGateway.emitAgentEvent(
                context?.organizationId || 'system',
                agentId,
                'skill.executed',
                {
                    agentId,
                    skillId,
                    success: true,
                    duration: executionTime,
                    parameters: validatedParams,
                },
                { priority: 'high' }
            );

            return {
                success: true,
                result: validatedResult,
                duration: executionTime,
                metadata: {
                    skillId,
                    skillName: skill.name,
                    version: skill.version,
                },
            };

        } catch (error) {
            const executionTime = Date.now() - startTime;

            this.logger.error(`Skill execution failed: ${skillId}`, error.stack);

            // Track failure
            await this.trackSkillExecution(agentId, skillId, executionTime, false);

            // Emit real-time error event
            await this.apixGateway.emitAgentEvent(
                context?.organizationId || 'system',
                agentId,
                'skill.error',
                {
                    agentId,
                    skillId,
                    error: error.message,
                    duration: executionTime,
                },
                { priority: 'high' }
            );

            return {
                success: false,
                error: error.message,
                duration: executionTime,
                metadata: {
                    skillId,
                    errorType: error.constructor.name,
                },
            };
        }
    }

    private async performSkillExecution(
        skill: SkillDefinition,
        parameters: any,
        context: SkillExecutionContext,
    ): Promise<any> {
        const timeout = skill.timeout || 30000; // 30 seconds default

        // Create execution environment
        const executionEnv = {
            prisma: this.prisma,
            cache: this.cacheManager,
            config: this.configService,
            context,
            logger: this.logger,
            emit: async (event: string, data: any) => {
                await this.apixGateway.emitAgentEvent(
                    context.organizationId || 'system',
                    context.agentId,
                    event,
                    data,
                    { priority: 'normal' }
                );
            },
        };

        // Execute with timeout
        return Promise.race([
            this.executeWithRetry(skill, parameters, executionEnv),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Skill execution timeout')), timeout)
            ),
        ]);
    }

    private async executeWithRetry(
        skill: SkillDefinition,
        parameters: any,
        env: any,
    ): Promise<any> {
        const retryPolicy = skill.retryPolicy || {
            maxRetries: 1,
            backoffStrategy: 'linear',
            initialDelay: 1000,
        };

        let lastError: Error | null = null;

        for (let attempt = 0; attempt <= retryPolicy.maxRetries; attempt++) {
            try {
                if (typeof skill.implementation === 'function') {
                    return await skill.implementation(parameters, env);
                } else {
                    // Execute string-based implementation (sandboxed)
                    return await this.executeSandboxedCode(skill.implementation, parameters, env);
                }
            } catch (error) {
                lastError = error;

                if (attempt < retryPolicy.maxRetries) {
                    const delay = this.calculateBackoffDelay(
                        attempt,
                        retryPolicy.backoffStrategy,
                        retryPolicy.initialDelay,
                    );

                    this.logger.warn(
                        `Skill execution attempt ${attempt + 1} failed, retrying in ${delay}ms: ${skill.id}`,
                    );

                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError;
    }

    private calculateBackoffDelay(
        attempt: number,
        strategy: 'linear' | 'exponential',
        initialDelay: number,
    ): number {
        if (strategy === 'exponential') {
            return initialDelay * Math.pow(2, attempt);
        } else {
            return initialDelay * (attempt + 1);
        }
    }

    // ============================================================================
    // BUILT-IN SKILLS INITIALIZATION
    // ============================================================================

    private initializeBuiltInSkills(): void {
        // Data processing skills
        this.registerBuiltInSkill({
            id: 'text_analyzer',
            name: 'Text Analyzer',
            description: 'Analyze text for sentiment, entities, and key phrases',
            category: 'DATA_ANALYSIS',
            version: '1.0.0',
            parameters: z.object({
                text: z.string().min(1).max(10000),
                analysis_types: z.array(z.enum(['sentiment', 'entities', 'keywords'])).optional().default(['sentiment']),
            }),
            returns: z.object({
                sentiment: z.object({
                    score: z.number(),
                    label: z.enum(['positive', 'negative', 'neutral']),
                }).optional(),
                entities: z.array(z.object({
                    text: z.string(),
                    type: z.string(),
                    confidence: z.number(),
                })).optional(),
                keywords: z.array(z.string()).optional(),
            }),
            implementation: this.textAnalyzerSkill.bind(this),
            permissions: ['text:analyze'],
            tags: ['nlp', 'analysis'],
            author: 'system',
        });

        // Web scraping skills
        this.registerBuiltInSkill({
            id: 'web_scraper',
            name: 'Web Scraper',
            description: 'Extract content from web pages',
            category: 'INTEGRATION',
            version: '1.0.0',
            parameters: z.object({
                url: z.string().url(),
                selectors: z.object({
                    title: z.string().optional(),
                    content: z.string().optional(),
                    links: z.string().optional(),
                }).optional(),
                options: z.object({
                    timeout: z.number().optional().default(10000),
                    user_agent: z.string().optional(),
                }).optional(),
            }),
            returns: z.object({
                title: z.string().optional(),
                content: z.string().optional(),
                links: z.array(z.string()).optional(),
                metadata: z.object({
                    url: z.string(),
                    timestamp: z.string(),
                    status: z.number(),
                }),
            }),
            implementation: this.webScraperSkill.bind(this),
            permissions: ['web:scrape'],
            tags: ['web', 'scraping'],
            author: 'system',
            rateLimits: {
                requestsPerMinute: 10,
                requestsPerHour: 100,
            },
        });

        // Mathematical calculation skills
        this.registerBuiltInSkill({
            id: 'calculator',
            name: 'Calculator',
            description: 'Perform mathematical calculations',
            category: 'AUTOMATION',
            version: '1.0.0',
            parameters: z.object({
                expression: z.string().min(1),
                precision: z.number().optional().default(10),
            }),
            returns: z.object({
                result: z.number(),
                expression: z.string(),
                precision: z.number(),
            }),
            implementation: this.calculatorSkill.bind(this),
            permissions: ['math:calculate'],
            tags: ['math', 'calculation'],
            author: 'system',
        });

        // File processing skills
        this.registerBuiltInSkill({
            id: 'file_processor',
            name: 'File Processor',
            description: 'Process and analyze files',
            category: 'AUTOMATION',
            version: '1.0.0',
            parameters: z.object({
                file_path: z.string(),
                operation: z.enum(['read', 'analyze', 'convert']),
                options: z.object({
                    encoding: z.string().optional().default('utf-8'),
                    max_size: z.number().optional().default(10485760), // 10MB
                }).optional(),
            }),
            returns: z.object({
                content: z.string().optional(),
                metadata: z.object({
                    size: z.number(),
                    type: z.string(),
                    encoding: z.string(),
                }),
                analysis: z.any().optional(),
            }),
            implementation: this.fileProcessorSkill.bind(this),
            permissions: ['file:process'],
            tags: ['file', 'processing'],
            author: 'system',
        });

        this.logger.log('Initialized built-in skills');
    }

    private async registerBuiltInSkill(skill: Omit<SkillDefinition, 'dependencies' | 'requirements' | 'documentation' | 'examples'>): Promise<void> {
        const fullSkill: SkillDefinition = {
            ...skill,
            dependencies: [],
            requirements: {},
            documentation: `Built-in skill: ${skill.description}`,
            examples: [],
        };

        await this.registerSkill(fullSkill);
    }

    // ============================================================================
    // BUILT-IN SKILL IMPLEMENTATIONS
    // ============================================================================

    private async textAnalyzerSkill(params: any, env: any): Promise<any> {
        const { text, analysis_types } = params;
        const result: any = {};

        if (analysis_types.includes('sentiment')) {
            result.sentiment = this.analyzeSentiment(text);
        }

        if (analysis_types.includes('entities')) {
            result.entities = this.extractEntities(text);
        }

        if (analysis_types.includes('keywords')) {
            result.keywords = this.extractKeywords(text);
        }

        return result;
    }

    private analyzeSentiment(text: string): any {
        // Simple sentiment analysis - can be enhanced with ML models
        const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic'];
        const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];

        const words = text.toLowerCase().match(/\b\w+\b/g) || [];
        const positiveCount = words.filter(word => positiveWords.includes(word)).length;
        const negativeCount = words.filter(word => negativeWords.includes(word)).length;

        const score = (positiveCount - negativeCount) / Math.max(words.length, 1);

        let label: 'positive' | 'negative' | 'neutral';
        if (score > 0.1) label = 'positive';
        else if (score < -0.1) label = 'negative';
        else label = 'neutral';

        return { score, label };
    }

    private extractEntities(text: string): any[] {
        // Simple entity extraction
        const entities: any[] = [];

        // Extract potential person names (capitalized words)
        const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g;
        const nameMatches = text.match(namePattern) || [];

        nameMatches.forEach(match => {
            entities.push({
                text: match,
                type: 'PERSON',
                confidence: 0.8,
            });
        });

        // Extract potential organizations
        const orgPattern = /\b[A-Z][a-z]+ (Inc|Corp|LLC|Ltd|Company)\b/g;
        const orgMatches = text.match(orgPattern) || [];

        orgMatches.forEach(match => {
            entities.push({
                text: match,
                type: 'ORGANIZATION',
                confidence: 0.7,
            });
        });

        return entities;
    }

    private extractKeywords(text: string): string[] {
        const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        const words = text.toLowerCase().match(/\b\w+\b/g) || [];

        const wordCount = new Map<string, number>();
        words.forEach(word => {
            if (!stopWords.has(word) && word.length > 3) {
                wordCount.set(word, (wordCount.get(word) || 0) + 1);
            }
        });

        return Array.from(wordCount.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([word]) => word);
    }

    private async webScraperSkill(params: any, env: any): Promise<any> {
        const { url, selectors = {}, options = {} } = params;

        // Simple web scraping implementation
        // In production, would use a proper web scraping library
        try {
            const response = await fetch(url, {
                headers: {
                    'User-Agent': options.user_agent || 'SynapseAI Agent/1.0',
                },
                signal: AbortSignal.timeout(options.timeout || 10000),
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();

            // Basic HTML parsing (would use cheerio or similar in production)
            const result: any = {
                metadata: {
                    url,
                    timestamp: new Date().toISOString(),
                    status: response.status,
                },
            };

            if (selectors.title) {
                const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
                result.title = titleMatch ? titleMatch[1].trim() : null;
            }

            if (selectors.content) {
                // Extract text content (simplified)
                const textContent = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
                result.content = textContent.substring(0, 5000); // Limit content length
            }

            if (selectors.links) {
                const linkMatches = html.match(/href="([^"]+)"/g) || [];
                result.links = linkMatches
                    .map(match => match.match(/href="([^"]+)"/)?.[1])
                    .filter(Boolean)
                    .slice(0, 20); // Limit number of links
            }

            return result;
        } catch (error) {
            throw new Error(`Web scraping failed: ${error.message}`);
        }
    }

    private async calculatorSkill(params: any, env: any): Promise<any> {
        const { expression, precision } = params;

        try {
            // Safe mathematical expression evaluation
            // In production, would use a proper math parser
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');

            if (sanitized !== expression) {
                throw new Error('Invalid characters in expression');
            }

            const result = Function(`"use strict"; return (${sanitized})`)();

            if (!Number.isFinite(result)) {
                throw new Error('Result is not a finite number');
            }

            return {
                result: Number(result.toFixed(precision)),
                expression,
                precision,
            };
        } catch (error) {
            throw new Error(`Calculation failed: ${error.message}`);
        }
    }

    private async fileProcessorSkill(params: any, env: any): Promise<any> {
        const { file_path, operation, options = {} } = params;

        // This is a placeholder implementation
        // In production, would implement proper file processing
        throw new Error('File processing skill not implemented in this environment');
    }

    // ============================================================================
    // VALIDATION AND SECURITY
    // ============================================================================

    private validateSkillDefinition(skill: SkillDefinition): void {
        if (!skill.id || !skill.name || !skill.description) {
            throw new BadRequestException('Skill must have id, name, and description');
        }

        if (!skill.parameters || !skill.returns) {
            throw new BadRequestException('Skill must have parameters and returns schemas');
        }

        if (!skill.implementation) {
            throw new BadRequestException('Skill must have an implementation');
        }
    }

    private async validateSkillAccess(agentId: string, skill: SkillDefinition): Promise<void> {
        const agent = await this.prisma.agentInstance.findUnique({
            where: { id: agentId },
            select: { skills: true, organizationId: true },
        });

        if (!agent) {
            throw new NotFoundException('Agent not found');
        }

        if (!agent.skills.includes(skill.id)) {
            throw new BadRequestException('Agent does not have access to this skill');
        }

        // Check permissions
        // Would implement proper permission checking based on user roles
    }

    private validateParameters(skill: SkillDefinition, parameters: any): any {
        try {
            return skill.parameters.parse(parameters);
        } catch (error) {
            throw new BadRequestException(`Invalid parameters: ${error.message}`);
        }
    }

    private validateResult(skill: SkillDefinition, result: any): any {
        try {
            return skill.returns.parse(result);
        } catch (error) {
            this.logger.warn(`Skill result validation failed for ${skill.id}: ${error.message}`);
            return result; // Return as-is if validation fails
        }
    }

    private async checkRateLimits(agentId: string, skillId: string, skill: SkillDefinition): Promise<void> {
        if (!skill.rateLimits) {
            return;
        }

        const now = Date.now();
        const minuteKey = `rate_limit:${agentId}:${skillId}:${Math.floor(now / 60000)}`;
        const hourKey = `rate_limit:${agentId}:${skillId}:${Math.floor(now / 3600000)}`;

        const [minuteCount, hourCount] = await Promise.all([
            this.cacheManager.get(minuteKey) as Promise<number>,
            this.cacheManager.get(hourKey) as Promise<number>,
        ]);

        if ((minuteCount || 0) >= skill.rateLimits.requestsPerMinute) {
            throw new BadRequestException('Rate limit exceeded: too many requests per minute');
        }

        if ((hourCount || 0) >= skill.rateLimits.requestsPerHour) {
            throw new BadRequestException('Rate limit exceeded: too many requests per hour');
        }

        // Increment counters
        await Promise.all([
            this.cacheManager.set(minuteKey, (minuteCount || 0) + 1, 60000),
            this.cacheManager.set(hourKey, (hourCount || 0) + 1, 3600000),
        ]);
    }

    private async trackSkillExecution(
        agentId: string,
        skillId: string,
        duration: number,
        success: boolean,
    ): Promise<void> {
        const statsKey = `skill_stats:${agentId}:${skillId}`;
        const stats = await this.cacheManager.get(statsKey) as any || {
            totalExecutions: 0,
            successfulExecutions: 0,
            totalDuration: 0,
            averageDuration: 0,
            lastExecution: null,
        };

        stats.totalExecutions++;
        if (success) stats.successfulExecutions++;
        stats.totalDuration += duration;
        stats.averageDuration = stats.totalDuration / stats.totalExecutions;
        stats.lastExecution = new Date().toISOString();

        await this.cacheManager.set(statsKey, stats, 86400000); // 24 hours
    }

    private zodSchemaToOpenAPISchema(schema: z.ZodSchema): any {
        // Convert Zod schema to OpenAPI/JSON Schema format
        // This is a simplified conversion - would use a proper library in production
        if (schema instanceof z.ZodObject) {
            const properties: any = {};
            const shape = schema.shape;
            const required: string[] = [];

            for (const [key, value] of Object.entries(shape)) {
                properties[key] = this.zodSchemaToOpenAPISchema(value as z.ZodSchema);
                if (!(value as any).isOptional()) {
                    required.push(key);
                }
            }

            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined,
            };
        } else if (schema instanceof z.ZodString) {
            return { type: 'string' };
        } else if (schema instanceof z.ZodNumber) {
            return { type: 'number' };
        } else if (schema instanceof z.ZodBoolean) {
            return { type: 'boolean' };
        } else if (schema instanceof z.ZodArray) {
            return {
                type: 'array',
                items: this.zodSchemaToOpenAPISchema((schema as any)._def.type),
            };
        } else if (schema instanceof z.ZodEnum) {
            return {
                type: 'string',
                enum: schema.options,
            };
        }

        return { type: 'any' };
    }

    private async executeSandboxedCode(code: string, parameters: any, env: any): Promise<any> {
        // This would implement sandboxed code execution in production
        // For now, throw an error as it's not safe to execute arbitrary code
        throw new Error('Sandboxed code execution not implemented');
    }

    // ============================================================================
    // PUBLIC QUERY METHODS
    // ============================================================================

    async getAvailableSkills(category?: string): Promise<SkillDefinition[]> {
        const skills = Array.from(this.skillRegistry.values());

        if (category) {
            return skills.filter(skill => skill.category === category);
        }

        return skills;
    }

    async getSkillCategories(): Promise<string[]> {
        const skills = Array.from(this.skillRegistry.values());
        const categories = new Set(skills.map(skill => skill.category));
        return Array.from(categories);
    }

    async getSkillStats(agentId: string, skillId: string): Promise<any> {
        const statsKey = `skill_stats:${agentId}:${skillId}`;
        return this.cacheManager.get(statsKey) || null;
    }
}