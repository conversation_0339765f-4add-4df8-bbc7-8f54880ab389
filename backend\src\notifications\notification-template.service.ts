import { Injectable } from '@nestjs/common';

@Injectable()
export class NotificationTemplateService {
    private templates = new Map<string, string>();

    constructor() {
        this.loadDefaultTemplates();
    }

    private loadDefaultTemplates() {
        this.templates.set('welcome', 'Welcome to SynapseAI, {{firstName}}!');
        this.templates.set('workflow_complete', 'Your workflow "{{workflowName}}" has completed successfully.');
        this.templates.set('security_alert', 'Security Alert: {{alertType}} detected.');
    }

    getTemplate(name: string): string | undefined {
        return this.templates.get(name);
    }

    renderTemplate(template: string, data: Record<string, any>): string {
        return template.replace(/{{(\w+)}}/g, (match, key) => data[key] || match);
    }
}