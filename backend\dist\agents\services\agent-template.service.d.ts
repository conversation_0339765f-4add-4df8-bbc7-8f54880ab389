import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { AgentType } from '@prisma/client';
export interface AgentTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    type: AgentType;
    systemPrompt: string;
    instructions: string;
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    skills: string[];
    tools: string[];
    capabilities: {
        canCollaborate: boolean;
        shareContext: boolean;
        maxConcurrentTasks?: number;
        memoryWindow: number;
        providerRequirements?: any;
    };
    communication: {
        enableAgentToAgent: boolean;
        allowBroadcast: boolean;
        priority: 'low' | 'normal' | 'high' | 'urgent';
    };
    fallback?: {
        enabled: boolean;
        fallbackAgentId?: string;
        maxRetries: number;
        retryDelay: number;
    };
    monitoring: {
        trackMetrics: boolean;
        logConversations: boolean;
        alertOnErrors: boolean;
    };
    isPublic: boolean;
    tags: string[];
    metadata?: any;
    usageCount: number;
    rating: number;
    createdBy: string;
    organizationId: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateTemplateDto {
    name: string;
    description: string;
    category: string;
    type: AgentType;
    systemPrompt: string;
    instructions?: string;
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: Partial<AgentTemplate['capabilities']>;
    communication?: Partial<AgentTemplate['communication']>;
    fallback?: Partial<AgentTemplate['fallback']>;
    monitoring?: Partial<AgentTemplate['monitoring']>;
    isPublic?: boolean;
    tags?: string[];
    metadata?: any;
}
export declare class AgentTemplateService {
    private prisma;
    private apixGateway;
    private readonly logger;
    constructor(prisma: PrismaService, apixGateway: ApixGateway);
    private initializeDefaultTemplates;
    private createDefaultTemplates;
    createTemplate(createDto: CreateTemplateDto, createdBy: string, organizationId: string): Promise<AgentTemplate>;
    getTemplates(organizationId: string, filters?: {
        category?: string;
        type?: AgentType;
        isPublic?: boolean;
        search?: string;
        tags?: string[];
        limit?: number;
        offset?: number;
    }): Promise<{
        templates: AgentTemplate[];
        total: number;
    }>;
    getTemplate(templateId: string, organizationId: string): Promise<AgentTemplate | null>;
    updateTemplate(templateId: string, updateDto: Partial<CreateTemplateDto>, userId: string, organizationId: string): Promise<AgentTemplate>;
    deleteTemplate(templateId: string, userId: string, organizationId: string): Promise<void>;
    incrementUsageCount(templateId: string): Promise<void>;
    rateTemplate(templateId: string, rating: number, userId: string, organizationId: string): Promise<void>;
    getCategories(organizationId: string): Promise<string[]>;
    getPopularTemplates(organizationId: string, limit?: number): Promise<AgentTemplate[]>;
    searchTemplates(query: string, organizationId: string, filters?: {
        category?: string;
        type?: AgentType;
        tags?: string[];
    }): Promise<AgentTemplate[]>;
}
