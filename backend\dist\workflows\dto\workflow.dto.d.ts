import { z } from 'zod';
export declare const CreateWorkflowSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    definition: z.ZodObject<{
        nodes: z.Zod<PERSON>rray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodEnum<{
                agent: "agent";
                tool: "tool";
                condition: "condition";
                parallel: "parallel";
                human_input: "human_input";
                delay: "delay";
            }>;
            name: z.ZodString;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
            position: z.ZodObject<{
                x: z.ZodNumber;
                y: z.ZodNumber;
            }, z.core.$strip>;
            inputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
            outputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
        }, z.core.$strip>>;
        edges: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            source: z.ZodString;
            target: z.ZodString;
            condition: z.ZodOptional<z.ZodString>;
        }, z.core.$strip>>;
        triggers: z.ZodDefault<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<{
                manual: "manual";
                scheduled: "scheduled";
                webhook: "webhook";
                event: "event";
            }>;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
        }, z.core.$strip>>>;
        settings: z.ZodDefault<z.ZodObject<{
            timeout: z.ZodOptional<z.ZodNumber>;
            retryPolicy: z.ZodOptional<z.ZodObject<{
                maxRetries: z.ZodNumber;
                backoffStrategy: z.ZodEnum<{
                    linear: "linear";
                    exponential: "exponential";
                }>;
                retryDelay: z.ZodNumber;
            }, z.core.$strip>>;
            errorHandling: z.ZodOptional<z.ZodObject<{
                onError: z.ZodEnum<{
                    stop: "stop";
                    continue: "continue";
                    retry: "retry";
                }>;
                fallbackNode: z.ZodOptional<z.ZodString>;
            }, z.core.$strip>>;
        }, z.core.$strip>>;
    }, z.core.$strip>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString>>;
}, z.core.$strip>;
export declare const UpdateWorkflowSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    definition: z.ZodOptional<z.ZodObject<{
        nodes: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodEnum<{
                agent: "agent";
                tool: "tool";
                condition: "condition";
                parallel: "parallel";
                human_input: "human_input";
                delay: "delay";
            }>;
            name: z.ZodString;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
            position: z.ZodObject<{
                x: z.ZodNumber;
                y: z.ZodNumber;
            }, z.core.$strip>;
            inputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
            outputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
        }, z.core.$strip>>;
        edges: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            source: z.ZodString;
            target: z.ZodString;
            condition: z.ZodOptional<z.ZodString>;
        }, z.core.$strip>>;
        triggers: z.ZodDefault<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<{
                manual: "manual";
                scheduled: "scheduled";
                webhook: "webhook";
                event: "event";
            }>;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
        }, z.core.$strip>>>;
        settings: z.ZodDefault<z.ZodObject<{
            timeout: z.ZodOptional<z.ZodNumber>;
            retryPolicy: z.ZodOptional<z.ZodObject<{
                maxRetries: z.ZodNumber;
                backoffStrategy: z.ZodEnum<{
                    linear: "linear";
                    exponential: "exponential";
                }>;
                retryDelay: z.ZodNumber;
            }, z.core.$strip>>;
            errorHandling: z.ZodOptional<z.ZodObject<{
                onError: z.ZodEnum<{
                    stop: "stop";
                    continue: "continue";
                    retry: "retry";
                }>;
                fallbackNode: z.ZodOptional<z.ZodString>;
            }, z.core.$strip>>;
        }, z.core.$strip>>;
    }, z.core.$strip>>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString>>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, z.core.$strip>;
export declare const ExecuteWorkflowSchema: z.ZodObject<{
    input: z.ZodDefault<z.ZodRecord<z.ZodAny, z.core.SomeType>>;
    variables: z.ZodDefault<z.ZodRecord<z.ZodAny, z.core.SomeType>>;
    options: z.ZodDefault<z.ZodObject<{
        timeout: z.ZodOptional<z.ZodNumber>;
        priority: z.ZodDefault<z.ZodEnum<{
            low: "low";
            normal: "normal";
            high: "high";
        }>>;
        async: z.ZodDefault<z.ZodBoolean>;
    }, z.core.$strip>>;
}, z.core.$strip>;
export declare const WorkflowFilterSchema: z.ZodObject<{
    isActive: z.ZodOptional<z.ZodBoolean>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString>>;
    creatorId: z.ZodOptional<z.ZodString>;
    search: z.ZodOptional<z.ZodString>;
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
}, z.core.$strip>;
export declare const ScheduleWorkflowSchema: z.ZodObject<{
    cronExpression: z.ZodString;
    timezone: z.ZodDefault<z.ZodString>;
    startDate: z.ZodOptional<z.ZodDate>;
    endDate: z.ZodOptional<z.ZodDate>;
    maxRuns: z.ZodOptional<z.ZodNumber>;
    input: z.ZodDefault<z.ZodRecord<z.ZodAny, z.core.SomeType>>;
    isActive: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
export declare const WorkflowTemplateSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    category: z.ZodString;
    definition: z.ZodObject<{
        nodes: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            type: z.ZodEnum<{
                agent: "agent";
                tool: "tool";
                condition: "condition";
                parallel: "parallel";
                human_input: "human_input";
                delay: "delay";
            }>;
            name: z.ZodString;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
            position: z.ZodObject<{
                x: z.ZodNumber;
                y: z.ZodNumber;
            }, z.core.$strip>;
            inputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
            outputs: z.ZodDefault<z.ZodArray<z.ZodString>>;
        }, z.core.$strip>>;
        edges: z.ZodArray<z.ZodObject<{
            id: z.ZodString;
            source: z.ZodString;
            target: z.ZodString;
            condition: z.ZodOptional<z.ZodString>;
        }, z.core.$strip>>;
        triggers: z.ZodDefault<z.ZodArray<z.ZodObject<{
            type: z.ZodEnum<{
                manual: "manual";
                scheduled: "scheduled";
                webhook: "webhook";
                event: "event";
            }>;
            config: z.ZodRecord<z.ZodAny, z.core.SomeType>;
        }, z.core.$strip>>>;
        settings: z.ZodDefault<z.ZodObject<{
            timeout: z.ZodOptional<z.ZodNumber>;
            retryPolicy: z.ZodOptional<z.ZodObject<{
                maxRetries: z.ZodNumber;
                backoffStrategy: z.ZodEnum<{
                    linear: "linear";
                    exponential: "exponential";
                }>;
                retryDelay: z.ZodNumber;
            }, z.core.$strip>>;
            errorHandling: z.ZodOptional<z.ZodObject<{
                onError: z.ZodEnum<{
                    stop: "stop";
                    continue: "continue";
                    retry: "retry";
                }>;
                fallbackNode: z.ZodOptional<z.ZodString>;
            }, z.core.$strip>>;
        }, z.core.$strip>>;
    }, z.core.$strip>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString>>;
    isPublic: z.ZodDefault<z.ZodBoolean>;
    variables: z.ZodDefault<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        type: z.ZodEnum<{
            string: "string";
            number: "number";
            boolean: "boolean";
            object: "object";
        }>;
        description: z.ZodOptional<z.ZodString>;
        required: z.ZodDefault<z.ZodBoolean>;
        defaultValue: z.ZodOptional<z.ZodAny>;
    }, z.core.$strip>>>;
}, z.core.$strip>;
export declare const WorkflowImportSchema: z.ZodObject<{
    format: z.ZodEnum<{
        json: "json";
        yaml: "yaml";
    }>;
    data: z.ZodString;
    overwrite: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
export declare const WorkflowExportSchema: z.ZodObject<{
    format: z.ZodDefault<z.ZodEnum<{
        json: "json";
        yaml: "yaml";
    }>>;
    includeExecutions: z.ZodDefault<z.ZodBoolean>;
    includeMetadata: z.ZodDefault<z.ZodBoolean>;
}, z.core.$strip>;
declare const CreateWorkflowDto_base: any;
export declare class CreateWorkflowDto extends CreateWorkflowDto_base {
}
declare const UpdateWorkflowDto_base: any;
export declare class UpdateWorkflowDto extends UpdateWorkflowDto_base {
}
declare const ExecuteWorkflowDto_base: any;
export declare class ExecuteWorkflowDto extends ExecuteWorkflowDto_base {
}
declare const WorkflowFilterDto_base: any;
export declare class WorkflowFilterDto extends WorkflowFilterDto_base {
}
declare const ScheduleWorkflowDto_base: any;
export declare class ScheduleWorkflowDto extends ScheduleWorkflowDto_base {
}
declare const WorkflowTemplateDto_base: any;
export declare class WorkflowTemplateDto extends WorkflowTemplateDto_base {
}
declare const WorkflowImportDto_base: any;
export declare class WorkflowImportDto extends WorkflowImportDto_base {
}
declare const WorkflowExportDto_base: any;
export declare class WorkflowExportDto extends WorkflowExportDto_base {
}
export interface WorkflowResponse {
    id: string;
    name: string;
    description?: string;
    definition: any;
    tags: string[];
    version: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    creator: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
    };
    executionCount: number;
    lastExecution?: {
        id: string;
        status: string;
        startedAt: string;
        completedAt?: string;
    };
}
export interface WorkflowExecutionResponse {
    id: string;
    workflowId: string;
    status: string;
    input: any;
    output?: any;
    error?: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    steps: Array<{
        id: string;
        stepId: string;
        name: string;
        type: string;
        status: string;
        input: any;
        output?: any;
        error?: string;
        startedAt: string;
        completedAt?: string;
        duration?: number;
    }>;
}
export interface WorkflowAnalyticsResponse {
    totalWorkflows: number;
    activeWorkflows: number;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    avgExecutionTime: number;
    executionsByStatus: Record<string, number>;
    executionsByDay: Array<{
        date: string;
        count: number;
        successCount: number;
        failureCount: number;
    }>;
    topWorkflows: Array<{
        id: string;
        name: string;
        executionCount: number;
        successRate: number;
    }>;
}
export {};
