import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class CSRFProtectionGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();

        // Skip CSRF protection for GET, HEAD, OPTIONS requests
        if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
            return true;
        }

        // Skip for API endpoints with valid JWT tokens
        if (request.headers.authorization) {
            return true;
        }

        const csrfToken = request.headers['x-csrf-token'] || request.body._csrf;
        const sessionToken = request.session?.csrfToken;

        if (!csrfToken || !sessionToken || csrfToken !== sessionToken) {
            throw new ForbiddenException('Invalid CSRF token');
        }

        return true;
    }
}