import { AuthService } from './auth.service';
import { LoginDto, RegisterDto, RefreshTokenDto, ForgotPasswordDto, ResetPasswordDto, ChangePasswordDto, EnableMFADto, VerifyMFADto, UpdateProfileDto, InviteUserDto, AcceptInviteDto, UpdateUserRoleDto, CreateAPIKeyDto, AuthResponse, MFASetupResponse, APIKeyResponse } from './dto/auth.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse>;
    login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<AuthResponse>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
        expiresAt: number;
    }>;
    logout(req: any): Promise<void>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<void>;
    getProfile(req: any): Promise<{
        id: any;
        email: any;
        firstName: any;
        lastName: any;
        role: any;
        avatar: any;
        organization: any;
        preferences: any;
        lastLoginAt: any;
        createdAt: any;
    }>;
    updateProfile(req: any, updateProfileDto: UpdateProfileDto): Promise<void>;
    setupMFA(req: any): Promise<MFASetupResponse>;
    enableMFA(req: any, enableMFADto: EnableMFADto): Promise<void>;
    disableMFA(req: any, verifyMFADto: VerifyMFADto): Promise<void>;
    getPermissions(req: any): Promise<{
        userId: any;
        role: any;
        permissions: string[];
    }>;
    checkPermission(req: any, resource: string, action: string): Promise<{
        userId: any;
        resource: string;
        action: string;
        hasPermission: boolean;
    }>;
    createAPIKey(req: any, createAPIKeyDto: CreateAPIKeyDto): Promise<APIKeyResponse>;
    getSessions(req: any): Promise<{
        userId: any;
        sessions: any[];
    }>;
    revokeSession(req: any, sessionId: string): Promise<void>;
    revokeAllSessions(req: any): Promise<void>;
    inviteUser(req: any, inviteUserDto: InviteUserDto): Promise<{
        message: string;
        inviteId: string;
    }>;
    acceptInvite(acceptInviteDto: AcceptInviteDto): Promise<{
        message: string;
    }>;
    updateUserRole(req: any, userId: string, updateUserRoleDto: UpdateUserRoleDto): Promise<void>;
    getAuditLogs(req: any, page?: string, limit?: string, action?: string, resource?: string, userId?: string): Promise<{
        logs: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
}
