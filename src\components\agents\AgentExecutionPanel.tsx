"use client";

import React, { useState, useEffect, useRef } from "react";
import { useParams } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAgent } from "@/hooks/useAgents";
import { AgentMessage, AgentInstance } from "@/lib/agent-api";
import { Bot, Send, User, RefreshCw, Zap, AlertCircle } from "lucide-react";
import agentApi from "@/lib/agent-api";

const AgentExecutionPanel = () => {
  const params = useParams();
  const { toast } = useToast();
  const agentId = params.id as string;
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [sessionId, setSessionId] = useState<string>(`session_${Date.now()}`);
  const [isExecuting, setIsExecuting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    agent,
    loading: agentLoading,
    error: agentError,
    executeAgent,
  } = useAgent(agentId);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Load session messages if sessionId is provided
  useEffect(() => {
    if (sessionId) {
      loadSessionMessages();
    }
  }, [sessionId]);

  const loadSessionMessages = async () => {
    try {
      const result = await agentApi.getSessionMessages(sessionId);
      setMessages(result as AgentMessage[]);
    } catch (error: any) {
      console.error("Failed to load session messages:", error);
      // Don't show error toast here as this might be a new session
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    try {
      setIsExecuting(true);
      setError(null);

      // Add user message to the UI immediately
      const userMessage: AgentMessage = {
        id: `temp_${Date.now()}`,
        role: "user",
        content: input,
        timestamp: new Date().toISOString(),
      };
      setMessages(prev => [...prev, userMessage]);

      // Clear input
      setInput("");

      // Execute agent
      const result = await executeAgent({
        input,
        sessionId,
        context: {
          previousMessages: messages.length,
        },
      });

      // Add agent response to the UI
      const agentMessage: AgentMessage = {
        id: `response_${Date.now()}`,
        role: "assistant",
        content: (result  as { output: string }).output,
        agentId: agentId,
        sessionId: sessionId,
        taskId: (result  as { taskId: string }).taskId,
        workflowId: (result  as { workflowId: string }).workflowId,
        toolId: (result  as { toolId: string }).toolId,
        toolName: (result  as { toolName: string }).toolName,
        toolInput: (result  as { toolInput: any }).toolInput,
        timestamp: new Date().toISOString(),
        metadata: {
          tokens: (result  as { tokens: number }).tokens,
          cost: (result as { cost: number }).cost,
          duration: (result as { duration: number }).duration,
        },
      };
      setMessages(prev => [...prev, agentMessage]);

    } catch (error: any) {
      console.error("Agent execution failed:", error);
      setError(error.message || "Failed to execute agent");
      toast({
        title: "Execution failed",
        description: error.message || "An error occurred during execution",
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startNewSession = () => {
    const newSessionId = `session_${Date.now()}`;
    setSessionId(newSessionId);
    setMessages([]);
    toast({
      title: "New session started",
      description: `Session ID: ${newSessionId}`,
    });
  };

  if (agentLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading agent...</span>
      </div>
    );
  }

  if (agentError || !agent) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-destructive mb-2">Unable to Load Agent</h3>
          <p className="text-muted-foreground mb-4">{agentError || "Agent not found"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Bot className="h-5 w-5" />
          <h2 className="text-xl font-semibold">{agent.name}</h2>
          <Badge className={
            agent.status === "ACTIVE" ? "bg-green-100 text-green-800" :
              "bg-yellow-100 text-yellow-800"
          }>
            {agent.status}
          </Badge>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={startNewSession}>
            <RefreshCw className="mr-2 h-4 w-4" />
            New Session
          </Button>
          <Badge variant="outline">Session: {sessionId.substring(0, 8)}...</Badge>
        </div>
      </div>

      {/* Messages Area */}
      <Card className="flex-1 mb-4">
        <CardContent className="p-0 h-[500px] flex flex-col">
          <ScrollArea className="flex-1 p-4">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <Bot className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">Start a conversation with {agent.name}</h3>
                <p className="text-muted-foreground mt-1 max-w-md">
                  This agent is a {agent.type.replace('_', ' ').toLowerCase()} agent using {agent.provider} ({agent.model}).
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={message.id || index}
                    className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${message.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : message.role === "assistant"
                          ? "bg-muted"
                          : "bg-secondary text-secondary-foreground"
                        }`}
                    >
                      <div className="flex items-center space-x-2 mb-1">
                        {message.role === "user" ? (
                          <User className="h-4 w-4" />
                        ) : message.role === "assistant" ? (
                          <Bot className="h-4 w-4" />
                        ) : (
                          <Zap className="h-4 w-4" />
                        )}
                        <span className="text-xs font-medium">
                          {message.role === "user"
                            ? "You"
                            : message.role === "assistant"
                              ? agent.name
                              : "System"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="whitespace-pre-wrap">{message.content}</div>
                      {message.metadata && (
                        <div className="mt-1 text-xs text-muted-foreground">
                          {message.metadata.tokens && `${message.metadata.tokens} tokens • `}
                          {message.metadata.duration && `${message.metadata.duration}ms`}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center text-red-800">
          <AlertCircle className="h-4 w-4 mr-2" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* Input Area */}
      <div className="flex space-x-2">
        <div className="flex-1 relative">
          <Textarea
            placeholder={`Message ${agent.name}...`}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            className="min-h-[80px] resize-none pr-12"
            disabled={isExecuting || agent.status !== "ACTIVE"}
          />
          <Button
            size="icon"
            className="absolute bottom-2 right-2"
            onClick={handleSendMessage}
            disabled={!input.trim() || isExecuting || agent.status !== "ACTIVE"}
          >
            {isExecuting ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AgentExecutionPanel;