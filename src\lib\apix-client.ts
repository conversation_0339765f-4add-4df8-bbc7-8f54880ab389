
import pako from "pako";
import { io, Socket } from "socket.io-client";

// Production-grade event types
export type APXEventType =
  | "tool_call_start"
  | "tool_call_result"
  | "tool_call_error"
  | "thinking_status"
  | "text_chunk"
  | "state_update"
  | "request_user_input"
  | "session_start"
  | "session_end"
  | "error_occurred"
  | "fallback_triggered"
  | "workflow_started"
  | "workflow_completed"
  | "workflow_failed"
  | "agent_status"
  | "agent_thinking"
  | "agent_response"
  | "tool_executed"
  | "provider_health"
  | "provider_fallback"
  | "system_alert"
  | "system_maintenance"
  | "security.event"
  | "admin.event"
  | "admin_user_created"
  | "admin_user_updated"
  | "organization.event"
  | "system.alert"
  | "user.activity"
  | "organization.updated"
  | "organization.created";

export type APXChannel =
  | "agent-events"
  | "tool-events"
  | "workflow-events"
  | "provider-events"
  | "system-events"
  | "session-events";

export interface APXEvent {
  type: APXEventType;
  channel?: APXChannel;
  data: any;
  timestamp: string;
  sessionId?: string;
  userId?: string;
  organizationId?: string;
  compressed?: boolean;
  retryCount?: number;
  latency?: number;
  priority?: "low" | "normal" | "high";
  metadata?: Record<string, any>;
}

export interface APXEventHandler {
  (event: APXEvent): void;
}

export interface APXConfig {
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  compressionThreshold?: number;
  enableLatencyTracking?: boolean;
  enableEventReplay?: boolean;
  queueMaxSize?: number;
  enableCompression?: boolean;
  heartbeatInterval?: number;
  connectionTimeout?: number;
  enableRetryLogic?: boolean;
  maxEventHistory?: number;
}

export interface APXConnectionStatus {
  connected: boolean;
  reconnectAttempts: number;
  queuedMessages: number;
  latencyStats: APXLatencyStats | null;
  eventHistory: number;
  lastHeartbeat?: number;
  serverFeatures?: {
    compression: boolean;
    latencyTracking: boolean;
    eventReplay: boolean;
    maxRetryAttempts: number;
  };
}

export interface APXLatencyStats {
  avg: number;
  min: number;
  max: number;
  count: number;
  p95?: number;
  p99?: number;
}

export interface APXEventReplay {
  events: APXEvent[];
  totalCount: number;
  timestamp: number;
}

export class APXClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private compressionThreshold: number;
  private enableLatencyTracking: boolean;
  private enableEventReplay: boolean;
  private queueMaxSize: number;
  private enableCompression: boolean;
  private heartbeatInterval: number;
  private connectionTimeout: number;
  private enableRetryLogic: boolean;
  private maxEventHistory: number;

  private eventHandlers: Map<APXEventType | "*", Set<APXEventHandler>> =
    new Map();
  private messageQueue: APXEvent[] = [];
  private isConnected = false;
  private token: string | null = null;
  private organizationId: string | null = null;
  private latencyMetrics: Map<string, number[]> = new Map();
  private eventHistory: APXEvent[] = [];
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = 0;
  private serverFeatures: any = null;
  private connectionStartTime: number = 0;
  private totalEventsReceived: number = 0;
  private totalEventsSent: number = 0;

  constructor(
    private url: string = process.env.NEXT_PUBLIC_WS_URL ||
      "ws://localhost:3001",
    private options: any = {},
    config: APXConfig = {},
  ) {
    this.maxReconnectAttempts = config.maxReconnectAttempts || 10;
    this.reconnectDelay = config.reconnectDelay || 1000;
    this.compressionThreshold = config.compressionThreshold || 1024; // 1KB
    this.enableLatencyTracking = config.enableLatencyTracking ?? true;
    this.enableEventReplay = config.enableEventReplay ?? true;
    this.queueMaxSize = config.queueMaxSize || 1000;
    this.enableCompression = config.enableCompression ?? true;
    this.heartbeatInterval = config.heartbeatInterval || 30000; // 30 seconds
    this.connectionTimeout = config.connectionTimeout || 10000; // 10 seconds
    this.enableRetryLogic = config.enableRetryLogic ?? true;
    this.maxEventHistory = config.maxEventHistory || 1000;
  }

  connect(token: string, organizationId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.token = token;
      this.organizationId = organizationId;
      this.connectionStartTime = Date.now();

      this.socket = io(`${this.url}/apix`, {
        auth: {
          token,
          organizationId,
        },
        transports: ["websocket", "polling"],
        upgrade: true,
        rememberUpgrade: true,
        compression: this.enableCompression,
        timeout: this.connectionTimeout,
        forceNew: true,
        ...this.options,
      });

      this.socket.on("connect", () => {
        // APIX: Connected to server with production features
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.lastHeartbeat = Date.now();

        // Start heartbeat
        this.startHeartbeat();

        // Process queued messages
        this.processMessageQueue();

        // Request event replay if enabled
        if (this.enableEventReplay) {
          this.requestEventReplay();
        }

        resolve();
      });

      this.socket.on("apix:connected", (data) => {
        this.serverFeatures = data.features;
        // APIX: Server features loaded

      });

      this.socket.on("disconnect", (reason) => {
        // APIX: Disconnected from server
        this.isConnected = false;
        this.stopHeartbeat();

        if (reason === "io server disconnect") {
          return;
        }

        if (this.enableRetryLogic) {
          this.handleReconnect();
        }
      });

      this.socket.on("connect_error", (error) => {
        console.error("APIX: Connection error:", error);
        this.isConnected = false;

        if (this.reconnectAttempts === 0) {
          reject(error);
        }

        if (this.enableRetryLogic) {
          this.handleReconnect();
        }
      });

      this.socket.on("apix:error", (error) => {
        console.error("APIX: Server error:", error);
        this.handleIncomingEvent({
          type: "error_occurred",
          data: error,
          timestamp: new Date().toISOString(),
        });
      });

      // Handle all APIX event types
      this.setupEventHandlers();

      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error("Connection timeout"));
        }
      }, this.connectionTimeout);
    });
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Generic event handler
    this.socket.on("apix:event", (event: APXEvent) => {
      this.totalEventsReceived++;
      this.handleIncomingEvent(event);
    });

    // Production-grade specific event type handlers
    const eventTypes: APXEventType[] = [
      "tool_call_start",
      "tool_call_result",
      "tool_call_error",
      "thinking_status",
      "text_chunk",
      "state_update",
      "request_user_input",
      "session_start",
      "session_end",
      "error_occurred",
      "fallback_triggered",
      "workflow_started",
      "workflow_completed",
      "workflow_failed",
      "agent_status",
      "agent_thinking",
      "agent_response",
      "tool_executed",
      "provider_health",
      "provider_fallback",
      "system_alert",
      "security.event",
      "system_maintenance",
      "user.activity",
      "admin.event",
      "organization.event",
      "organization.updated",
      "organization.created",
      "user.activity",
      "security.event",
      "admin.event",
      "organization.event",
      "system.alert",
      "user.activity",
      "security.event",
      "admin_user_created",
      "admin_user_updated",
    ];

    eventTypes.forEach((eventType) => {
      this.socket!.on(eventType, (data) => {
        this.totalEventsReceived++;
        const event: APXEvent = {
          type: eventType,
          data,
          timestamp: new Date().toISOString(),
          latency: this.enableLatencyTracking
            ? this.calculateLatency(data.sentAt)
            : undefined,
          organizationId: this.organizationId || undefined,
        };
        this.handleIncomingEvent(event);
      });
    });

    // Enhanced event replay handler
    this.socket.on("apix:replay", (replayData: APXEventReplay) => {
      // APIX: Received replay events
      replayData.events.forEach((event) => {
        this.totalEventsReceived++;
        this.handleIncomingEvent(event);
      });
    });

    // Heartbeat handler
    this.socket.on("apix:heartbeat", () => {
      this.lastHeartbeat = Date.now();
    });
  }

  private handleIncomingEvent(event: APXEvent): void {
    // Decompress if needed
    if (event.compressed && event.data) {
      try {
        const decompressed = pako.inflate(event.data, { to: "string" });
        event.data = JSON.parse(decompressed);
        event.compressed = false;
      } catch (error) {
        console.error("APIX: Decompression failed:", error);
        return;
      }
    }

    // Track latency
    if (this.enableLatencyTracking && event.latency) {
      this.trackLatency(event.type, event.latency);
    }

    // Store in history
    if (this.enableEventReplay) {
      this.eventHistory.push(event);
      if (this.eventHistory.length > 1000) {
        this.eventHistory.shift();
      }
    }

    // Trigger handlers
    const handlers = this.eventHandlers.get(event.type);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(event);
        } catch (error) {
          console.error("APIX: Error in event handler:", error);
        }
      });
    }

    // Trigger wildcard handlers
    const wildcardHandlers = this.eventHandlers.get("*");
    if (wildcardHandlers) {
      wildcardHandlers.forEach((handler) => {
        try {
          handler(event);
        } catch (error) {
          console.error("APIX: Error in wildcard event handler:", error);
        }
      });
    }
  }

  private calculateLatency(sentAt?: number): number {
    if (!sentAt) return 0;
    return Date.now() - sentAt;
  }

  private trackLatency(eventType: string, latency: number): void {
    if (!this.latencyMetrics.has(eventType)) {
      this.latencyMetrics.set(eventType, []);
    }

    const metrics = this.latencyMetrics.get(eventType)!;
    metrics.push(latency);

    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  private requestEventReplay(): void {
    if (this.socket && this.isConnected) {
      this.socket.emit("apix:request_replay", {
        since:
          this.eventHistory.length > 0
            ? this.eventHistory[this.eventHistory.length - 1].timestamp
            : new Date(Date.now() - 300000).toISOString(), // Last 5 minutes
      });
    }
  }

  // Production-grade emit with compression, retry, and priority
  emit(
    eventType: APXEventType,
    data: any,
    options: {
      compress?: boolean;
      retry?: boolean;
      maxRetries?: number;
      priority?: "low" | "normal" | "high";
      channel?: APXChannel;
      metadata?: Record<string, any>;
    } = {},
  ): void {
    let processedData = data;
    let compressed = false;

    // Auto-compress large payloads if compression is enabled
    const shouldCompress =
      this.enableCompression &&
      (options.compress ||
        (typeof data === "object" &&
          JSON.stringify(data).length > this.compressionThreshold) ||
        (typeof data === "string" && data.length > this.compressionThreshold));

    if (shouldCompress) {
      try {
        const jsonString =
          typeof data === "string" ? data : JSON.stringify(data);
        const compressedData = pako.deflate(jsonString);
        processedData = Array.from(compressedData); // Convert to array for JSON serialization
        compressed = true;
      } catch (error) {
        console.warn("APIX: Compression failed, sending uncompressed:", error);
      }
    }

    const event: APXEvent = {
      type: eventType,
      channel: options.channel,
      data: processedData,
      timestamp: new Date().toISOString(),
      organizationId: this.organizationId || undefined,
      compressed,
      retryCount: 0,
      priority: options.priority || "normal",
      metadata: {
        clientTimestamp: Date.now(),
        ...options.metadata,
      },
    };

    this.sendEvent(event, options);
    this.totalEventsSent++;
  }

  private sendEvent(event: APXEvent, options: any): void {
    if (this.isConnected && this.socket) {
      // Add sent timestamp for latency tracking
      if (this.enableLatencyTracking) {
        event.data = { ...event.data, sentAt: Date.now() };
      }

      this.socket.emit("apix:event", event);
    } else {
      // Queue with priority
      if (options.priority === "high") {
        this.messageQueue.unshift(event);
      } else {
        this.messageQueue.push(event);
      }

      // Limit queue size
      if (this.messageQueue.length > this.queueMaxSize) {
        this.messageQueue.shift();
      }

      // Retry connection if enabled
      if (options.retry && !this.isConnected) {
        this.handleReconnect();
      }
    }
  }

  // Get latency statistics
  getLatencyStats(eventType?: string): {
    avg: number;
    min: number;
    max: number;
    count: number;
  } | null {
    const metrics = eventType
      ? this.latencyMetrics.get(eventType)
      : Array.from(this.latencyMetrics.values()).flat();

    if (!metrics || metrics.length === 0) return null;

    return {
      avg: metrics.reduce((a, b) => a + b, 0) / metrics.length,
      min: Math.min(...metrics),
      max: Math.max(...metrics),
      count: metrics.length,
    };
  }

  // Get event history
  getEventHistory(eventType?: string, limit?: number): APXEvent[] {
    let history = eventType
      ? this.eventHistory.filter((e) => e.type === eventType)
      : this.eventHistory;

    if (limit) {
      history = history.slice(-limit);
    }

    return history;
  }

  // Production-grade connection status with comprehensive metrics
  getConnectionStatus(): APXConnectionStatus {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      latencyStats: this.getLatencyStats(),
      eventHistory: this.eventHistory.length,
      lastHeartbeat: this.lastHeartbeat,
      serverFeatures: this.serverFeatures,
    };
  }

  // Get comprehensive connection metrics
  getConnectionMetrics(): {
    uptime: number;
    totalEventsReceived: number;
    totalEventsSent: number;
    averageLatency: number;
    connectionQuality: "excellent" | "good" | "poor" | "disconnected";
    features: any;
    eventThroughput: number;
    compressionRatio: number;
    reconnectAttempts: number;
    queuedMessages: number;
    lastHeartbeat: number;
  } {
    const uptime = this.isConnected ? Date.now() - this.connectionStartTime : 0;
    const avgLatency = this.getLatencyStats()?.avg || 0;
    const eventThroughput =
      uptime > 0
        ? (this.totalEventsReceived + this.totalEventsSent) / (uptime / 1000)
        : 0;

    let quality: "excellent" | "good" | "poor" | "disconnected" =
      "disconnected";
    if (this.isConnected) {
      if (avgLatency < 100) quality = "excellent";
      else if (avgLatency < 300) quality = "good";
      else quality = "poor";
    }

    // Calculate compression ratio from event history
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    let compressedEvents = 0;

    for (const event of this.eventHistory) {
      if (event.metadata?.originalSize && event.metadata?.compressedSize) {
        totalOriginalSize += event.metadata.originalSize;
        totalCompressedSize += event.metadata.compressedSize;
        compressedEvents++;
      }
    }

    const compressionRatio =
      compressedEvents > 0 && totalOriginalSize > 0
        ? (1 - totalCompressedSize / totalOriginalSize) * 100
        : 0;

    return {
      uptime,
      totalEventsReceived: this.totalEventsReceived,
      totalEventsSent: this.totalEventsSent,
      averageLatency: avgLatency,
      connectionQuality: quality,
      features: this.serverFeatures,
      eventThroughput: Math.round(eventThroughput * 100) / 100,
      compressionRatio: Math.round(compressionRatio * 100) / 100,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      lastHeartbeat: this.lastHeartbeat,
    };
  }

  // ===== PRODUCTION-GRADE EVENT EMISSION METHODS =====

  // Tool Events
  emitToolCallStart(
    toolId: string,
    input: any,
    sessionId?: string,
    metadata?: any,
  ): void {
    this.emit(
      "tool_call_start",
      {
        toolId,
        input,
        sessionId,
        startTime: Date.now(),
      },
      {
        priority: "high",
        channel: "tool-events",
        metadata: {
          inputSize: JSON.stringify(input).length,
          ...metadata,
        },
      },
    );
  }

  emitToolCallResult(
    toolId: string,
    result: any,
    sessionId?: string,
    metadata?: any,
  ): void {
    this.emit(
      "tool_call_result",
      {
        toolId,
        result,
        sessionId,
        endTime: Date.now(),
      },
      {
        priority: "high",
        channel: "tool-events",
        compress: true, // Results can be large
        metadata: {
          resultSize: JSON.stringify(result).length,
          success: true,
          ...metadata,
        },
      },
    );
  }

  emitToolCallError(
    toolId: string,
    error: any,
    sessionId?: string,
    metadata?: any,
  ): void {
    this.emit(
      "tool_call_error",
      {
        toolId,
        error: {
          message: error.message || error,
          stack: error.stack,
          code: error.code,
          type: error.constructor?.name || "Error",
        },
        sessionId,
        endTime: Date.now(),
      },
      {
        priority: "high",
        channel: "tool-events",
        metadata: {
          success: false,
          retryable: this.isRetryableError(error),
          ...metadata,
        },
      },
    );
  }

  // Agent Events
  emitThinkingStatus(
    agentId: string,
    status: string,
    progress?: number,
    context?: any,
  ): void {
    this.emit(
      "thinking_status",
      {
        agentId,
        status,
        progress: Math.max(0, Math.min(100, progress || 0)),
        context,
      },
      {
        channel: "agent-events",
        metadata: {
          statusTimestamp: Date.now(),
          estimatedCompletion: progress
            ? this.estimateCompletion(progress)
            : null,
        },
      },
    );
  }

  // Text Streaming Events
  emitTextChunk(
    sessionId: string,
    chunk: string,
    isComplete?: boolean,
    metadata?: any,
  ): void {
    this.emit(
      "text_chunk",
      {
        sessionId,
        chunk,
        isComplete: isComplete || false,
      },
      {
        compress: chunk.length > this.compressionThreshold,
        channel: "session-events",
        metadata: {
          chunkSize: chunk.length,
          chunkIndex: metadata?.chunkIndex || 0,
          totalChunks: metadata?.totalChunks,
          encoding: "utf-8",
          ...metadata,
        },
      },
    );
  }

  // State Management Events
  emitStateUpdate(
    entityId: string,
    entityType: string,
    state: any,
    previousState?: any,
  ): void {
    this.emit(
      "state_update",
      {
        entityId,
        entityType,
        state,
        previousState,
      },
      {
        compress: true, // State can be large
        metadata: {
          stateSize: JSON.stringify(state).length,
          changeType: previousState ? "update" : "create",
          timestamp: Date.now(),
        },
      },
    );
  }

  // User Interaction Events
  emitUserInputRequest(
    sessionId: string,
    prompt: string,
    inputType: string,
    options?: any,
  ): void {
    this.emit(
      "request_user_input",
      {
        sessionId,
        prompt,
        inputType,
        options: {
          timeout: 300000, // 5 minutes default
          required: true,
          ...options,
        },
      },
      {
        priority: "high",
        channel: "session-events",
        metadata: {
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          expiresAt: Date.now() + (options?.timeout || 300000),
        },
      },
    );
  }

  // Session Events
  emitSessionStart(sessionId: string, metadata: any): void {
    this.emit(
      "session_start",
      {
        sessionId,
        metadata: {
          startTime: Date.now(),
          clientCapabilities: {
            compression: this.enableCompression,
            latencyTracking: this.enableLatencyTracking,
            eventReplay: this.enableEventReplay,
          },
          ...metadata,
        },
      },
      {
        channel: "session-events",
      },
    );
  }

  emitSessionEnd(sessionId: string, summary: any): void {
    this.emit(
      "session_end",
      {
        sessionId,
        summary: {
          endTime: Date.now(),
          duration: Date.now() - this.connectionStartTime,
          eventsReceived: this.totalEventsReceived,
          eventsSent: this.totalEventsSent,
          ...summary,
        },
      },
      {
        channel: "session-events",
      },
    );
  }

  // Error Events
  emitError(error: any, context?: any): void {
    this.emit(
      "error_occurred",
      {
        error: {
          message: error.message || error,
          stack: error.stack,
          code: error.code,
          type: error.constructor?.name || "Error",
        },
        context,
      },
      {
        priority: "high",
        channel: "system-events",
        metadata: {
          errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          clientInfo: {
            userAgent: navigator.userAgent,
            url: window.location.href,
          },
        },
      },
    );
  }

  // Provider Events
  emitFallbackTriggered(
    originalProvider: string,
    fallbackProvider: string,
    reason: string,
    metadata?: any,
  ): void {
    this.emit(
      "fallback_triggered",
      {
        originalProvider,
        fallbackProvider,
        reason,
      },
      {
        priority: "high",
        channel: "provider-events",
        metadata: {
          fallbackTime: Date.now(),
          ...metadata,
        },
      },
    );
  }

  disconnect(): void {
    // APIX: Disconnecting client

    this.stopHeartbeat();

    if (this.socket) {
      // Emit session end before disconnecting
      if (this.isConnected) {
        this.emitSessionEnd("client_disconnect", {
          reason: "client_initiated",
          metrics: this.getConnectionMetrics(),
        });
      }

      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.eventHandlers.clear();
    this.messageQueue = [];
    this.latencyMetrics.clear();
    this.eventHistory = [];
    this.serverFeatures = null;
    this.totalEventsReceived = 0;
    this.totalEventsSent = 0;
    this.lastHeartbeat = 0;

    // APIX: Client disconnected and cleaned up
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error("APIX: Max reconnection attempts reached");
      this.emit(
        "error_occurred",
        {
          error: "Max reconnection attempts reached",
          reconnectAttempts: this.reconnectAttempts,
        },
        { priority: "high" },
      );
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    // APIX: Connection error
    console.error(
      `APIX: Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`
    );

    setTimeout(() => {
      if (this.token && this.organizationId) {
        this.connect(this.token, this.organizationId).catch((error) => {
          console.error("APIX: Reconnection failed:", error);
        });
      }
    }, delay);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const event = this.messageQueue.shift();
      if (event) {
        this.sendEvent(event, {});
      }
    }
  }

  // ===== PRODUCTION-GRADE HELPER METHODS =====

  private startHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit("apix:heartbeat", { timestamp: Date.now() });
      }
    }, this.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private isRetryableError(error: any): boolean {
    const retryableCodes = [
      "ECONNRESET",
      "ETIMEDOUT",
      "ENOTFOUND",
      "EAI_AGAIN",
    ];
    const retryableMessages = ["timeout", "connection", "network", "temporary"];

    return (
      retryableCodes.includes(error.code) ||
      retryableMessages.some((msg) =>
        error.message?.toLowerCase().includes(msg),
      )
    );
  }

  private estimateCompletion(progress: number): number | null {
    if (progress <= 0) return null;
    const now = Date.now();
    const estimatedTotal = (now * 100) / progress;
    return Math.round(estimatedTotal - now);
  }

  // Enhanced event handler management
  on(eventType: APXEventType | "*", handler: APXEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }

    this.eventHandlers.get(eventType)!.add(handler);

    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType);
        }
      }
    };
  }

  off(eventType: APXEventType | "*", handler?: APXEventHandler): void {
    if (!handler) {
      this.eventHandlers.delete(eventType);
      return;
    }

    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
      }
    }
  }

  joinRoom(roomId: string): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("join:room", roomId);
    }
  }

  leaveRoom(roomId: string): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("leave:room", roomId);
    }
  }

  subscribeToWorkflow(workflowId: string): void {
    this.joinRoom(`workflow:${workflowId}`);
  }

  unsubscribeFromWorkflow(workflowId: string): void {
    this.leaveRoom(`workflow:${workflowId}`);
  }

  subscribeToAgent(agentId: string): void {
    this.joinRoom(`agent:${agentId}`);
  }

  unsubscribeFromAgent(agentId: string): void {
    this.leaveRoom(`agent:${agentId}`);
  }

  subscribeToOrganization(): void {
    if (this.organizationId) {
      this.joinRoom(`organization:${this.organizationId}`);
    }
  }

  subscribeToSystem(): void {
    this.joinRoom("system");
  }

  // Advanced subscription methods with filters
  subscribeToEventType(
    eventType: APXEventType,
    filters?: Record<string, any>,
  ): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("apix:subscribe", {
        channels: [`event:${eventType}`],
        filters,
      });
    }
  }

  unsubscribeFromEventType(eventType: APXEventType): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("apix:unsubscribe", {
        channels: [`event:${eventType}`],
      });
    }
  }

  // Bulk subscription management
  subscribeToMultipleChannels(
    channels: string[],
    filters?: Record<string, any>,
  ): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("apix:subscribe", {
        channels,
        filters,
      });
    }
  }

  unsubscribeFromMultipleChannels(channels: string[]): void {
    if (this.isConnected && this.socket) {
      this.socket.emit("apix:unsubscribe", {
        channels,
      });
    }
  }

  // Get active subscriptions
  getActiveSubscriptions(): string[] {
    // This would be populated by server responses to subscription requests
    return Array.from(this.eventHandlers.keys()).filter((key) => key !== "*");
  }

  // Performance monitoring
  getPerformanceMetrics(): {
    memoryUsage: number;
    eventHistorySize: number;
    latencyMetricsSize: number;
    averageEventSize: number;
    peakLatency: number;
    eventProcessingRate: number;
  } {
    const eventSizes = this.eventHistory.map((event) => {
      try {
        return JSON.stringify(event).length;
      } catch {
        return 0;
      }
    });

    const allLatencies = Array.from(this.latencyMetrics.values()).flat();
    const peakLatency = allLatencies.length > 0 ? Math.max(...allLatencies) : 0;

    const uptime = this.isConnected ? Date.now() - this.connectionStartTime : 1;
    const eventProcessingRate = this.totalEventsReceived / (uptime / 1000) || 0;

    return {
      memoryUsage: this.eventHistory.length + this.messageQueue.length,
      eventHistorySize: this.eventHistory.length,
      latencyMetricsSize: allLatencies.length,
      averageEventSize:
        eventSizes.length > 0
          ? eventSizes.reduce((sum, size) => sum + size, 0) / eventSizes.length
          : 0,
      peakLatency,
      eventProcessingRate: Math.round(eventProcessingRate * 100) / 100,
    };
  }

  // Health check
  async performHealthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    checks: {
      connection: boolean;
      latency: boolean;
      eventProcessing: boolean;
      memoryUsage: boolean;
    };
    details: Record<string, any>;
  }> {
    const metrics = this.getConnectionMetrics();
    const performance = this.getPerformanceMetrics();

    const checks = {
      connection: this.isConnected && Date.now() - this.lastHeartbeat < 60000,
      latency: metrics.averageLatency < 1000,
      eventProcessing:
        performance.eventProcessingRate > 0 || this.totalEventsReceived === 0,
      memoryUsage: performance.memoryUsage < 10000,
    };

    const healthyChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;

    let status: "healthy" | "degraded" | "unhealthy";
    if (healthyChecks === totalChecks) {
      status = "healthy";
    } else if (healthyChecks >= totalChecks * 0.5) {
      status = "degraded";
    } else {
      status = "unhealthy";
    }

    return {
      status,
      checks,
      details: {
        ...metrics,
        ...performance,
        timestamp: Date.now(),
      },
    };
  }
}

// ===== PRODUCTION-GRADE HELPER METHODS =====


// Create singleton instance with production config
const apixClient = new APXClient(
  process.env.NEXT_PUBLIC_WS_URL || "ws://localhost:3001",
  {},
  {
    maxReconnectAttempts: 10,
    reconnectDelay: 1000,
    compressionThreshold: 1024,
    enableLatencyTracking: true,
    enableEventReplay: true,
    queueMaxSize: 1000,
  },
);

export default apixClient;
