import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ToolExecutionStatus } from '@prisma/client';

@Injectable()
export class ToolAnalyticsService {
  constructor(private prisma: PrismaService) {}

  // ============================================================================
  // REAL-TIME ANALYTICS COLLECTION
  // ============================================================================

  async recordExecution(executionData: {
    toolId: string;
    organizationId: string;
    status: ToolExecutionStatus;
    duration: number;
    tokensUsed?: number;
    cost?: number;
    memoryUsed?: number;
    cpuTime?: number;
    errorType?: string;
    errorMessage?: string;
    cached?: boolean;
  }) {
    const now = new Date();
    const date = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const hour = now.getHours();

    // Update or create analytics record
    await this.prisma.toolAnalytics.upsert({
      where: {
        toolId_date_hour: {
          toolId: executionData.toolId,
          date,
          hour,
        },
      },
      update: {
        executionCount: { increment: 1 },
        totalDuration: { increment: executionData.duration },
        avgDuration: {
          // Recalculate average duration
          set: await this.calculateNewAverage(
            executionData.toolId,
            date,
            hour,
            'avgDuration',
            executionData.duration
          ),
        },
        ...(executionData.status === ToolExecutionStatus.COMPLETED && {
          successCount: { increment: 1 },
        }),
        ...(executionData.status === ToolExecutionStatus.FAILED && {
          errorCount: { increment: 1 },
        }),
        ...(executionData.status === ToolExecutionStatus.TIMEOUT && {
          timeoutCount: { increment: 1 },
        }),
        ...(executionData.tokensUsed && {
          totalTokens: { increment: executionData.tokensUsed },
        }),
        ...(executionData.cost && {
          totalCost: { increment: executionData.cost },
        }),
        ...(executionData.cached && {
          cacheHits: { increment: 1 },
        }),
        ...(!executionData.cached && {
          cacheMisses: { increment: 1 },
        }),
        // Update min/max duration
        minDuration: {
          set: await this.calculateMinDuration(executionData.toolId, date, hour, executionData.duration),
        },
        maxDuration: {
          set: await this.calculateMaxDuration(executionData.toolId, date, hour, executionData.duration),
        },
        // Update error breakdown
        ...(executionData.errorType && {
          errorTypes: await this.updateErrorTypes(
            executionData.toolId,
            date,
            hour,
            executionData.errorType
          ),
        }),
      },
      create: {
        toolId: executionData.toolId,
        date,
        hour,
        executionCount: 1,
        totalDuration: executionData.duration,
        avgDuration: executionData.duration,
        successCount: executionData.status === ToolExecutionStatus.COMPLETED ? 1 : 0,
        errorCount: executionData.status === ToolExecutionStatus.FAILED ? 1 : 0,
        timeoutCount: executionData.status === ToolExecutionStatus.TIMEOUT ? 1 : 0,
        totalTokens: executionData.tokensUsed || 0,
        totalCost: executionData.cost || 0,
        cacheHits: executionData.cached ? 1 : 0,
        cacheMisses: executionData.cached ? 0 : 1,
        minDuration: executionData.duration,
        maxDuration: executionData.duration,
        errorTypes: executionData.errorType ? { [executionData.errorType]: 1 } : {},
        errorMessages: executionData.errorMessage ? { [executionData.errorMessage]: 1 } : {},
      },
    });

    // Update cache hit rate
    await this.updateCacheHitRate(executionData.toolId, date, hour);
  }

  // ============================================================================
  // ANALYTICS QUERIES
  // ============================================================================

  async getToolAnalytics(
    toolId: string,
    timeRange: { start: Date; end: Date },
    granularity: 'hour' | 'day' = 'day'
  ) {
    const analytics = await this.prisma.toolAnalytics.findMany({
      where: {
        toolId,
        date: {
          gte: timeRange.start,
          lte: timeRange.end,
        },
      },
      orderBy: [
        { date: 'asc' },
        { hour: 'asc' },
      ],
    });

    if (granularity === 'day') {
      // Aggregate by day
      const dailyAnalytics = new Map();
      
      analytics.forEach(record => {
        const dateKey = record.date.toISOString().split('T')[0];
        
        if (!dailyAnalytics.has(dateKey)) {
          dailyAnalytics.set(dateKey, {
            date: dateKey,
            executionCount: 0,
            successCount: 0,
            errorCount: 0,
            timeoutCount: 0,
            totalDuration: 0,
            avgDuration: 0,
            totalTokens: 0,
            totalCost: 0,
            cacheHits: 0,
            cacheMisses: 0,
            cacheHitRate: 0,
            minDuration: null,
            maxDuration: null,
          });
        }

        const dayData = dailyAnalytics.get(dateKey);
        dayData.executionCount += record.executionCount;
        dayData.successCount += record.successCount;
        dayData.errorCount += record.errorCount;
        dayData.timeoutCount += record.timeoutCount;
        dayData.totalDuration += record.totalDuration;
        dayData.totalTokens += record.totalTokens;
        dayData.totalCost += record.totalCost;
        dayData.cacheHits += record.cacheHits;
        dayData.cacheMisses += record.cacheMisses;
        
        if (dayData.minDuration === null || (record.minDuration && record.minDuration < dayData.minDuration)) {
          dayData.minDuration = record.minDuration;
        }
        if (dayData.maxDuration === null || (record.maxDuration && record.maxDuration > dayData.maxDuration)) {
          dayData.maxDuration = record.maxDuration;
        }
      });

      // Calculate averages and rates
      dailyAnalytics.forEach(dayData => {
        dayData.avgDuration = dayData.executionCount > 0 ? dayData.totalDuration / dayData.executionCount : 0;
        dayData.cacheHitRate = (dayData.cacheHits + dayData.cacheMisses) > 0 
          ? (dayData.cacheHits / (dayData.cacheHits + dayData.cacheMisses)) * 100 
          : 0;
      });

      return Array.from(dailyAnalytics.values());
    }

    return analytics;
  }

  async getToolPerformanceMetrics(toolId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await this.prisma.toolAnalytics.findMany({
      where: {
        toolId,
        date: { gte: startDate },
      },
    });

    const totals = analytics.reduce(
      (acc, record) => ({
        executions: acc.executions + record.executionCount,
        successes: acc.successes + record.successCount,
        errors: acc.errors + record.errorCount,
        timeouts: acc.timeouts + record.timeoutCount,
        totalDuration: acc.totalDuration + record.totalDuration,
        totalTokens: acc.totalTokens + record.totalTokens,
        totalCost: acc.totalCost + record.totalCost,
        cacheHits: acc.cacheHits + record.cacheHits,
        cacheMisses: acc.cacheMisses + record.cacheMisses,
      }),
      {
        executions: 0,
        successes: 0,
        errors: 0,
        timeouts: 0,
        totalDuration: 0,
        totalTokens: 0,
        totalCost: 0,
        cacheHits: 0,
        cacheMisses: 0,
      }
    );

    return {
      totalExecutions: totals.executions,
      successRate: totals.executions > 0 ? (totals.successes / totals.executions) * 100 : 0,
      errorRate: totals.executions > 0 ? (totals.errors / totals.executions) * 100 : 0,
      timeoutRate: totals.executions > 0 ? (totals.timeouts / totals.executions) * 100 : 0,
      avgDuration: totals.executions > 0 ? totals.totalDuration / totals.executions : 0,
      totalTokensUsed: totals.totalTokens,
      totalCost: totals.totalCost,
      avgCostPerExecution: totals.executions > 0 ? totals.totalCost / totals.executions : 0,
      cacheHitRate: (totals.cacheHits + totals.cacheMisses) > 0 
        ? (totals.cacheHits / (totals.cacheHits + totals.cacheMisses)) * 100 
        : 0,
      period: {
        start: startDate,
        end: new Date(),
        days,
      },
    };
  }

  async getOrganizationToolAnalytics(organizationId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get all tools for the organization
    const tools = await this.prisma.toolDefinition.findMany({
      where: { organizationId },
      select: { id: true, name: true, type: true, category: true },
    });

    const toolIds = tools.map(t => t.id);

    const analytics = await this.prisma.toolAnalytics.findMany({
      where: {
        toolId: { in: toolIds },
        date: { gte: startDate },
      },
      include: {
        tool: {
          select: { name: true, type: true, category: true },
        },
      },
    });

    // Aggregate by tool
    const toolMetrics = new Map();
    
    analytics.forEach(record => {
      if (!toolMetrics.has(record.toolId)) {
        toolMetrics.set(record.toolId, {
          toolId: record.toolId,
          toolName: record.tool.name,
          toolType: record.tool.type,
          toolCategory: record.tool.category,
          executions: 0,
          successes: 0,
          errors: 0,
          totalDuration: 0,
          totalCost: 0,
          cacheHits: 0,
          cacheMisses: 0,
        });
      }

      const metrics = toolMetrics.get(record.toolId);
      metrics.executions += record.executionCount;
      metrics.successes += record.successCount;
      metrics.errors += record.errorCount;
      metrics.totalDuration += record.totalDuration;
      metrics.totalCost += record.totalCost;
      metrics.cacheHits += record.cacheHits;
      metrics.cacheMisses += record.cacheMisses;
    });

    // Calculate derived metrics
    const toolAnalytics = Array.from(toolMetrics.values()).map(metrics => ({
      ...metrics,
      successRate: metrics.executions > 0 ? (metrics.successes / metrics.executions) * 100 : 0,
      errorRate: metrics.executions > 0 ? (metrics.errors / metrics.executions) * 100 : 0,
      avgDuration: metrics.executions > 0 ? metrics.totalDuration / metrics.executions : 0,
      avgCost: metrics.executions > 0 ? metrics.totalCost / metrics.executions : 0,
      cacheHitRate: (metrics.cacheHits + metrics.cacheMisses) > 0 
        ? (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100 
        : 0,
    }));

    // Overall organization metrics
    const totals = toolAnalytics.reduce(
      (acc, tool) => ({
        totalExecutions: acc.totalExecutions + tool.executions,
        totalSuccesses: acc.totalSuccesses + tool.successes,
        totalErrors: acc.totalErrors + tool.errors,
        totalDuration: acc.totalDuration + tool.totalDuration,
        totalCost: acc.totalCost + tool.totalCost,
        totalCacheHits: acc.totalCacheHits + tool.cacheHits,
        totalCacheMisses: acc.totalCacheMisses + tool.cacheMisses,
      }),
      {
        totalExecutions: 0,
        totalSuccesses: 0,
        totalErrors: 0,
        totalDuration: 0,
        totalCost: 0,
        totalCacheHits: 0,
        totalCacheMisses: 0,
      }
    );

    return {
      overview: {
        totalTools: tools.length,
        totalExecutions: totals.totalExecutions,
        overallSuccessRate: totals.totalExecutions > 0 
          ? (totals.totalSuccesses / totals.totalExecutions) * 100 
          : 0,
        overallErrorRate: totals.totalExecutions > 0 
          ? (totals.totalErrors / totals.totalExecutions) * 100 
          : 0,
        avgExecutionTime: totals.totalExecutions > 0 
          ? totals.totalDuration / totals.totalExecutions 
          : 0,
        totalCost: totals.totalCost,
        overallCacheHitRate: (totals.totalCacheHits + totals.totalCacheMisses) > 0 
          ? (totals.totalCacheHits / (totals.totalCacheHits + totals.totalCacheMisses)) * 100 
          : 0,
      },
      toolBreakdown: toolAnalytics.sort((a, b) => b.executions - a.executions),
      period: {
        start: startDate,
        end: new Date(),
        days,
      },
    };
  }

  async getTopPerformingTools(organizationId?: string, limit: number = 10) {
    const where: any = {
      tool: {
        isActive: true,
        ...(organizationId && { organizationId }),
      },
      date: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      },
    };

    const analytics = await this.prisma.toolAnalytics.groupBy({
      by: ['toolId'],
      where,
      _sum: {
        executionCount: true,
        successCount: true,
        totalDuration: true,
        totalCost: true,
      },
      _avg: {
        avgDuration: true,
      },
      orderBy: {
        _sum: {
          executionCount: 'desc',
        },
      },
      take: limit,
    });

    // Get tool details
    const toolIds = analytics.map(a => a.toolId);
    const tools = await this.prisma.toolDefinition.findMany({
      where: { id: { in: toolIds } },
      select: { id: true, name: true, type: true, category: true },
    });

    const toolMap = new Map(tools.map(t => [t.id, t]));

    return analytics.map(a => {
      const tool = toolMap.get(a.toolId);
      const executions = a._sum.executionCount || 0;
      const successes = a._sum.successCount || 0;
      
      return {
        toolId: a.toolId,
        toolName: tool?.name || 'Unknown',
        toolType: tool?.type || 'Unknown',
        toolCategory: tool?.category || 'Unknown',
        executions,
        successRate: executions > 0 ? (successes / executions) * 100 : 0,
        avgDuration: a._avg.avgDuration || 0,
        totalCost: a._sum.totalCost || 0,
      };
    });
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private async calculateNewAverage(
    toolId: string,
    date: Date,
    hour: number,
    field: string,
    newValue: number
  ): Promise<number> {
    const existing = await this.prisma.toolAnalytics.findUnique({
      where: {
        toolId_date_hour: { toolId, date, hour },
      },
    });

    if (!existing) return newValue;

    const currentAvg = existing[field] || 0;
    const currentCount = existing.executionCount;
    
    return ((currentAvg * currentCount) + newValue) / (currentCount + 1);
  }

  private async calculateMinDuration(
    toolId: string,
    date: Date,
    hour: number,
    newDuration: number
  ): Promise<number> {
    const existing = await this.prisma.toolAnalytics.findUnique({
      where: {
        toolId_date_hour: { toolId, date, hour },
      },
    });

    if (!existing || !existing.minDuration) return newDuration;
    return Math.min(existing.minDuration, newDuration);
  }

  private async calculateMaxDuration(
    toolId: string,
    date: Date,
    hour: number,
    newDuration: number
  ): Promise<number> {
    const existing = await this.prisma.toolAnalytics.findUnique({
      where: {
        toolId_date_hour: { toolId, date, hour },
      },
    });

    if (!existing || !existing.maxDuration) return newDuration;
    return Math.max(existing.maxDuration, newDuration);
  }

  private async updateErrorTypes(
    toolId: string,
    date: Date,
    hour: number,
    errorType: string
  ): Promise<any> {
    const existing = await this.prisma.toolAnalytics.findUnique({
      where: {
        toolId_date_hour: { toolId, date, hour },
      },
    });

    const errorTypes = (existing?.errorTypes as any) || {};
    errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    
    return errorTypes;
  }

  private async updateCacheHitRate(toolId: string, date: Date, hour: number) {
    const analytics = await this.prisma.toolAnalytics.findUnique({
      where: {
        toolId_date_hour: { toolId, date, hour },
      },
    });

    if (!analytics) return;

    const totalCacheRequests = analytics.cacheHits + analytics.cacheMisses;
    const hitRate = totalCacheRequests > 0 ? (analytics.cacheHits / totalCacheRequests) * 100 : 0;

    await this.prisma.toolAnalytics.update({
      where: { id: analytics.id },
      data: { cacheHitRate: hitRate },
    });
  }
}