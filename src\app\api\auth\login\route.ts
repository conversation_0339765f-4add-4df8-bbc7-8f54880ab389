/**
 * Login API Route
 * Production-grade authentication endpoint with MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import {
  authenticateUser,
  createTokens,
  setAuthCookies
} from '@/lib/auth/auth-service';
import { withValidation } from '@/lib/middleware/auth.middleware';
import { trackAuthEvent } from '@/lib/mcp-tools';

// Login request schema validation
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  organizationSlug: z.string().optional(),
  mfaCode: z.string().optional(),
  rememberMe: z.boolean().optional().default(false)
});

// Login handler
async function handler(req: NextRequest): Promise<NextResponse> {
  try {
    const body = await req.json();

    // Authenticate user
    const authResponse = await authenticateUser(
      body.email,
      body.password,
      {
        organizationSlug: body.organizationSlug,
        mfaCode: body.mfaCode,
        rememberMe: body.rememberMe
      }
    );

    // Create response
    const response = NextResponse.json({
      success: true,
      user: authResponse.user,
      mfaRequired: authResponse.mfaRequired,
      availableOrganizations: authResponse.availableOrganizations || []
    });

    // Set auth cookies
    if (authResponse.tokens && !authResponse.mfaRequired) {
      setAuthCookies(response, authResponse.tokens, body.rememberMe);
    }

    // Track successful login in MCP Memory
    if (authResponse.user && !authResponse.mfaRequired) {
      trackAuthEvent(authResponse.user.id, 'login', {
        email: body.email,
        ip: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      });
    }

    return response;
  } catch (error: any) {
    console.error('Login error:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Authentication failed',
        code: error.code || 'AUTH_ERROR'
      },
      { status: 401 }
    );
  }
}

// Export with validation middleware
export const POST = withValidation(handler, {
  body: loginSchema
});