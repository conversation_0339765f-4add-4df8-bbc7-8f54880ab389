import { PrismaService } from '../prisma/prisma.service';
import { Cache } from 'cache-manager';
interface AnalyticsEvent {
    event: string;
    userId: string;
    organizationId: string;
    properties: Record<string, any>;
    timestamp: Date;
}
export declare class AnalyticsService {
    private prisma;
    private cacheManager;
    constructor(prisma: PrismaService, cacheManager: Cache);
    trackEvent(event: AnalyticsEvent): Promise<boolean>;
    getDashboardAnalytics(organizationId: string): Promise<{
        overview: {
            totalUsers: number;
            activeUsers: number;
            totalWorkflows: number;
            activeWorkflows: number;
            totalAgents: number;
            activeAgents: number;
            totalSessions: number;
            activeSessions: number;
            workflowExecutions: number;
        };
        recentActivity: ({
            user: {
                email: string;
                firstName: string;
                lastName: string;
            };
        } & {
            id: string;
            userId: string;
            ipAddress: string | null;
            userAgent: string | null;
            createdAt: Date;
            organizationId: string;
            resource: string;
            action: string;
            resourceId: string | null;
            details: import("@prisma/client/runtime/library").JsonValue;
        })[];
    }>;
    getWorkflowAnalytics(organizationId: string, timeRange?: string): Promise<{
        stats: {
            totalExecutions: number;
            avgDuration: number;
        };
        statusDistribution: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.WorkflowExecutionGroupByOutputType, "status"[]> & {
            _count: {
                id: number;
            };
        })[];
        topWorkflows: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.WorkflowExecutionGroupByOutputType, "workflowId"[]> & {
            _count: {
                id: number;
            };
        })[];
        trends: Record<string, Record<string, number>>;
    }>;
    getAgentAnalytics(organizationId: string, timeRange?: string): Promise<{
        stats: {
            totalAgents: number;
            totalSessions: number;
        };
        typeDistribution: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.AgentGroupByOutputType, "type"[]> & {
            _count: {
                id: number;
            };
        })[];
        topAgents: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.AgentSessionGroupByOutputType, "agentId"[]> & {
            _count: {
                id: number;
            };
        })[];
    }>;
    getUserAnalytics(organizationId: string, timeRange?: string): Promise<{
        stats: {
            totalUsers: number;
            activeUsers: number;
        };
        roleDistribution: (import(".prisma/client").Prisma.PickEnumerable<import(".prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
            _count: {
                id: number;
            };
        })[];
        trends: Record<string, number>;
    }>;
    getSystemHealth(organizationId: string): Promise<{
        errorRate: number;
        avgResponseTime: number;
        uptime: number;
        resourceUsage: {
            activeSessions: number;
            activeWorkflows: number;
            activeAgents: number;
        };
        status: string;
    }>;
    private getExecutionTrends;
    private getLoginTrends;
    private calculateErrorRate;
    private calculateAvgResponseTime;
    private calculateUptime;
    private getResourceUsage;
    private getSystemStatus;
}
export {};
