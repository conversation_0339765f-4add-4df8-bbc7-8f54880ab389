import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ToolsController } from './tools.controller';
import { ToolsService } from './tools.service';
import { PrismaModule } from '../prisma/prisma.module';
import { ToolManagerService } from './services/tool-manager.service';
import { ToolExecutionService } from './services/tool-execution.service';
import { ToolAnalyticsService } from './services/tool-analytics.service';
import { ToolCacheService } from './services/tool-cache.service';

@Module({
  imports: [PrismaModule],
  controllers: [ToolsController],
  providers: [
    ToolsService,
    ToolManagerService,
    ToolExecutionService,
    ToolAnalyticsService,
    ToolCacheService,
  ],
  exports: [
    ToolsService,
    ToolManagerService,
    ToolExecutionService,
    ToolAnalyticsService,
    ToolCacheService,
  ],
})
export class ToolsModule {}