/**
 * 🎯 API Response Types
 * Standardized response types for all API endpoints
 */

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    code?: string;
    message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

export interface AuthTokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
}

// List response with stats
export interface ListResponse<T = any, S = any> extends ApiResponse<T[]> {
    stats?: S;
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

// Error response with details
export interface ErrorResponse extends ApiResponse<never> {
    success: false;
    error: string;
    code: string;
    details?: any;
}

// Success response
export interface SuccessResponse<T = any> extends ApiResponse<T> {
    success: true;
    data?: T;
    message?: string;
}