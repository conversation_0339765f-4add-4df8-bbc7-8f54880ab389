/**
 * API Response Types
 * Standardized response types for all API endpoints
 */

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    code?: string;
    statusCode?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

export interface AuthTokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
}