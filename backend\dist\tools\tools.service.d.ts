import { PrismaService } from '../prisma/prisma.service';
import { ToolManagerService } from './services/tool-manager.service';
import { ToolExecutionService, ToolExecutionContext } from './services/tool-execution.service';
export declare class ToolsService {
    private prisma;
    private toolManager;
    private toolExecution;
    constructor(prisma: PrismaService, toolManager: ToolManagerService, toolExecution: ToolExecutionService);
    findAll(organizationId?: string): Promise<any>;
    findOne(id: string, organizationId?: string): Promise<any>;
    create(createToolDto: any, creatorId: string, organizationId?: string): Promise<any>;
    update(id: string, updateToolDto: any, userId: string, organizationId: string): Promise<any>;
    remove(id: string, userId: string, organizationId: string): Promise<void>;
    executeTool(toolId: string, input: any, context: Partial<ToolExecutionContext>): Promise<import("./services/tool-execution.service").ToolExecutionResult>;
    getToolsByType(type: string, organizationId?: string): Promise<any>;
    getPopularTools(organizationId?: string, limit?: number): Promise<any[]>;
    getToolStats(organizationId?: string): Promise<any>;
    importTool(importOptions: any, creatorId: string, organizationId?: string): Promise<any>;
    exportTool(toolId: string, format: string, organizationId?: string): Promise<any>;
}
