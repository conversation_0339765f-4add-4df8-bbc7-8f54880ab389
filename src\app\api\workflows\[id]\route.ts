import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getAuthenticatedSession, unauthorizedResponse } from "@/lib/auth";
import { z } from "zod";

const prisma = new PrismaClient();

const updateWorkflowSchema = z.object({
    name: z.string().min(1).optional(),
    description: z.string().optional(),
    definition: z.object({
        nodes: z.array(z.any()),
        edges: z.array(z.any()),
    }).optional(),
    tags: z.array(z.string()).optional(),
    isActive: z.boolean().optional(),
});

// GET /api/workflows/[id] - Get workflow by ID
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const workflow = await prisma.workflow.findFirst({
            where: {
                id: params.id,
                organizationId: session.user.organizationId,
            },
            include: {
                creator: {
                    select: {
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                executions: {
                    select: {
                        id: true,
                        status: true,
                        startedAt: true,
                        completedAt: true,
                        duration: true,
                        error: true,
                    },
                    orderBy: { startedAt: "desc" },
                    take: 10,
                },
                _count: {
                    select: {
                        executions: true,
                    },
                },
            },
        });

        if (!workflow) {
            return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
        }

        const completedExecutions = workflow.executions.filter(
            (exec) => exec.status === "COMPLETED"
        );
        const successRate = workflow.executions.length > 0
            ? Math.round((completedExecutions.length / workflow.executions.length) * 100)
            : 0;

        const avgDuration = completedExecutions.length > 0
            ? Math.round(
                completedExecutions.reduce((sum, exec) => sum + (exec.duration || 0), 0) /
                completedExecutions.length
            )
            : 0;

        const response = {
            ...workflow,
            status: workflow.isActive ? "active" : "inactive",
            successRate,
            totalRuns: workflow._count.executions,
            avgDuration,
            creator: {
                name: `${workflow.creator.firstName} ${workflow.creator.lastName}`,
                avatar: workflow.creator.avatar,
            },
        };

        return NextResponse.json(response);
    } catch (error) {
        console.error("Failed to fetch workflow:", error);
        return NextResponse.json(
            { error: "Failed to fetch workflow" },
            { status: 500 }
        );
    }
}

// PUT /api/workflows/[id] - Update workflow
export async function PUT(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        const body = await request.json();
        const validatedData = updateWorkflowSchema.parse(body);

        // Check if workflow exists and user has access
        const existingWorkflow = await prisma.workflow.findFirst({
            where: {
                id: params.id,
                organizationId: session.user.organizationId,
            },
        });

        if (!existingWorkflow) {
            return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
        }

        // Update workflow
        const workflow = await prisma.workflow.update({
            where: { id: params.id },
            data: {
                ...validatedData,
                version: existingWorkflow.version + 1,
            },
            include: {
                creator: {
                    select: {
                        firstName: true,
                        lastName: true,
                        avatar: true,
                    },
                },
                _count: {
                    select: {
                        executions: true,
                    },
                },
            },
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.user.id,
                action: "WORKFLOW_UPDATED",
                resource: "WORKFLOW",
                resourceId: workflow.id,
                details: {
                    changes: validatedData,
                    newVersion: workflow.version,
                },
                organizationId: session.user.organizationId,
            },
        });

        const response = {
            id: workflow.id,
            name: workflow.name,
            description: workflow.description,
            status: workflow.isActive ? "active" : "inactive",
            version: workflow.version,
            tags: workflow.tags,
            definition: workflow.definition,
            creator: {
                name: `${workflow.creator.firstName} ${workflow.creator.lastName}`,
                avatar: workflow.creator.avatar,
            },
            createdAt: workflow.createdAt,
            updatedAt: workflow.updatedAt,
        };

        return NextResponse.json(response);
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { error: "Validation failed", details: error.issues.map((err: any) => err.message) },
                { status: 400 }
            );
        }

        console.error("Failed to update workflow:", error);
        return NextResponse.json(
            { error: "Failed to update workflow" },
            { status: 500 }
        );
    }
}

// DELETE /api/workflows/[id] - Delete workflow
export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const session = await getAuthenticatedSession();
        if (!session?.user?.id) {
            return unauthorizedResponse();
        }

        // Check if workflow exists and user has access
        const workflow = await prisma.workflow.findFirst({
            where: {
                id: params.id,
                organizationId: session.user.organizationId,
            },
        });

        if (!workflow) {
            return NextResponse.json({ error: "Workflow not found" }, { status: 404 });
        }

        // Soft delete by marking as inactive
        await prisma.workflow.update({
            where: { id: params.id },
            data: { isActive: false },
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.user.id,
                action: "WORKFLOW_DELETED",
                resource: "WORKFLOW",
                resourceId: workflow.id,
                details: {
                    name: workflow.name,
                },
                organizationId: session.user.organizationId,
            },
        });

        return NextResponse.json({ message: "Workflow deleted successfully" });
    } catch (error) {
        console.error("Failed to delete workflow:", error);
        return NextResponse.json(
            { error: "Failed to delete workflow" },
            { status: 500 }
        );
    }
}