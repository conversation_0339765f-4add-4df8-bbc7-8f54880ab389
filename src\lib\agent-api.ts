import apiClient from '@/lib/api-client';
import apixClient from '@/lib/apix-client';

// Agent Instance Types
export interface AgentInstance {
  id: string;
  name: string;
  description?: string;
  type: 'STANDALONE' | 'TOOL_DRIVEN' | 'HYBRID' | 'MULTI_TASKING' | 'MULTI_PROVIDER';
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR' | 'PAUSED' | 'TESTING' | 'ARCHIVED';

  // Configuration
  config: any;
  systemPrompt?: string;
  instructions?: string;

  // AI Model settings
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;

  // Skills and capabilities
  skills: string[];
  tools: string[];
  capabilities: {
    canCollaborate: boolean;
    shareContext: boolean;
    maxConcurrentTasks?: number;
    memoryWindow: number;
  };    

  // Communication
  communication: AgentCommunication;

  // Metadata
  creatorId: string;
  organizationId: string;
  templateId?: string;
  createdAt: string;
  updatedAt: string;

  // Relations
  template?: AgentTemplate;
  creator?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  _count?: {
    sessions: number;
    tasks: number;
    conversations: number;
    collaborations: number;
  };
}

export interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: AgentInstance['type'];
  systemPrompt?: string;
  instructions?: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  skills: string[];
  tools: string[];
  capabilities: AgentInstance['capabilities'];
  isPublic: boolean;
  usageCount: number;
  rating?: number;
  createdBy: string;
  communication: AgentCommunication;
}

export interface AgentSession {
  id: string;
  agentId: string;
  userId?: string;
  name?: string;
  type: string;
  status: string;
  messageCount: number;
  tokenUsage: {
    total: number;
    input: number;
    output: number;
  };
  startedAt: string;
  lastActivityAt: string;
  endedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AgentCommunication {
  enableAgentToAgent: boolean;
  allowBroadcast: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

export interface AgentMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: string;
  agentId?: string;
  sessionId?: string;
  taskId?: string;
  workflowId?: string;
  toolId?: string;
  toolName?: string;
  toolInput?: any;
  toolOutput?: any;
  toolError?: any;
  toolStatus?: 'pending' | 'running' | 'completed' | 'failed';
  toolDuration?: number;
  toolStartedAt?: string;
  toolCompletedAt?: string;
  metadata?: any;
}

export interface AgentTask {
  id: string;
  name: string;
  description?: string;
  agentId: string;
  sessionId?: string;
  type: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'PAUSED';
  priority: number;
  input: any;
  output?: any;
  error?: string;
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  createdAt: string;
  updatedAt: string;
}

// Agent API Client
export const agentApi = {
  // Agent Instances
  async getAgents(params?: {
    type?: string;
    status?: string;
    userId?: string;
    organizationId?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.type) queryParams.append('type', params.type);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.organizationId) queryParams.append('organizationId', params.organizationId);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const url = `/agents${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  async getAgent(id: string) {
    return await apiClient.get(`/agents/${id}`);
  },

  async createAgent(data: {
    name: string;
    description?: string;
    organizationId: string;
    templateId?: string;
    type: AgentInstance['type'];
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: Partial<AgentInstance['capabilities']>;
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
    communication?: AgentCommunication;
  }) {
    return await apiClient.post('/agents', data);
  },

  async updateAgent(id: string, data: {
    name?: string;
    description?: string;
    status?: AgentInstance['status'];
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: Partial<AgentInstance['capabilities']>;
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
    communication?: AgentCommunication;
  }) {
    return await apiClient.put(`/agents/${id}`, data);
  },

  async deleteAgent(id: string) {
    return await apiClient.delete(`/agents/${id}`);
  },

  // Agent Execution
  async executeAgent(id: string, data: {
    input: any;
    sessionId?: string;
    context?: any;
    metadata?: any;
    communication?: AgentCommunication;
  }) {
    return await apiClient.post(`/agents/${id}/execute`, data);
  },

  // Agent Templates
  async getTemplates(params?: {
    category?: string;
    type?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.category) queryParams.append('category', params.category);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const url = `/agents/templates${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  async getTemplate(id: string) {
    return await apiClient.get(`/agents/templates/${id}`);
  },

  async createTemplate(data: {
    name: string;
    description: string;
    category: string;
    type: AgentInstance['type'];
    systemPrompt: string;
    instructions?: string;
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: any;
    isPublic?: boolean;
    tags?: string[];
    communication?: AgentCommunication;
    metadata?: any;
  }) {
    return await apiClient.post('/agents/templates', data);
  },

  async updateTemplate(id: string, data: Partial<Parameters<typeof this.createTemplate>[0]>) {
    return await apiClient.put(`/agents/templates/${id}`, data);
  },

  async deleteTemplate(id: string) {
    return await apiClient.delete(`/agents/templates/${id}`);
  },

  // Agent Sessions
  async getAgentSessions(agentId: string, params?: {
    organizationId?: string;
    userId?: string;
    status?: string;
    type?: string;
    search?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.organizationId) queryParams.append('organizationId', params.organizationId);
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.search) queryParams.append('search', params.search);
    const url = `/agents/${agentId}/sessions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  async getSession(sessionId: string) {
    return await apiClient.get(`/agents/sessions/${sessionId}`);
  },

  async getSessionMessages(sessionId: string, params?: {
    limit?: number;
    offset?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    const url = `/agents/sessions/${sessionId}/messages${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  // Agent Tasks
  async getAgentTasks(agentId: string, params?: {
    status?: string;
    organizationId?: string;
    userId?: string;
    type?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.organizationId) queryParams.append('organizationId', params.organizationId);
    if (params?.userId) queryParams.append('userId', params.userId);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    const url = `/agents/${agentId}/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  async createTask(agentId: string, data: {
    name: string;
    description?: string;
    sessionId?: string;
    type?: string;
    priority?: number;
    input: any;
    context?: any;
    metadata?: any;
    dependencies?: string[];
    maxRetries?: number;
    timeout?: number;
    communication?: AgentCommunication;
  }) {
    return await apiClient.post(`/agents/${agentId}/tasks`, data);
  },

  // Agent Communication
  async sendMessage(fromAgentId: string, data: {
    toAgentId: string;
    content: any;
    options?: {
      type?: 'request' | 'response' | 'notification';
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      requiresResponse?: boolean;
      timeout?: number;
      sessionId?: string;
      workflowId?: string;
      communication?: AgentCommunication;
    };
  }) {
    return await apiClient.post(`/agents/${fromAgentId}/messages`, data);
  },

  async getAgentMessages(agentId: string, params?: {
    limit?: number;
    offset?: number;
    status?: 'pending' | 'delivered' | 'acknowledged' | 'failed';
    type?: 'request' | 'response' | 'broadcast' | 'notification';
    communication?: AgentCommunication;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.status) queryParams.append('status', params.status);
    if (params?.type) queryParams.append('type', params.type);
    if (params?.communication) queryParams.append('communication', JSON.stringify(params.communication));
    const url = `/agents/${agentId}/messages${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  // Agent Metrics
  async getAgentMetrics(agentId: string, params?: {
    from?: string;
    to?: string;
    communication?: AgentCommunication;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.from) queryParams.append('from', params.from);
    if (params?.to) queryParams.append('to', params.to);
    if (params?.communication) queryParams.append('communication', JSON.stringify(params.communication));
    const url = `/agents/${agentId}/metrics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return await apiClient.get(url);
  },

  // Real-time events using APIX client methods
  subscribeToAgentEvents(agentId: string, callback: (event: any) => void) {
    apixClient.subscribeToAgent(agentId);
    return apixClient.on('agent_status', callback);
  },

  subscribeToAgentMessages(agentId: string, callback: (message: any) => void) {
    apixClient.subscribeToAgent(agentId);
    return apixClient.on('agent_response', callback);
  },

  subscribeToAgentTasks(agentId: string, callback: (task: any) => void) {
    apixClient.subscribeToAgent(agentId);
    return apixClient.on('agent_thinking', callback);
  },
};

export default agentApi;