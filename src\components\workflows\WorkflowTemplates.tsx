"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Search,
    Download,
    Eye,
    Star,
    Clock,
    Users,
    Zap,
    Database,
    Mail,
    Webhook,
    FileText,
    MessageSquare,
    Calendar,
    Image,
} from "lucide-react";

interface WorkflowTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    tags: string[];
    rating: number;
    downloads: number;
    complexity: "beginner" | "intermediate" | "advanced";
    estimatedTime: string;
    author: {
        name: string;
        avatar?: string;
    };
    preview: {
        nodes: number;
        connections: number;
    };
    definition: any;
}

interface WorkflowTemplatesProps {
    onSelectTemplate: (template: WorkflowTemplate) => void;
}

const templates: WorkflowTemplate[] = [
    {
        id: "1",
        name: "Email Marketing Campaign",
        description: "Automate email campaigns with customer segmentation and analytics tracking",
        category: "Marketing",
        tags: ["email", "marketing", "automation", "analytics"],
        rating: 4.8,
        downloads: 1250,
        complexity: "intermediate",
        estimatedTime: "15 min",
        author: { name: "SynapseAI Team" },
        preview: { nodes: 8, connections: 12 },
        definition: {},
    },
    {
        id: "2",
        name: "Data Processing Pipeline",
        description: "Process and transform data from multiple sources with validation and error handling",
        category: "Data",
        tags: ["data", "etl", "processing", "validation"],
        rating: 4.6,
        downloads: 890,
        complexity: "advanced",
        estimatedTime: "30 min",
        author: { name: "Data Team" },
        preview: { nodes: 12, connections: 18 },
        definition: {},
    },
    {
        id: "3",
        name: "Customer Support Bot",
        description: "Intelligent chatbot for handling customer inquiries and ticket routing",
        category: "Support",
        tags: ["chatbot", "ai", "support", "automation"],
        rating: 4.9,
        downloads: 2100,
        complexity: "beginner",
        estimatedTime: "10 min",
        author: { name: "AI Team" },
        preview: { nodes: 6, connections: 8 },
        definition: {},
    },
    {
        id: "4",
        name: "Invoice Processing",
        description: "Automate invoice processing with OCR, validation, and approval workflows",
        category: "Finance",
        tags: ["invoice", "ocr", "finance", "approval"],
        rating: 4.7,
        downloads: 650,
        complexity: "intermediate",
        estimatedTime: "20 min",
        author: { name: "Finance Team" },
        preview: { nodes: 10, connections: 15 },
        definition: {},
    },
    {
        id: "5",
        name: "Social Media Monitor",
        description: "Monitor social media mentions and automatically respond to customer feedback",
        category: "Social",
        tags: ["social", "monitoring", "engagement", "alerts"],
        rating: 4.4,
        downloads: 480,
        complexity: "beginner",
        estimatedTime: "12 min",
        author: { name: "Marketing Team" },
        preview: { nodes: 7, connections: 9 },
        definition: {},
    },
    {
        id: "6",
        name: "Content Generation",
        description: "Generate and publish content across multiple platforms using AI",
        category: "Content",
        tags: ["ai", "content", "generation", "publishing"],
        rating: 4.5,
        downloads: 720,
        complexity: "intermediate",
        estimatedTime: "18 min",
        author: { name: "Content Team" },
        preview: { nodes: 9, connections: 13 },
        definition: {},
    },
];

const categories = [
    { value: "all", label: "All Categories" },
    { value: "marketing", label: "Marketing" },
    { value: "data", label: "Data Processing" },
    { value: "support", label: "Customer Support" },
    { value: "finance", label: "Finance" },
    { value: "social", label: "Social Media" },
    { value: "content", label: "Content" },
];

const complexityColors = {
    beginner: "bg-green-500",
    intermediate: "bg-yellow-500",
    advanced: "bg-red-500",
};

const categoryIcons = {
    Marketing: Mail,
    Data: Database,
    Support: MessageSquare,
    Finance: FileText,
    Social: Users,
    Content: Image,
};

export default function WorkflowTemplates({ onSelectTemplate }: WorkflowTemplatesProps) {
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedCategory, setSelectedCategory] = useState("all");
    const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);

    const filteredTemplates = templates.filter((template) => {
        const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesCategory = selectedCategory === "all" ||
            template.category.toLowerCase() === selectedCategory.toLowerCase();

        return matchesSearch && matchesCategory;
    });

    const handleUseTemplate = (template: WorkflowTemplate) => {
        onSelectTemplate(template);
        setSelectedTemplate(null);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">Workflow Templates</h3>
                    <p className="text-sm text-muted-foreground">
                        Get started quickly with pre-built workflow templates
                    </p>
                </div>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search templates..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                    />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                        {categories.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                                {category.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            {/* Templates Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredTemplates.length === 0 ? (
                    <div className="col-span-full text-center py-12">
                        <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No templates found</h3>
                        <p className="text-muted-foreground">
                            Try adjusting your search terms or category filter
                        </p>
                    </div>
                ) : (
                    filteredTemplates.map((template) => {
                        const IconComponent = categoryIcons[template.category as keyof typeof categoryIcons] || Zap;

                        return (
                            <Card key={template.id} className="hover:shadow-md transition-shadow cursor-pointer group">
                                <CardHeader>
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-2">
                                            <div className="p-2 rounded-lg bg-primary/10">
                                                <IconComponent className="h-4 w-4 text-primary" />
                                            </div>
                                            <div>
                                                <Badge
                                                    className={`${complexityColors[template.complexity]} text-white text-xs`}
                                                >
                                                    {template.complexity}
                                                </Badge>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                            <span>{template.rating}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <CardTitle className="text-lg group-hover:text-primary transition-colors">
                                            {template.name}
                                        </CardTitle>
                                        <p className="text-sm text-muted-foreground mt-1">
                                            {template.description}
                                        </p>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {/* Tags */}
                                        <div className="flex flex-wrap gap-1">
                                            {template.tags.slice(0, 3).map((tag, index) => (
                                                <Badge key={index} variant="outline" className="text-xs">
                                                    {tag}
                                                </Badge>
                                            ))}
                                            {template.tags.length > 3 && (
                                                <Badge variant="outline" className="text-xs">
                                                    +{template.tags.length - 3}
                                                </Badge>
                                            )}
                                        </div>

                                        {/* Stats */}
                                        <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                                            <div className="flex items-center gap-1">
                                                <Download className="h-3 w-3" />
                                                <span>{template.downloads}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Clock className="h-3 w-3" />
                                                <span>{template.estimatedTime}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Zap className="h-3 w-3" />
                                                <span>{template.preview.nodes} nodes</span>
                                            </div>
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center gap-2">
                                            <Dialog
                                                open={selectedTemplate?.id === template.id}
                                                onOpenChange={(open) => !open && setSelectedTemplate(null)}
                                            >
                                                <DialogTrigger asChild>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="flex-1"
                                                        onClick={() => setSelectedTemplate(template)}
                                                    >
                                                        <Eye className="h-3 w-3 mr-1" />
                                                        Preview
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent className="max-w-2xl">
                                                    <DialogHeader>
                                                        <DialogTitle>{template.name}</DialogTitle>
                                                    </DialogHeader>
                                                    <div className="space-y-4">
                                                        <p className="text-muted-foreground">{template.description}</p>

                                                        <div className="grid grid-cols-2 gap-4">
                                                            <div>
                                                                <h4 className="font-semibold mb-2">Details</h4>
                                                                <div className="space-y-2 text-sm">
                                                                    <div className="flex justify-between">
                                                                        <span>Category:</span>
                                                                        <span>{template.category}</span>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Complexity:</span>
                                                                        <Badge className={`${complexityColors[template.complexity]} text-white text-xs`}>
                                                                            {template.complexity}
                                                                        </Badge>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Est. Time:</span>
                                                                        <span>{template.estimatedTime}</span>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Rating:</span>
                                                                        <span className="flex items-center gap-1">
                                                                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                                                            {template.rating}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div>
                                                                <h4 className="font-semibold mb-2">Workflow Structure</h4>
                                                                <div className="space-y-2 text-sm">
                                                                    <div className="flex justify-between">
                                                                        <span>Nodes:</span>
                                                                        <span>{template.preview.nodes}</span>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Connections:</span>
                                                                        <span>{template.preview.connections}</span>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Downloads:</span>
                                                                        <span>{template.downloads}</span>
                                                                    </div>
                                                                    <div className="flex justify-between">
                                                                        <span>Author:</span>
                                                                        <span>{template.author.name}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div>
                                                            <h4 className="font-semibold mb-2">Tags</h4>
                                                            <div className="flex flex-wrap gap-1">
                                                                {template.tags.map((tag, index) => (
                                                                    <Badge key={index} variant="outline" className="text-xs">
                                                                        {tag}
                                                                    </Badge>
                                                                ))}
                                                            </div>
                                                        </div>

                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                onClick={() => setSelectedTemplate(null)}
                                                            >
                                                                Close
                                                            </Button>
                                                            <Button onClick={() => handleUseTemplate(template)}>
                                                                Use Template
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </DialogContent>
                                            </Dialog>

                                            <Button
                                                size="sm"
                                                className="flex-1"
                                                onClick={() => handleUseTemplate(template)}
                                            >
                                                Use Template
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })
                )}
            </div>
        </div>
    );
}