/**
 * Production-grade Authentication Service
 * Centralized authentication handling with JWT, RBAC, and multi-tenant support
 */

import { jwtVerify, SignJWT } from 'jose';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import {
    User,
    Role,
    Organization,
    AuthTokens,
    SessionUser,
    Session,
    AuthResponse
} from '@/lib/types/auth';
import { mcp_Memory_create_entities } from '@/lib/mcp-tools';
import apiClient from '../api-client';

// Constants
const JWT_SECRET = new TextEncoder().encode(
    process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET || 'fallback-secret-for-dev'
);
const ACCESS_TOKEN_EXPIRY = '15m'; // 15 minutes
const REFRESH_TOKEN_EXPIRY = '7d'; // 7 days
const COOKIE_OPTIONS = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    path: '/'
};

/**
 * Authenticate user with email and password
 */
export async function authenticateUser(
    email: string,
    password: string,
    options?: {
        rememberMe?: boolean;
        organizationSlug?: string;
        mfaCode?: string;
        mfaEnabled?: boolean;
    }
): Promise<AuthResponse> {
    try {
        // Call backend authentication endpoint
        const response = await apiClient.post('/auth/login', {
                email,
                password,
                organizationSlug: options?.organizationSlug,
                mfaCode: options?.mfaCode,
                mfaEnabled: options?.mfaEnabled,
                rememberMe: options?.rememberMe
            }) as { status: number, data: AuthResponse };

        if (response.status !== 200) {
            const errorData = response.data;
            throw new Error(errorData.error || errorData.message     || 'Authentication failed');
        }

        const data = response.data;

        // Store auth data in MCP Memory for better session management
        await mcp_Memory_create_entities({
            entities: [
                {
                    name: `user:${data.user.id}`,
                    entityType: 'user',
                    observations: [
                        `User ${data.user.firstName} ${data.user.lastName} authenticated successfully`,
                        `Role: ${data.user.role}`,
                        `Organization: ${data.user.organization.name}`,
                        `Last login: ${new Date().toISOString()}`
                    ]
                }
            ]
        });

        return {
            user: data.user,
            tokens: data.tokens,
            mfaRequired: data.mfaRequired || false,
            availableOrganizations: data.availableOrganizations || [],
            error: data.error || null,
            message: data.message || null
        };
    } catch (error: any) {
        console.error('Authentication error:', error);
        throw new Error(error.message || 'Authentication failed');
    }
}

/**
 * Create JWT tokens for authenticated user
 */
export async function createTokens(user: User): Promise<AuthTokens> {
    const now = Math.floor(Date.now() / 1000);

    // Create access token with user data and permissions
    const accessToken = await new SignJWT({
        sub: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organizationId: user.organizationId,
        organizationName: user.organization.name,
        organizationSlug: user.organization.slug,
        permissions: user.permissions || [],
        emailVerified: user.emailVerified,
        isActive: user.isActive
    })
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt(now)
        .setExpirationTime(ACCESS_TOKEN_EXPIRY)
        .sign(JWT_SECRET);

    // Create refresh token with minimal data
    const refreshToken = await new SignJWT({
        sub: user.id,
        type: 'refresh'
    })
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt(now)
        .setExpirationTime(REFRESH_TOKEN_EXPIRY)
        .sign(JWT_SECRET);

    // Calculate expiration time for client
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15); // 15 minutes

    return {
        accessToken,
        refreshToken,
        expiresAt: expiresAt.toISOString()
    };
}

/**
 * Set authentication cookies
 */
export function setAuthCookies(
    response: NextResponse,
    tokens: AuthTokens,
    rememberMe: boolean = false
): void {
    // Set access token cookie
    response.cookies.set('access_token', tokens.accessToken, {
        ...COOKIE_OPTIONS,
        maxAge: rememberMe ? 60 * 60 * 24 * 7 : undefined // 7 days if remember me
    });

    // Set refresh token cookie with longer expiry
    response.cookies.set('refresh_token', tokens.refreshToken, {
        ...COOKIE_OPTIONS,
        maxAge: 60 * 60 * 24 * 30 // 30 days
    });
}

/**
 * Clear authentication cookies
 */
export function clearAuthCookies(response: NextResponse): void {
    response.cookies.delete('access_token');
    response.cookies.delete('refresh_token');
}

/**
 * Get authenticated session from request or server-side cookies
 */
export async function getAuthenticatedSession(
    request?: NextRequest
): Promise<Session | null> {
    try {
        // If no request is provided, use server-side cookies (for API routes)
        if (!request) {
            const cookieStore = await cookies();
            const accessToken = cookieStore.get('access_token')?.value;

            if (!accessToken) {
                return null;
            }

            // Verify JWT token
            const { payload } = await jwtVerify(accessToken, JWT_SECRET, {
                algorithms: ['HS256']
            });

            if (!payload || !payload.sub || !payload.organizationId) {
                return null;
            }

            // Create session user from token payload
            const sessionUser: SessionUser = {
                id: payload.sub as string,
                email: payload.email as string,
                firstName: payload.firstName as string,
                lastName: payload.lastName as string,
                role: payload.role as Role,
                organizationId: payload.organizationId as string,
                organization: {
                    id: payload.organizationId as string,
                    name: payload.organizationName as string,
                    slug: payload.organizationSlug as string
                },
                permissions: (payload.permissions as string[]) || [],
                emailVerified: payload.emailVerified as boolean,
                isActive: payload.isActive as boolean
            };

            return {
                user: sessionUser,
                expires: new Date((payload.exp as number) * 1000).toISOString(),
                accessToken
            };
        }

        // For middleware/client-side: Try cookie first (primary method)
        const accessToken = request.cookies.get('access_token')?.value;

        if (!accessToken) {
            // Try Authorization header as fallback
            const authHeader = request.headers.get('authorization');
            if (!authHeader?.startsWith('Bearer ')) {
                return null;
            }
        }

        const token = accessToken || request.headers.get('authorization')?.substring(7);

        if (!token) {
            return null;
        }

        // Verify JWT token
        const { payload } = await jwtVerify(token, JWT_SECRET, {
            algorithms: ['HS256']
        });

        if (!payload || !payload.sub || !payload.organizationId) {
            return null;
        }

        // Create session user from token payload
        const sessionUser: SessionUser = {
            id: payload.sub as string,
            email: payload.email as string,
            firstName: payload.firstName as string,
            lastName: payload.lastName as string,
            role: payload.role as Role,
            organizationId: payload.organizationId as string,
            organization: {
                id: payload.organizationId as string,
                name: payload.organizationName as string,
                slug: payload.organizationSlug as string
            },
            permissions: (payload.permissions as string[]) || [],
            emailVerified: payload.emailVerified as boolean,
            isActive: payload.isActive as boolean
        };

        return {
            user: sessionUser,
            expires: new Date((payload.exp as number) * 1000).toISOString(),
            accessToken: token
        };
    } catch (error) {
        console.error('Session verification failed:', error);
        return null;
    }
}

/**
 * Refresh authentication tokens
 */
export async function refreshTokens(refreshToken: string): Promise<AuthTokens> {
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refreshToken })
        });

        if (!response.ok) {
            throw new Error('Failed to refresh tokens');
        }

        const data = await response.json();
        return data.tokens;
    } catch (error) {
        console.error('Token refresh failed:', error);
        throw new Error('Failed to refresh authentication');
    }
}

/**
 * Check if user has required role level
 */
export function hasRequiredRole(userRole: Role, requiredRoles: Role[]): boolean {
    // Role hierarchy for permission checking
    const ROLE_HIERARCHY: Record<Role, number> = {
        'SUPER_ADMIN': 100,
        'ORG_ADMIN': 80,
        'ADMIN': 80, // Same level as ORG_ADMIN for compatibility
        'DEVELOPER': 60,
        'VIEWER': 40,
        'USER': 20
    };

    // Super admin always has access
    if (userRole === 'SUPER_ADMIN') return true;

    // Get user's role level
    const userLevel = ROLE_HIERARCHY[userRole];

    // Check if user's role level meets any of the required role levels
    return requiredRoles.some(role => userLevel >= ROLE_HIERARCHY[role]);
}

/**
 * Check if user has specific permission
 */
export function hasPermission(user: SessionUser, permission: string): boolean {
    // Super admin and org admin always have all permissions
    if (['SUPER_ADMIN', 'ORG_ADMIN', 'ADMIN'].includes(user.role)) {
        return true;
    }

    // Check user's explicit permissions
    return user.permissions.includes(permission);
}

/**
 * Check if user has all required permissions
 */
export function hasPermissions(user: SessionUser, permissions: string[]): boolean {
    return permissions.every(permission => hasPermission(user, permission));
}

/**
 * Check if user has specific role
 */
export function hasRole(user: SessionUser, roles: Role[]): boolean {
    return roles.includes(user.role);
}

/**
 * Register a new user
 */
export async function registerUser(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    company: string;
    subscribeNewsletter?: boolean;
}): Promise<AuthResponse> {
    try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Registration failed');
        }

        const data = await response.json();

        // Store new user in MCP Memory
        await mcp_Memory_create_entities({
            entities: [
                {
                    name: `user:${data.user.id}`,
                    entityType: 'user',
                    observations: [
                        `User ${userData.firstName} ${userData.lastName} registered successfully`,
                        `Email: ${userData.email}`,
                        `Organization: ${data.user.organization.name}`,
                        `Registration date: ${new Date().toISOString()}`
                    ]
                }
            ]
        });

        return {
            user: data.user,
            tokens: data.tokens,
            mfaRequired: false,
            availableOrganizations: data.availableOrganizations || [],
            error: data.error || null,
            message: data.message || null
        };
    } catch (error: any) {
        console.error('Registration error:', error);
        throw new Error(error.message || 'Registration failed');
    }
}

/**
 * Error response for unauthorized access
 */
export function unauthorizedResponse(message: string = 'Unauthorized') {
    return NextResponse.json(
        { success: false, message, statusCode: 401 },
        { status: 401 }
    );
}

/**
 * Error response for forbidden access
 */
export function forbiddenResponse(message: string = 'Forbidden') {
    return NextResponse.json(
        { success: false, message, statusCode: 403 },
        { status: 403 }
    );
}

/**
 * Error response for general errors
 */
export function errorResponse(message: string = 'Internal server error', status: number = 500) {
    return NextResponse.json(
        { success: false, message, statusCode: status },
        { status }
    );
}