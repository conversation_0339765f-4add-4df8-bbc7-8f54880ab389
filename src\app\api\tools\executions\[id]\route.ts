import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { ToolExecutionListQuerySchema } from '@/lib/schemas/tool.schema';
import { z } from 'zod';
import { toPrismaJson } from '@/lib/types/prisma';

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const organizationId = request.headers.get('x-organization-id');

    if (!userId || !organizationId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const execution = await prisma.toolExecution.findFirst({
      where: {
        id: params.id,
        organizationId
      },
      include: {
        tool: {
          select: {
            id: true,
            name: true,
            type: true,
            version: true
          }
        }
      }
    });

    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(execution);

  } catch (error) {
    console.error('Error fetching execution:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    const organizationId = request.headers.get('x-organization-id');

    if (!userId || !organizationId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    const execution = await prisma.toolExecution.findFirst({
      where: {
        id: params.id,
        organizationId
      }
    });

    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    switch (action) {
      case 'cancel':
        if (execution.status === 'PENDING' || execution.status === 'RUNNING') {
          const cancelledExecution = await prisma.toolExecution.update({
            where: { id: params.id },
            data: {
              status: 'CANCELLED',
              completedAt: new Date(),
              duration: execution.startedAt
                ? Date.now() - execution.startedAt.getTime()
                : 0
            }
          });

          // Emit cancellation event
          console.log('Tool execution cancelled:', params.id);

          return NextResponse.json(cancelledExecution);
        } else {
          return NextResponse.json(
            { error: 'Cannot cancel execution in current state' },
            { status: 400 }
          );
        }

      case 'retry':
        if (execution.status === 'FAILED' || execution.status === 'TIMEOUT') {
          // Create new execution with incremented retry count
          const retryExecution = await prisma.toolExecution.create({
            data: {
              toolId: execution.toolId,
              executorType: execution.executorType,
              executorId: execution.executorId,
              sessionId: execution.sessionId,
              organizationId: execution.organizationId,
              status: 'PENDING',
              input: toPrismaJson(execution.input),
              metadata: toPrismaJson({
                ...(execution.metadata as any || {}),
                retryOf: execution.id,
                retryCount: (execution.retryCount || 0) + 1
              }),
              retryCount: (execution.retryCount || 0) + 1,
              parentId: execution.id
            }
          });

          // Start retry execution
          // Note: In a real implementation, you'd trigger the execution service here

          return NextResponse.json(retryExecution, { status: 201 });
        } else {
          return NextResponse.json(
            { error: 'Cannot retry execution in current state' },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error performing execution action:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}