import { Injectable, Logger, BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ProviderType, ProviderStatus, RequestStatus, Prisma } from '@prisma/client';
import { 
  CreateProviderDto, 
  UpdateProviderDto, 
  CreateProviderModelDto, 
  CreateRoutingRuleDto,
  AiRequestDto,
  ProviderHealthDto,
  ProviderTestDto,
  BulkProviderActionDto,
  ProviderAnalyticsQueryDto
} from './dto/provider.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);
  private readonly providerAdapters = new Map();

  constructor(
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2,
  ) {
    this.initializeProviderAdapters();
  }

  private initializeProviderAdapters() {
    // Initialize all provider adapters
    this.providerAdapters.set(ProviderType.OPENAI, this.createOpenAIAdapter());
    this.providerAdapters.set(ProviderType.CLAUDE, this.createClaudeAdapter());
    this.providerAdapters.set(ProviderType.ANTHROPIC, this.createAnthropicAdapter());
    this.providerAdapters.set(ProviderType.GEMINI, this.createGeminiAdapter());
    this.providerAdapters.set(ProviderType.MISTRAL, this.createMistralAdapter());
    this.providerAdapters.set(ProviderType.GROQ, this.createGroqAdapter());
    this.providerAdapters.set(ProviderType.DEEPSEEK, this.createDeepSeekAdapter());
    this.providerAdapters.set(ProviderType.HUGGING_FACE, this.createHuggingFaceAdapter());
    this.providerAdapters.set(ProviderType.OLLAMA, this.createOllamaAdapter());
    this.providerAdapters.set(ProviderType.OPENROUTER, this.createOpenRouterAdapter());
    this.providerAdapters.set(ProviderType.GROK, this.createGrokAdapter());
  }

  // Provider CRUD Operations
  async createProvider(organizationId: string, dto: CreateProviderDto) {
    try {
      const provider = await this.prisma.provider.create({
        data: {
          ...dto,
          organizationId,
        },
        include: {
          models: true,
          healthMetrics: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      // Initialize health monitoring
      await this.initializeProviderHealth(provider.id);

      // Emit provider created event
      this.eventEmitter.emit('provider.created', {
        providerId: provider.id,
        organizationId,
        type: provider.type,
      });

      this.logger.log(`Provider created: ${provider.name} (${provider.type})`);
      return provider;
    } catch (error) {
      this.logger.error(`Failed to create provider: ${error.message}`);
      throw new InternalServerErrorException('Failed to create provider');
    }
  }

  async getProviders(organizationId: string, includeInactive = false) {
    const where: Prisma.ProviderWhereInput = {
      organizationId,
      ...(includeInactive ? {} : { isActive: true }),
    };

    return this.prisma.provider.findMany({
      where,
      include: {
        models: {
          where: { isActive: true },
          orderBy: { isDefault: 'desc' },
        },
        healthMetrics: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
        _count: {
          select: {
            usageLogs: true,
          },
        },
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' },
      ],
    });
  }

  async getProvider(organizationId: string, providerId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: {
        id: providerId,
        organizationId,
      },
      include: {
        models: {
          orderBy: { isDefault: 'desc' },
        },
        healthMetrics: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        routingRules: {
          where: { isActive: true },
          orderBy: { priority: 'desc' },
        },
        _count: {
          select: {
            usageLogs: true,
          },
        },
      },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    return provider;
  }

  async updateProvider(organizationId: string, providerId: string, dto: UpdateProviderDto) {
    const provider = await this.prisma.provider.findFirst({
      where: { id: providerId, organizationId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    const updated = await this.prisma.provider.update({
      where: { id: providerId },
      data: dto,
      include: {
        models: true,
        healthMetrics: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });

    this.eventEmitter.emit('provider.updated', {
      providerId,
      organizationId,
      changes: dto,
    });

    return updated;
  }

  async deleteProvider(organizationId: string, providerId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id: providerId, organizationId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    await this.prisma.provider.delete({
      where: { id: providerId },
    });

    this.eventEmitter.emit('provider.deleted', {
      providerId,
      organizationId,
      type: provider.type,
    });

    this.logger.log(`Provider deleted: ${provider.name} (${provider.type})`);
  }

  // Provider Model Management
  async createProviderModel(organizationId: string, providerId: string, dto: CreateProviderModelDto) {
    const provider = await this.prisma.provider.findFirst({
      where: { id: providerId, organizationId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    // If this is set as default, unset other defaults
    if (dto.isDefault) {
      await this.prisma.providerModel.updateMany({
        where: { providerId },
        data: { isDefault: false },
      });
    }

    const model = await this.prisma.providerModel.create({
      data: {
        ...dto,
        providerId,
      },
    });

    this.eventEmitter.emit('provider.model.created', {
      providerId,
      modelId: model.id,
      organizationId,
    });

    return model;
  }

  async getProviderModels(organizationId: string, providerId: string) {
    const provider = await this.prisma.provider.findFirst({
      where: { id: providerId, organizationId },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    return this.prisma.providerModel.findMany({
      where: { providerId },
      orderBy: [
        { isDefault: 'desc' },
        { displayName: 'asc' },
      ],
    });
  }

  // Smart Routing System
  async createRoutingRule(organizationId: string, dto: CreateRoutingRuleDto) {
    const rule = await this.prisma.routingRule.create({
      data: {
        ...dto,
        organizationId,
      },
      include: {
        provider: true,
        model: true,
      },
    });

    this.eventEmitter.emit('routing.rule.created', {
      ruleId: rule.id,
      organizationId,
    });

    return rule;
  }

  async getRoutingRules(organizationId: string) {
    return this.prisma.routingRule.findMany({
      where: { organizationId },
      include: {
        provider: true,
        model: true,
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' },
      ],
    });
  }

  // AI Request Processing
  async processAiRequest(organizationId: string, userId: string, dto: AiRequestDto) {
    const requestId = uuidv4();
    const startTime = Date.now();

    try {
      // Create usage log entry
      const usageLog = await this.prisma.aiUsageLog.create({
        data: {
          requestId,
          sessionId: dto.sessionId,
          organizationId,
          userId,
          executorType: dto.executorType || 'user',
          executorId: dto.executorId,
          requestType: dto.requestType,
          input: this.sanitizeInput(dto.input),
          status: RequestStatus.PENDING,
          metadata: dto.metadata || {},
          providerId: '', // Will be updated after routing
        },
      });

      // Smart routing to select best provider/model
      const routingResult = await this.smartRoute(organizationId, dto);
      
      if (!routingResult) {
        throw new BadRequestException('No suitable provider found for request');
      }

      // Update usage log with selected provider
      await this.prisma.aiUsageLog.update({
        where: { id: usageLog.id },
        data: {
          providerId: routingResult.provider.id,
          modelId: routingResult.model?.id,
          status: RequestStatus.PROCESSING,
        },
      });

      // Execute request with selected provider
      const adapter = this.providerAdapters.get(routingResult.provider.type);
      if (!adapter) {
        throw new InternalServerErrorException(`No adapter found for provider type: ${routingResult.provider.type}`);
      }

      const result = await adapter.execute({
        provider: routingResult.provider,
        model: routingResult.model,
        request: dto,
        streaming: dto.streaming || false,
        onChunk: dto.streaming ? (chunk: any) => {
          this.eventEmitter.emit('ai.chunk', {
            requestId,
            organizationId,
            userId,
            chunk,
          });
        } : undefined,
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update usage log with results
      await this.prisma.aiUsageLog.update({
        where: { id: usageLog.id },
        data: {
          output: this.sanitizeOutput(result.output),
          status: RequestStatus.COMPLETED,
          completedAt: new Date(),
          durationMs: duration,
          inputTokens: result.usage?.inputTokens || 0,
          outputTokens: result.usage?.outputTokens || 0,
          totalTokens: result.usage?.totalTokens || 0,
          inputCost: this.calculateCost(result.usage?.inputTokens || 0, routingResult.model?.inputCostPer1k),
          outputCost: this.calculateCost(result.usage?.outputTokens || 0, routingResult.model?.outputCostPer1k),
          totalCost: this.calculateTotalCost(result.usage, routingResult.model),
          isStreaming: dto.streaming || false,
          streamChunks: result.streamChunks || 0,
        },
      });

      // Update provider health metrics
      await this.updateProviderMetrics(routingResult.provider.id, {
        success: true,
        latency: duration,
      });

      // Emit completion event
      this.eventEmitter.emit('ai.completed', {
        requestId,
        organizationId,
        userId,
        providerId: routingResult.provider.id,
        modelId: routingResult.model?.id,
        duration,
        cost: this.calculateTotalCost(result.usage, routingResult.model),
      });

      return {
        requestId,
        output: result.output,
        usage: result.usage,
        provider: {
          id: routingResult.provider.id,
          name: routingResult.provider.name,
          type: routingResult.provider.type,
        },
        model: routingResult.model ? {
          id: routingResult.model.id,
          modelId: routingResult.model.modelId,
          displayName: routingResult.model.displayName,
        } : null,
        duration,
        cost: this.calculateTotalCost(result.usage, routingResult.model),
      };

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update usage log with error
      await this.prisma.aiUsageLog.update({
        where: { requestId },
        data: {
          status: RequestStatus.FAILED,
          completedAt: new Date(),
          durationMs: duration,
          error: error.message,
          errorCode: error.code || 'UNKNOWN_ERROR',
        },
      });

      // Emit error event
      this.eventEmitter.emit('ai.error', {
        requestId,
        organizationId,
        userId,
        error: error.message,
      });

      this.logger.error(`AI request failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Smart Routing Logic
  private async smartRoute(organizationId: string, request: AiRequestDto) {
    // Get active routing rules
    const rules = await this.prisma.routingRule.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        provider: {
          include: {
            models: {
              where: { isActive: true },
            },
            healthMetrics: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
        model: true,
      },
      orderBy: { priority: 'desc' },
    });

    // Apply routing rules
    for (const rule of rules) {
      if (this.matchesConditions(request, rule.conditions)) {
        const provider = rule.provider;
        const model = rule.model || this.selectBestModel(provider.models, request);

        // Check provider health
        const health = provider.healthMetrics[0];
        if (health && health.status === ProviderStatus.HEALTHY) {
          // Update rule usage
          await this.prisma.routingRule.update({
            where: { id: rule.id },
            data: {
              usageCount: { increment: 1 },
            },
          });

          return { provider, model };
        }
      }
    }

    // Fallback to best available provider
    return this.fallbackRouting(organizationId, request);
  }

  private matchesConditions(request: AiRequestDto, conditions: any): boolean {
    // Implement condition matching logic
    if (conditions.requestType && conditions.requestType !== request.requestType) {
      return false;
    }

    if (conditions.maxLatencyMs && request.maxLatencyMs && request.maxLatencyMs < conditions.maxLatencyMs) {
      return false;
    }

    if (conditions.maxCost && request.maxCost && request.maxCost < conditions.maxCost) {
      return false;
    }

    if (conditions.requiredCapabilities && request.requiredCapabilities) {
      const hasAllCapabilities = conditions.requiredCapabilities.every((cap: string) =>
        request.requiredCapabilities.includes(cap)
      );
      if (!hasAllCapabilities) {
        return false;
      }
    }

    return true;
  }

  private selectBestModel(models: any[], request: AiRequestDto) {
    if (!models.length) return null;

    // Filter models by capabilities
    let candidates = models;
    if (request.requiredCapabilities) {
      candidates = models.filter(model => {
        const capabilities = model.capabilities || {};
        return request.requiredCapabilities.every(cap => capabilities[cap] === true);
      });
    }

    if (!candidates.length) return null;

    // Sort by performance and cost
    candidates.sort((a, b) => {
      const scoreA = this.calculateModelScore(a, request);
      const scoreB = this.calculateModelScore(b, request);
      return scoreB - scoreA;
    });

    return candidates[0];
  }

  private calculateModelScore(model: any, request: AiRequestDto): number {
    let score = 0;

    // Prefer default models
    if (model.isDefault) score += 10;

    // Prefer models with better success rate
    score += model.successRate * 5;

    // Prefer models with lower latency
    if (model.avgLatencyMs > 0) {
      score += Math.max(0, 5 - (model.avgLatencyMs / 1000));
    }

    // Consider cost if specified
    if (request.maxCost && model.inputCostPer1k) {
      const estimatedCost = this.estimateRequestCost(request, model);
      if (estimatedCost <= request.maxCost) {
        score += Math.max(0, 3 - (estimatedCost / request.maxCost));
      } else {
        score -= 10; // Heavily penalize over-budget models
      }
    }

    return score;
  }

  private async fallbackRouting(organizationId: string, request: AiRequestDto) {
    const providers = await this.prisma.provider.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        models: {
          where: { isActive: true },
        },
        healthMetrics: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
      orderBy: { priority: 'desc' },
    });

    for (const provider of providers) {
      const health = provider.healthMetrics[0];
      if (health && health.status === ProviderStatus.HEALTHY) {
        const model = this.selectBestModel(provider.models, request);
        if (model) {
          return { provider, model };
        }
      }
    }

    return null;
  }

  // Health Monitoring
  @Cron(CronExpression.EVERY_5_MINUTES)
  async monitorProviderHealth() {
    this.logger.log('Starting provider health monitoring...');

    const providers = await this.prisma.provider.findMany({
      where: { isActive: true },
      include: { organization: true },
    });

    for (const provider of providers) {
      try {
        await this.checkProviderHealth(provider);
      } catch (error) {
        this.logger.error(`Health check failed for provider ${provider.name}: ${error.message}`);
      }
    }

    this.logger.log('Provider health monitoring completed');
  }

  private async checkProviderHealth(provider: any) {
    const adapter = this.providerAdapters.get(provider.type);
    if (!adapter) return;

    const startTime = Date.now();
    let status = ProviderStatus.HEALTHY;
    let error = null;

    try {
      await adapter.healthCheck(provider);
    } catch (err) {
      status = ProviderStatus.UNHEALTHY;
      error = err.message;
    }

    const latency = Date.now() - startTime;

    // Get recent error rate
    const recentLogs = await this.prisma.aiUsageLog.findMany({
      where: {
        providerId: provider.id,
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
        },
      },
    });

    const errorRate = recentLogs.length > 0 
      ? (recentLogs.filter(log => log.status === RequestStatus.FAILED).length / recentLogs.length) * 100
      : 0;

    // Calculate uptime
    const healthMetrics = await this.prisma.providerHealth.findMany({
      where: {
        providerId: provider.id,
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });

    const uptime = healthMetrics.length > 0
      ? (healthMetrics.filter(h => h.status === ProviderStatus.HEALTHY).length / healthMetrics.length) * 100
      : 100;

    // Create health record
    await this.prisma.providerHealth.create({
      data: {
        providerId: provider.id,
        status,
        uptime,
        avgLatencyMs: latency,
        errorRate,
        lastError: error,
        consecutiveErrors: error ? 1 : 0, // Simplified for now
      },
    });

    // Emit health event
    this.eventEmitter.emit('provider.health', {
      providerId: provider.id,
      organizationId: provider.organizationId,
      status,
      uptime,
      latency,
      errorRate,
    });
  }

  // Provider Adapters
  private createOpenAIAdapter() {
    return {
      async execute({ provider, model, request, streaming, onChunk }) {
        // OpenAI implementation
        const config = provider.config;
        const endpoint = model?.customEndpoint || 'https://api.openai.com/v1/chat/completions';
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
            'Content-Type': 'application/json',
            ...model?.customHeaders,
          },
          body: JSON.stringify({
            model: model?.modelId || 'gpt-4',
            messages: request.input.messages,
            stream: streaming,
            ...request.input.parameters,
          }),
        });

        if (!response.ok) {
          throw new Error(`OpenAI API error: ${response.statusText}`);
        }

        if (streaming) {
          return this.handleStreamingResponse(response, onChunk);
        } else {
          const data = await response.json();
          return {
            output: data.choices[0].message,
            usage: {
              inputTokens: data.usage?.prompt_tokens || 0,
              outputTokens: data.usage?.completion_tokens || 0,
              totalTokens: data.usage?.total_tokens || 0,
            },
          };
        }
      },

      async healthCheck(provider) {
        const config = provider.config;
        const response = await fetch('https://api.openai.com/v1/models', {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`,
          },
        });

        if (!response.ok) {
          throw new Error(`OpenAI health check failed: ${response.statusText}`);
        }
      },
    };
  }

  private createClaudeAdapter() {
    return {
      async execute({ provider, model, request, streaming, onChunk }) {
        const config = provider.config;
        const endpoint = model?.customEndpoint || 'https://api.anthropic.com/v1/messages';
        
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'x-api-key': config.apiKey,
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json',
            ...model?.customHeaders,
          },
          body: JSON.stringify({
            model: model?.modelId || 'claude-3-opus-20240229',
            messages: request.input.messages,
            stream: streaming,
            max_tokens: request.input.parameters?.max_tokens || 4000,
            ...request.input.parameters,
          }),
        });

        if (!response.ok) {
          throw new Error(`Claude API error: ${response.statusText}`);
        }

        if (streaming) {
          return this.handleStreamingResponse(response, onChunk);
        } else {
          const data = await response.json();
          return {
            output: { content: data.content[0].text, role: 'assistant' },
            usage: {
              inputTokens: data.usage?.input_tokens || 0,
              outputTokens: data.usage?.output_tokens || 0,
              totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
            },
          };
        }
      },

      async healthCheck(provider) {
        // Claude doesn't have a dedicated health endpoint, so we'll use a minimal request
        const config = provider.config;
        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': config.apiKey,
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'claude-3-haiku-20240307',
            messages: [{ role: 'user', content: 'Hi' }],
            max_tokens: 1,
          }),
        });

        if (!response.ok) {
          throw new Error(`Claude health check failed: ${response.statusText}`);
        }
      },
    };
  }

  // Additional adapter implementations would follow similar patterns
  private createAnthropicAdapter() { return this.createClaudeAdapter(); }
  private createGeminiAdapter() { return this.createGenericAdapter('Gemini'); }
  private createMistralAdapter() { return this.createGenericAdapter('Mistral'); }
  private createGroqAdapter() { return this.createGenericAdapter('Groq'); }
  private createDeepSeekAdapter() { return this.createGenericAdapter('DeepSeek'); }
  private createHuggingFaceAdapter() { return this.createGenericAdapter('HuggingFace'); }
  private createOllamaAdapter() { return this.createGenericAdapter('Ollama'); }
  private createOpenRouterAdapter() { return this.createGenericAdapter('OpenRouter'); }
  private createGrokAdapter() { return this.createGenericAdapter('Grok'); }

  private createGenericAdapter(providerName: string) {
    return {
      async execute({ provider, model, request, streaming, onChunk }) {
        throw new Error(`${providerName} adapter not yet implemented`);
      },
      async healthCheck(provider) {
        throw new Error(`${providerName} health check not yet implemented`);
      },
    };
  }

  // Utility Methods
  private async handleStreamingResponse(response: Response, onChunk?: (chunk: any) => void) {
    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    let chunks = 0;
    let fullContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n').filter(line => line.trim());

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content || '';
              if (content) {
                fullContent += content;
                chunks++;
                if (onChunk) {
                  onChunk({ content, type: 'text_chunk' });
                }
              }
            } catch (e) {
              // Ignore parsing errors for streaming
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return {
      output: { content: fullContent, role: 'assistant' },
      usage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 }, // Streaming doesn't provide usage
      streamChunks: chunks,
    };
  }

  private sanitizeInput(input: any): any {
    // Remove sensitive data from input before storing
    const sanitized = { ...input };
    if (sanitized.apiKey) delete sanitized.apiKey;
    if (sanitized.password) delete sanitized.password;
    if (sanitized.token) delete sanitized.token;
    return sanitized;
  }

  private sanitizeOutput(output: any): any {
    // Remove sensitive data from output before storing
    return output;
  }

  private calculateCost(tokens: number, costPer1k?: number): number {
    if (!costPer1k || !tokens) return 0;
    return (tokens / 1000) * costPer1k;
  }

  private calculateTotalCost(usage: any, model: any): number {
    if (!usage || !model) return 0;
    const inputCost = this.calculateCost(usage.inputTokens, model.inputCostPer1k);
    const outputCost = this.calculateCost(usage.outputTokens, model.outputCostPer1k);
    return inputCost + outputCost;
  }

  private estimateRequestCost(request: AiRequestDto, model: any): number {
    // Rough estimation based on input size
    const inputText = JSON.stringify(request.input);
    const estimatedTokens = Math.ceil(inputText.length / 4); // Rough token estimation
    return this.calculateCost(estimatedTokens, model.inputCostPer1k);
  }

  private async updateProviderMetrics(providerId: string, metrics: { success: boolean; latency: number }) {
    // Update provider model metrics
    await this.prisma.providerModel.updateMany({
      where: { providerId },
      data: {
        avgLatencyMs: metrics.latency,
        successRate: metrics.success ? { increment: 0.01 } : { decrement: 0.01 },
      },
    });
  }

  private async initializeProviderHealth(providerId: string) {
    await this.prisma.providerHealth.create({
      data: {
        providerId,
        status: ProviderStatus.HEALTHY,
        uptime: 100,
        avgLatencyMs: 0,
        errorRate: 0,
      },
    });
  }

  // Analytics and Reporting
  async getProviderAnalytics(organizationId: string, query: ProviderAnalyticsQueryDto) {
    const where: Prisma.AiUsageLogWhereInput = {
      organizationId,
      ...(query.startDate && { createdAt: { gte: query.startDate } }),
      ...(query.endDate && { createdAt: { lte: query.endDate } }),
      ...(query.providerIds && { providerId: { in: query.providerIds } }),
      ...(query.modelIds && { modelId: { in: query.modelIds } }),
      ...(query.executorTypes && { executorType: { in: query.executorTypes } }),
    };

    const logs = await this.prisma.aiUsageLog.findMany({
      where,
      include: {
        provider: true,
        model: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Group and aggregate data based on query.groupBy
    const grouped = this.groupAnalyticsData(logs, query.groupBy);
    
    return {
      summary: {
        totalRequests: logs.length,
        totalCost: logs.reduce((sum, log) => sum + log.totalCost, 0),
        totalTokens: logs.reduce((sum, log) => sum + log.totalTokens, 0),
        avgLatency: logs.length > 0 ? logs.reduce((sum, log) => sum + (log.durationMs || 0), 0) / logs.length : 0,
        successRate: logs.length > 0 ? (logs.filter(log => log.status === RequestStatus.COMPLETED).length / logs.length) * 100 : 0,
      },
      grouped,
    };
  }

  private groupAnalyticsData(logs: any[], groupBy?: string) {
    if (!groupBy) return logs;

    const groups = new Map();

    logs.forEach(log => {
      let key;
      switch (groupBy) {
        case 'provider':
          key = log.provider?.name || 'Unknown';
          break;
        case 'model':
          key = log.model?.displayName || 'Unknown';
          break;
        case 'executorType':
          key = log.executorType;
          break;
        case 'date':
          key = log.createdAt.toISOString().split('T')[0];
          break;
        case 'hour':
          key = log.createdAt.toISOString().split('T')[0] + 'T' + log.createdAt.getHours().toString().padStart(2, '0');
          break;
        default:
          key = 'all';
      }

      if (!groups.has(key)) {
        groups.set(key, {
          key,
          requests: 0,
          totalCost: 0,
          totalTokens: 0,
          totalLatency: 0,
          successCount: 0,
        });
      }

      const group = groups.get(key);
      group.requests++;
      group.totalCost += log.totalCost;
      group.totalTokens += log.totalTokens;
      group.totalLatency += log.durationMs || 0;
      if (log.status === RequestStatus.COMPLETED) {
        group.successCount++;
      }
    });

    // Calculate averages
    return Array.from(groups.values()).map(group => ({
      ...group,
      avgLatency: group.requests > 0 ? group.totalLatency / group.requests : 0,
      successRate: group.requests > 0 ? (group.successCount / group.requests) * 100 : 0,
    }));
  }

  // Bulk Operations
  async bulkProviderAction(organizationId: string, dto: BulkProviderActionDto) {
    const providers = await this.prisma.provider.findMany({
      where: {
        id: { in: dto.providerIds },
        organizationId,
      },
    });

    if (providers.length !== dto.providerIds.length) {
      throw new BadRequestException('Some providers not found');
    }

    const results = [];

    for (const provider of providers) {
      try {
        switch (dto.action) {
          case 'activate':
            await this.prisma.provider.update({
              where: { id: provider.id },
              data: { isActive: true },
            });
            results.push({ providerId: provider.id, success: true });
            break;

          case 'deactivate':
            await this.prisma.provider.update({
              where: { id: provider.id },
              data: { isActive: false },
            });
            results.push({ providerId: provider.id, success: true });
            break;

          case 'delete':
            await this.prisma.provider.delete({
              where: { id: provider.id },
            });
            results.push({ providerId: provider.id, success: true });
            break;

          case 'test':
            const adapter = this.providerAdapters.get(provider.type);
            if (adapter) {
              await adapter.healthCheck(provider);
              results.push({ providerId: provider.id, success: true });
            } else {
              results.push({ providerId: provider.id, success: false, error: 'No adapter available' });
            }
            break;
        }
      } catch (error) {
        results.push({ providerId: provider.id, success: false, error: error.message });
      }
    }

    return { results };
  }

  // Provider Testing
  async testProvider(organizationId: string, providerId: string, dto: ProviderTestDto) {
    const provider = await this.prisma.provider.findFirst({
      where: { id: providerId, organizationId },
      include: {
        models: {
          where: { isActive: true },
        },
      },
    });

    if (!provider) {
      throw new NotFoundException('Provider not found');
    }

    const model = dto.modelId 
      ? provider.models.find(m => m.id === dto.modelId)
      : provider.models.find(m => m.isDefault) || provider.models[0];

    const adapter = this.providerAdapters.get(provider.type);
    if (!adapter) {
      throw new InternalServerErrorException(`No adapter found for provider type: ${provider.type}`);
    }

    const startTime = Date.now();
    try {
      const result = await adapter.execute({
        provider,
        model,
        request: {
          requestType: 'test',
          input: dto.input,
        },
        streaming: false,
      });

      const duration = Date.now() - startTime;

      return {
        success: true,
        duration,
        result: result.output,
        usage: result.usage,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        duration,
        error: error.message,
      };
    }
  }
}