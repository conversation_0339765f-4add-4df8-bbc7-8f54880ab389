"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TenantsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const bcrypt = require("bcryptjs");
let TenantsService = class TenantsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTenant(createTenantDto) {
        const { name, slug, domain, adminUser } = createTenantDto;
        return this.prisma.$transaction(async (tx) => {
            const organization = await tx.organization.create({
                data: {
                    name,
                    slug,
                    domain,
                    settings: {
                        theme: 'light',
                        language: 'en',
                        timezone: 'UTC',
                        features: {
                            workflows: true,
                            agents: true,
                            tools: true,
                            analytics: true,
                        },
                    },
                    branding: {
                        primaryColor: '#3b82f6',
                        secondaryColor: '#64748b',
                        logo: null,
                        favicon: null,
                    },
                },
            });
            const hashedPassword = await bcrypt.hash(adminUser.password, 12);
            const user = await tx.user.create({
                data: {
                    email: adminUser.email,
                    password: hashedPassword,
                    firstName: adminUser.firstName,
                    lastName: adminUser.lastName,
                    role: client_1.Role.ORG_ADMIN,
                    organizationId: organization.id,
                    preferences: {
                        theme: 'system',
                        notifications: true,
                        viewMode: 'grid',
                        language: 'en',
                    },
                },
            });
            return { organization, user };
        });
    }
    async getTenants(page = 1, limit = 10, search) {
        const skip = (page - 1) * limit;
        const where = search
            ? {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { slug: { contains: search, mode: 'insensitive' } },
                    { domain: { contains: search, mode: 'insensitive' } },
                ],
            }
            : {};
        const [organizations, total] = await Promise.all([
            this.prisma.organization.findMany({
                where,
                skip,
                take: limit,
                include: {
                    _count: {
                        select: {
                            users: true,
                            workflows: true,
                            agents: true,
                            tools: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.organization.count({ where }),
        ]);
        return {
            data: organizations,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async getTenantById(id) {
        return this.prisma.organization.findUnique({
            where: { id },
            include: {
                users: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                        role: true,
                        isActive: true,
                        lastLoginAt: true,
                        createdAt: true,
                    },
                },
                _count: {
                    select: {
                        workflows: true,
                        agents: true,
                        tools: true,
                        sessions: true,
                    },
                },
            },
        });
    }
    async updateTenant(id, updateTenantDto) {
        return this.prisma.organization.update({
            where: { id },
            data: updateTenantDto,
        });
    }
    async deleteTenant(id) {
        return this.prisma.organization.delete({
            where: { id },
        });
    }
    async getTenantUsers(organizationId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where: { organizationId },
                skip,
                take: limit,
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    role: true,
                    isActive: true,
                    lastLoginAt: true,
                    createdAt: true,
                    preferences: true,
                },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.user.count({ where: { organizationId } }),
        ]);
        return {
            data: users,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async createTenantUser(organizationId, createUserDto) {
        const hashedPassword = await bcrypt.hash(createUserDto.password, 12);
        return this.prisma.user.create({
            data: {
                email: createUserDto.email,
                password: hashedPassword,
                firstName: createUserDto.firstName,
                lastName: createUserDto.lastName,
                role: createUserDto.role || client_1.Role.VIEWER,
                organizationId,
                preferences: {
                    theme: 'system',
                    notifications: true,
                    viewMode: 'grid',
                    language: 'en',
                },
            },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                createdAt: true,
            },
        });
    }
    async updateTenantUser(organizationId, userId, updateData) {
        const data = { ...updateData };
        if (updateData.password) {
            data.password = await bcrypt.hash(updateData.password, 12);
        }
        return this.prisma.user.update({
            where: {
                id: userId,
                organizationId,
            },
            data,
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                updatedAt: true,
            },
        });
    }
    async deleteTenantUser(organizationId, userId) {
        return this.prisma.user.delete({
            where: {
                id: userId,
                organizationId,
            },
        });
    }
    async getTenantAnalytics(organizationId) {
        const [userStats, workflowStats, agentStats, sessionStats, recentActivity,] = await Promise.all([
            this.prisma.user.groupBy({
                by: ['role'],
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.workflow.groupBy({
                by: ['isActive'],
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.agent.groupBy({
                by: ['type'],
                where: { organizationId },
                _count: { id: true },
            }),
            this.prisma.session.count({
                where: {
                    organizationId,
                    isActive: true,
                },
            }),
            this.prisma.auditLog.findMany({
                where: { organizationId },
                take: 10,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                },
            }),
        ]);
        return {
            users: userStats,
            workflows: workflowStats,
            agents: agentStats,
            activeSessions: sessionStats,
            recentActivity,
        };
    }
};
exports.TenantsService = TenantsService;
exports.TenantsService = TenantsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TenantsService);
//# sourceMappingURL=tenants.service.js.map