-- Create<PERSON><PERSON>
CREATE TYPE "ProviderStatus" AS ENUM ('HEALTHY', 'DEGRADED', 'UNHEALTHY', 'MAINTENANCE', 'RATE_LIMITED');

-- Create<PERSON>num
CREATE TYPE "RequestStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT');

-- AlterEnum
ALTER TYPE "ProviderType" ADD VALUE 'OPENROUTER';
ALTER TYPE "ProviderType" ADD VALUE 'ANTHROPIC';
ALTER TYPE "ProviderType" ADD VALUE 'GROK';

-- CreateTable
CREATE TABLE "provider_models" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "capabilities" JSONB NOT NULL DEFAULT '{}',
    "contextWindow" INTEGER NOT NULL DEFAULT 4096,
    "maxTokens" INTEGER,
    "inputCostPer1k" DOUBLE PRECISION,
    "outputCostPer1k" DOUBLE PRECISION,
    "avgLatencyMs" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "customEndpoint" TEXT,
    "customHeaders" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_models_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provider_health" (
    "id" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "status" "ProviderStatus" NOT NULL DEFAULT 'HEALTHY',
    "uptime" DOUBLE PRECISION NOT NULL DEFAULT 100.0,
    "avgLatencyMs" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "errorRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "rateLimitRemaining" INTEGER,
    "rateLimitReset" TIMESTAMP(3),
    "lastCheckAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastError" TEXT,
    "consecutiveErrors" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "provider_health_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "routing_rules" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "providerId" TEXT,
    "modelId" TEXT,
    "conditions" JSONB NOT NULL DEFAULT '{}',
    "priority" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "fallbackRules" JSONB NOT NULL DEFAULT '[]',
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "successCount" INTEGER NOT NULL DEFAULT 0,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "routing_rules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ai_usage_logs" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "sessionId" TEXT,
    "traceId" TEXT,
    "providerId" TEXT NOT NULL,
    "modelId" TEXT,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT,
    "executorType" TEXT NOT NULL,
    "executorId" TEXT,
    "requestType" TEXT NOT NULL,
    "input" JSONB NOT NULL,
    "output" JSONB,
    "status" "RequestStatus" NOT NULL DEFAULT 'PENDING',
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "durationMs" INTEGER,
    "inputTokens" INTEGER NOT NULL DEFAULT 0,
    "outputTokens" INTEGER NOT NULL DEFAULT 0,
    "totalTokens" INTEGER NOT NULL DEFAULT 0,
    "inputCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "outputCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "error" TEXT,
    "errorCode" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "isStreaming" BOOLEAN NOT NULL DEFAULT false,
    "streamChunks" INTEGER NOT NULL DEFAULT 0,
    "hitlTriggered" BOOLEAN NOT NULL DEFAULT false,
    "hitlApproved" BOOLEAN,
    "hitlFeedback" TEXT,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ai_usage_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "provider_models_providerId_modelId_key" ON "provider_models"("providerId", "modelId");

-- CreateIndex
CREATE INDEX "provider_models_providerId_isActive_idx" ON "provider_models"("providerId", "isActive");

-- CreateIndex
CREATE INDEX "provider_health_providerId_lastCheckAt_idx" ON "provider_health"("providerId", "lastCheckAt");

-- CreateIndex
CREATE INDEX "provider_health_status_lastCheckAt_idx" ON "provider_health"("status", "lastCheckAt");

-- CreateIndex
CREATE INDEX "routing_rules_organizationId_priority_idx" ON "routing_rules"("organizationId", "priority");

-- CreateIndex
CREATE INDEX "routing_rules_isActive_priority_idx" ON "routing_rules"("isActive", "priority");

-- CreateIndex
CREATE UNIQUE INDEX "ai_usage_logs_requestId_key" ON "ai_usage_logs"("requestId");

-- CreateIndex
CREATE INDEX "ai_usage_logs_organizationId_createdAt_idx" ON "ai_usage_logs"("organizationId", "createdAt");

-- CreateIndex
CREATE INDEX "ai_usage_logs_providerId_createdAt_idx" ON "ai_usage_logs"("providerId", "createdAt");

-- CreateIndex
CREATE INDEX "ai_usage_logs_userId_createdAt_idx" ON "ai_usage_logs"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "ai_usage_logs_executorType_executorId_idx" ON "ai_usage_logs"("executorType", "executorId");

-- CreateIndex
CREATE INDEX "ai_usage_logs_status_createdAt_idx" ON "ai_usage_logs"("status", "createdAt");

-- AddForeignKey
ALTER TABLE "provider_models" ADD CONSTRAINT "provider_models_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_health" ADD CONSTRAINT "provider_health_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routing_rules" ADD CONSTRAINT "routing_rules_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routing_rules" ADD CONSTRAINT "routing_rules_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "provider_models"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "routing_rules" ADD CONSTRAINT "routing_rules_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "providers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_modelId_fkey" FOREIGN KEY ("modelId") REFERENCES "provider_models"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ai_usage_logs" ADD CONSTRAINT "ai_usage_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;