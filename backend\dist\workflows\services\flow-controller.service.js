"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FlowControllerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlowControllerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const sessions_service_1 = require("../../sessions/sessions.service");
const client_1 = require("@prisma/client");
const node_executor_service_1 = require("./node-executor.service");
const variable_resolver_service_1 = require("./variable-resolver.service");
const event_emitter_1 = require("@nestjs/event-emitter");
let FlowControllerService = FlowControllerService_1 = class FlowControllerService {
    constructor(prisma, apixGateway, sessionsService, nodeExecutor, variableResolver, eventEmitter) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.sessionsService = sessionsService;
        this.nodeExecutor = nodeExecutor;
        this.variableResolver = variableResolver;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(FlowControllerService_1.name);
        this.activeExecutions = new Map();
    }
    async startExecution(workflowId, userId, organizationId, input = {}, options = {}) {
        this.logger.log(`Starting workflow execution: ${workflowId}`);
        const workflow = await this.prisma.workflow.findFirst({
            where: { id: workflowId, organizationId, isActive: true },
        });
        if (!workflow) {
            throw new Error('Workflow not found or inactive');
        }
        const definition = workflow.definition;
        const execution = await this.prisma.workflowExecution.create({
            data: {
                workflowId,
                userId,
                status: client_1.ExecutionStatus.PENDING,
                input: input,
                variables: input,
                startedAt: new Date(),
                options: options,
            },
        });
        const context = {
            executionId: execution.id,
            workflowId,
            organizationId,
            userId,
            variables: { ...input, ...definition.variables },
            nodeStates: {},
            completedNodes: new Set(),
            failedNodes: new Set(),
            pausedNodes: new Set(),
            startTime: Date.now(),
            options,
        };
        this.activeExecutions.set(execution.id, context);
        await this.apixGateway.emitWorkflowEvent(organizationId, workflowId, 'workflow_started', {
            executionId: execution.id,
            startTime: context.startTime,
            input,
        });
        if (options.async !== false) {
            this.executeWorkflowAsync(context, definition);
        }
        else {
            await this.executeWorkflow(context, definition);
        }
        return execution.id;
    }
    async executeWorkflowAsync(context, definition) {
        try {
            await this.executeWorkflow(context, definition);
        }
        catch (error) {
            this.logger.error(`Async workflow execution failed: ${error.message}`, error.stack);
            await this.handleExecutionError(context, error);
        }
    }
    async executeWorkflow(context, definition) {
        this.logger.log(`Executing workflow: ${context.workflowId}`);
        await this.updateExecutionStatus(context.executionId, client_1.ExecutionStatus.RUNNING);
        try {
            const startNodes = this.findStartNodes(definition);
            if (startNodes.length === 0) {
                throw new Error('No start nodes found in workflow');
            }
            const startPromises = startNodes.map(node => this.executeNode(context, definition, node));
            await Promise.all(startPromises);
            await this.continueExecution(context, definition);
            await this.completeExecution(context);
        }
        catch (error) {
            await this.handleExecutionError(context, error);
        }
    }
    async executeNode(context, definition, node) {
        this.logger.log(`Executing node: ${node.id} (${node.type})`);
        if (context.completedNodes.has(node.id) || context.failedNodes.has(node.id)) {
            return context.nodeStates[node.id];
        }
        if (context.pausedNodes.has(node.id)) {
            this.logger.log(`Node ${node.id} is paused, skipping execution`);
            return null;
        }
        try {
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'node_started', {
                executionId: context.executionId,
                nodeId: node.id,
                nodeType: node.type,
                nodeName: node.name,
            });
            const step = await this.prisma.workflowExecutionStep.create({
                data: {
                    executionId: context.executionId,
                    nodeId: node.id,
                    nodeType: node.type,
                    nodeName: node.name,
                    status: client_1.ExecutionStatus.RUNNING,
                    startedAt: new Date(),
                    input: context.variables,
                },
            });
            const resolvedConfig = await this.variableResolver.resolveVariables(node.config, context.variables);
            const result = await this.nodeExecutor.executeNode(node.type, resolvedConfig, context.variables, {
                executionId: context.executionId,
                nodeId: node.id,
                organizationId: context.organizationId,
                userId: context.userId,
            });
            context.nodeStates[node.id] = result;
            context.completedNodes.add(node.id);
            await this.prisma.workflowExecutionStep.update({
                where: { id: step.id },
                data: {
                    status: client_1.ExecutionStatus.COMPLETED,
                    completedAt: new Date(),
                    output: result,
                    duration: Date.now() - step.startedAt.getTime(),
                },
            });
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'node_completed', {
                executionId: context.executionId,
                nodeId: node.id,
                result,
                duration: Date.now() - step.startedAt.getTime(),
            });
            if (result && typeof result === 'object') {
                context.variables = { ...context.variables, ...result };
            }
            return result;
        }
        catch (error) {
            this.logger.error(`Node execution failed: ${node.id}`, error.stack);
            context.failedNodes.add(node.id);
            await this.prisma.workflowExecutionStep.updateMany({
                where: {
                    executionId: context.executionId,
                    nodeId: node.id,
                    status: client_1.ExecutionStatus.RUNNING,
                },
                data: {
                    status: client_1.ExecutionStatus.FAILED,
                    completedAt: new Date(),
                    error: error.message,
                },
            });
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'node_failed', {
                executionId: context.executionId,
                nodeId: node.id,
                error: error.message,
            });
            const errorHandling = definition.settings?.errorHandling;
            if (errorHandling?.onError === 'continue') {
                this.logger.warn(`Continuing execution despite node failure: ${node.id}`);
                return null;
            }
            else if (errorHandling?.onError === 'retry') {
                return await this.retryNode(context, definition, node, error);
            }
            else {
                throw error;
            }
        }
    }
    async retryNode(context, definition, node, originalError, retryCount = 0) {
        const retryPolicy = definition.settings?.retryPolicy || context.options.retryPolicy;
        const maxRetries = retryPolicy?.maxRetries || 3;
        if (retryCount >= maxRetries) {
            throw originalError;
        }
        this.logger.log(`Retrying node ${node.id}, attempt ${retryCount + 1}/${maxRetries}`);
        const baseDelay = retryPolicy?.retryDelay || 1000;
        const delay = retryPolicy?.backoffStrategy === 'exponential'
            ? baseDelay * Math.pow(2, retryCount)
            : baseDelay * (retryCount + 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        context.failedNodes.delete(node.id);
        try {
            return await this.executeNode(context, definition, node);
        }
        catch (error) {
            return await this.retryNode(context, definition, node, originalError, retryCount + 1);
        }
    }
    async continueExecution(context, definition) {
        const nextNodes = this.findNextNodes(definition, context.completedNodes);
        if (nextNodes.length === 0) {
            this.logger.log('No more nodes to execute, workflow completed');
            return;
        }
        const promises = nextNodes.map(node => this.executeNode(context, definition, node));
        await Promise.all(promises);
        await this.continueExecution(context, definition);
    }
    findStartNodes(definition) {
        return definition.nodes.filter(node => {
            return !definition.edges.some(edge => edge.target === node.id);
        });
    }
    findNextNodes(definition, completedNodes) {
        const nextNodes = [];
        for (const node of definition.nodes) {
            if (completedNodes.has(node.id)) {
                continue;
            }
            const inputEdges = definition.edges.filter(edge => edge.target === node.id);
            const allInputsCompleted = inputEdges.every(edge => completedNodes.has(edge.source));
            if (allInputsCompleted && inputEdges.length > 0) {
                nextNodes.push(node);
            }
        }
        return nextNodes;
    }
    async completeExecution(context) {
        this.logger.log(`Completing workflow execution: ${context.executionId}`);
        const duration = Date.now() - context.startTime;
        await this.prisma.workflowExecution.update({
            where: { id: context.executionId },
            data: {
                status: client_1.ExecutionStatus.COMPLETED,
                completedAt: new Date(),
                duration,
                output: context.nodeStates,
            },
        });
        await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'workflow_completed', {
            executionId: context.executionId,
            duration,
            completedNodes: Array.from(context.completedNodes),
            output: context.nodeStates,
        });
        this.activeExecutions.delete(context.executionId);
        this.eventEmitter.emit('workflow.completed', {
            executionId: context.executionId,
            workflowId: context.workflowId,
            organizationId: context.organizationId,
            duration,
        });
    }
    async handleExecutionError(context, error) {
        this.logger.error(`Workflow execution failed: ${context.executionId}`, error.stack);
        const duration = Date.now() - context.startTime;
        await this.prisma.workflowExecution.update({
            where: { id: context.executionId },
            data: {
                status: client_1.ExecutionStatus.FAILED,
                completedAt: new Date(),
                duration,
                error: error.message,
            },
        });
        await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'workflow_failed', {
            executionId: context.executionId,
            error: error.message,
            duration,
            completedNodes: Array.from(context.completedNodes),
            failedNodes: Array.from(context.failedNodes),
        });
        this.activeExecutions.delete(context.executionId);
        this.eventEmitter.emit('workflow.failed', {
            executionId: context.executionId,
            workflowId: context.workflowId,
            organizationId: context.organizationId,
            error: error.message,
        });
    }
    async cancelExecution(executionId, organizationId) {
        const context = this.activeExecutions.get(executionId);
        if (!context) {
            throw new Error('Execution not found or not active');
        }
        this.logger.log(`Cancelling workflow execution: ${executionId}`);
        await this.updateExecutionStatus(executionId, client_1.ExecutionStatus.CANCELLED);
        await this.apixGateway.emitWorkflowEvent(organizationId, context.workflowId, 'workflow_cancelled', {
            executionId,
            cancelledAt: Date.now(),
        });
        this.activeExecutions.delete(executionId);
    }
    async pauseExecution(executionId, organizationId) {
        const context = this.activeExecutions.get(executionId);
        if (!context) {
            throw new Error('Execution not found or not active');
        }
        this.logger.log(`Pausing workflow execution: ${executionId}`);
        await this.updateExecutionStatus(executionId, client_1.ExecutionStatus.PAUSED);
        await this.apixGateway.emitWorkflowEvent(organizationId, context.workflowId, 'workflow_paused', {
            executionId,
            pausedAt: Date.now(),
        });
    }
    async resumeExecution(executionId, organizationId) {
        const context = this.activeExecutions.get(executionId);
        if (!context) {
            throw new Error('Execution not found or not active');
        }
        this.logger.log(`Resuming workflow execution: ${executionId}`);
        await this.updateExecutionStatus(executionId, client_1.ExecutionStatus.RUNNING);
        await this.apixGateway.emitWorkflowEvent(organizationId, context.workflowId, 'workflow_resumed', {
            executionId,
            resumedAt: Date.now(),
        });
        const workflow = await this.prisma.workflow.findUnique({
            where: { id: context.workflowId },
        });
        if (workflow) {
            const definition = workflow.definition;
            await this.continueExecution(context, definition);
        }
    }
    async getExecutionStatus(executionId, organizationId) {
        const execution = await this.prisma.workflowExecution.findFirst({
            where: {
                id: executionId,
                workflow: { organizationId },
            },
            include: {
                steps: {
                    orderBy: { startedAt: 'asc' },
                },
            },
        });
        if (!execution) {
            throw new Error('Execution not found');
        }
        const context = this.activeExecutions.get(executionId);
        return {
            ...execution,
            isActive: !!context,
            currentNode: context?.currentNode,
            completedNodes: context ? Array.from(context.completedNodes) : [],
            failedNodes: context ? Array.from(context.failedNodes) : [],
            variables: context?.variables || execution.variables,
        };
    }
    async updateExecutionStatus(executionId, status) {
        await this.prisma.workflowExecution.update({
            where: { id: executionId },
            data: { status },
        });
    }
    getActiveExecutions() {
        return Array.from(this.activeExecutions.values());
    }
    getHealthStatus() {
        return {
            activeExecutions: this.activeExecutions.size,
            status: 'healthy',
            uptime: process.uptime(),
        };
    }
};
exports.FlowControllerService = FlowControllerService;
exports.FlowControllerService = FlowControllerService = FlowControllerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        sessions_service_1.SessionsService,
        node_executor_service_1.NodeExecutorService,
        variable_resolver_service_1.VariableResolverService,
        event_emitter_1.EventEmitter2])
], FlowControllerService);
//# sourceMappingURL=flow-controller.service.js.map