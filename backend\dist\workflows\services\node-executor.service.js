"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NodeExecutorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const agents_service_1 = require("../../agents/agents.service");
const tools_service_1 = require("../../tools/tools.service");
let NodeExecutorService = NodeExecutorService_1 = class NodeExecutorService {
    constructor(prisma, apixGateway, agentsService, toolsService) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.agentsService = agentsService;
        this.toolsService = toolsService;
        this.logger = new common_1.Logger(NodeExecutorService_1.name);
    }
    async executeNode(nodeType, config, variables, context) {
        this.logger.log(`Executing ${nodeType} node: ${context.nodeId}`);
        switch (nodeType) {
            case 'agent':
                return this.executeAgentNode(config, variables, context);
            case 'tool':
                return this.executeToolNode(config, variables, context);
            case 'condition':
                return this.executeConditionNode(config, variables, context);
            case 'parallel':
                return this.executeParallelNode(config, variables, context);
            case 'human_input':
                return this.executeHumanInputNode(config, variables, context);
            case 'delay':
                return this.executeDelayNode(config, variables, context);
            case 'hybrid':
                return this.executeHybridNode(config, variables, context);
            default:
                throw new Error(`Unknown node type: ${nodeType}`);
        }
    }
    async executeAgentNode(config, variables, context) {
        const { agentId, systemPrompt, maxTokens, temperature } = config;
        if (!agentId) {
            throw new Error('Agent ID is required');
        }
        const agent = await this.prisma.agent.findFirst({
            where: { id: agentId, organizationId: context.organizationId },
        });
        if (!agent) {
            throw new Error(`Agent not found: ${agentId}`);
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'agent_thinking', {
            nodeId: context.nodeId,
            agentId,
            status: 'processing',
            progress: 0,
        });
        try {
            const result = await this.agentsService.executeAgent(agentId, {
                input: variables.input || variables.message || 'Process the given data',
                systemPrompt: systemPrompt || agent.systemPrompt,
                maxTokens: maxTokens || 2000,
                temperature: temperature || 0.7,
                variables,
            }, context.organizationId);
            await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'agent_response', {
                nodeId: context.nodeId,
                agentId,
                response: result.response,
                usage: result.usage,
            });
            return {
                response: result.response,
                usage: result.usage,
                agentId,
            };
        }
        catch (error) {
            await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'agent_error', {
                nodeId: context.nodeId,
                agentId,
                error: error.message,
            });
            throw error;
        }
    }
    async executeToolNode(config, variables, context) {
        const { toolId, parameters, timeout } = config;
        if (!toolId) {
            throw new Error('Tool ID is required');
        }
        const tool = await this.prisma.tool.findFirst({
            where: { id: toolId, organizationId: context.organizationId },
        });
        if (!tool) {
            throw new Error(`Tool not found: ${toolId}`);
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'tool_call_start', {
            nodeId: context.nodeId,
            toolId,
            input: parameters,
        });
        try {
            const timeoutMs = timeout || 30000;
            const result = await Promise.race([
                this.toolsService.executeTool(toolId, { ...parameters, ...variables }, {
                    organizationId: context.organizationId,
                    executorType: 'workflow',
                    executorId: context.executionId,
                    sessionId: context.sessionId,
                }),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Tool execution timeout')), timeoutMs))
            ]);
            await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'tool_call_result', {
                nodeId: context.nodeId,
                toolId,
                result,
            });
            return result;
        }
        catch (error) {
            await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'tool_call_error', {
                nodeId: context.nodeId,
                toolId,
                error: error.message,
            });
            throw error;
        }
    }
    async executeConditionNode(config, variables, context) {
        const { condition, conditionType, truePath, falsePath } = config;
        if (!condition) {
            throw new Error('Condition is required');
        }
        let result;
        try {
            switch (conditionType) {
                case 'javascript':
                    const func = new Function('variables', `return ${condition}`);
                    result = Boolean(func(variables));
                    break;
                case 'jsonpath':
                    const jsonpath = require('jsonpath');
                    const value = jsonpath.query(variables, condition);
                    result = Boolean(value && value.length > 0);
                    break;
                case 'simple':
                default:
                    result = this.evaluateSimpleCondition(condition, variables);
                    break;
            }
            await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'condition_evaluated', {
                nodeId: context.nodeId,
                condition,
                result,
                nextPath: result ? truePath : falsePath,
            });
            return {
                conditionResult: result,
                nextPath: result ? truePath : falsePath,
            };
        }
        catch (error) {
            throw new Error(`Condition evaluation failed: ${error.message}`);
        }
    }
    evaluateSimpleCondition(condition, variables) {
        const match = condition.match(/\$\{([^}]+)\}\s*(==|!=|>|<|>=|<=)\s*(.+)/);
        if (!match) {
            throw new Error('Invalid simple condition format');
        }
        const [, variablePath, operator, valueStr] = match;
        const variableValue = this.getNestedValue(variables, variablePath);
        let compareValue = valueStr.trim();
        if (compareValue.startsWith('"') && compareValue.endsWith('"')) {
            compareValue = compareValue.slice(1, -1);
        }
        else if (compareValue === 'true') {
            compareValue = true;
        }
        else if (compareValue === 'false') {
            compareValue = false;
        }
        else if (!isNaN(Number(compareValue))) {
            compareValue = Number(compareValue);
        }
        switch (operator) {
            case '==': return variableValue == compareValue;
            case '!=': return variableValue != compareValue;
            case '>': return variableValue > compareValue;
            case '<': return variableValue < compareValue;
            case '>=': return variableValue >= compareValue;
            case '<=': return variableValue <= compareValue;
            default: throw new Error(`Unknown operator: ${operator}`);
        }
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    async executeParallelNode(config, variables, context) {
        const { nodes, executionMode, aggregateResults } = config;
        if (!nodes || nodes.length === 0) {
            throw new Error('Parallel node requires at least one sub-node');
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'parallel_started', {
            nodeId: context.nodeId,
            nodeCount: nodes.length,
            executionMode,
        });
        const enabledNodes = nodes.filter((node) => node.enabled);
        const promises = enabledNodes.map(async (node) => {
            try {
                const result = await this.executeNode(node.type || 'tool', node.config || {}, variables, { ...context, nodeId: node.id });
                return { nodeId: node.id, result, success: true };
            }
            catch (error) {
                return { nodeId: node.id, error: error.message, success: false };
            }
        });
        let results;
        switch (executionMode) {
            case 'all':
                results = await Promise.all(promises);
                break;
            case 'race':
                const firstResult = await Promise.race(promises);
                results = [firstResult];
                break;
            case 'some':
                const someCount = config.someCount || Math.ceil(enabledNodes.length / 2);
                results = await this.waitForSome(promises, someCount);
                break;
            default:
                results = await Promise.all(promises);
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'parallel_completed', {
            nodeId: context.nodeId,
            results: results.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
        });
        if (aggregateResults) {
            return this.aggregateParallelResults(results, config.aggregationStrategy || 'merge');
        }
        return results;
    }
    async waitForSome(promises, count) {
        const results = [];
        const settled = promises.map(async (promise, index) => {
            try {
                const result = await promise;
                return { index, result, success: true };
            }
            catch (error) {
                return { index, error, success: false };
            }
        });
        for await (const result of settled) {
            results.push(result);
            if (results.filter(r => r.success).length >= count) {
                break;
            }
        }
        return results;
    }
    aggregateParallelResults(results, strategy) {
        const successfulResults = results.filter(r => r.success).map(r => r.result);
        switch (strategy) {
            case 'merge':
                return successfulResults.reduce((acc, result) => ({ ...acc, ...result }), {});
            case 'array':
                return successfulResults;
            case 'custom':
                return { results: successfulResults, count: successfulResults.length };
            default:
                return { results: successfulResults, count: successfulResults.length };
        }
    }
    async executeHumanInputNode(config, variables, context) {
        const { prompt, inputType, timeout, allowSkip } = config;
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'request_user_input', {
            nodeId: context.nodeId,
            prompt,
            inputType,
            timeout: timeout || 300000,
            allowSkip: allowSkip || false,
        });
        const humanInput = await this.prisma.workflowHumanInput.create({
            data: {
                executionId: context.executionId,
                nodeId: context.nodeId,
                prompt,
                inputType,
                status: 'PENDING',
                expiresAt: new Date(Date.now() + (timeout || 300000)),
            },
        });
        return new Promise((resolve, reject) => {
            const checkInterval = setInterval(async () => {
                const updated = await this.prisma.workflowHumanInput.findUnique({
                    where: { id: humanInput.id },
                });
                if (updated?.status === 'COMPLETED') {
                    clearInterval(checkInterval);
                    resolve({
                        userInput: updated.response,
                        inputType: updated.inputType,
                        completedAt: updated.completedAt,
                    });
                }
                else if (updated?.status === 'EXPIRED' || updated?.status === 'CANCELLED') {
                    clearInterval(checkInterval);
                    if (allowSkip) {
                        resolve({ userInput: config.skipValue || null, skipped: true });
                    }
                    else {
                        reject(new Error('Human input timeout or cancelled'));
                    }
                }
            }, 1000);
            setTimeout(() => {
                clearInterval(checkInterval);
                if (allowSkip) {
                    resolve({ userInput: config.skipValue || null, skipped: true });
                }
                else {
                    reject(new Error('Human input timeout'));
                }
            }, timeout || 300000);
        });
    }
    async executeDelayNode(config, variables, context) {
        let { delay, unit, dynamic, variableName } = config;
        let delayMs;
        if (dynamic && variableName) {
            delayMs = variables[variableName] || delay || 1000;
        }
        else {
            switch (unit) {
                case 'seconds':
                    delayMs = delay * 1000;
                    break;
                case 'minutes':
                    delayMs = delay * 60 * 1000;
                    break;
                default:
                    delayMs = delay;
            }
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'delay_started', {
            nodeId: context.nodeId,
            delayMs,
            unit,
        });
        await new Promise(resolve => setTimeout(resolve, delayMs));
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'delay_completed', {
            nodeId: context.nodeId,
            actualDelay: delayMs,
        });
        return {
            delayCompleted: true,
            actualDelay: delayMs,
            unit,
        };
    }
    async executeHybridNode(config, variables, context) {
        const { agentId, toolIds, executionPattern, maxIterations } = config;
        if (!agentId || !toolIds || toolIds.length === 0) {
            throw new Error('Hybrid node requires both agent and tools');
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'hybrid_started', {
            nodeId: context.nodeId,
            agentId,
            toolIds,
            executionPattern,
        });
        let result;
        switch (executionPattern) {
            case 'agent-first':
                result = await this.executeAgentFirstPattern(agentId, toolIds, variables, context);
                break;
            case 'tool-first':
                result = await this.executeToolFirstPattern(agentId, toolIds, variables, context);
                break;
            case 'parallel':
                result = await this.executeParallelPattern(agentId, toolIds, variables, context);
                break;
            case 'multi-tool-orchestration':
                result = await this.executeOrchestrationPattern(agentId, toolIds, variables, context, maxIterations || 5);
                break;
            default:
                throw new Error(`Unknown execution pattern: ${executionPattern}`);
        }
        await this.apixGateway.emitToRoom(`workflow:${context.executionId}`, 'hybrid_completed', {
            nodeId: context.nodeId,
            result,
            pattern: executionPattern,
        });
        return result;
    }
    async executeAgentFirstPattern(agentId, toolIds, variables, context) {
        const agentResult = await this.executeAgentNode({ agentId }, variables, context);
        const toolResults = [];
        for (const toolId of toolIds) {
            try {
                const toolResult = await this.executeToolNode({ toolId, parameters: agentResult }, { ...variables, ...agentResult }, context);
                toolResults.push({ toolId, result: toolResult, success: true });
            }
            catch (error) {
                toolResults.push({ toolId, error: error.message, success: false });
            }
        }
        return {
            agentResult,
            toolResults,
            pattern: 'agent-first',
        };
    }
    async executeToolFirstPattern(agentId, toolIds, variables, context) {
        const toolResults = [];
        for (const toolId of toolIds) {
            try {
                const toolResult = await this.executeToolNode({ toolId, parameters: variables }, variables, context);
                toolResults.push({ toolId, result: toolResult, success: true });
            }
            catch (error) {
                toolResults.push({ toolId, error: error.message, success: false });
            }
        }
        const aggregatedData = toolResults
            .filter(r => r.success)
            .reduce((acc, r) => ({ ...acc, ...r.result }), {});
        const agentResult = await this.executeAgentNode({ agentId }, { ...variables, toolResults: aggregatedData }, context);
        return {
            toolResults,
            agentResult,
            pattern: 'tool-first',
        };
    }
    async executeParallelPattern(agentId, toolIds, variables, context) {
        const promises = [
            this.executeAgentNode({ agentId }, variables, context),
            ...toolIds.map(toolId => this.executeToolNode({ toolId, parameters: variables }, variables, context)),
        ];
        const results = await Promise.allSettled(promises);
        const agentResult = results[0].status === 'fulfilled' ? results[0].value : null;
        const toolResults = results.slice(1).map((result, index) => ({
            toolId: toolIds[index],
            result: result.status === 'fulfilled' ? result.value : null,
            error: result.status === 'rejected' ? result.reason.message : null,
            success: result.status === 'fulfilled',
        }));
        return {
            agentResult,
            toolResults,
            pattern: 'parallel',
        };
    }
    async executeOrchestrationPattern(agentId, toolIds, variables, context, maxIterations) {
        let currentVariables = { ...variables };
        const executionHistory = [];
        for (let iteration = 0; iteration < maxIterations; iteration++) {
            const agentResult = await this.executeAgentNode({
                agentId,
                systemPrompt: `You are orchestrating tools. Available tools: ${toolIds.join(', ')}. 
                        Current data: ${JSON.stringify(currentVariables)}. 
                        Decide which tool to use next or if the task is complete.`
            }, currentVariables, context);
            executionHistory.push({
                iteration,
                type: 'agent',
                result: agentResult,
            });
            const decision = this.parseAgentDecision(agentResult.response, toolIds);
            if (decision.complete) {
                break;
            }
            if (decision.toolId) {
                try {
                    const toolResult = await this.executeToolNode({ toolId: decision.toolId, parameters: decision.parameters || {} }, currentVariables, context);
                    executionHistory.push({
                        iteration,
                        type: 'tool',
                        toolId: decision.toolId,
                        result: toolResult,
                    });
                    currentVariables = { ...currentVariables, ...toolResult };
                }
                catch (error) {
                    executionHistory.push({
                        iteration,
                        type: 'tool',
                        toolId: decision.toolId,
                        error: error.message,
                    });
                }
            }
        }
        return {
            finalResult: currentVariables,
            executionHistory,
            iterations: executionHistory.length,
            pattern: 'multi-tool-orchestration',
        };
    }
    parseAgentDecision(response, availableTools) {
        const lowerResponse = response.toLowerCase();
        if (lowerResponse.includes('complete') || lowerResponse.includes('done') || lowerResponse.includes('finished')) {
            return { complete: true };
        }
        for (const toolId of availableTools) {
            if (lowerResponse.includes(toolId.toLowerCase())) {
                return {
                    complete: false,
                    toolId,
                    parameters: {},
                };
            }
        }
        return { complete: true };
    }
};
exports.NodeExecutorService = NodeExecutorService;
exports.NodeExecutorService = NodeExecutorService = NodeExecutorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        agents_service_1.AgentsService,
        tools_service_1.ToolsService])
], NodeExecutorService);
//# sourceMappingURL=node-executor.service.js.map