#!/usr/bin/env tsx

/**
 * 🚀 SynapseAI Production Setup Script
 * 
 * This script handles the complete setup of the SynapseAI platform:
 * - Environment validation
 * - Database initialization
 * - Prisma generation and migration
 * - Database seeding
 * - Health checks
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile, access } from 'fs/promises';
import { join } from 'path';

const execAsync = promisify(exec);

interface SetupConfig {
    skipSeed?: boolean;
    force?: boolean;
    verbose?: boolean;
    environment?: 'development' | 'production' | 'test';
}

class SynapseAISetup {
    private config: SetupConfig;
    private startTime: number;

    constructor(config: SetupConfig = {}) {
        this.config = {
            skipSeed: false,
            force: false,
            verbose: false,
            environment: 'development',
            ...config,
        };
        this.startTime = Date.now();
    }

    private log(message: string, level: 'info' | 'success' | 'error' | 'warn' = 'info') {
        const icons = { info: '🔍', success: '✅', error: '❌', warn: '⚠️' };
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${icons[level]} [${timestamp}] ${message}`);
    }

    private async runCommand(command: string, description: string): Promise<void> {
        this.log(`Running: ${description}...`);

        try {
            const { stdout, stderr } = await execAsync(command);

            if (this.config.verbose && stdout) {
                console.log(stdout);
            }

            if (stderr && !stderr.includes('warning')) {
                this.log(`Warning: ${stderr}`, 'warn');
            }

            this.log(`${description} completed`, 'success');
        } catch (error: any) {
            this.log(`${description} failed: ${error.message}`, 'error');
            throw error;
        }
    }

    private async validateEnvironment(): Promise<void> {
        this.log('🔍 Validating environment...');

        // Check for required environment variables
        const requiredEnvVars = [
            'DATABASE_URL',
            'JWT_SECRET',
            'JWT_REFRESH_SECRET',
        ];

        const missingVars: string[] = [];

        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                missingVars.push(envVar);
            }
        }

        if (missingVars.length > 0) {
            this.log(`Missing required environment variables: ${missingVars.join(', ')}`, 'error');
            this.log('Please copy .env.example to .env and configure the values', 'warn');
            throw new Error('Environment validation failed');
        }

        // Validate JWT secrets strength
        const jwtSecret = process.env.JWT_SECRET!;
        if (jwtSecret.includes('change-me') || jwtSecret.length < 32) {
            this.log('JWT_SECRET appears to be using default/weak value', 'warn');
            this.log('Please generate a strong, unique JWT secret for production', 'warn');
        }

        this.log('Environment validation passed', 'success');
    }

    private async checkDatabaseConnection(): Promise<void> {
        this.log('🔌 Testing database connection...');

        try {
            await this.runCommand(
                'npx prisma db execute --command "SELECT 1" --schema prisma/schema.prisma',
                'Database connection test'
            );
        } catch (error) {
            this.log('Database connection failed. Attempting to create database...', 'warn');

            try {
                await this.runCommand(
                    'npx prisma db push --force-reset --schema prisma/schema.prisma',
                    'Database creation'
                );
            } catch (createError) {
                this.log('Failed to create database. Please check your DATABASE_URL', 'error');
                throw createError;
            }
        }
    }

    private async setupPrisma(): Promise<void> {
        this.log('🗃️ Setting up Prisma...');

        // Generate Prisma client
        await this.runCommand(
            'npx prisma generate --schema prisma/schema.prisma',
            'Prisma client generation'
        );

        // Run migrations
        if (this.config.force) {
            await this.runCommand(
                'npx prisma migrate reset --force --schema prisma/schema.prisma',
                'Database reset and migration'
            );
        } else {
            await this.runCommand(
                'npx prisma migrate deploy --schema prisma/schema.prisma',
                'Database migration'
            );
        }

        this.log('Prisma setup completed', 'success');
    }

    private async seedDatabase(): Promise<void> {
        if (this.config.skipSeed) {
            this.log('⏭️ Skipping database seeding (--skip-seed flag)');
            return;
        }

        this.log('🌱 Seeding database with initial data...');

        await this.runCommand(
            'npx tsx prisma/seed.ts',
            'Database seeding'
        );
    }

    private async validateSetup(): Promise<void> {
        this.log('🔍 Validating setup...');

        try {
            // Check if we can query the database
            await this.runCommand(
                'npx prisma db execute --command "SELECT COUNT(*) FROM organizations" --schema prisma/schema.prisma',
                'Database validation'
            );

            // Check if seeded data exists
            await this.runCommand(
                'npx prisma db execute --command "SELECT COUNT(*) FROM users" --schema prisma/schema.prisma',
                'Seed data validation'
            );

            this.log('Setup validation passed', 'success');
        } catch (error) {
            this.log('Setup validation failed', 'error');
            throw error;
        }
    }

    public async run(): Promise<void> {
        const steps = [
            'Environment Validation',
            'Database Connection',
            'Prisma Setup',
            'Database Seeding',
            'Setup Validation',
        ];

        console.log('🚀 SynapseAI Production Setup');
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        console.log(`Environment: ${this.config.environment}`);
        console.log(`Steps: ${steps.join(' → ')}`);
        console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

        try {
            await this.validateEnvironment();
            await this.checkDatabaseConnection();
            await this.setupPrisma();
            await this.seedDatabase();
            await this.validateSetup();

            const duration = Math.round((Date.now() - this.startTime) / 1000);

            console.log('\n🎉 Setup completed successfully!');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`⏱️  Total time: ${duration}s`);
            console.log('🔧 Your SynapseAI platform is ready!');
            console.log('\n🚀 Next steps:');
            console.log('1. Start the backend: npm run dev');
            console.log('2. Start the frontend: npm run dev (in root directory)');
            console.log('3. Visit http://localhost:3000');
            console.log('4. Login with the seeded credentials');
            console.log('\n📚 Documentation: https://docs.synapseai.com');
            console.log('🆘 Support: https://github.com/synapseai/platform/issues');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

        } catch (error) {
            const duration = Math.round((Date.now() - this.startTime) / 1000);

            console.log('\n💥 Setup failed!');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`⏱️  Time elapsed: ${duration}s`);
            console.log(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            console.log('\n🔧 Troubleshooting:');
            console.log('1. Check your .env file configuration');
            console.log('2. Ensure PostgreSQL is running and accessible');
            console.log('3. Verify database credentials and permissions');
            console.log('4. Check the logs above for specific errors');
            console.log('\n📚 Documentation: https://docs.synapseai.com/setup');
            console.log('🆘 Support: https://github.com/synapseai/platform/issues');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

            process.exit(1);
        }
    }
}

// CLI argument parsing
const args = process.argv.slice(2);
const config: SetupConfig = {};

for (const arg of args) {
    switch (arg) {
        case '--skip-seed':
            config.skipSeed = true;
            break;
        case '--force':
            config.force = true;
            break;
        case '--verbose':
            config.verbose = true;
            break;
        case '--production':
            config.environment = 'production';
            break;
        case '--test':
            config.environment = 'test';
            break;
        case '--help':
            console.log(`
🚀 SynapseAI Setup Script

Usage: npm run setup [options]

Options:
  --skip-seed     Skip database seeding
  --force         Force reset database (destructive)
  --verbose       Show detailed output
  --production    Run in production mode
  --test          Run in test mode
  --help          Show this help message

Examples:
  npm run setup                    # Standard setup
  npm run setup -- --skip-seed    # Setup without seeding
  npm run setup -- --force        # Reset and setup from scratch
  npm run setup -- --production   # Production setup
      `);
            process.exit(0);
    }
}

// Set environment
if (config.environment === 'production') {
    process.env.NODE_ENV = 'production';
} else if (config.environment === 'test') {
    process.env.NODE_ENV = 'test';
}

// Run setup
const setup = new SynapseAISetup(config);
setup.run();