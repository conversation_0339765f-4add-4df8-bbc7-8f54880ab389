import { z } from 'zod';
import { PrismaService } from '../../prisma/prisma.service';
export interface SchemaField {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum' | 'union';
    description?: string;
    required: boolean;
    default?: any;
    validation?: {
        min?: number;
        max?: number;
        pattern?: string;
        options?: any[];
        format?: string;
    };
    properties?: Record<string, SchemaField>;
    items?: SchemaField;
}
export interface GeneratedSchema {
    id: string;
    nodeType: 'hybrid' | 'agent' | 'tool';
    componentId: string;
    schemaType: 'input' | 'output' | 'config';
    zodSchema: z.ZodSchema;
    jsonSchema: any;
    uiSchema: any;
    fields: SchemaField[];
    metadata: {
        version: string;
        generatedAt: Date;
        capabilities?: string[];
        dependencies?: string[];
    };
}
export declare class HybridSchemaGeneratorService {
    private prisma;
    private readonly logger;
    private schemaCache;
    constructor(prisma: PrismaService);
    generateHybridNodeSchema(agentId: string, toolIds: string[], executionPattern: string, organizationId: string): Promise<{
        inputSchema: GeneratedSchema;
        outputSchema: GeneratedSchema;
        configSchema: GeneratedSchema;
    }>;
    generateAgentSchema(agentId: string, organizationId: string): Promise<{
        inputSchema: GeneratedSchema;
        outputSchema: GeneratedSchema;
    }>;
    generateToolSchema(toolId: string, organizationId: string): Promise<{
        inputSchema: GeneratedSchema;
        outputSchema: GeneratedSchema;
    }>;
    private generateInputSchema;
    private generateOutputSchema;
    private generateConfigSchema;
    private getAgentDefinition;
    private getToolDefinitions;
    private getToolDefinition;
    private extractAgentInputFields;
    private extractAgentOutputFields;
    private extractToolInputFields;
    private extractToolOutputFields;
    private combineToolInputFields;
    private generateToolConfigFields;
    private createAgentInputSchema;
    private createAgentOutputSchema;
    private createToolInputSchema;
    private createToolOutputSchema;
    private createZodSchemaFromFields;
    private zodToJsonSchema;
    private generateUISchema;
    private jsonSchemaToFields;
    private jsonSchemaToFieldArray;
    private jsonTypeToSchemaType;
    private extractValidation;
    getSchema(schemaId: string): Promise<GeneratedSchema | null>;
    validateData(schemaId: string, data: any): Promise<{
        valid: boolean;
        errors?: any[];
    }>;
    clearCache(): void;
    getCacheStats(): {
        size: number;
        keys: string[];
    };
}
