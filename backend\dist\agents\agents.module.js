"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsModule = void 0;
const common_1 = require("@nestjs/common");
const agents_controller_1 = require("./agents.controller");
const agents_service_1 = require("./agents.service");
const prisma_module_1 = require("../prisma/prisma.module");
const apix_module_1 = require("../apix/apix.module");
const sessions_module_1 = require("../sessions/sessions.module");
const agent_orchestrator_service_1 = require("./services/agent-orchestrator.service");
const session_memory_service_1 = require("./services/session-memory.service");
const skill_executor_service_1 = require("./services/skill-executor.service");
const task_tracker_service_1 = require("./services/task-tracker.service");
const agent_communication_service_1 = require("./services/agent-communication.service");
const agent_template_service_1 = require("./services/agent-template.service");
const provider_router_service_1 = require("./services/provider-router.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const cache_manager_1 = require("@nestjs/cache-manager");
let AgentsModule = class AgentsModule {
};
exports.AgentsModule = AgentsModule;
exports.AgentsModule = AgentsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            apix_module_1.ApixModule,
            sessions_module_1.SessionsModule,
            event_emitter_1.EventEmitterModule,
            cache_manager_1.CacheModule.register({
                ttl: 300,
                max: 1000,
            }),
        ],
        controllers: [agents_controller_1.AgentsController],
        providers: [
            agents_service_1.AgentsService,
            agent_orchestrator_service_1.AgentOrchestratorService,
            session_memory_service_1.SessionMemoryService,
            skill_executor_service_1.SkillExecutorService,
            task_tracker_service_1.TaskTrackerService,
            agent_communication_service_1.AgentCommunicationService,
            agent_template_service_1.AgentTemplateService,
            provider_router_service_1.ProviderRouterService,
        ],
        exports: [
            agents_service_1.AgentsService,
            agent_orchestrator_service_1.AgentOrchestratorService,
            session_memory_service_1.SessionMemoryService,
            skill_executor_service_1.SkillExecutorService,
            task_tracker_service_1.TaskTrackerService,
            agent_communication_service_1.AgentCommunicationService,
            agent_template_service_1.AgentTemplateService,
            provider_router_service_1.ProviderRouterService,
        ],
    })
], AgentsModule);
//# sourceMappingURL=agents.module.js.map