import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { PrismaService } from '../../prisma/prisma.service';

export interface SchemaField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum' | 'union';
  description?: string;
  required: boolean;
  default?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: any[];
    format?: string;
  };
  properties?: Record<string, SchemaField>; // For object types
  items?: SchemaField; // For array types
}

export interface GeneratedSchema {
  id: string;
  nodeType: 'hybrid' | 'agent' | 'tool';
  componentId: string;
  schemaType: 'input' | 'output' | 'config';
  zodSchema: z.ZodSchema;
  jsonSchema: any;
  uiSchema: any;
  fields: SchemaField[];
  metadata: {
    version: string;
    generatedAt: Date;
    capabilities?: string[];
    dependencies?: string[];
  };
}

@Injectable()
export class HybridSchemaGeneratorService {
  private readonly logger = new Logger(HybridSchemaGeneratorService.name);
  private schemaCache = new Map<string, GeneratedSchema>();

  constructor(private prisma: PrismaService) {}

  async generateHybridNodeSchema(
    agentId: string,
    toolIds: string[],
    executionPattern: string,
    organizationId: string,
  ): Promise<{
    inputSchema: GeneratedSchema;
    outputSchema: GeneratedSchema;
    configSchema: GeneratedSchema;
  }> {
    try {
      this.logger.log(`Generating hybrid node schema for agent: ${agentId}, tools: ${toolIds.join(', ')}`);

      // Get agent and tool definitions
      const [agent, tools] = await Promise.all([
        this.getAgentDefinition(agentId, organizationId),
        this.getToolDefinitions(toolIds, organizationId),
      ]);

      // Generate schemas based on execution pattern
      const inputSchema = await this.generateInputSchema(agent, tools, executionPattern);
      const outputSchema = await this.generateOutputSchema(agent, tools, executionPattern);
      const configSchema = await this.generateConfigSchema(agent, tools, executionPattern);

      // Cache the schemas
      const cacheKey = `hybrid_${agentId}_${toolIds.join('_')}_${executionPattern}`;
      this.schemaCache.set(`${cacheKey}_input`, inputSchema);
      this.schemaCache.set(`${cacheKey}_output`, outputSchema);
      this.schemaCache.set(`${cacheKey}_config`, configSchema);

      return { inputSchema, outputSchema, configSchema };

    } catch (error) {
      this.logger.error(`Failed to generate hybrid node schema: ${error.message}`, error.stack);
      throw error;
    }
  }

  async generateAgentSchema(agentId: string, organizationId: string): Promise<{
    inputSchema: GeneratedSchema;
    outputSchema: GeneratedSchema;
  }> {
    try {
      const agent = await this.getAgentDefinition(agentId, organizationId);
      
      const inputSchema = this.createAgentInputSchema(agent);
      const outputSchema = this.createAgentOutputSchema(agent);

      return { inputSchema, outputSchema };

    } catch (error) {
      this.logger.error(`Failed to generate agent schema: ${error.message}`, error.stack);
      throw error;
    }
  }

  async generateToolSchema(toolId: string, organizationId: string): Promise<{
    inputSchema: GeneratedSchema;
    outputSchema: GeneratedSchema;
  }> {
    try {
      const tool = await this.getToolDefinition(toolId, organizationId);
      
      const inputSchema = this.createToolInputSchema(tool);
      const outputSchema = this.createToolOutputSchema(tool);

      return { inputSchema, outputSchema };

    } catch (error) {
      this.logger.error(`Failed to generate tool schema: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async generateInputSchema(
    agent: any,
    tools: any[],
    executionPattern: string,
  ): Promise<GeneratedSchema> {
    const fields: SchemaField[] = [];
    
    // Base input fields
    fields.push({
      name: 'message',
      type: 'string',
      description: 'Primary input message or query',
      required: true,
    });

    // Pattern-specific fields
    switch (executionPattern) {
      case 'agent-first':
        fields.push({
          name: 'agentContext',
          type: 'object',
          description: 'Additional context for the agent',
          required: false,
          properties: this.extractAgentInputFields(agent),
        });
        break;

      case 'tool-first':
        // Add tool-specific input fields
        tools.forEach(tool => {
          const toolFields = this.extractToolInputFields(tool);
          fields.push({
            name: `${tool.name}_params`,
            type: 'object',
            description: `Parameters for ${tool.name}`,
            required: false,
            properties: toolFields,
          });
        });
        break;

      case 'parallel':
        fields.push({
          name: 'agentContext',
          type: 'object',
          description: 'Context for parallel agent execution',
          required: false,
          properties: this.extractAgentInputFields(agent),
        });
        
        fields.push({
          name: 'toolParameters',
          type: 'object',
          description: 'Parameters for parallel tool execution',
          required: false,
          properties: this.combineToolInputFields(tools),
        });
        break;

      case 'multi-tool-orchestration':
        fields.push({
          name: 'orchestrationGoal',
          type: 'string',
          description: 'High-level goal for the orchestration',
          required: true,
        });
        
        fields.push({
          name: 'availableData',
          type: 'object',
          description: 'Initial data available for orchestration',
          required: false,
        });
        break;
    }

    // Common fields
    fields.push(
      {
        name: 'variables',
        type: 'object',
        description: 'Additional variables and context',
        required: false,
      },
      {
        name: 'metadata',
        type: 'object',
        description: 'Execution metadata',
        required: false,
      }
    );

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `hybrid_input_${Date.now()}`,
      nodeType: 'hybrid',
      componentId: `${agent.id}_${tools.map(t => t.id).join('_')}`,
      schemaType: 'input',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: [executionPattern, ...tools.map(t => t.type)],
        dependencies: [agent.id, ...tools.map(t => t.id)],
      },
    };
  }

  private async generateOutputSchema(
    agent: any,
    tools: any[],
    executionPattern: string,
  ): Promise<GeneratedSchema> {
    const fields: SchemaField[] = [];

    // Pattern-specific output fields
    switch (executionPattern) {
      case 'agent-first':
        fields.push(
          {
            name: 'agentResult',
            type: 'object',
            description: 'Result from agent execution',
            required: true,
            properties: this.extractAgentOutputFields(agent),
          },
          {
            name: 'toolResults',
            type: 'array',
            description: 'Results from tool executions',
            required: true,
            items: {
              name: 'toolResult',
              type: 'object',
              required: true,
              properties: {
                toolId: { name: 'toolId', type: 'string', required: true },
                result: { name: 'result', type: 'object', required: true },
                success: { name: 'success', type: 'boolean', required: true },
              },
            },
          }
        );
        break;

      case 'tool-first':
        fields.push(
          {
            name: 'toolResults',
            type: 'array',
            description: 'Results from initial tool executions',
            required: true,
            items: {
              name: 'toolResult',
              type: 'object',
              required: true,
              properties: {
                toolId: { name: 'toolId', type: 'string', required: true },
                result: { name: 'result', type: 'object', required: true },
                success: { name: 'success', type: 'boolean', required: true },
              },
            },
          },
          {
            name: 'agentResult',
            type: 'object',
            description: 'Final result from agent processing',
            required: true,
            properties: this.extractAgentOutputFields(agent),
          }
        );
        break;

      case 'parallel':
        fields.push(
          {
            name: 'agentResult',
            type: 'object',
            description: 'Result from parallel agent execution',
            required: true,
            properties: this.extractAgentOutputFields(agent),
          },
          {
            name: 'toolResults',
            type: 'array',
            description: 'Results from parallel tool executions',
            required: true,
            items: {
              name: 'toolResult',
              type: 'object',
              required: true,
              properties: {
                toolId: { name: 'toolId', type: 'string', required: true },
                result: { name: 'result', type: 'object', required: true },
                success: { name: 'success', type: 'boolean', required: true },
              },
            },
          },
          {
            name: 'synchronizationPoints',
            type: 'array',
            description: 'Synchronization points reached during execution',
            required: false,
            items: { name: 'syncPoint', type: 'string', required: true },
          }
        );
        break;

      case 'multi-tool-orchestration':
        fields.push(
          {
            name: 'finalResult',
            type: 'object',
            description: 'Final orchestrated result',
            required: true,
          },
          {
            name: 'executionHistory',
            type: 'array',
            description: 'History of orchestration steps',
            required: true,
            items: {
              name: 'historyEntry',
              type: 'object',
              required: true,
              properties: {
                iteration: { name: 'iteration', type: 'number', required: true },
                type: { name: 'type', type: 'enum', required: true, validation: { options: ['agent', 'tool'] } },
                componentId: { name: 'componentId', type: 'string', required: true },
                result: { name: 'result', type: 'object', required: true },
              },
            },
          },
          {
            name: 'totalIterations',
            type: 'number',
            description: 'Total number of orchestration iterations',
            required: true,
          }
        );
        break;
    }

    // Common output fields
    fields.push(
      {
        name: 'pattern',
        type: 'string',
        description: 'Execution pattern used',
        required: true,
      },
      {
        name: 'executionTime',
        type: 'number',
        description: 'Total execution time in milliseconds',
        required: false,
      },
      {
        name: 'metadata',
        type: 'object',
        description: 'Execution metadata and metrics',
        required: false,
      },
      {
        name: 'timestamp',
        type: 'number',
        description: 'Execution completion timestamp',
        required: true,
      }
    );

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `hybrid_output_${Date.now()}`,
      nodeType: 'hybrid',
      componentId: `${agent.id}_${tools.map(t => t.id).join('_')}`,
      schemaType: 'output',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: [executionPattern, ...tools.map(t => t.type)],
        dependencies: [agent.id, ...tools.map(t => t.id)],
      },
    };
  }

  private async generateConfigSchema(
    agent: any,
    tools: any[],
    executionPattern: string,
  ): Promise<GeneratedSchema> {
    const fields: SchemaField[] = [
      {
        name: 'agentId',
        type: 'string',
        description: 'ID of the primary agent',
        required: true,
      },
      {
        name: 'toolIds',
        type: 'array',
        description: 'IDs of the tools to use',
        required: true,
        items: { name: 'toolId', type: 'string', required: true },
      },
      {
        name: 'executionPattern',
        type: 'enum',
        description: 'Execution pattern for the hybrid node',
        required: true,
        validation: {
          options: ['agent-first', 'tool-first', 'parallel', 'multi-tool-orchestration'],
        },
      },
      {
        name: 'maxIterations',
        type: 'number',
        description: 'Maximum number of iterations for orchestration',
        required: false,
        default: 5,
        validation: { min: 1, max: 20 },
      },
      {
        name: 'agentConfig',
        type: 'object',
        description: 'Configuration for the agent',
        required: true,
        properties: {
          systemPrompt: {
            name: 'systemPrompt',
            type: 'string',
            description: 'System prompt for the agent',
            required: false,
          },
          maxTokens: {
            name: 'maxTokens',
            type: 'number',
            description: 'Maximum tokens for agent response',
            required: true,
            default: 2000,
            validation: { min: 1, max: 8000 },
          },
          temperature: {
            name: 'temperature',
            type: 'number',
            description: 'Temperature for agent response generation',
            required: true,
            default: 0.7,
            validation: { min: 0, max: 2 },
          },
          contextWindow: {
            name: 'contextWindow',
            type: 'number',
            description: 'Context window size for the agent',
            required: true,
            default: 8000,
            validation: { min: 1000, max: 32000 },
          },
        },
      },
      {
        name: 'toolConfigs',
        type: 'object',
        description: 'Configuration for each tool',
        required: true,
        properties: this.generateToolConfigFields(tools),
      },
      {
        name: 'coordination',
        type: 'object',
        description: 'Coordination settings',
        required: true,
        properties: {
          shareContext: {
            name: 'shareContext',
            type: 'boolean',
            description: 'Whether to share context between components',
            required: true,
            default: true,
          },
          contextStrategy: {
            name: 'contextStrategy',
            type: 'enum',
            description: 'Strategy for context sharing',
            required: true,
            default: 'full',
            validation: { options: ['full', 'summary', 'selective'] },
          },
          syncPoints: {
            name: 'syncPoints',
            type: 'array',
            description: 'Synchronization points',
            required: false,
            items: { name: 'syncPoint', type: 'string', required: true },
          },
          errorPropagation: {
            name: 'errorPropagation',
            type: 'boolean',
            description: 'Whether to propagate errors between components',
            required: true,
            default: true,
          },
        },
      },
      {
        name: 'performance',
        type: 'object',
        description: 'Performance settings',
        required: true,
        properties: {
          parallelLimit: {
            name: 'parallelLimit',
            type: 'number',
            description: 'Maximum parallel executions',
            required: true,
            default: 3,
            validation: { min: 1, max: 10 },
          },
          timeout: {
            name: 'timeout',
            type: 'number',
            description: 'Overall timeout in milliseconds',
            required: true,
            default: 120000,
            validation: { min: 10000, max: 600000 },
          },
          memoryLimit: {
            name: 'memoryLimit',
            type: 'number',
            description: 'Memory limit in MB',
            required: true,
            default: 50,
            validation: { min: 1, max: 100 },
          },
        },
      },
      {
        name: 'fallback',
        type: 'object',
        description: 'Fallback configuration',
        required: true,
        properties: {
          enabled: {
            name: 'enabled',
            type: 'boolean',
            description: 'Whether fallback is enabled',
            required: true,
            default: true,
          },
          fallbackAgent: {
            name: 'fallbackAgent',
            type: 'string',
            description: 'ID of fallback agent',
            required: false,
          },
          fallbackTools: {
            name: 'fallbackTools',
            type: 'array',
            description: 'IDs of fallback tools',
            required: false,
            items: { name: 'toolId', type: 'string', required: true },
          },
          conditions: {
            name: 'conditions',
            type: 'array',
            description: 'Conditions that trigger fallback',
            required: true,
            default: ['timeout', 'error', 'low_confidence'],
            items: { name: 'condition', type: 'string', required: true },
          },
        },
      },
    ];

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `hybrid_config_${Date.now()}`,
      nodeType: 'hybrid',
      componentId: `${agent.id}_${tools.map(t => t.id).join('_')}`,
      schemaType: 'config',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: [executionPattern, ...tools.map(t => t.type)],
        dependencies: [agent.id, ...tools.map(t => t.id)],
      },
    };
  }

  private async getAgentDefinition(agentId: string, organizationId: string): Promise<any> {
    const agent = await this.prisma.agent.findFirst({
      where: { id: agentId, organizationId },
      include: { capabilities: true },
    });

    if (!agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    return agent;
  }

  private async getToolDefinitions(toolIds: string[], organizationId: string): Promise<any[]> {
    const tools = await this.prisma.tool.findMany({
      where: { id: { in: toolIds }, organizationId },
    });

    if (tools.length !== toolIds.length) {
      const foundIds = tools.map(t => t.id);
      const missingIds = toolIds.filter(id => !foundIds.includes(id));
      throw new Error(`Tools not found: ${missingIds.join(', ')}`);
    }

    return tools;
  }

  private async getToolDefinition(toolId: string, organizationId: string): Promise<any> {
    const tool = await this.prisma.tool.findFirst({
      where: { id: toolId, organizationId },
    });

    if (!tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    return tool;
  }

  private extractAgentInputFields(agent: any): Record<string, SchemaField> {
    const fields: Record<string, SchemaField> = {
      systemPrompt: {
        name: 'systemPrompt',
        type: 'string',
        description: 'System prompt override',
        required: false,
      },
      maxTokens: {
        name: 'maxTokens',
        type: 'number',
        description: 'Maximum tokens for response',
        required: false,
        validation: { min: 1, max: 8000 },
      },
      temperature: {
        name: 'temperature',
        type: 'number',
        description: 'Response generation temperature',
        required: false,
        validation: { min: 0, max: 2 },
      },
    };

    // Add agent-specific fields based on capabilities
    if (agent.capabilities) {
      agent.capabilities.forEach((capability: any) => {
        if (capability.inputSchema) {
          try {
            const schema = JSON.parse(capability.inputSchema);
            Object.assign(fields, this.jsonSchemaToFields(schema));
          } catch (error) {
            this.logger.warn(`Failed to parse agent capability schema: ${error.message}`);
          }
        }
      });
    }

    return fields;
  }

  private extractAgentOutputFields(agent: any): Record<string, SchemaField> {
    return {
      response: {
        name: 'response',
        type: 'string',
        description: 'Agent response text',
        required: true,
      },
      usage: {
        name: 'usage',
        type: 'object',
        description: 'Token usage information',
        required: false,
        properties: {
          promptTokens: { name: 'promptTokens', type: 'number', required: true },
          completionTokens: { name: 'completionTokens', type: 'number', required: true },
          totalTokens: { name: 'totalTokens', type: 'number', required: true },
        },
      },
      metadata: {
        name: 'metadata',
        type: 'object',
        description: 'Additional response metadata',
        required: false,
      },
    };
  }

  private extractToolInputFields(tool: any): Record<string, SchemaField> {
    const fields: Record<string, SchemaField> = {};

    // Parse tool schema if available
    if (tool.inputSchema) {
      try {
        const schema = JSON.parse(tool.inputSchema);
        Object.assign(fields, this.jsonSchemaToFields(schema));
      } catch (error) {
        this.logger.warn(`Failed to parse tool input schema: ${error.message}`);
        // Fallback to generic fields
        fields.parameters = {
          name: 'parameters',
          type: 'object',
          description: 'Tool parameters',
          required: false,
        };
      }
    } else {
      // Default tool input fields
      fields.parameters = {
        name: 'parameters',
        type: 'object',
        description: 'Tool parameters',
        required: false,
      };
    }

    return fields;
  }

  private extractToolOutputFields(tool: any): Record<string, SchemaField> {
    const fields: Record<string, SchemaField> = {};

    // Parse tool output schema if available
    if (tool.outputSchema) {
      try {
        const schema = JSON.parse(tool.outputSchema);
        Object.assign(fields, this.jsonSchemaToFields(schema));
      } catch (error) {
        this.logger.warn(`Failed to parse tool output schema: ${error.message}`);
        // Fallback to generic fields
        fields.result = {
          name: 'result',
          type: 'object',
          description: 'Tool execution result',
          required: true,
        };
      }
    } else {
      // Default tool output fields
      fields.result = {
        name: 'result',
        type: 'object',
        description: 'Tool execution result',
        required: true,
      };
    }

    return fields;
  }

  private combineToolInputFields(tools: any[]): Record<string, SchemaField> {
    const combined: Record<string, SchemaField> = {};

    tools.forEach(tool => {
      const toolFields = this.extractToolInputFields(tool);
      Object.entries(toolFields).forEach(([key, field]) => {
        combined[`${tool.name}_${key}`] = {
          ...field,
          name: `${tool.name}_${key}`,
          description: `${field.description} (for ${tool.name})`,
        };
      });
    });

    return combined;
  }

  private generateToolConfigFields(tools: any[]): Record<string, SchemaField> {
    const fields: Record<string, SchemaField> = {};

    tools.forEach(tool => {
      fields[tool.id] = {
        name: tool.id,
        type: 'object',
        description: `Configuration for ${tool.name}`,
        required: true,
        properties: {
          timeout: {
            name: 'timeout',
            type: 'number',
            description: 'Execution timeout in milliseconds',
            required: true,
            default: 30000,
            validation: { min: 1000, max: 300000 },
          },
          retryPolicy: {
            name: 'retryPolicy',
            type: 'object',
            description: 'Retry policy configuration',
            required: true,
            properties: {
              enabled: {
                name: 'enabled',
                type: 'boolean',
                description: 'Whether retry is enabled',
                required: true,
                default: true,
              },
              maxRetries: {
                name: 'maxRetries',
                type: 'number',
                description: 'Maximum number of retries',
                required: true,
                default: 2,
                validation: { min: 0, max: 5 },
              },
            },
          },
          parameters: {
            name: 'parameters',
            type: 'object',
            description: 'Default parameters for the tool',
            required: false,
          },
        },
      };
    });

    return fields;
  }

  private createAgentInputSchema(agent: any): GeneratedSchema {
    const fields: SchemaField[] = [
      {
        name: 'input',
        type: 'string',
        description: 'Input message for the agent',
        required: true,
      },
      {
        name: 'systemPrompt',
        type: 'string',
        description: 'System prompt override',
        required: false,
      },
      {
        name: 'maxTokens',
        type: 'number',
        description: 'Maximum tokens for response',
        required: false,
        default: 2000,
        validation: { min: 1, max: 8000 },
      },
      {
        name: 'temperature',
        type: 'number',
        description: 'Response generation temperature',
        required: false,
        default: 0.7,
        validation: { min: 0, max: 2 },
      },
      {
        name: 'variables',
        type: 'object',
        description: 'Additional variables',
        required: false,
      },
    ];

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `agent_input_${agent.id}`,
      nodeType: 'agent',
      componentId: agent.id,
      schemaType: 'input',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: agent.capabilities?.map((c: any) => c.name) || [],
        dependencies: [agent.id],
      },
    };
  }

  private createAgentOutputSchema(agent: any): GeneratedSchema {
    const fields: SchemaField[] = [
      {
        name: 'response',
        type: 'string',
        description: 'Agent response',
        required: true,
      },
      {
        name: 'usage',
        type: 'object',
        description: 'Token usage information',
        required: false,
        properties: {
          promptTokens: { name: 'promptTokens', type: 'number', required: true },
          completionTokens: { name: 'completionTokens', type: 'number', required: true },
          totalTokens: { name: 'totalTokens', type: 'number', required: true },
        },
      },
    ];

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `agent_output_${agent.id}`,
      nodeType: 'agent',
      componentId: agent.id,
      schemaType: 'output',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: agent.capabilities?.map((c: any) => c.name) || [],
        dependencies: [agent.id],
      },
    };
  }

  private createToolInputSchema(tool: any): GeneratedSchema {
    const fields: SchemaField[] = [];

    // Parse tool schema if available
    if (tool.inputSchema) {
      try {
        const schema = JSON.parse(tool.inputSchema);
        fields.push(...this.jsonSchemaToFieldArray(schema));
      } catch (error) {
        this.logger.warn(`Failed to parse tool input schema: ${error.message}`);
        fields.push({
          name: 'parameters',
          type: 'object',
          description: 'Tool parameters',
          required: false,
        });
      }
    } else {
      fields.push({
        name: 'parameters',
        type: 'object',
        description: 'Tool parameters',
        required: false,
      });
    }

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `tool_input_${tool.id}`,
      nodeType: 'tool',
      componentId: tool.id,
      schemaType: 'input',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: [tool.type],
        dependencies: [tool.id],
      },
    };
  }

  private createToolOutputSchema(tool: any): GeneratedSchema {
    const fields: SchemaField[] = [];

    // Parse tool output schema if available
    if (tool.outputSchema) {
      try {
        const schema = JSON.parse(tool.outputSchema);
        fields.push(...this.jsonSchemaToFieldArray(schema));
      } catch (error) {
        this.logger.warn(`Failed to parse tool output schema: ${error.message}`);
        fields.push({
          name: 'result',
          type: 'object',
          description: 'Tool execution result',
          required: true,
        });
      }
    } else {
      fields.push({
        name: 'result',
        type: 'object',
        description: 'Tool execution result',
        required: true,
      });
    }

    const zodSchema = this.createZodSchemaFromFields(fields);
    const jsonSchema = this.zodToJsonSchema(zodSchema);
    const uiSchema = this.generateUISchema(fields);

    return {
      id: `tool_output_${tool.id}`,
      nodeType: 'tool',
      componentId: tool.id,
      schemaType: 'output',
      zodSchema,
      jsonSchema,
      uiSchema,
      fields,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date(),
        capabilities: [tool.type],
        dependencies: [tool.id],
      },
    };
  }

  private createZodSchemaFromFields(fields: SchemaField[]): z.ZodSchema {
    const schemaObject: Record<string, z.ZodTypeAny> = {};

    fields.forEach(field => {
      let zodType: z.ZodTypeAny;

      switch (field.type) {
        case 'string':
          zodType = z.string();
          if (field.validation?.min) zodType = (zodType as z.ZodString).min(field.validation.min);
          if (field.validation?.max) zodType = (zodType as z.ZodString).max(field.validation.max);
          if (field.validation?.pattern) zodType = (zodType as z.ZodString).regex(new RegExp(field.validation.pattern));
          break;

        case 'number':
          zodType = z.number();
          if (field.validation?.min !== undefined) zodType = (zodType as z.ZodNumber).min(field.validation.min);
          if (field.validation?.max !== undefined) zodType = (zodType as z.ZodNumber).max(field.validation.max);
          break;

        case 'boolean':
          zodType = z.boolean();
          break;

        case 'array':
          const itemType = field.items ? this.createZodSchemaFromFields([field.items]) : z.any();
          zodType = z.array(itemType);
          break;

        case 'object':
          if (field.properties) {
            const objectFields = Object.values(field.properties);
            zodType = this.createZodSchemaFromFields(objectFields);
          } else {
            zodType = z.record(z.any());
          }
          break;

        case 'enum':
          if (field.validation?.options) {
            zodType = z.enum(field.validation.options as [string, ...string[]]);
          } else {
            zodType = z.string();
          }
          break;

        default:
          zodType = z.any();
      }

      if (field.default !== undefined) {
        zodType = zodType.default(field.default);
      }

      if (!field.required) {
        zodType = zodType.optional();
      }

      schemaObject[field.name] = zodType;
    });

    return z.object(schemaObject);
  }

  private zodToJsonSchema(zodSchema: z.ZodSchema): any {
    // This is a simplified conversion - in production, use a proper library like zod-to-json-schema
    return {
      type: 'object',
      properties: {},
      required: [],
    };
  }

  private generateUISchema(fields: SchemaField[]): any {
    const uiSchema: any = {};

    fields.forEach(field => {
      switch (field.type) {
        case 'string':
          if (field.validation?.pattern) {
            uiSchema[field.name] = { 'ui:widget': 'text', 'ui:pattern': field.validation.pattern };
          } else if (field.name.toLowerCase().includes('prompt')) {
            uiSchema[field.name] = { 'ui:widget': 'textarea', 'ui:rows': 4 };
          }
          break;

        case 'number':
          uiSchema[field.name] = { 'ui:widget': 'updown' };
          break;

        case 'boolean':
          uiSchema[field.name] = { 'ui:widget': 'checkbox' };
          break;

        case 'enum':
          uiSchema[field.name] = { 'ui:widget': 'select' };
          break;

        case 'array':
          uiSchema[field.name] = { 'ui:widget': 'array' };
          break;

        case 'object':
          uiSchema[field.name] = { 'ui:widget': 'object' };
          break;
      }

      if (field.description) {
        uiSchema[field.name] = { ...uiSchema[field.name], 'ui:description': field.description };
      }
    });

    return uiSchema;
  }

  private jsonSchemaToFields(jsonSchema: any): Record<string, SchemaField> {
    const fields: Record<string, SchemaField> = {};

    if (jsonSchema.properties) {
      Object.entries(jsonSchema.properties).forEach(([key, value]: [string, any]) => {
        fields[key] = {
          name: key,
          type: this.jsonTypeToSchemaType(value.type),
          description: value.description,
          required: jsonSchema.required?.includes(key) || false,
          default: value.default,
          validation: this.extractValidation(value),
        };
      });
    }

    return fields;
  }

  private jsonSchemaToFieldArray(jsonSchema: any): SchemaField[] {
    const fields: SchemaField[] = [];

    if (jsonSchema.properties) {
      Object.entries(jsonSchema.properties).forEach(([key, value]: [string, any]) => {
        fields.push({
          name: key,
          type: this.jsonTypeToSchemaType(value.type),
          description: value.description,
          required: jsonSchema.required?.includes(key) || false,
          default: value.default,
          validation: this.extractValidation(value),
        });
      });
    }

    return fields;
  }

  private jsonTypeToSchemaType(jsonType: string): SchemaField['type'] {
    switch (jsonType) {
      case 'string': return 'string';
      case 'number': case 'integer': return 'number';
      case 'boolean': return 'boolean';
      case 'array': return 'array';
      case 'object': return 'object';
      default: return 'string';
    }
  }

  private extractValidation(jsonSchema: any): SchemaField['validation'] {
    const validation: any = {};

    if (jsonSchema.minimum !== undefined) validation.min = jsonSchema.minimum;
    if (jsonSchema.maximum !== undefined) validation.max = jsonSchema.maximum;
    if (jsonSchema.minLength !== undefined) validation.min = jsonSchema.minLength;
    if (jsonSchema.maxLength !== undefined) validation.max = jsonSchema.maxLength;
    if (jsonSchema.pattern) validation.pattern = jsonSchema.pattern;
    if (jsonSchema.enum) validation.options = jsonSchema.enum;
    if (jsonSchema.format) validation.format = jsonSchema.format;

    return Object.keys(validation).length > 0 ? validation : undefined;
  }

  // Public API methods

  async getSchema(schemaId: string): Promise<GeneratedSchema | null> {
    return this.schemaCache.get(schemaId) || null;
  }

  async validateData(schemaId: string, data: any): Promise<{ valid: boolean; errors?: any[] }> {
    const schema = this.schemaCache.get(schemaId);
    
    if (!schema) {
      return { valid: false, errors: ['Schema not found'] };
    }

    try {
      schema.zodSchema.parse(data);
      return { valid: true };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { valid: false, errors: error.errors };
      }
      return { valid: false, errors: [error.message] };
    }
  }

  clearCache(): void {
    this.schemaCache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.schemaCache.size,
      keys: Array.from(this.schemaCache.keys()),
    };
  }
}