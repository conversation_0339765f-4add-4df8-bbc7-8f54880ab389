"use client";

import { useState, useEffect } from "react";

export type ViewMode = "grid" | "list";
export type SortDirection = "asc" | "desc";

interface ViewPreferences {
  viewMode: ViewMode;
  sortBy: string;
  sortDirection: SortDirection;
  pageSize: number;
  filters: Record<string, any>;
}

interface UseViewPreferencesOptions {
  key: string;
  defaultViewMode?: ViewMode;
  defaultSortBy?: string;
  defaultSortDirection?: SortDirection;
  defaultPageSize?: number;
  defaultFilters?: Record<string, any>;
}

export function useViewPreferences({
  key,
  defaultViewMode = "grid",
  defaultSortBy = "createdAt",
  defaultSortDirection = "desc",
  defaultPageSize = 20,
  defaultFilters = {},
}: UseViewPreferencesOptions) {
  const [preferences, setPreferences] = useState<ViewPreferences>({
    viewMode: defaultViewMode,
    sortBy: defaultSortBy,
    sortDirection: defaultSortDirection,
    pageSize: defaultPageSize,
    filters: defaultFilters,
  });

  const [loading, setLoading] = useState(true);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(`viewPreferences_${key}`);
      if (stored) {
        const parsed = JSON.parse(stored);
        setPreferences({
          viewMode: parsed.viewMode || defaultViewMode,
          sortBy: parsed.sortBy || defaultSortBy,
          sortDirection: parsed.sortDirection || defaultSortDirection,
          pageSize: parsed.pageSize || defaultPageSize,
          filters: { ...defaultFilters, ...(parsed.filters || {}) },
        });
      }
    } catch (error) {
      console.error("Failed to load view preferences:", error);
    } finally {
      setLoading(false);
    }
  }, [key, defaultViewMode, defaultSortBy, defaultSortDirection, defaultPageSize, defaultFilters]);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    if (!loading) {
      try {
        localStorage.setItem(`viewPreferences_${key}`, JSON.stringify(preferences));
      } catch (error) {
        console.error("Failed to save view preferences:", error);
      }
    }
  }, [key, preferences, loading]);

  const updatePreferences = (updates: Partial<ViewPreferences>) => {
    setPreferences(prev => ({ ...prev, ...updates }));
  };

  const setViewMode = (viewMode: ViewMode) => {
    updatePreferences({ viewMode });
  };

  const setSorting = (sortBy: string, sortDirection?: SortDirection) => {
    updatePreferences({
      sortBy,
      sortDirection: sortDirection || (preferences.sortBy === sortBy 
        ? preferences.sortDirection === "asc" ? "desc" : "asc"
        : "asc"
      ),
    });
  };

  const setPageSize = (pageSize: number) => {
    updatePreferences({ pageSize });
  };

  const setFilters = (filters: Record<string, any>) => {
    updatePreferences({ filters });
  };

  const clearFilters = () => {
    updatePreferences({ filters: defaultFilters });
  };

  const resetPreferences = () => {
    setPreferences({
      viewMode: defaultViewMode,
      sortBy: defaultSortBy,
      sortDirection: defaultSortDirection,
      pageSize: defaultPageSize,
      filters: defaultFilters,
    });
  };

  return {
    preferences,
    loading,
    setViewMode,
    setSorting,
    setPageSize,
    setFilters,
    clearFilters,
    resetPreferences,
    updatePreferences,
  };
}