"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExportDto = exports.WorkflowImportDto = exports.WorkflowTemplateDto = exports.ScheduleWorkflowDto = exports.WorkflowFilterDto = exports.ExecuteWorkflowDto = exports.UpdateWorkflowDto = exports.CreateWorkflowDto = exports.WorkflowExportSchema = exports.WorkflowImportSchema = exports.WorkflowTemplateSchema = exports.ScheduleWorkflowSchema = exports.WorkflowFilterSchema = exports.ExecuteWorkflowSchema = exports.UpdateWorkflowSchema = exports.CreateWorkflowSchema = void 0;
const zod_1 = require("zod");
const nestjs_zod_1 = require("nestjs-zod");
const WorkflowNodeSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Node ID is required'),
    type: zod_1.z.enum(['agent', 'tool', 'condition', 'parallel', 'human_input', 'delay']),
    name: zod_1.z.string().min(1, 'Node name is required'),
    config: zod_1.z.record(zod_1.z.any()),
    position: zod_1.z.object({
        x: zod_1.z.number(),
        y: zod_1.z.number(),
    }),
    inputs: zod_1.z.array(zod_1.z.string()).default([]),
    outputs: zod_1.z.array(zod_1.z.string()).default([]),
});
const WorkflowEdgeSchema = zod_1.z.object({
    id: zod_1.z.string().min(1, 'Edge ID is required'),
    source: zod_1.z.string().min(1, 'Source node ID is required'),
    target: zod_1.z.string().min(1, 'Target node ID is required'),
    condition: zod_1.z.string().optional(),
});
const WorkflowTriggerSchema = zod_1.z.object({
    type: zod_1.z.enum(['manual', 'scheduled', 'webhook', 'event']),
    config: zod_1.z.record(zod_1.z.any()),
});
const WorkflowSettingsSchema = zod_1.z.object({
    timeout: zod_1.z.number().positive().optional(),
    retryPolicy: zod_1.z.object({
        maxRetries: zod_1.z.number().min(0).max(10),
        backoffStrategy: zod_1.z.enum(['linear', 'exponential']),
        retryDelay: zod_1.z.number().positive(),
    }).optional(),
    errorHandling: zod_1.z.object({
        onError: zod_1.z.enum(['stop', 'continue', 'retry']),
        fallbackNode: zod_1.z.string().optional(),
    }).optional(),
});
const WorkflowDefinitionSchema = zod_1.z.object({
    nodes: zod_1.z.array(WorkflowNodeSchema).min(1, 'Workflow must have at least one node'),
    edges: zod_1.z.array(WorkflowEdgeSchema),
    triggers: zod_1.z.array(WorkflowTriggerSchema).default([]),
    settings: WorkflowSettingsSchema.default({}),
});
exports.CreateWorkflowSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Workflow name is required').max(100),
    description: zod_1.z.string().max(500).optional(),
    definition: WorkflowDefinitionSchema,
    tags: zod_1.z.array(zod_1.z.string()).max(20).optional(),
});
exports.UpdateWorkflowSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Workflow name is required').max(100).optional(),
    description: zod_1.z.string().max(500).optional(),
    definition: WorkflowDefinitionSchema.optional(),
    tags: zod_1.z.array(zod_1.z.string()).max(20).optional(),
    isActive: zod_1.z.boolean().optional(),
});
exports.ExecuteWorkflowSchema = zod_1.z.object({
    input: zod_1.z.record(zod_1.z.any()).default({}),
    variables: zod_1.z.record(zod_1.z.any()).default({}),
    options: zod_1.z.object({
        timeout: zod_1.z.number().positive().optional(),
        priority: zod_1.z.enum(['low', 'normal', 'high']).default('normal'),
        async: zod_1.z.boolean().default(false),
    }).default({}),
});
exports.WorkflowFilterSchema = zod_1.z.object({
    isActive: zod_1.z.boolean().optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    creatorId: zod_1.z.string().optional(),
    search: zod_1.z.string().optional(),
    page: zod_1.z.number().positive().default(1),
    limit: zod_1.z.number().positive().max(100).default(20),
});
exports.ScheduleWorkflowSchema = zod_1.z.object({
    cronExpression: zod_1.z.string().min(1, 'Cron expression is required'),
    timezone: zod_1.z.string().default('UTC'),
    startDate: zod_1.z.date().optional(),
    endDate: zod_1.z.date().optional(),
    maxRuns: zod_1.z.number().positive().optional(),
    input: zod_1.z.record(zod_1.z.any()).default({}),
    isActive: zod_1.z.boolean().default(true),
});
exports.WorkflowTemplateSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Template name is required').max(100),
    description: zod_1.z.string().max(500).optional(),
    category: zod_1.z.string().min(1, 'Category is required'),
    definition: WorkflowDefinitionSchema,
    tags: zod_1.z.array(zod_1.z.string()).max(20).default([]),
    isPublic: zod_1.z.boolean().default(false),
    variables: zod_1.z.array(zod_1.z.object({
        name: zod_1.z.string(),
        type: zod_1.z.enum(['string', 'number', 'boolean', 'object']),
        description: zod_1.z.string().optional(),
        required: zod_1.z.boolean().default(false),
        defaultValue: zod_1.z.any().optional(),
    })).default([]),
});
exports.WorkflowImportSchema = zod_1.z.object({
    format: zod_1.z.enum(['json', 'yaml']),
    data: zod_1.z.string().min(1, 'Import data is required'),
    overwrite: zod_1.z.boolean().default(false),
});
exports.WorkflowExportSchema = zod_1.z.object({
    format: zod_1.z.enum(['json', 'yaml']).default('json'),
    includeExecutions: zod_1.z.boolean().default(false),
    includeMetadata: zod_1.z.boolean().default(true),
});
class CreateWorkflowDto extends (0, nestjs_zod_1.createZodDto)(exports.CreateWorkflowSchema) {
}
exports.CreateWorkflowDto = CreateWorkflowDto;
class UpdateWorkflowDto extends (0, nestjs_zod_1.createZodDto)(exports.UpdateWorkflowSchema) {
}
exports.UpdateWorkflowDto = UpdateWorkflowDto;
class ExecuteWorkflowDto extends (0, nestjs_zod_1.createZodDto)(exports.ExecuteWorkflowSchema) {
}
exports.ExecuteWorkflowDto = ExecuteWorkflowDto;
class WorkflowFilterDto extends (0, nestjs_zod_1.createZodDto)(exports.WorkflowFilterSchema) {
}
exports.WorkflowFilterDto = WorkflowFilterDto;
class ScheduleWorkflowDto extends (0, nestjs_zod_1.createZodDto)(exports.ScheduleWorkflowSchema) {
}
exports.ScheduleWorkflowDto = ScheduleWorkflowDto;
class WorkflowTemplateDto extends (0, nestjs_zod_1.createZodDto)(exports.WorkflowTemplateSchema) {
}
exports.WorkflowTemplateDto = WorkflowTemplateDto;
class WorkflowImportDto extends (0, nestjs_zod_1.createZodDto)(exports.WorkflowImportSchema) {
}
exports.WorkflowImportDto = WorkflowImportDto;
class WorkflowExportDto extends (0, nestjs_zod_1.createZodDto)(exports.WorkflowExportSchema) {
}
exports.WorkflowExportDto = WorkflowExportDto;
//# sourceMappingURL=workflow.dto.js.map