"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
    CommandDialog,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import {
    Search,
    FileText,
    Users,
    Building2,
    Workflow,
    Bot,
    Wrench,
    BarChart3,
    Settings,
    Calendar,
    Hash,
    ArrowRight,
} from "lucide-react";
import apiClient from "@/lib/api-client";

interface SearchResult {
    id: string;
    title: string;
    description?: string;
    type: "workflow" | "agent" | "tool" | "user" | "organization" | "page" | "setting";
    url: string;
    metadata?: Record<string, any>;
}

interface GlobalSearchProps {
    trigger?: React.ReactNode;
}

const searchTypeIcons = {
    workflow: Workflow,
    agent: Bot,
    tool: Wrench,
    user: Users,
    organization: Building2,
    page: FileText,
    setting: Settings,
};

const searchTypeLabels = {
    workflow: "Workflows",
    agent: "Agents",
    tool: "Tools",
    user: "Users",
    organization: "Organizations",
    page: "Pages",
    setting: "Settings",
};

export function GlobalSearch({ trigger }: GlobalSearchProps) {
    const [open, setOpen] = useState(false);
    const [query, setQuery] = useState("");
    const [results, setResults] = useState<SearchResult[]>([]);
    const [loading, setLoading] = useState(false);
    const [recentSearches, setRecentSearches] = useState<SearchResult[]>([]);
    const router = useRouter();

    // Load recent searches from localStorage
    useEffect(() => {
        const recent = localStorage.getItem("recentSearches");
        if (recent) {
            try {
                setRecentSearches(JSON.parse(recent).slice(0, 5));
            } catch (error) {
                console.error("Failed to parse recent searches:", error);
            }
        }
    }, []);

    // Global keyboard shortcut
    useEffect(() => {
        const down = (e: KeyboardEvent) => {
            if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
                setOpen((open) => !open);
            }
        };
        document.addEventListener("keydown", down);
        return () => document.removeEventListener("keydown", down);
    }, []);

    // Debounced search
    const performSearch = useCallback(async (searchQuery: string) => {
        if (!searchQuery.trim()) {
            setResults([]);
            return;
        }

        setLoading(true);
        try {
            const response = await apiClient.get(`/search?q=${encodeURIComponent(searchQuery)}&limit=20`) as { data: { results: SearchResult[] } };
            const searchResults: SearchResult[] = (response as any).data?.results || [];
            setResults(searchResults);
        } catch (error) {
            console.error("Search failed:", error);
            setResults([]);
        } finally {
            setLoading(false);
        }
    }, []);

    // Debounce search queries
    useEffect(() => {
        const timer = setTimeout(() => {
            performSearch(query);
        }, 300);

        return () => clearTimeout(timer);
    }, [query, performSearch]);

    const handleSelect = useCallback((result: SearchResult) => {
        // Add to recent searches
        const updatedRecent = [result, ...recentSearches.filter((r: any) => r.id !== result.id)].slice(0, 5);
        setRecentSearches(updatedRecent);
        localStorage.setItem("recentSearches", JSON.stringify(updatedRecent));

        // Navigate to result
        router.push(result.url);
        setOpen(false);
        setQuery("");
    }, [recentSearches, router]);

    const quickActions = [
        {
            id: "create-workflow",
            title: "Create Workflow",
            description: "Start building a new AI workflow",
            type: "page" as const,
            url: "/dashboard/workflows/new",
            icon: Workflow,
        },
        {
            id: "deploy-agent",
            title: "Deploy Agent",
            description: "Launch a new AI agent",
            type: "page" as const,
            url: "/dashboard/agents/new",
            icon: Bot,
        },
        {
            id: "add-tool",
            title: "Add Tool",
            description: "Configure a new tool",
            type: "page" as const,
            url: "/dashboard/tools/new",
            icon: Wrench,
        },
        {
            id: "view-analytics",
            title: "View Analytics",
            description: "Monitor performance metrics",
            type: "page" as const,
            url: "/dashboard/analytics",
            icon: BarChart3,
        },
    ];

    const groupedResults = results.reduce((groups, result) => {
        const type = result.type;
        if (!groups[type]) {
            groups[type] = [];
        }
        groups[type].push(result);
        return groups;
    }, {} as Record<string, SearchResult[]>);

    return (
        <CommandDialog open={open} onOpenChange={setOpen}>
            <CommandInput
                placeholder="Search workflows, agents, tools, users..."
                value={query}
                onValueChange={setQuery}
            />
            <CommandList>
                <CommandEmpty>
                    {loading ? (
                        <div className="flex items-center justify-center space-x-2 py-6">
                            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                            <span>Searching...</span>
                        </div>
                    ) : (
                        <div className="py-6 text-center">
                            <Search className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                            <p>No results found for "{query}"</p>
                            <p className="text-xs text-muted-foreground mt-1">
                                Try searching for workflows, agents, tools, or users
                            </p>
                        </div>
                    )}
                </CommandEmpty>

                {!query && recentSearches.length > 0 && (
                    <CommandGroup heading="Recent Searches">
                        {recentSearches.map((result) => {
                            const IconComponent = searchTypeIcons[result.type];
                            return (
                                <CommandItem
                                    key={`recent-${result.id}`}
                                    value={result.title}
                                    onSelect={() => handleSelect(result)}
                                    className="flex items-center space-x-3 py-3"
                                >
                                    <IconComponent className="h-4 w-4 text-muted-foreground" />
                                    <div className="flex-1 min-w-0">
                                        <p className="truncate font-medium">{result.title}</p>
                                        {result.description && (
                                            <p className="text-xs text-muted-foreground truncate">
                                                {result.description}
                                            </p>
                                        )}
                                    </div>
                                    <Badge variant="outline" className="text-xs">
                                        {searchTypeLabels[result.type]}
                                    </Badge>
                                </CommandItem>
                            );
                        })}
                    </CommandGroup>
                )}

                {!query && (
                    <CommandGroup heading="Quick Actions">
                        {quickActions.map((action) => (
                            <CommandItem
                                key={action.id}
                                value={action.title}
                                onSelect={() => handleSelect(action)}
                                className="flex items-center space-x-3 py-3"
                            >
                                <action.icon className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1 min-w-0">
                                    <p className="font-medium">{action.title}</p>
                                    <p className="text-xs text-muted-foreground">
                                        {action.description}
                                    </p>
                                </div>
                                <ArrowRight className="h-3 w-3 text-muted-foreground" />
                            </CommandItem>
                        ))}
                    </CommandGroup>
                )}

                {Object.entries(groupedResults).map(([type, typeResults], index) => {
                    const IconComponent = searchTypeIcons[type as keyof typeof searchTypeIcons];
                    const label = searchTypeLabels[type as keyof typeof searchTypeLabels];

                    return (
                        <div key={type}>
                            {index > 0 && <CommandSeparator />}
                            <CommandGroup heading={label}>
                                {typeResults.map((result) => (
                                    <CommandItem
                                        key={result.id}
                                        value={result.title}
                                        onSelect={() => handleSelect(result)}
                                        className="flex items-center space-x-3 py-3"
                                    >
                                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                                        <div className="flex-1 min-w-0">
                                            <p className="truncate font-medium">{result.title}</p>
                                            {result.description && (
                                                <p className="text-xs text-muted-foreground truncate">
                                                    {result.description}
                                                </p>
                                            )}
                                        </div>
                                        {result.metadata && (
                                            <div className="flex items-center space-x-1">
                                                {result.metadata.status && (
                                                    <Badge
                                                        variant={result.metadata.status === 'active' ? 'secondary' : 'outline'}
                                                        className="text-xs"
                                                    >
                                                        {result.metadata.status}
                                                    </Badge>
                                                )}
                                            </div>
                                        )}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </div>
                    );
                })}
            </CommandList>
        </CommandDialog>
    );
}