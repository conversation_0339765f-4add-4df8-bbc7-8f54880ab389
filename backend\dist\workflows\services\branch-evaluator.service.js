"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var BranchEvaluatorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BranchEvaluatorService = void 0;
const common_1 = require("@nestjs/common");
let BranchEvaluatorService = BranchEvaluatorService_1 = class BranchEvaluatorService {
    constructor() {
        this.logger = new common_1.Logger(BranchEvaluatorService_1.name);
    }
    evaluateCondition(condition, variables, nodeResult) {
        try {
            return this.safeEval(condition, variables, nodeResult);
        }
        catch (error) {
            this.logger.error(`Condition evaluation failed: ${error.message}`, error.stack);
            throw new Error(`Condition evaluation failed: ${error.message}`);
        }
    }
    evaluateExpression(expression, variables, nodeResult) {
        try {
            return this.safeEval(expression, variables, nodeResult);
        }
        catch (error) {
            this.logger.error(`Expression evaluation failed: ${error.message}`, error.stack);
            throw new Error(`Expression evaluation failed: ${error.message}`);
        }
    }
    evaluateSwitch(value, cases, defaultCase) {
        try {
            const stringValue = String(value);
            if (cases[stringValue] !== undefined) {
                return cases[stringValue];
            }
            return defaultCase;
        }
        catch (error) {
            this.logger.error(`Switch evaluation failed: ${error.message}`, error.stack);
            throw new Error(`Switch evaluation failed: ${error.message}`);
        }
    }
    evaluateLoop(items, iterationLimit = 100) {
        try {
            if (!Array.isArray(items)) {
                throw new Error('Loop items must be an array');
            }
            if (items.length > iterationLimit) {
                return { valid: false, count: items.length };
            }
            return { valid: true, count: items.length };
        }
        catch (error) {
            this.logger.error(`Loop evaluation failed: ${error.message}`, error.stack);
            throw new Error(`Loop evaluation failed: ${error.message}`);
        }
    }
    safeEval(expression, variables, nodeResult) {
        let processedExpression = expression;
        if (nodeResult !== undefined) {
            processedExpression = expression.replace(/\$result/g, JSON.stringify(nodeResult));
        }
        processedExpression = processedExpression.replace(/\${(\w+)}/g, (match, varName) => {
            return variables[varName] !== undefined ?
                JSON.stringify(variables[varName]) : 'undefined';
        });
        const keys = Object.keys(variables);
        const values = Object.values(variables);
        if (nodeResult !== undefined) {
            keys.push('$result');
            values.push(nodeResult);
        }
        const evalFunction = new Function(...keys, `return ${processedExpression};`);
        return evalFunction(...values);
    }
    isEqual(a, b) {
        return a === b;
    }
    isNotEqual(a, b) {
        return a !== b;
    }
    isGreaterThan(a, b) {
        return a > b;
    }
    isLessThan(a, b) {
        return a < b;
    }
    isGreaterThanOrEqual(a, b) {
        return a >= b;
    }
    isLessThanOrEqual(a, b) {
        return a <= b;
    }
    contains(a, b) {
        if (typeof a === 'string') {
            return a.includes(b);
        }
        if (Array.isArray(a)) {
            return a.includes(b);
        }
        return false;
    }
    startsWith(a, b) {
        if (typeof a === 'string' && typeof b === 'string') {
            return a.startsWith(b);
        }
        return false;
    }
    endsWith(a, b) {
        if (typeof a === 'string' && typeof b === 'string') {
            return a.endsWith(b);
        }
        return false;
    }
    and(a, b) {
        return a && b;
    }
    or(a, b) {
        return a || b;
    }
    not(a) {
        return !a;
    }
};
exports.BranchEvaluatorService = BranchEvaluatorService;
exports.BranchEvaluatorService = BranchEvaluatorService = BranchEvaluatorService_1 = __decorate([
    (0, common_1.Injectable)()
], BranchEvaluatorService);
//# sourceMappingURL=branch-evaluator.service.js.map