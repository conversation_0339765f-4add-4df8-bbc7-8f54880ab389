-- Create<PERSON><PERSON>
CREATE TYPE "ToolExecutionStatus" AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT');

-- CreateEnum
CREATE TYPE "ToolCacheStrategy" AS ENUM ('NONE', 'INPUT_HASH', 'TIME_BASED', 'DEPENDENCY_BASED', 'CUSTOM');

-- CreateTable
CREATE TABLE "tool_definitions" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" "SkillCategory" NOT NULL DEFAULT 'CUSTOM',
    "type" "ToolType" NOT NULL DEFAULT 'FUNCTION_CALL',
    "config" JSONB NOT NULL DEFAULT '{}',
    "inputSchema" JSONB NOT NULL DEFAULT '{}',
    "outputSchema" JSONB NOT NULL DEFAULT '{}',
    "timeout" INTEGER NOT NULL DEFAULT 30000,
    "retryPolicy" JSONB NOT NULL DEFAULT '{}',
    "cacheStrategy" "ToolCacheStrategy" NOT NULL DEFAULT 'INPUT_HASH',
    "cacheTTL" INTEGER NOT NULL DEFAULT 3600,
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "documentation" TEXT,
    "examples" JSONB NOT NULL DEFAULT '[]',
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "dependencies" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "requirements" JSONB NOT NULL DEFAULT '{}',
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "avgLatency" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdBy" TEXT NOT NULL,
    "organizationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tool_definitions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_versions" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "version" TEXT NOT NULL,
    "description" TEXT,
    "changes" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "config" JSONB NOT NULL,
    "inputSchema" JSONB NOT NULL,
    "outputSchema" JSONB NOT NULL,
    "requirements" JSONB NOT NULL DEFAULT '{}',
    "isStable" BOOLEAN NOT NULL DEFAULT false,
    "isDeprecated" BOOLEAN NOT NULL DEFAULT false,
    "releaseNotes" TEXT,
    "migrationPath" JSONB,
    "breakingChanges" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tool_versions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_compositions" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "flow" JSONB NOT NULL DEFAULT '{}',
    "variables" JSONB NOT NULL DEFAULT '{}',
    "conditions" JSONB NOT NULL DEFAULT '{}',
    "parallel" BOOLEAN NOT NULL DEFAULT false,
    "timeout" INTEGER NOT NULL DEFAULT 300000,
    "errorHandling" JSONB NOT NULL DEFAULT '{}',
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "isTemplate" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "executionCount" INTEGER NOT NULL DEFAULT 0,
    "successRate" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "avgDuration" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdBy" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tool_compositions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_composition_steps" (
    "id" TEXT NOT NULL,
    "compositionId" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "stepIndex" INTEGER NOT NULL,
    "stepName" TEXT,
    "inputMapping" JSONB NOT NULL DEFAULT '{}',
    "outputMapping" JSONB NOT NULL DEFAULT '{}',
    "conditions" JSONB NOT NULL DEFAULT '{}',
    "dependencies" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "continueOnError" BOOLEAN NOT NULL DEFAULT false,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "fallbackTool" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tool_composition_steps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_executions" (
    "id" TEXT NOT NULL,
    "toolId" TEXT,
    "compositionId" TEXT,
    "executorType" TEXT NOT NULL DEFAULT 'user',
    "executorId" TEXT,
    "sessionId" TEXT,
    "organizationId" TEXT NOT NULL,
    "status" "ToolExecutionStatus" NOT NULL DEFAULT 'PENDING',
    "input" JSONB NOT NULL,
    "output" JSONB,
    "error" TEXT,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "duration" INTEGER,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "tokensUsed" INTEGER NOT NULL DEFAULT 0,
    "cost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "memoryUsed" INTEGER,
    "cpuTime" INTEGER,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "traceId" TEXT,
    "parentId" TEXT,
    "cached" BOOLEAN NOT NULL DEFAULT false,
    "cacheKey" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tool_executions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_composition_executions" (
    "id" TEXT NOT NULL,
    "compositionId" TEXT NOT NULL,
    "executorType" TEXT NOT NULL DEFAULT 'user',
    "executorId" TEXT,
    "sessionId" TEXT,
    "organizationId" TEXT NOT NULL,
    "status" "ToolExecutionStatus" NOT NULL DEFAULT 'PENDING',
    "input" JSONB NOT NULL,
    "output" JSONB,
    "error" TEXT,
    "currentStep" INTEGER NOT NULL DEFAULT 0,
    "stepResults" JSONB NOT NULL DEFAULT '{}',
    "variables" JSONB NOT NULL DEFAULT '{}',
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "duration" INTEGER,
    "totalSteps" INTEGER NOT NULL DEFAULT 0,
    "metadata" JSONB NOT NULL DEFAULT '{}',
    "traceId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tool_composition_executions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_cache" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "inputHash" TEXT NOT NULL,
    "input" JSONB NOT NULL,
    "output" JSONB NOT NULL,
    "strategy" "ToolCacheStrategy" NOT NULL,
    "ttl" INTEGER NOT NULL,
    "hits" INTEGER NOT NULL DEFAULT 0,
    "lastAccessed" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isValid" BOOLEAN NOT NULL DEFAULT true,
    "invalidatedBy" TEXT,
    "invalidatedAt" TIMESTAMP(3),
    "dependencies" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "dependencyHash" TEXT,
    "size" INTEGER,
    "compressionRatio" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tool_cache_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_analytics" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "hour" INTEGER,
    "executionCount" INTEGER NOT NULL DEFAULT 0,
    "uniqueUsers" INTEGER NOT NULL DEFAULT 0,
    "totalDuration" INTEGER NOT NULL DEFAULT 0,
    "avgDuration" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "successCount" INTEGER NOT NULL DEFAULT 0,
    "errorCount" INTEGER NOT NULL DEFAULT 0,
    "timeoutCount" INTEGER NOT NULL DEFAULT 0,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "minDuration" INTEGER,
    "maxDuration" INTEGER,
    "p95Duration" INTEGER,
    "p99Duration" INTEGER,
    "totalTokens" INTEGER NOT NULL DEFAULT 0,
    "totalCost" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avgMemoryUsed" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "avgCpuTime" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "cacheHits" INTEGER NOT NULL DEFAULT 0,
    "cacheMisses" INTEGER NOT NULL DEFAULT 0,
    "cacheHitRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "errorTypes" JSONB NOT NULL DEFAULT '{}',
    "errorMessages" JSONB NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "tool_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tool_api_keys" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "keyType" TEXT NOT NULL DEFAULT 'external',
    "encryptedKey" TEXT NOT NULL,
    "keyHash" TEXT NOT NULL,
    "oauthConfig" JSONB,
    "refreshToken" TEXT,
    "tokenExpiry" TIMESTAMP(3),
    "permissions" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "rateLimits" JSONB NOT NULL DEFAULT '{}',
    "lastUsed" TIMESTAMP(3),
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "expiresAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tool_api_keys_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "tool_definitions_type_category_idx" ON "tool_definitions"("type", "category");

-- CreateIndex
CREATE INDEX "tool_definitions_organizationId_isActive_idx" ON "tool_definitions"("organizationId", "isActive");

-- CreateIndex
CREATE INDEX "tool_definitions_createdBy_createdAt_idx" ON "tool_definitions"("createdBy", "createdAt");

-- CreateIndex
CREATE INDEX "tool_definitions_isPublic_category_idx" ON "tool_definitions"("isPublic", "category");

-- CreateIndex
CREATE UNIQUE INDEX "tool_versions_toolId_version_key" ON "tool_versions"("toolId", "version");

-- CreateIndex
CREATE INDEX "tool_versions_toolId_createdAt_idx" ON "tool_versions"("toolId", "createdAt");

-- CreateIndex
CREATE INDEX "tool_compositions_organizationId_isActive_idx" ON "tool_compositions"("organizationId", "isActive");

-- CreateIndex
CREATE INDEX "tool_compositions_createdBy_createdAt_idx" ON "tool_compositions"("createdBy", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "tool_composition_steps_compositionId_stepIndex_key" ON "tool_composition_steps"("compositionId", "stepIndex");

-- CreateIndex
CREATE INDEX "tool_composition_steps_compositionId_stepIndex_idx" ON "tool_composition_steps"("compositionId", "stepIndex");

-- CreateIndex
CREATE INDEX "tool_executions_toolId_status_idx" ON "tool_executions"("toolId", "status");

-- CreateIndex
CREATE INDEX "tool_executions_organizationId_createdAt_idx" ON "tool_executions"("organizationId", "createdAt");

-- CreateIndex
CREATE INDEX "tool_executions_executorId_executorType_idx" ON "tool_executions"("executorId", "executorType");

-- CreateIndex
CREATE INDEX "tool_executions_sessionId_idx" ON "tool_executions"("sessionId");

-- CreateIndex
CREATE INDEX "tool_executions_status_startedAt_idx" ON "tool_executions"("status", "startedAt");

-- CreateIndex
CREATE INDEX "tool_composition_executions_compositionId_status_idx" ON "tool_composition_executions"("compositionId", "status");

-- CreateIndex
CREATE INDEX "tool_composition_executions_organizationId_createdAt_idx" ON "tool_composition_executions"("organizationId", "createdAt");

-- CreateIndex
CREATE INDEX "tool_composition_executions_executorId_executorType_idx" ON "tool_composition_executions"("executorId", "executorType");

-- CreateIndex
CREATE UNIQUE INDEX "tool_cache_toolId_inputHash_key" ON "tool_cache"("toolId", "inputHash");

-- CreateIndex
CREATE INDEX "tool_cache_toolId_lastAccessed_idx" ON "tool_cache"("toolId", "lastAccessed");

-- CreateIndex
CREATE INDEX "tool_cache_expiresAt_isValid_idx" ON "tool_cache"("expiresAt", "isValid");

-- CreateIndex
CREATE UNIQUE INDEX "tool_analytics_toolId_date_hour_key" ON "tool_analytics"("toolId", "date", "hour");

-- CreateIndex
CREATE INDEX "tool_analytics_toolId_date_idx" ON "tool_analytics"("toolId", "date");

-- CreateIndex
CREATE INDEX "tool_analytics_date_idx" ON "tool_analytics"("date");

-- CreateIndex
CREATE INDEX "tool_api_keys_toolId_isActive_idx" ON "tool_api_keys"("toolId", "isActive");

-- CreateIndex
CREATE INDEX "tool_api_keys_organizationId_isActive_idx" ON "tool_api_keys"("organizationId", "isActive");

-- CreateIndex
CREATE INDEX "tool_api_keys_keyHash_idx" ON "tool_api_keys"("keyHash");

-- AddForeignKey
ALTER TABLE "tool_definitions" ADD CONSTRAINT "tool_definitions_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_definitions" ADD CONSTRAINT "tool_definitions_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_versions" ADD CONSTRAINT "tool_versions_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_versions" ADD CONSTRAINT "tool_versions_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_compositions" ADD CONSTRAINT "tool_compositions_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_compositions" ADD CONSTRAINT "tool_compositions_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_composition_steps" ADD CONSTRAINT "tool_composition_steps_compositionId_fkey" FOREIGN KEY ("compositionId") REFERENCES "tool_compositions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_composition_steps" ADD CONSTRAINT "tool_composition_steps_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_executions" ADD CONSTRAINT "tool_executions_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_executions" ADD CONSTRAINT "tool_executions_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "tool_executions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_composition_executions" ADD CONSTRAINT "tool_composition_executions_compositionId_fkey" FOREIGN KEY ("compositionId") REFERENCES "tool_compositions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_cache" ADD CONSTRAINT "tool_cache_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_analytics" ADD CONSTRAINT "tool_analytics_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_api_keys" ADD CONSTRAINT "tool_api_keys_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tool_definitions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_api_keys" ADD CONSTRAINT "tool_api_keys_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tool_api_keys" ADD CONSTRAINT "tool_api_keys_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;