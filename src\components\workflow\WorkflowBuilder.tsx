"use client";

import React, { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Plus,
  Save,
  Play,
  Settings,
  Trash2,
  Copy,
  ArrowRight,
  ArrowDown,
  Workflow,
  Bot,
  Code,
  Database,
  Search,
  Wrench,
  Layers,
  GitBranch,
  Split,
  User,
  Clock,
  Activity,
  CheckCircle,
  XCircle,
  MessageSquare,
} from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import apiClient from '@/lib/api-client';
import apixClient from '@/lib/apix-client';
import { useToast } from '@/components/ui/use-toast';

interface Node {
  id: string;
  type: "agent" | "tool" | "condition" | "parallel" | "human_input" | "delay" | "hybrid" | "input" | "output";
  position: { x: number; y: number };
  data: {
    label: string;
    description?: string;
    config?: Record<string, any>;
  };
}

interface Edge {
  id: string;
  source: string;
  target: string;
  label?: string;
  condition?: string;
}

interface WorkflowBuilderProps {
  workflowId?: string;
  initialNodes?: Node[];
  initialEdges?: Edge[];
  readOnly?: boolean;
}

const WorkflowBuilder = ({
  workflowId = "",
  initialNodes = [],
  initialEdges = [],
  readOnly = false,
}: WorkflowBuilderProps) => {
  const [nodes, setNodes] = useState<Node[]>(
    initialNodes.length > 0
      ? initialNodes
      : [
        {
          id: "1",
          type: "input",
          position: { x: 250, y: 100 },
          data: { label: "Start" },
        },
        {
          id: "2",
          type: "agent",
          position: { x: 250, y: 200 },
          data: { label: "Agent", description: "Processes user input" },
        },
        {
          id: "3",
          type: "tool",
          position: { x: 250, y: 300 },
          data: {
            label: "Search Tool",
            description: "Searches for information",
          },
        },
        {
          id: "4",
          type: "output",
          position: { x: 250, y: 400 },
          data: { label: "End" },
        },
      ],
  );

  const [edges, setEdges] = useState<Edge[]>(
    initialEdges.length > 0
      ? initialEdges
      : [
        { id: "e1-2", source: "1", target: "2" },
        { id: "e2-3", source: "2", target: "3" },
        { id: "e3-4", source: "3", target: "4" },
      ],
  );

  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [workflowName, setWorkflowName] = useState<string>(
    workflowId ? `Workflow ${workflowId}` : "New Workflow",
  );
  const [activeTab, setActiveTab] = useState<string>("canvas");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [agents, setAgents] = useState<any[]>([]);
  const [tools, setTools] = useState<any[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [nodeStatuses, setNodeStatuses] = useState<Record<string, any>>({});
  const { toast } = useToast();

  // Fetch agents and tools
  useEffect(() => {
    // In a real implementation, this would fetch from API
    setAgents([
      { id: "agent1", name: "Conversational Agent", type: "STANDALONE" },
      { id: "agent2", name: "Task Agent", type: "TOOL_DRIVEN" },
      { id: "agent3", name: "Hybrid Agent", type: "HYBRID" },
    ]);

    setTools([
      { id: "tool1", name: "Search Tool", type: "FUNCTION_CALL" },
      { id: "tool2", name: "Database Tool", type: "DATABASE" },
      { id: "tool3", name: "API Tool", type: "API_FETCH" },
      { id: "tool4", name: "RAG Tool", type: "RAG" },
    ]);
  }, []);

  const handleNodeClick = useCallback((node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setActiveTab("properties");
  }, []);

  const handleEdgeClick = useCallback((edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setActiveTab("properties");
  }, []);

  const handleAddNode = useCallback(
    (type: Node["type"]) => {
      const newNode: Node = {
        id: `${nodes.length + 1}`,
        type,
        position: { x: 250, y: (nodes.length + 1) * 100 },
        data: {
          label: type.charAt(0).toUpperCase() + type.slice(1),
          description: `New ${type} node`,
          config: getDefaultConfigForType(type),
        },
      };

      setNodes([...nodes, newNode]);

      // If there are existing nodes, connect the new node to the last one
      if (nodes.length > 0) {
        const lastNode = nodes[nodes.length - 1];
        const newEdge: Edge = {
          id: `e${lastNode.id}-${newNode.id}`,
          source: lastNode.id,
          target: newNode.id,
        };

        setEdges([...edges, newEdge]);
      }

      setSelectedNode(newNode);
      setActiveTab("properties");
    },
    [nodes, edges],
  );

  const getDefaultConfigForType = (type: Node["type"]) => {
    switch (type) {
      case "agent":
        return { agentId: "", systemPrompt: "", maxTokens: 1000 };
      case "tool":
        return { toolId: "", parameters: {} };
      case "condition":
        return { condition: "", paths: { true: "", false: "" } };
      case "parallel":
        return { nodes: [], aggregateResults: false };
      case "human_input":
        return { prompt: "Please provide input", inputType: "text", timeout: 300000 };
      case "delay":
        return { delay: 1000 };
      case "hybrid":
        return {
          agentId: "",
          toolIds: [],
          executionPattern: "agent-first",
          agentConfig: {},
          toolConfigs: {},
          maxIterations: 5
        };
      default:
        return {};
    }
  };

  const handleUpdateNodeData = useCallback(
    (updatedData: Partial<Node["data"]>) => {
      if (!selectedNode) return;

      setNodes(
        nodes.map((node) =>
          node.id === selectedNode.id
            ? { ...node, data: { ...node.data, ...updatedData } }
            : node,
        ),
      );

      setSelectedNode((prev) =>
        prev ? { ...prev, data: { ...prev.data, ...updatedData } } : null,
      );
    },
    [selectedNode, nodes],
  );

  const handleUpdateNodeConfig = useCallback(
    (updatedConfig: Partial<Record<string, any>>) => {
      if (!selectedNode) return;

      const currentConfig = selectedNode.data.config || {};
      const newConfig = { ...currentConfig, ...updatedConfig };

      setNodes(
        nodes.map((node) =>
          node.id === selectedNode.id
            ? {
              ...node,
              data: {
                ...node.data,
                config: newConfig
              }
            }
            : node,
        ),
      );

      setSelectedNode((prev) =>
        prev ? {
          ...prev,
          data: {
            ...prev.data,
            config: newConfig
          }
        } : null,
      );
    },
    [selectedNode, nodes],
  );

  const handleUpdateEdge = useCallback(
    (updatedEdge: Partial<Edge>) => {
      if (!selectedEdge) return;

      setEdges(
        edges.map((edge) =>
          edge.id === selectedEdge.id
            ? { ...edge, ...updatedEdge }
            : edge,
        ),
      );

      setSelectedEdge((prev) =>
        prev ? { ...prev, ...updatedEdge } : null,
      );
    },
    [selectedEdge, edges],
  );

  const handleDeleteNode = useCallback(() => {
    if (!selectedNode) return;

    // Remove all edges connected to this node
    const filteredEdges = edges.filter(
      (edge) =>
        edge.source !== selectedNode.id && edge.target !== selectedNode.id,
    );

    setEdges(filteredEdges);
    setNodes(nodes.filter((node) => node.id !== selectedNode.id));
    setSelectedNode(null);
    setActiveTab("canvas");
  }, [selectedNode, nodes, edges]);

  const handleDeleteEdge = useCallback(() => {
    if (!selectedEdge) return;

    setEdges(edges.filter((edge) => edge.id !== selectedEdge.id));
    setSelectedEdge(null);
    setActiveTab("canvas");
  }, [selectedEdge, edges]);

  // Real workflow saving functionality
  const handleSaveWorkflow = useCallback(async () => {
    if (isSaving) return;

    try {
      setIsSaving(true);

      // Validate workflow before saving
      const validationErrors = validateWorkflow();
      if (validationErrors.length > 0) {
        toast({
          title: 'Validation Error',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      const workflowData = {
        name: workflowName,
        description: `Workflow with ${nodes.length} nodes`,
        definition: {
          nodes: nodes.map(node => ({
            id: node.id,
            type: node.type,
            name: node.data.label,
            config: node.data.config || {},
            position: node.position,
            inputs: getNodeInputs(node),
            outputs: getNodeOutputs(node),
          })),
          edges: edges.map(edge => ({
            id: edge.id,
            source: edge.source,
            target: edge.target,
            condition: edge.condition,
          })),
          triggers: [{ type: 'manual', config: {} }],
          settings: {
            timeout: 300000, // 5 minutes
            retryPolicy: {
              maxRetries: 3,
              backoffStrategy: 'exponential',
              retryDelay: 1000,
            },
            errorHandling: {
              onError: 'stop',
            },
          },
        },
        tags: ['workflow-builder'],
      };

      let savedWorkflow;
      if (workflowId) {
        // Update existing workflow
        savedWorkflow = await apiClient.updateWorkflow(workflowId, workflowData);
        toast({
          title: 'Success',
          description: 'Workflow updated successfully',
        });
      } else {
        // Create new workflow
        savedWorkflow = await apiClient.createWorkflow(workflowData);
        toast({
          title: 'Success',
          description: 'Workflow created successfully',
        });
        // Update URL or redirect if needed
        window.history.replaceState(null, '', `/workflows/${savedWorkflow.id}`);
      }

      
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save workflow',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [workflowName, nodes, edges, workflowId, isSaving, toast]);

  // Real workflow execution functionality
  const handleRunWorkflow = useCallback(async () => {
    if (isExecuting) return;

    try {
      setIsExecuting(true);
      setNodeStatuses({});

      // Validate workflow before execution
      const validationErrors = validateWorkflow();
      if (validationErrors.length > 0) {
        toast({
          title: 'Validation Error',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      // Save workflow first if it's not saved
      if (!workflowId) {
        await handleSaveWorkflow();
        // Wait a bit for the save to complete
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const currentWorkflowId = workflowId || 'temp-workflow-id';

      // Subscribe to workflow events
      apixClient.subscribeToWorkflow(currentWorkflowId);

      // Set up event listeners for real-time updates
      const unsubscribeWorkflowEvents = apixClient.on('workflow_started', (event) => {
        if (event.data.workflowId === currentWorkflowId) {
          toast({
            title: 'Workflow Started',
            description: 'Workflow execution has begun',
          });
        }
      });

      const unsubscribeNodeEvents = apixClient.on('*', (event) => {
        if (event.data?.workflowId === currentWorkflowId || event.data?.executionId === executionId) {
          handleWorkflowEvent(event);
        }
      });

      // Start workflow execution
      const executionResponse = await apiClient.executeWorkflow(currentWorkflowId, {
        input: {},
        options: {
          async: true,
          priority: 'normal',
        },
      });

      setExecutionId(executionResponse.executionId);

      toast({
        title: 'Execution Started',
        description: `Workflow execution started with ID: ${executionResponse.executionId}`,
      });

      // Clean up event listeners after execution completes
      setTimeout(() => {
        unsubscribeWorkflowEvents();
        unsubscribeNodeEvents();
        apixClient.unsubscribeFromWorkflow(currentWorkflowId);
      }, 300000); // 5 minutes timeout

    } catch (error) {
      console.error('Failed to execute workflow:', error);
      toast({
        title: 'Execution Error',
        description: error instanceof Error ? error.message : 'Failed to execute workflow',
        variant: 'destructive',
      });
      setIsExecuting(false);
    }
  }, [workflowId, nodes, edges, isExecuting, executionId, handleSaveWorkflow, toast]);

  // Handle real-time workflow events
  const handleWorkflowEvent = useCallback((event: any) => {
    const { type, data } = event;

    switch (type) {
      case 'workflow_started':
        setNodeStatuses(prev => ({
          ...prev,
          [data.startNodeId]: { status: 'running', startTime: Date.now() },
        }));
        break;

      case 'workflow_completed':
        setIsExecuting(false);
        setNodeStatuses(prev => {
          const updated = { ...prev };
          Object.keys(updated).forEach(nodeId => {
            if (updated[nodeId].status === 'running') {
              updated[nodeId] = { ...updated[nodeId], status: 'success' };
            }
          });
          return updated;
        });
        toast({
          title: 'Workflow Completed',
          description: 'Workflow execution completed successfully',
        });
        break;

      case 'workflow_failed':
        setIsExecuting(false);
        setNodeStatuses(prev => {
          const updated = { ...prev };
          Object.keys(updated).forEach(nodeId => {
            if (updated[nodeId].status === 'running') {
              updated[nodeId] = { ...updated[nodeId], status: 'error', error: data.error };
            }
          });
          return updated;
        });
        toast({
          title: 'Workflow Failed',
          description: data.error || 'Workflow execution failed',
          variant: 'destructive',
        });
        break;

      case 'tool_call_start':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              status: 'running',
              startTime: Date.now(),
              toolId: data.toolId,
              input: data.input,
            },
          }));
        }
        break;

      case 'tool_call_result':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              ...prev[data.nodeId],
              status: 'success',
              result: data.result,
              endTime: Date.now(),
              duration: Date.now() - (prev[data.nodeId]?.startTime || Date.now()),
            },
          }));
        }
        break;

      case 'tool_call_error':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              ...prev[data.nodeId],
              status: 'error',
              error: data.error,
              endTime: Date.now(),
            },
          }));
        }
        break;

      case 'agent_thinking':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              ...prev[data.nodeId],
              status: 'thinking',
              progress: data.progress,
              context: data.context,
            },
          }));
        }
        break;

      case 'agent_response':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              ...prev[data.nodeId],
              status: 'success',
              response: data.response,
              endTime: Date.now(),
              duration: Date.now() - (prev[data.nodeId]?.startTime || Date.now()),
            },
          }));
        }
        break;

      case 'text_chunk':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: {
              ...prev[data.nodeId],
              streamingText: (prev[data.nodeId]?.streamingText || '') + data.chunk,
              isComplete: data.isComplete,
            },
          }));
        }
        break;

      case 'state_update':
        if (data.entityType === 'workflow_node' && data.entityId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.entityId]: {
              ...prev[data.entityId],
              ...data.state,
            },
          }));
        }
        break;

      default:

    }
  }, [toast]);

  // Workflow validation
  const validateWorkflow = useCallback(() => {
    const errors: string[] = [];

    if (nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    // Check for unconnected nodes
    const connectedNodes = new Set<string>();
    edges.forEach(edge => {
      connectedNodes.add(edge.source);
      connectedNodes.add(edge.target);
    });

    const unconnectedNodes = nodes.filter(node =>
      node.type !== 'input' && node.type !== 'output' && !connectedNodes.has(node.id)
    );

    if (unconnectedNodes.length > 0) {
      errors.push(`Unconnected nodes: ${unconnectedNodes.map(n => n.data.label).join(', ')}`);
    }

    // Check for missing configuration
    const invalidNodes = nodes.filter(node => {
      const config = node.data.config;
      switch (node.type) {
        case 'agent':
          return !config?.agentId;
        case 'tool':
          return !config?.toolId;
        case 'condition':
          return !config?.condition;
        case 'hybrid':
          return !config?.agentId || !config?.toolIds?.length;
        default:
          return false;
      }
    });

    if (invalidNodes.length > 0) {
      errors.push(`Invalid configuration: ${invalidNodes.map(n => n.data.label).join(', ')}`);
    }

    // Check for circular dependencies
    if (hasCircularDependency()) {
      errors.push('Workflow contains circular dependencies');
    }

    return errors;
  }, [nodes, edges]);

  // Check for circular dependencies
  const hasCircularDependency = useCallback(() => {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingEdges = edges.filter(edge => edge.source === nodeId);
      for (const edge of outgoingEdges) {
        if (hasCycle(edge.target)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of nodes) {
      if (!visited.has(node.id) && hasCycle(node.id)) {
        return true;
      }
    }

    return false;
  }, [nodes, edges]);

  // Helper functions for node inputs/outputs
  const getNodeInputs = (node: Node) => {
    return edges.filter(edge => edge.target === node.id).map(edge => edge.source);
  };

  const getNodeOutputs = (node: Node) => {
    return edges.filter(edge => edge.source === node.id).map(edge => edge.target);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSaveWorkflow();
            break;
          case 'r':
            event.preventDefault();
            handleRunWorkflow();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleSaveWorkflow, handleRunWorkflow]);

  const filteredNodeTypes = [
    "agent",
    "tool",
    "condition",
    "parallel",
    "human_input",
    "delay",
    "hybrid",
    "input",
    "output",
  ].filter((type) =>
    searchTerm ? type.includes(searchTerm.toLowerCase()) : true,
  );

  const getNodeIcon = (type: Node["type"]) => {
    switch (type) {
      case "agent":
        return <Bot className="h-5 w-5" />;
      case "tool":
        return <Wrench className="h-5 w-5" />;
      case "condition":
        return <GitBranch className="h-5 w-5" />;
      case "parallel":
        return <Split className="h-5 w-5" />;
      case "human_input":
        return <User className="h-5 w-5" />;
      case "delay":
        return <Clock className="h-5 w-5" />;
      case "hybrid":
        return <Layers className="h-5 w-5" />;
      case "input":
        return <ArrowDown className="h-5 w-5" />;
      case "output":
        return <ArrowRight className="h-5 w-5" />;
      default:
        return <Workflow className="h-5 w-5" />;
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <Workflow className="h-6 w-6" />
          <Input
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
            className="w-64 h-9 text-lg font-medium"
            disabled={readOnly}
          />
          <Badge variant="outline" className="ml-2">
            {workflowId ? "Saved" : "Draft"}
          </Badge>
          {isExecuting && (
            <Badge variant="default" className="bg-blue-100 text-blue-800">
              <Activity className="h-3 w-3 mr-1 animate-pulse" />
              Executing
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {!readOnly && (
            <>
              <Button
                variant="outline"
                onClick={handleSaveWorkflow}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
              <Button
                onClick={handleRunWorkflow}
                disabled={isExecuting || nodes.length === 0}
              >
                {isExecuting ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Run
                  </>
                )}
              </Button>
            </>
          )}
          <Button variant="outline">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Node Palette */}
        <div className="w-64 border-r p-4 flex flex-col">
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Agents</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("agent")}
                    disabled={readOnly}
                  >
                    <Bot className="mr-2 h-4 w-4" /> Agent
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Tools</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("tool")}
                    disabled={readOnly}
                  >
                    <Wrench className="mr-2 h-4 w-4" /> Generic Tool
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("tool")}
                    disabled={readOnly}
                  >
                    <Search className="mr-2 h-4 w-4" /> Search Tool
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("tool")}
                    disabled={readOnly}
                  >
                    <Database className="mr-2 h-4 w-4" /> Database Tool
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("tool")}
                    disabled={readOnly}
                  >
                    <Code className="mr-2 h-4 w-4" /> Code Execution
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Flow Control</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("condition")}
                    disabled={readOnly}
                  >
                    <GitBranch className="mr-2 h-4 w-4" /> Condition
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("parallel")}
                    disabled={readOnly}
                  >
                    <Split className="mr-2 h-4 w-4" /> Parallel
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("delay")}
                    disabled={readOnly}
                  >
                    <Clock className="mr-2 h-4 w-4" /> Delay
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("human_input")}
                    disabled={readOnly}
                  >
                    <User className="mr-2 h-4 w-4" /> Human Input
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Advanced</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("hybrid")}
                    disabled={readOnly}
                  >
                    <Layers className="mr-2 h-4 w-4" /> Hybrid Agent-Tool
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Input/Output</h3>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("input")}
                    disabled={readOnly}
                  >
                    <ArrowDown className="mr-2 h-4 w-4" /> Input
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start"
                    onClick={() => handleAddNode("output")}
                    disabled={readOnly}
                  >
                    <ArrowRight className="mr-2 h-4 w-4" /> Output
                  </Button>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Main Canvas and Properties Panel */}
        <div className="flex-1 flex flex-col">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex-1 flex flex-col"
          >
            <div className="border-b px-4">
              <TabsList>
                <TabsTrigger value="canvas">Canvas</TabsTrigger>
                <TabsTrigger value="properties">Properties</TabsTrigger>
                <TabsTrigger value="code">Code</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="canvas" className="flex-1 p-4 overflow-auto">
              <div className="bg-muted/20 rounded-lg border border-dashed h-full w-full flex flex-col items-center justify-center relative">
                <div className="absolute inset-0 overflow-auto p-4">
                  {nodes.map((node) => {
                    const nodeStatus = nodeStatuses[node.id];
                    return (
                      <div
                        key={node.id}
                        className={`absolute p-4 rounded-md border transition-all duration-200 ${selectedNode?.id === node.id
                          ? "border-primary ring-2 ring-primary/20"
                          : "border-border"
                          } ${nodeStatus?.status === 'running' ? 'border-blue-500 bg-blue-50' :
                            nodeStatus?.status === 'success' ? 'border-green-500 bg-green-50' :
                              nodeStatus?.status === 'error' ? 'border-red-500 bg-red-50' :
                                nodeStatus?.status === 'thinking' ? 'border-purple-500 bg-purple-50' :
                                  'bg-background'
                          }`}
                        style={{
                          left: `${node.position.x}px`,
                          top: `${node.position.y}px`,
                          minWidth: "120px",
                          cursor: "pointer",
                        }}
                        onClick={() => handleNodeClick(node)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getNodeIcon(node.type)}
                            <span className="ml-2 font-medium">
                              {node.data.label}
                            </span>
                          </div>
                          {nodeStatus && (
                            <div className="flex items-center space-x-1">
                              {nodeStatus.status === 'running' && (
                                <Activity className="h-3 w-3 text-blue-600 animate-pulse" />
                              )}
                              {nodeStatus.status === 'success' && (
                                <CheckCircle className="h-3 w-3 text-green-600" />
                              )}
                              {nodeStatus.status === 'error' && (
                                <XCircle className="h-3 w-3 text-red-600" />
                              )}
                              {nodeStatus.status === 'thinking' && (
                                <MessageSquare className="h-3 w-3 text-purple-600 animate-pulse" />
                              )}
                            </div>
                          )}
                        </div>

                        {node.data.description && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {node.data.description}
                          </p>
                        )}

                        {/* Show execution status */}
                        {nodeStatus && (
                          <div className="mt-2 text-xs">
                            {nodeStatus.status === 'running' && nodeStatus.progress && (
                              <div className="w-full bg-gray-200 rounded-full h-1">
                                <div
                                  className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                                  style={{ width: `${nodeStatus.progress}%` }}
                                />
                              </div>
                            )}

                            {nodeStatus.duration && (
                              <div className="text-muted-foreground">
                                {nodeStatus.duration}ms
                              </div>
                            )}

                            {nodeStatus.error && (
                              <div className="text-red-600 truncate">
                                Error: {nodeStatus.error.message || nodeStatus.error}
                              </div>
                            )}

                            {nodeStatus.streamingText && (
                              <div className="text-gray-700 truncate font-mono">
                                {nodeStatus.streamingText}
                                {!nodeStatus.isComplete && <span className="animate-pulse">|</span>}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}

                  {/* Simple visual representation of edges */}
                  <svg className="absolute inset-0 pointer-events-none">
                    {edges.map((edge) => {
                      const sourceNode = nodes.find(
                        (n) => n.id === edge.source,
                      );
                      const targetNode = nodes.find(
                        (n) => n.id === edge.target,
                      );

                      if (!sourceNode || !targetNode) return null;

                      const sourceX = sourceNode.position.x + 60;
                      const sourceY = sourceNode.position.y + 30;
                      const targetX = targetNode.position.x + 60;
                      const targetY = targetNode.position.y + 30;

                      // Calculate midpoint for edge label
                      const midX = (sourceX + targetX) / 2;
                      const midY = (sourceY + targetY) / 2;

                      return (
                        <g key={edge.id} onClick={() => handleEdgeClick(edge)} style={{ cursor: "pointer" }}>
                          <line
                            x1={sourceX}
                            y1={sourceY}
                            x2={targetX}
                            y2={targetY}
                            stroke={selectedEdge?.id === edge.id ? "var(--primary)" : "var(--border)"}
                            strokeWidth={selectedEdge?.id === edge.id ? "3" : "2"}
                            markerEnd="url(#arrowhead)"
                          />
                          {edge.condition && (
                            <foreignObject
                              x={midX - 50}
                              y={midY - 10}
                              width="100"
                              height="20"
                            >
                              <div className="bg-background/80 text-xs px-1 rounded border border-border text-center">
                                {edge.condition.length > 15
                                  ? edge.condition.substring(0, 15) + "..."
                                  : edge.condition}
                              </div>
                            </foreignObject>
                          )}
                          <defs>
                            <marker
                              id="arrowhead"
                              markerWidth="10"
                              markerHeight="7"
                              refX="9"
                              refY="3.5"
                              orient="auto"
                            >
                              <polygon
                                points="0 0, 10 3.5, 0 7"
                                fill={selectedEdge?.id === edge.id ? "var(--primary)" : "var(--border)"}
                              />
                            </marker>
                          </defs>
                        </g>
                      );
                    })}
                  </svg>
                </div>

                {nodes.length === 0 && (
                  <div className="text-center">
                    <Workflow className="h-12 w-12 mx-auto text-muted-foreground" />
                    <h3 className="mt-2 text-lg font-medium">Empty Workflow</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Drag nodes from the palette to start building your
                      workflow
                    </p>
                    <Button
                      onClick={() => handleAddNode("input")}
                      disabled={readOnly}
                    >
                      <Plus className="mr-2 h-4 w-4" /> Add Start Node
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent
              value="properties"
              className="flex-1 p-4 overflow-auto"
            >
              {selectedNode ? (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-md font-medium">
                      {getNodeIcon(selectedNode.type)}
                      <span className="ml-2">
                        {selectedNode.type.charAt(0).toUpperCase() +
                          selectedNode.type.slice(1)}{" "}
                        Properties
                      </span>
                    </CardTitle>
                    <div className="flex space-x-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                // Clone node functionality would go here
                                const clonedNode = {
                                  ...selectedNode,
                                  id: `${nodes.length + 1}`,
                                  position: {
                                    x: selectedNode.position.x + 20,
                                    y: selectedNode.position.y + 20,
                                  },
                                };
                                setNodes([...nodes, clonedNode]);
                              }}
                              disabled={readOnly}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Duplicate node</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={handleDeleteNode}
                              disabled={readOnly}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete node</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Label</label>
                        <Input
                          value={selectedNode.data.label}
                          onChange={(e) =>
                            handleUpdateNodeData({ label: e.target.value })
                          }
                          disabled={readOnly}
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Description
                        </label>
                        <Input
                          value={selectedNode.data.description || ""}
                          onChange={(e) =>
                            handleUpdateNodeData({
                              description: e.target.value,
                            })
                          }
                          disabled={readOnly}
                        />
                      </div>

                      {selectedNode.type === "agent" && (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Agent
                            </label>
                            <Select
                              value={selectedNode.data.config?.agentId || ""}
                              disabled={readOnly}
                              onValueChange={(value) =>
                                handleUpdateNodeConfig({
                                  agentId: value,
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select agent" />
                              </SelectTrigger>
                              <SelectContent>
                                {agents.map(agent => (
                                  <SelectItem key={agent.id} value={agent.id}>
                                    {agent.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              System Prompt
                            </label>
                            <Textarea
                              value={selectedNode.data.config?.systemPrompt || ""}
                              onChange={(e) =>
                                handleUpdateNodeConfig({
                                  systemPrompt: e.target.value,
                                })
                              }
                              disabled={readOnly}
                              rows={3}
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Max Tokens
                            </label>
                            <Input
                              type="number"
                              value={selectedNode.data.config?.maxTokens || 1000}
                              onChange={(e) =>
                                handleUpdateNodeConfig({
                                  maxTokens: parseInt(e.target.value),
                                })
                              }
                              disabled={readOnly}
                            />
                          </div>
                        </div>
                      )}

                      {selectedNode.type === "tool" && (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Tool
                            </label>
                            <Select
                              value={selectedNode.data.config?.toolId || ""}
                              disabled={readOnly}
                              onValueChange={(value) =>
                                handleUpdateNodeConfig({
                                  toolId: value,
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select tool" />
                              </SelectTrigger>
                              <SelectContent>
                                {tools.map(tool => (
                                  <SelectItem key={tool.id} value={tool.id}>
                                    {tool.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Parameters (JSON)
                            </label>
                            <Textarea
                              value={JSON.stringify(selectedNode.data.config?.parameters || {}, null, 2)}
                              onChange={(e) => {
                                try {
                                  const params = JSON.parse(e.target.value);
                                  handleUpdateNodeConfig({
                                    parameters: params,
                                  });
                                } catch (error) {
                                  // Handle invalid JSON
                                }
                              }}
                              disabled={readOnly}
                              rows={3}
                            />
                          </div>
                        </div>
                      )}

                      {selectedNode.type === "condition" && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium">
                            Condition Expression
                          </label>
                          <Input
                            value={selectedNode.data.config?.condition || ""}
                            onChange={(e) =>
                              handleUpdateNodeConfig({
                                condition: e.target.value,
                              })
                            }
                            disabled={readOnly}
                            placeholder="e.g. ${variable} > 10"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Use $&#123;variable&#125; syntax to reference workflow variables
                          </p>
                        </div>
                      )}

                      {selectedNode.type === "hybrid" && (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Execution Pattern
                            </label>
                            <Select
                              value={selectedNode.data.config?.executionPattern || "agent-first"}
                              disabled={readOnly}
                              onValueChange={(value) =>
                                handleUpdateNodeConfig({
                                  executionPattern: value,
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select pattern" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="agent-first">Agent First</SelectItem>
                                <SelectItem value="tool-first">Tool First</SelectItem>
                                <SelectItem value="parallel">Parallel</SelectItem>
                                <SelectItem value="multi-tool-orchestration">Multi-Tool Orchestration</SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-xs text-muted-foreground mt-1">
                              {selectedNode.data.config?.executionPattern === "agent-first" && "Agent executes first, then tools based on agent output"}
                              {selectedNode.data.config?.executionPattern === "tool-first" && "Tools execute first, then agent processes tool results"}
                              {selectedNode.data.config?.executionPattern === "parallel" && "Agent and tools execute in parallel"}
                              {selectedNode.data.config?.executionPattern === "multi-tool-orchestration" && "Agent coordinates multiple tools in sequence"}
                            </p>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Agent
                            </label>
                            <Select
                              value={selectedNode.data.config?.agentId || ""}
                              disabled={readOnly}
                              onValueChange={(value) =>
                                handleUpdateNodeConfig({
                                  agentId: value,
                                })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select agent" />
                              </SelectTrigger>
                              <SelectContent>
                                {agents.map(agent => (
                                  <SelectItem key={agent.id} value={agent.id}>
                                    {agent.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Tools
                            </label>
                            <div className="space-y-2 border rounded-md p-2">
                              {tools.map(tool => (
                                <div key={tool.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`tool-${tool.id}`}
                                    checked={(selectedNode.data.config?.toolIds || []).includes(tool.id)}
                                    onCheckedChange={(checked) => {
                                      const currentTools = selectedNode.data.config?.toolIds || [];
                                      const newTools = checked
                                        ? [...currentTools, tool.id]
                                        : currentTools.filter((id: string) => id !== tool.id);

                                      handleUpdateNodeConfig({
                                        toolIds: newTools,
                                      });
                                    }}
                                    disabled={readOnly}
                                  />
                                  <Label htmlFor={`tool-${tool.id}`}>{tool.name}</Label>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">
                              Max Iterations
                            </label>
                            <Input
                              type="number"
                              value={selectedNode.data.config?.maxIterations || 5}
                              onChange={(e) =>
                                handleUpdateNodeConfig({
                                  maxIterations: parseInt(e.target.value),
                                })
                              }
                              disabled={readOnly}
                            />
                            <p className="text-xs text-muted-foreground mt-1">
                              Maximum number of iterations for multi-tool orchestration
                            </p>
                          </div>
                        </div>
                      )}

                      <Separator />

                      <div className="pt-2">
                        <h4 className="text-sm font-medium mb-2">
                          Node Information
                        </h4>
                        <div className="text-sm">
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">ID:</span>
                            <span>{selectedNode.id}</span>
                          </div>
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">Type:</span>
                            <span>{selectedNode.type}</span>
                          </div>
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">
                              Position:
                            </span>
                            <span>
                              x: {selectedNode.position.x}, y:{" "}
                              {selectedNode.position.y}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : selectedEdge ? (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-md font-medium">
                      <ArrowRight className="inline-block mr-2 h-5 w-5" />
                      Edge Properties
                    </CardTitle>
                    <div className="flex space-x-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={handleDeleteEdge}
                              disabled={readOnly}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete edge</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Condition</label>
                        <Input
                          value={selectedEdge.condition || ""}
                          onChange={(e) =>
                            handleUpdateEdge({ condition: e.target.value })
                          }
                          disabled={readOnly}
                          placeholder="e.g. $result.success === true"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Use $result to reference the source node's result
                        </p>
                      </div>

                      <Separator />

                      <div className="pt-2">
                        <h4 className="text-sm font-medium mb-2">
                          Edge Information
                        </h4>
                        <div className="text-sm">
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">ID:</span>
                            <span>{selectedEdge.id}</span>
                          </div>
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">Source:</span>
                            <span>{selectedEdge.source}</span>
                          </div>
                          <div className="flex justify-between py-1">
                            <span className="text-muted-foreground">Target:</span>
                            <span>{selectedEdge.target}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="flex h-full items-center justify-center">
                  <div className="text-center">
                    <Settings className="h-12 w-12 mx-auto text-muted-foreground" />
                    <h3 className="mt-2 text-lg font-medium">
                      No Item Selected
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Select a node or edge on the canvas to view and edit its
                      properties
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="code" className="flex-1 p-4 overflow-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Workflow Code</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                    {JSON.stringify({
                      name: workflowName,
                      definition: {
                        nodes,
                        edges,
                        triggers: [{ type: "manual", config: {} }],
                        settings: {},
                      }
                    }, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default WorkflowBuilder;