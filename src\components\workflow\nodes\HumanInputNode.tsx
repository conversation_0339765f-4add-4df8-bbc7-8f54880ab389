import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, CheckCircle, XCircle, Activity, AlertTriangle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface HumanInputNodeData {
  label: string;
  description?: string;
  config?: {
    prompt?: string;
    inputType?: 'text' | 'number' | 'boolean' | 'select';
    timeout?: number;
    allowSkip?: boolean;
  };
  status?: 'idle' | 'running' | 'success' | 'error' | 'waiting';
  isConfigValid?: boolean;
  lastOutput?: string;
  errorMessage?: string;
}

const HumanInputNode: React.FC<NodeProps<HumanInputNodeData>> = ({ data, selected }) => {
  const getStatusIcon = () => {
    switch (data.status) {
      case 'running':
      case 'waiting':
        return <Activity className="h-3 w-3 text-blue-600 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
      case 'waiting':
        return 'border-blue-500 bg-blue-50';
      case 'success':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  return (
    <Card
      className={cn(
        'min-w-[200px] max-w-[300px] transition-all duration-200',
        'hover:shadow-lg cursor-pointer',
        selected && 'ring-2 ring-primary ring-offset-2',
        getStatusColor(),
        !data.isConfigValid && 'border-red-300 bg-red-50'
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 border-2 border-white"
      />
      
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-indigo-600" />
            {getStatusIcon()}
          </div>
          {!data.isConfigValid && (
            <AlertTriangle className="h-4 w-4 text-red-500" />
          )}
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1 truncate">{data.label}</h3>

        {/* Type badge */}
        <Badge variant="outline" className="text-xs mb-2">
          <User className="h-2 w-2 mr-1" />
          Human Input
        </Badge>

        {/* Description */}
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
            {data.description}
          </p>
        )}

        {/* Prompt preview */}
        {data.config?.prompt && (
          <div className="mb-2 p-2 bg-gray-100 border border-gray-200 rounded text-xs">
            <div className="font-medium text-gray-800 mb-1">Prompt:</div>
            <p className="text-gray-700 line-clamp-2">
              {data.config.prompt}
            </p>
          </div>
        )}

        {/* Waiting status */}
        {data.status === 'waiting' && (
          <div className="mb-2 p-2 bg-blue-100 border border-blue-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <Clock className="h-3 w-3 text-blue-600" />
              <span className="font-medium text-blue-800">Waiting for Input</span>
            </div>
            <p className="text-blue-700">User input required to continue</p>
          </div>
        )}

        {/* Status-specific content */}
        {data.status === 'error' && data.errorMessage && (
          <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <XCircle className="h-3 w-3 text-red-600" />
              <span className="font-medium text-red-800">Error</span>
            </div>
            <p className="text-red-700 line-clamp-2">{data.errorMessage}</p>
          </div>
        )}

        {/* Success output */}
        {data.status === 'success' && data.lastOutput && (
          <div className="mb-2 p-2 bg-green-100 border border-green-200 rounded text-xs">
            <div className="flex items-center space-x-1 mb-1">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="font-medium text-green-800">Input Received</span>
            </div>
            <p className="text-green-700 line-clamp-2 font-mono">
              {data.lastOutput}
            </p>
          </div>
        )}

        {/* Configuration summary */}
        {data.config && (
          <div className="text-xs text-muted-foreground space-y-1">
            {data.config.inputType && (
              <div>Type: {data.config.inputType}</div>
            )}
            {data.config.timeout && (
              <div>Timeout: {Math.round(data.config.timeout / 60000)}m</div>
            )}
            {data.config.allowSkip && (
              <div>Skippable: Yes</div>
            )}
          </div>
        )}
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(HumanInputNode);