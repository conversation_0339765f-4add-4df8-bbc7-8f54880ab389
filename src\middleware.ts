import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getAuthenticatedSession, unauthorizedResponse, forbiddenResponse } from '@/lib/auth/auth-service';

// Paths that don't require authentication
const publicPaths = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/verify-email',
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/refresh',
    '/api/auth/logout',
    '/api/auth/forgot-password',
    '/api/auth/reset-password',
    '/api/auth/verify-email',
];

// Paths that require authentication but are exempt from role checks
const exemptPaths = [
    '/api/auth/session',
    '/api/auth/ws-token',
];

// Use centralized auth service for all authentication

export async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Check if the path is public
    if (publicPaths.some(path => pathname.startsWith(path))) {
        return NextResponse.next();
    }

    // Check if path is for static assets
    if (
        pathname.startsWith('/_next') ||
        pathname.startsWith('/favicon.ico') ||
        pathname.startsWith('/logo.svg') ||
        pathname.startsWith('/api/mcp') // MCP endpoints handled separately
    ) {
        return NextResponse.next();
    }

    try {
        // Get authenticated session using centralized auth service
        const session = await getAuthenticatedSession(request);

        // If no session, redirect to login
        if (!session) {
            // For API routes, return 401 Unauthorized
            if (pathname.startsWith('/api/')) {
                return unauthorizedResponse();
            }

            // For other routes, redirect to login
            const url = new URL('/auth/login', request.url);
            url.searchParams.set('from', encodeURIComponent(pathname));
            return NextResponse.redirect(url);
        }

        // Check if path is exempt from further checks
        if (exemptPaths.some(path => pathname.startsWith(path))) {
            return NextResponse.next();
        }

        // For admin routes, check if user has admin role
        if (pathname.startsWith('/admin') && !['SUPER_ADMIN', 'ORG_ADMIN', 'ADMIN'].includes(session.user.role)) {
            return forbiddenResponse();
        }

        // For API routes that require specific roles, add role checks here

        // Allow the request to proceed
        return NextResponse.next();
    } catch (error) {
        console.error('Authentication failed:', error);

        // For API routes, return 401 Unauthorized
        if (pathname.startsWith('/api/')) {
            return unauthorizedResponse('Authentication failed');
        }

        // For other routes, redirect to login
        const url = new URL('/auth/login', request.url);
        url.searchParams.set('from', encodeURIComponent(pathname));
        return NextResponse.redirect(url);
    }
}

// Configure the middleware to run on specific paths
export const config = {
    matcher: [
        /*
         * Match all request paths except for:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public folder files
         */
        '/((?!_next/static|_next/image|favicon.ico|public).*)',
    ],
};