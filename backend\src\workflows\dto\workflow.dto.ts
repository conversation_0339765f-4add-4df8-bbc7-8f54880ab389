import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

// Workflow Node Schema
const WorkflowNodeSchema = z.object({
  id: z.string().min(1, 'Node ID is required'),
  type: z.enum(['agent', 'tool', 'condition', 'parallel', 'human_input', 'delay']),
  name: z.string().min(1, 'Node name is required'),
  config: z.record(z.any()),
  position: z.object({
    x: z.number(),
    y: z.number(),
  }),
  inputs: z.array(z.string()).default([]),
  outputs: z.array(z.string()).default([]),
});

// Workflow Edge Schema
const WorkflowEdgeSchema = z.object({
  id: z.string().min(1, 'Edge ID is required'),
  source: z.string().min(1, 'Source node ID is required'),
  target: z.string().min(1, 'Target node ID is required'),
  condition: z.string().optional(),
});

// Workflow Trigger Schema
const WorkflowTriggerSchema = z.object({
  type: z.enum(['manual', 'scheduled', 'webhook', 'event']),
  config: z.record(z.any()),
});

// Workflow Settings Schema
const WorkflowSettingsSchema = z.object({
  timeout: z.number().positive().optional(),
  retryPolicy: z.object({
    maxRetries: z.number().min(0).max(10),
    backoffStrategy: z.enum(['linear', 'exponential']),
    retryDelay: z.number().positive(),
  }).optional(),
  errorHandling: z.object({
    onError: z.enum(['stop', 'continue', 'retry']),
    fallbackNode: z.string().optional(),
  }).optional(),
});

// Workflow Definition Schema
const WorkflowDefinitionSchema = z.object({
  nodes: z.array(WorkflowNodeSchema).min(1, 'Workflow must have at least one node'),
  edges: z.array(WorkflowEdgeSchema),
  triggers: z.array(WorkflowTriggerSchema).default([]),
  settings: WorkflowSettingsSchema.default({}),
});

// Create Workflow Schema
export const CreateWorkflowSchema = z.object({
  name: z.string().min(1, 'Workflow name is required').max(100),
  description: z.string().max(500).optional(),
  definition: WorkflowDefinitionSchema,
  tags: z.array(z.string()).max(20).optional(),
});

// Update Workflow Schema
export const UpdateWorkflowSchema = z.object({
  name: z.string().min(1, 'Workflow name is required').max(100).optional(),
  description: z.string().max(500).optional(),
  definition: WorkflowDefinitionSchema.optional(),
  tags: z.array(z.string()).max(20).optional(),
  isActive: z.boolean().optional(),
});

// Execute Workflow Schema
export const ExecuteWorkflowSchema = z.object({
  input: z.record(z.any()).default({}),
  variables: z.record(z.any()).default({}),
  options: z.object({
    timeout: z.number().positive().optional(),
    priority: z.enum(['low', 'normal', 'high']).default('normal'),
    async: z.boolean().default(false),
  }).default({}),
});

// Workflow Filter Schema
export const WorkflowFilterSchema = z.object({
  isActive: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  creatorId: z.string().optional(),
  search: z.string().optional(),
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
});

// Schedule Workflow Schema
export const ScheduleWorkflowSchema = z.object({
  cronExpression: z.string().min(1, 'Cron expression is required'),
  timezone: z.string().default('UTC'),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  maxRuns: z.number().positive().optional(),
  input: z.record(z.any()).default({}),
  isActive: z.boolean().default(true),
});

// Workflow Template Schema
export const WorkflowTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100),
  description: z.string().max(500).optional(),
  category: z.string().min(1, 'Category is required'),
  definition: WorkflowDefinitionSchema,
  tags: z.array(z.string()).max(20).default([]),
  isPublic: z.boolean().default(false),
  variables: z.array(z.object({
    name: z.string(),
    type: z.enum(['string', 'number', 'boolean', 'object']),
    description: z.string().optional(),
    required: z.boolean().default(false),
    defaultValue: z.any().optional(),
  })).default([]),
});

// Workflow Import Schema
export const WorkflowImportSchema = z.object({
  format: z.enum(['json', 'yaml']),
  data: z.string().min(1, 'Import data is required'),
  overwrite: z.boolean().default(false),
});

// Workflow Export Schema
export const WorkflowExportSchema = z.object({
  format: z.enum(['json', 'yaml']).default('json'),
  includeExecutions: z.boolean().default(false),
  includeMetadata: z.boolean().default(true),
});

// DTOs
export class CreateWorkflowDto extends createZodDto(CreateWorkflowSchema) {}
export class UpdateWorkflowDto extends createZodDto(UpdateWorkflowSchema) {}
export class ExecuteWorkflowDto extends createZodDto(ExecuteWorkflowSchema) {}
export class WorkflowFilterDto extends createZodDto(WorkflowFilterSchema) {}
export class ScheduleWorkflowDto extends createZodDto(ScheduleWorkflowSchema) {}
export class WorkflowTemplateDto extends createZodDto(WorkflowTemplateSchema) {}
export class WorkflowImportDto extends createZodDto(WorkflowImportSchema) {}
export class WorkflowExportDto extends createZodDto(WorkflowExportSchema) {}

// Response Types
export interface WorkflowResponse {
  id: string;
  name: string;
  description?: string;
  definition: any;
  tags: string[];
  version: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  executionCount: number;
  lastExecution?: {
    id: string;
    status: string;
    startedAt: string;
    completedAt?: string;
  };
}

export interface WorkflowExecutionResponse {
  id: string;
  workflowId: string;
  status: string;
  input: any;
  output?: any;
  error?: string;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  steps: Array<{
    id: string;
    stepId: string;
    name: string;
    type: string;
    status: string;
    input: any;
    output?: any;
    error?: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
  }>;
}

export interface WorkflowAnalyticsResponse {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  avgExecutionTime: number;
  executionsByStatus: Record<string, number>;
  executionsByDay: Array<{
    date: string;
    count: number;
    successCount: number;
    failureCount: number;
  }>;
  topWorkflows: Array<{
    id: string;
    name: string;
    executionCount: number;
    successRate: number;
  }>;
}