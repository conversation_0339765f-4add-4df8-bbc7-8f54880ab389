import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ToolManagerService } from './services/tool-manager.service';
import { ToolExecutionService, ToolExecutionContext } from './services/tool-execution.service';
import { ToolAnalyticsService } from './services/tool-analytics.service';
import { ToolCacheService } from './services/tool-cache.service';

@Injectable()
export class ToolsService {
  constructor(
    private prisma: PrismaService,
    private toolManager: ToolManagerService,
    private toolExecution: ToolExecutionService,
    private toolAnalytics: ToolAnalyticsService,
    private toolCache: ToolCacheService,
  ) { }

  // ============================================================================
  // MAIN TOOL OPERATIONS
  // ============================================================================

  async findAll(organizationId?: string) {
    return this.toolManager.searchTools({
      organizationId,
      limit: 100,
    });
  }

  async findOne(id: string, organizationId?: string) {
    return this.toolManager.getTool(id, organizationId);
  }

  async create(createToolDto: any, creatorId: string, organizationId?: string) {
    return this.toolManager.createTool(createToolDto, creatorId, organizationId);
  }

  async update(id: string, updateToolDto: any, userId: string, organizationId: string) {
    return this.toolManager.updateTool(id, updateToolDto, userId, organizationId);
  }

  async remove(id: string, userId: string, organizationId: string) {
    return this.toolManager.deleteTool(id, userId, organizationId);
  }

  // ============================================================================
  // TOOL EXECUTION
  // ============================================================================

  async executeTool(toolId: string, input: any, context: Partial<ToolExecutionContext>) {
    const executionContext: ToolExecutionContext = {
      toolId,
      input,
      executorType: context.executorType || 'user',
      executorId: context.executorId,
      sessionId: context.sessionId,
      organizationId: context.organizationId || '',
      metadata: context.metadata,
      timeout: context.timeout,
      retryPolicy: context.retryPolicy,
    };

    const result = await this.toolExecution.executeTool(executionContext);

    // Record analytics
    await this.toolAnalytics.recordExecution({
      toolId,
      organizationId: executionContext.organizationId,
      status: result.status,
      duration: result.duration || 0,
      tokensUsed: result.tokensUsed,
      cost: result.cost,
      cached: result.cached,
    });

    return result;
  }

  // ============================================================================
  // TOOL SEARCH AND DISCOVERY
  // ============================================================================

  async searchTools(searchDto: any, organizationId?: string) {
    const {
      query,
      type,
      category,
      tags,
      isPublic,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = searchDto;

    return this.toolManager.searchTools({
      query,
      type,
      category,
      tags,
      isPublic,
      organizationId,
      page,
      limit,
      sortBy,
      sortOrder
    });
  }

  async getPopularTools(organizationId?: string, limit = 10) {
    return this.toolManager.getPopularTools(organizationId, limit);
  }

  async getToolStats(organizationId?: string) {
    return this.toolManager.getToolStats(organizationId);
  }

  // ============================================================================
  // TOOL TESTING AND VALIDATION
  // ============================================================================

  async testTool(toolId: string, input: any, organizationId?: string) {
    const tool = await this.toolManager.getTool(toolId, organizationId);
    if (!tool) {
      throw new Error('Tool not found');
    }

    const testContext: ToolExecutionContext = {
      toolId,
      input,
      executorType: 'test',
      executorId: 'system',
      sessionId: `test-${Date.now()}`,
      organizationId: organizationId || '',
      metadata: { isTest: true },
      timeout: 30000,
    };

    const result = await this.toolExecution.executeTool(testContext);

    // Don't record test executions in regular analytics
    return result;
  }

  async validateToolInput(toolId: string, input: any, organizationId?: string) {
    const tool = await this.toolManager.getTool(toolId, organizationId);
    if (!tool) {
      throw new Error('Tool not found');
    }

    // Validate input against tool's input schema
    const errors: string[] = [];
    
    if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
      try {
        // This would use a proper JSON Schema validator
        // For now, basic validation
        if (tool.inputSchema.required) {
          for (const field of tool.inputSchema.required) {
            if (!(field in input)) {
              errors.push(`Required field '${field}' is missing`);
            }
          }
        }
      } catch (error) {
        errors.push(`Schema validation error: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ============================================================================
  // TOOL ANALYTICS
  // ============================================================================

  async getToolAnalytics(toolId: string, organizationId?: string, timeRange?: { start: Date; end: Date }) {
    const range = timeRange || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      end: new Date(),
    };

    return this.toolAnalytics.getToolAnalytics(toolId, range);
  }

  async getToolPerformanceMetrics(toolId: string, days: number = 30) {
    return this.toolAnalytics.getToolPerformanceMetrics(toolId, days);
  }

  async getOrganizationAnalytics(organizationId: string, days: number = 30) {
    return this.toolAnalytics.getOrganizationToolAnalytics(organizationId, days);
  }

  async getTopPerformingTools(organizationId?: string, limit: number = 10) {
    return this.toolAnalytics.getTopPerformingTools(organizationId, limit);
  }

  // ============================================================================
  // TOOL EXECUTION HISTORY
  // ============================================================================

  async getExecutionHistory(toolId: string, organizationId?: string, pagination?: { page: number; limit: number }) {
    const { page = 1, limit = 20 } = pagination || {};
    
    return this.prisma.toolExecution.findMany({
      where: {
        toolId,
        organizationId,
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit,
      include: {
        tool: {
          select: {
            name: true,
            type: true
          }
        }
      }
    });
  }

  async getExecutionDetails(executionId: string, organizationId?: string) {
    return this.prisma.toolExecution.findFirst({
      where: {
        id: executionId,
        organizationId,
      },
      include: {
        tool: {
          select: {
            name: true,
            type: true,
            category: true,
          }
        }
      }
    });
  }

  // ============================================================================
  // TOOL VERSIONING
  // ============================================================================

  async createToolVersion(toolId: string, versionData: any, creatorId: string) {
    return this.toolManager.createToolVersion(toolId, versionData, creatorId);
  }

  async getToolVersions(toolId: string, organizationId?: string) {
    return this.toolManager.getToolVersions(toolId, organizationId);
  }

  // ============================================================================
  // TOOL IMPORT/EXPORT
  // ============================================================================

  async importTool(importOptions: any, creatorId: string, organizationId?: string) {
    return this.toolManager.importTool(importOptions, creatorId, organizationId);
  }

  async exportTool(toolId: string, format: string, organizationId?: string) {
    return this.toolManager.exportTool(toolId, format, organizationId);
  }

  // ============================================================================
  // TOOL CACHING
  // ============================================================================

  async getCacheStats(toolId?: string) {
    return this.toolCache.getCacheStats(toolId);
  }

  async invalidateCache(toolId: string, reason?: string) {
    return this.toolCache.invalidateCache(toolId, reason);
  }

  async optimizeCache(toolId?: string) {
    return this.toolCache.optimizeCache(toolId);
  }

  async warmCache(toolId: string, strategy: 'popular' | 'recent' | 'predicted' = 'popular') {
    return this.toolCache.warmCache(toolId, strategy);
  }

  async getCacheAnalytics(toolId: string, days: number = 30) {
    return this.toolCache.getCacheAnalytics(toolId, days);
  }

  // ============================================================================
  // TOOL CATEGORIES AND TEMPLATES
  // ============================================================================

  async getCategories(organizationId?: string) {
    const categories = await this.prisma.toolDefinition.groupBy({
      by: ['category'],
      where: {
        OR: [
          { organizationId },
          { isPublic: true },
        ],
        isActive: true,
      },
      _count: {
        category: true
      }
    });

    return categories.map(cat => ({
      name: cat.category,
      count: cat._count.category
    }));
  }

  async getToolsByCategory(category: string, organizationId?: string) {
    return this.toolManager.searchTools({
      category: category as any,
      organizationId,
      limit: 50,
    });
  }

  // ============================================================================
  // TOOL COMPOSITIONS
  // ============================================================================

  async createToolComposition(compositionData: any, creatorId: string, organizationId: string) {
    return this.prisma.toolComposition.create({
      data: {
        ...compositionData,
        createdBy: creatorId,
        organizationId,
      },
      include: {
        steps: {
          include: {
            tool: {
              select: {
                name: true,
                type: true,
              }
            }
          }
        }
      }
    });
  }

  async getToolCompositions(organizationId: string) {
    return this.prisma.toolComposition.findMany({
      where: {
        organizationId,
        isActive: true,
      },
      include: {
        creator: {
          select: {
            firstName: true,
            lastName: true,
          }
        },
        steps: {
          include: {
            tool: {
              select: {
                name: true,
                type: true,
              }
            }
          }
        },
        _count: {
          select: {
            executions: true,
          }
        }
      }
    });
  }

  async executeToolComposition(compositionId: string, input: any, context: Partial<ToolExecutionContext>) {
    const composition = await this.prisma.toolComposition.findUnique({
      where: { id: compositionId },
      include: {
        steps: {
          orderBy: { stepIndex: 'asc' },
          include: {
            tool: true,
          }
        }
      }
    });

    if (!composition) {
      throw new Error('Tool composition not found');
    }

    // Create composition execution record
    const execution = await this.prisma.toolCompositionExecution.create({
      data: {
        compositionId,
        executorType: context.executorType || 'user',
        executorId: context.executorId,
        sessionId: context.sessionId,
        organizationId: context.organizationId || '',
        status: 'PENDING',
        input,
        totalSteps: composition.steps.length,
      }
    });

    try {
      let currentVariables = { ...input };
      const stepResults: any = {};

      for (const step of composition.steps) {
        // Map input for this step
        const stepInput = this.mapStepInput(step.inputMapping, currentVariables);

        // Execute the tool
        const stepResult = await this.executeTool(step.toolId, stepInput, {
          ...context,
          metadata: {
            ...context.metadata,
            compositionId,
            executionId: execution.id,
            stepIndex: step.stepIndex,
          }
        });

        stepResults[step.stepIndex] = stepResult;

        // Map output to variables
        if (step.outputMapping) {
          const mappedOutput = this.mapStepOutput(step.outputMapping, stepResult.output);
          currentVariables = { ...currentVariables, ...mappedOutput };
        }

        // Update execution progress
        await this.prisma.toolCompositionExecution.update({
          where: { id: execution.id },
          data: {
            currentStep: step.stepIndex + 1,
            stepResults,
            variables: currentVariables,
          }
        });

        // Check for step failure
        if (stepResult.status === 'FAILED' && !step.continueOnError) {
          throw new Error(`Step ${step.stepIndex} failed: ${stepResult.error}`);
        }
      }

      // Mark execution as completed
      await this.prisma.toolCompositionExecution.update({
        where: { id: execution.id },
        data: {
          status: 'COMPLETED',
          output: currentVariables,
          completedAt: new Date(),
          duration: Date.now() - execution.createdAt.getTime(),
        }
      });

      return {
        executionId: execution.id,
        status: 'COMPLETED',
        output: currentVariables,
        stepResults,
      };

    } catch (error) {
      await this.prisma.toolCompositionExecution.update({
        where: { id: execution.id },
        data: {
          status: 'FAILED',
          error: error.message,
          completedAt: new Date(),
          duration: Date.now() - execution.createdAt.getTime(),
        }
      });

      return {
        executionId: execution.id,
        status: 'FAILED',
        error: error.message,
      };
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private mapStepInput(inputMapping: any, variables: any): any {
    if (!inputMapping || Object.keys(inputMapping).length === 0) {
      return variables;
    }

    const mapped: any = {};
    for (const [key, sourcePath] of Object.entries(inputMapping)) {
      mapped[key] = this.getNestedValue(variables, sourcePath as string);
    }
    return mapped;
  }

  private mapStepOutput(outputMapping: any, output: any): any {
    if (!outputMapping || Object.keys(outputMapping).length === 0) {
      return output;
    }

    const mapped: any = {};
    for (const [targetPath, sourceKey] of Object.entries(outputMapping)) {
      const value = this.getNestedValue(output, sourceKey as string);
      this.setNestedValue(mapped, targetPath, value);
    }
    return mapped;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }
}