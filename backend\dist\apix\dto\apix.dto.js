"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HybridAnalyticsEventDto = exports.ExecutionHandoffDto = exports.WorkflowStateUpdateDto = exports.HumanInputResponseDto = exports.HumanInputRequestDto = exports.HybridCoordinationEventDto = exports.HybridExecutionEventDto = exports.ApiXErrorDto = exports.ApiXHeartbeatDto = exports.ApiXEventReplayDto = exports.ApiXLatencyMetricDto = exports.ApiXChannelDto = exports.ApiXSubscriptionDto = exports.ApiXEventDto = exports.ApiXConnectionDto = exports.HybridEventType = exports.EventPriority = exports.ChannelType = exports.ConnectionStatus = exports.ClientType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var ClientType;
(function (ClientType) {
    ClientType["WEB_APP"] = "WEB_APP";
    ClientType["MOBILE_APP"] = "MOBILE_APP";
    ClientType["SDK_WIDGET"] = "SDK_WIDGET";
    ClientType["API_CLIENT"] = "API_CLIENT";
    ClientType["INTERNAL_SERVICE"] = "INTERNAL_SERVICE";
})(ClientType || (exports.ClientType = ClientType = {}));
var ConnectionStatus;
(function (ConnectionStatus) {
    ConnectionStatus["CONNECTED"] = "CONNECTED";
    ConnectionStatus["DISCONNECTED"] = "DISCONNECTED";
    ConnectionStatus["RECONNECTING"] = "RECONNECTING";
    ConnectionStatus["SUSPENDED"] = "SUSPENDED";
})(ConnectionStatus || (exports.ConnectionStatus = ConnectionStatus = {}));
var ChannelType;
(function (ChannelType) {
    ChannelType["AGENT_EVENTS"] = "AGENT_EVENTS";
    ChannelType["TOOL_EVENTS"] = "TOOL_EVENTS";
    ChannelType["WORKFLOW_EVENTS"] = "WORKFLOW_EVENTS";
    ChannelType["PROVIDER_EVENTS"] = "PROVIDER_EVENTS";
    ChannelType["SYSTEM_EVENTS"] = "SYSTEM_EVENTS";
    ChannelType["SESSION_EVENTS"] = "SESSION_EVENTS";
    ChannelType["PRIVATE_USER"] = "PRIVATE_USER";
    ChannelType["ORGANIZATION"] = "ORGANIZATION";
    ChannelType["PUBLIC"] = "PUBLIC";
})(ChannelType || (exports.ChannelType = ChannelType = {}));
var EventPriority;
(function (EventPriority) {
    EventPriority["LOW"] = "LOW";
    EventPriority["NORMAL"] = "NORMAL";
    EventPriority["HIGH"] = "HIGH";
    EventPriority["CRITICAL"] = "CRITICAL";
})(EventPriority || (exports.EventPriority = EventPriority = {}));
var HybridEventType;
(function (HybridEventType) {
    HybridEventType["HYBRID_EXECUTION_STARTED"] = "hybrid_execution_started";
    HybridEventType["HYBRID_EXECUTION_COMPLETED"] = "hybrid_execution_completed";
    HybridEventType["HYBRID_EXECUTION_FAILED"] = "hybrid_execution_failed";
    HybridEventType["HYBRID_EXECUTION_CANCELLED"] = "hybrid_execution_cancelled";
    HybridEventType["AGENT_FIRST_STARTED"] = "agent_first_started";
    HybridEventType["AGENT_FIRST_COMPLETED"] = "agent_first_completed";
    HybridEventType["TOOL_FIRST_STARTED"] = "tool_first_started";
    HybridEventType["TOOL_FIRST_COMPLETED"] = "tool_first_completed";
    HybridEventType["PARALLEL_EXECUTION_STARTED"] = "parallel_execution_started";
    HybridEventType["PARALLEL_EXECUTION_COMPLETED"] = "parallel_execution_completed";
    HybridEventType["ORCHESTRATION_STARTED"] = "orchestration_started";
    HybridEventType["ORCHESTRATION_DECISION"] = "orchestration_decision";
    HybridEventType["ORCHESTRATION_COMPLETED"] = "orchestration_completed";
    HybridEventType["AGENT_EXECUTION_STARTED"] = "agent_execution_started";
    HybridEventType["AGENT_EXECUTION_COMPLETED"] = "agent_execution_completed";
    HybridEventType["AGENT_EXECUTION_FAILED"] = "agent_execution_failed";
    HybridEventType["TOOL_EXECUTION_STARTED"] = "tool_execution_started";
    HybridEventType["TOOL_EXECUTION_COMPLETED"] = "tool_execution_completed";
    HybridEventType["TOOL_EXECUTION_FAILED"] = "tool_execution_failed";
    HybridEventType["SYNC_POINT_REACHED"] = "sync_point_reached";
    HybridEventType["CONTEXT_SHARED"] = "context_shared";
    HybridEventType["CONTEXT_UPDATED"] = "context_updated";
    HybridEventType["FALLBACK_TRIGGERED"] = "fallback_triggered";
    HybridEventType["FALLBACK_COMPLETED"] = "fallback_completed";
    HybridEventType["FALLBACK_FAILED"] = "fallback_failed";
    HybridEventType["HUMAN_INPUT_REQUESTED"] = "human_input_requested";
    HybridEventType["HUMAN_INPUT_RECEIVED"] = "human_input_received";
    HybridEventType["HUMAN_INPUT_TIMEOUT"] = "human_input_timeout";
    HybridEventType["STATE_SYNCHRONIZATION"] = "state_synchronization";
    HybridEventType["EXECUTION_HANDOFF"] = "execution_handoff";
    HybridEventType["COORDINATION_EVENT"] = "coordination_event";
})(HybridEventType || (exports.HybridEventType = HybridEventType = {}));
class ApiXConnectionDto {
}
exports.ApiXConnectionDto = ApiXConnectionDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ClientType }),
    (0, class_validator_1.IsEnum)(ClientType),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "clientType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXConnectionDto.prototype, "subscriptions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXConnectionDto.prototype, "metadata", void 0);
class ApiXEventDto {
}
exports.ApiXEventDto = ApiXEventDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], ApiXEventDto.prototype, "payload", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "correlationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: EventPriority, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(EventPriority),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ApiXEventDto.prototype, "compressed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXEventDto.prototype, "metadata", void 0);
class ApiXSubscriptionDto {
}
exports.ApiXSubscriptionDto = ApiXSubscriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXSubscriptionDto.prototype, "channels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXSubscriptionDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ApiXSubscriptionDto.prototype, "acknowledgment", void 0);
class ApiXChannelDto {
}
exports.ApiXChannelDto = ApiXChannelDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ChannelType }),
    (0, class_validator_1.IsEnum)(ChannelType),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXChannelDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXChannelDto.prototype, "metadata", void 0);
class ApiXLatencyMetricDto {
}
exports.ApiXLatencyMetricDto = ApiXLatencyMetricDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "connectionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "eventType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXLatencyMetricDto.prototype, "latencyMs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "organizationId", void 0);
class ApiXEventReplayDto {
}
exports.ApiXEventReplayDto = ApiXEventReplayDto;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventReplayDto.prototype, "since", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXEventReplayDto.prototype, "eventTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXEventReplayDto.prototype, "limit", void 0);
class ApiXHeartbeatDto {
}
exports.ApiXHeartbeatDto = ApiXHeartbeatDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXHeartbeatDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXHeartbeatDto.prototype, "latency", void 0);
class ApiXErrorDto {
}
exports.ApiXErrorDto = ApiXErrorDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "stack", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXErrorDto.prototype, "context", void 0);
class HybridExecutionEventDto {
}
exports.HybridExecutionEventDto = HybridExecutionEventDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "workflowId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: HybridEventType }),
    (0, class_validator_1.IsEnum)(HybridEventType),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "eventType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], HybridExecutionEventDto.prototype, "payload", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "agentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], HybridExecutionEventDto.prototype, "toolIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridExecutionEventDto.prototype, "executionPattern", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HybridExecutionEventDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HybridExecutionEventDto.prototype, "iteration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HybridExecutionEventDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HybridExecutionEventDto.prototype, "timestamp", void 0);
class HybridCoordinationEventDto {
}
exports.HybridCoordinationEventDto = HybridCoordinationEventDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridCoordinationEventDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridCoordinationEventDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridCoordinationEventDto.prototype, "coordinationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], HybridCoordinationEventDto.prototype, "payload", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridCoordinationEventDto.prototype, "sourceComponent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridCoordinationEventDto.prototype, "targetComponent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HybridCoordinationEventDto.prototype, "contextData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HybridCoordinationEventDto.prototype, "timestamp", void 0);
class HumanInputRequestDto {
}
exports.HumanInputRequestDto = HumanInputRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputRequestDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputRequestDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputRequestDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputRequestDto.prototype, "prompt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputRequestDto.prototype, "inputType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HumanInputRequestDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HumanInputRequestDto.prototype, "allowSkip", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HumanInputRequestDto.prototype, "validationRules", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HumanInputRequestDto.prototype, "timestamp", void 0);
class HumanInputResponseDto {
}
exports.HumanInputResponseDto = HumanInputResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputResponseDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputResponseDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputResponseDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], HumanInputResponseDto.prototype, "userInput", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HumanInputResponseDto.prototype, "inputType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], HumanInputResponseDto.prototype, "skipped", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HumanInputResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HumanInputResponseDto.prototype, "timestamp", void 0);
class WorkflowStateUpdateDto {
}
exports.WorkflowStateUpdateDto = WorkflowStateUpdateDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStateUpdateDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStateUpdateDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], WorkflowStateUpdateDto.prototype, "stateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], WorkflowStateUpdateDto.prototype, "currentState", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], WorkflowStateUpdateDto.prototype, "previousState", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], WorkflowStateUpdateDto.prototype, "stateChanges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowStateUpdateDto.prototype, "progress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], WorkflowStateUpdateDto.prototype, "timestamp", void 0);
class ExecutionHandoffDto {
}
exports.ExecutionHandoffDto = ExecutionHandoffDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecutionHandoffDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecutionHandoffDto.prototype, "fromComponent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecutionHandoffDto.prototype, "toComponent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExecutionHandoffDto.prototype, "handoffType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], ExecutionHandoffDto.prototype, "contextData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ExecutionHandoffDto.prototype, "handoffMetadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ExecutionHandoffDto.prototype, "timestamp", void 0);
class HybridAnalyticsEventDto {
}
exports.HybridAnalyticsEventDto = HybridAnalyticsEventDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridAnalyticsEventDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridAnalyticsEventDto.prototype, "nodeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HybridAnalyticsEventDto.prototype, "metricType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], HybridAnalyticsEventDto.prototype, "metricValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HybridAnalyticsEventDto.prototype, "dimensions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], HybridAnalyticsEventDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], HybridAnalyticsEventDto.prototype, "timestamp", void 0);
//# sourceMappingURL=apix.dto.js.map